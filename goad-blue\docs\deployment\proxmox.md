# Proxmox VE Deployment

This guide covers deploying GOAD-Blue on Proxmox Virtual Environment (VE), an open-source virtualization platform that combines KVM hypervisor and LXC containers.

## 🏗️ Proxmox Architecture Overview

```mermaid
graph TB
    subgraph "🖥️ Proxmox VE Host"
        PVE[🔧 Proxmox VE<br/>Hypervisor Host]
        
        subgraph "🌐 Network Configuration"
            VMBR0[🌉 vmbr0<br/>Management Bridge]
            VMBR1[🌉 vmbr1<br/>GOAD-Blue Network]
            VMBR2[🌉 vmbr2<br/>Isolated Analysis]
        end
        
        subgraph "💾 Storage Configuration"
            LOCAL[💽 local<br/>System Storage]
            LOCAL_LVM[💾 local-lvm<br/>VM Storage]
            NFS[🗄️ NFS/CIFS<br/>Shared Storage]
        end
    end
    
    subgraph "🎯 GOAD-Blue VMs"
        SPLUNK[📊 Splunk VM<br/>SIEM Platform]
        SO_MGR[🧅 Security Onion Manager<br/>Network Monitoring]
        SO_SENSOR[📡 Security Onion Sensor<br/>Traffic Analysis]
        VELO[🦖 Velociraptor<br/>Endpoint Visibility]
        MISP[🧠 MISP<br/>Threat Intelligence]
        FLARE[🔥 FLARE-VM<br/>Malware Analysis]
    end
    
    subgraph "🎮 GOAD Integration"
        GOAD_DC[🏰 GOAD Domain Controllers<br/>Attack Targets]
        GOAD_SRV[⚔️ GOAD Servers<br/>Member Systems]
        GOAD_WS[🖥️ GOAD Workstations<br/>User Systems]
    end
    
    PVE --> VMBR0
    PVE --> VMBR1
    PVE --> VMBR2
    
    PVE --> LOCAL
    PVE --> LOCAL_LVM
    PVE --> NFS
    
    VMBR1 --> SPLUNK
    VMBR1 --> SO_MGR
    VMBR1 --> SO_SENSOR
    VMBR1 --> VELO
    VMBR1 --> MISP
    VMBR2 --> FLARE
    
    VMBR1 --> GOAD_DC
    VMBR1 --> GOAD_SRV
    VMBR1 --> GOAD_WS
    
    classDef proxmox fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef network fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef storage fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef goadblue fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef goad fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    
    class PVE proxmox
    class VMBR0,VMBR1,VMBR2 network
    class LOCAL,LOCAL_LVM,NFS storage
    class SPLUNK,SO_MGR,SO_SENSOR,VELO,MISP,FLARE goadblue
    class GOAD_DC,GOAD_SRV,GOAD_WS goad
```

## 📋 Prerequisites

### **Proxmox VE Requirements**

#### **Hardware Requirements**
- **CPU**: Intel VT-x or AMD-V support (64-bit)
- **RAM**: 64GB minimum (128GB recommended)
- **Storage**: 1TB SSD minimum (2TB recommended)
- **Network**: Multiple network interfaces (recommended)

#### **Software Requirements**
- **Proxmox VE**: 7.4+ or 8.0+
- **ZFS or LVM**: For VM storage
- **Network Configuration**: Proper bridge setup

### **Proxmox VE Installation**

```bash
# Download Proxmox VE ISO
wget https://www.proxmox.com/en/downloads/category/iso-images-pve

# Create bootable USB
dd if=proxmox-ve_8.0-2.iso of=/dev/sdX bs=1M status=progress

# Boot from USB and follow installation wizard
# Configure:
# - Root password
# - Network settings (IP, gateway, DNS)
# - Storage configuration
```

### **Post-Installation Configuration**

```bash
# Update Proxmox VE
apt update && apt upgrade -y

# Configure repositories (remove enterprise repo if no subscription)
echo "deb http://download.proxmox.com/debian/pve bookworm pve-no-subscription" > /etc/apt/sources.list.d/pve-no-subscription.list

# Remove enterprise repository
rm /etc/apt/sources.list.d/pve-enterprise.list

# Update package lists
apt update

# Install additional packages
apt install -y git curl wget unzip
```

## 🌐 Network Configuration

### **Bridge Configuration**

```bash
# Edit network configuration
nano /etc/network/interfaces
```

```bash
# /etc/network/interfaces
auto lo
iface lo inet loopback

# Management interface
iface eno1 inet manual

# Management bridge (Proxmox web interface)
auto vmbr0
iface vmbr0 inet static
    address *************/24
    gateway ***********
    bridge-ports eno1
    bridge-stp off
    bridge-fd 0

# GOAD-Blue network bridge
auto vmbr1
iface vmbr1 inet static
    address *************/24
    bridge-ports none
    bridge-stp off
    bridge-fd 0
    post-up echo 1 > /proc/sys/net/ipv4/ip_forward
    post-up iptables -t nat -A POSTROUTING -s '*************/24' -o vmbr0 -j MASQUERADE
    post-down iptables -t nat -D POSTROUTING -s '*************/24' -o vmbr0 -j MASQUERADE

# Isolated analysis network
auto vmbr2
iface vmbr2 inet static
    address *************/24
    bridge-ports none
    bridge-stp off
    bridge-fd 0
    # No internet access for analysis network
```

```bash
# Apply network configuration
systemctl restart networking

# Verify bridges
ip addr show
brctl show
```

### **Firewall Configuration**

```bash
# Configure Proxmox firewall
# Enable firewall in datacenter settings via web interface

# Create firewall rules via CLI
pvesh create /cluster/firewall/rules --type in --action ACCEPT --proto tcp --dport 22 --comment "SSH Access"
pvesh create /cluster/firewall/rules --type in --action ACCEPT --proto tcp --dport 8006 --comment "Proxmox Web Interface"
pvesh create /cluster/firewall/rules --type in --action ACCEPT --proto tcp --dport 8000 --comment "Splunk Web"
pvesh create /cluster/firewall/rules --type in --action ACCEPT --proto tcp --dport 443 --comment "HTTPS Services"

# Enable firewall
pvesh set /cluster/firewall/options --enable 1
```

## 💾 Storage Configuration

### **Storage Types for GOAD-Blue**

#### **Local Storage (Default)**
```bash
# Check current storage
pvesm status

# Local storage is automatically configured
# /var/lib/vz/images/ for VM disks
# /var/lib/vz/template/ for templates
```

#### **ZFS Storage (Recommended)**
```bash
# Create ZFS pool (if not done during installation)
zpool create -f rpool /dev/sdb

# Create ZFS dataset for VMs
zfs create rpool/vmdata

# Add ZFS storage to Proxmox
pvesm add zfspool vmdata --pool rpool/vmdata --content images,rootdir
```

#### **NFS Storage (Enterprise)**
```bash
# Add NFS storage
pvesm add nfs nfs-storage \
    --server 192.168.1.200 \
    --export /volume1/proxmox \
    --content images,iso,vztmpl,backup

# Mount NFS storage
pvesm set nfs-storage --options vers=3
```

### **ISO Storage Setup**

```bash
# Create ISO directory
mkdir -p /var/lib/vz/template/iso

# Download required ISOs
cd /var/lib/vz/template/iso

# Ubuntu Server 22.04 LTS
wget https://releases.ubuntu.com/22.04/ubuntu-22.04.3-live-server-amd64.iso

# Windows Server 2019 (if needed for FLARE-VM)
# Download from Microsoft Volume Licensing Service Center

# Security Onion ISO
wget https://github.com/Security-Onion-Solutions/securityonion/releases/download/2.4.60/securityonion-2.4.60.iso

# Update storage
pvesm scan iso local
```

## 🚀 GOAD-Blue Deployment on Proxmox

### **Terraform Proxmox Provider**

#### **Provider Configuration**

```hcl
# terraform/providers/proxmox/main.tf
terraform {
  required_providers {
    proxmox = {
      source  = "telmate/proxmox"
      version = "2.9.14"
    }
  }
}

provider "proxmox" {
  pm_api_url      = var.proxmox_api_url
  pm_user         = var.proxmox_user
  pm_password     = var.proxmox_password
  pm_tls_insecure = var.proxmox_tls_insecure
  pm_parallel     = 2
  pm_timeout      = 600
}

# Variables
variable "proxmox_api_url" {
  description = "Proxmox API URL"
  type        = string
  default     = "https://*************:8006/api2/json"
}

variable "proxmox_user" {
  description = "Proxmox username"
  type        = string
  default     = "root@pam"
}

variable "proxmox_password" {
  description = "Proxmox password"
  type        = string
  sensitive   = true
}

variable "proxmox_tls_insecure" {
  description = "Disable TLS verification"
  type        = bool
  default     = true
}
```

#### **VM Template Creation**

```hcl
# terraform/modules/proxmox-vm/main.tf
resource "proxmox_vm_qemu" "goad_blue_vm" {
  count       = var.vm_count
  name        = "${var.vm_name}-${count.index + 1}"
  target_node = var.target_node
  
  # Template configuration
  clone      = var.template_name
  full_clone = true
  
  # VM specifications
  cores   = var.cores
  sockets = var.sockets
  memory  = var.memory
  
  # Storage configuration
  disk {
    slot     = 0
    size     = var.disk_size
    type     = "scsi"
    storage  = var.storage
    iothread = 1
    ssd      = 1
  }
  
  # Network configuration
  network {
    model  = "virtio"
    bridge = var.network_bridge
  }
  
  # Cloud-init configuration
  os_type      = "cloud-init"
  ipconfig0    = "ip=${var.ip_address}/24,gw=${var.gateway}"
  nameserver   = var.nameserver
  searchdomain = var.search_domain
  
  # SSH configuration
  sshkeys = var.ssh_public_key
  
  # Lifecycle management
  lifecycle {
    ignore_changes = [
      network,
    ]
  }
  
  # Wait for VM to be ready
  agent = 1
  
  tags = var.tags
}

# Variables
variable "vm_count" {
  description = "Number of VMs to create"
  type        = number
  default     = 1
}

variable "vm_name" {
  description = "Base name for VMs"
  type        = string
}

variable "target_node" {
  description = "Proxmox node name"
  type        = string
}

variable "template_name" {
  description = "Template to clone from"
  type        = string
}

variable "cores" {
  description = "Number of CPU cores"
  type        = number
  default     = 2
}

variable "memory" {
  description = "Memory in MB"
  type        = number
  default     = 4096
}

variable "disk_size" {
  description = "Disk size"
  type        = string
  default     = "50G"
}

variable "storage" {
  description = "Storage pool"
  type        = string
  default     = "local-lvm"
}

variable "network_bridge" {
  description = "Network bridge"
  type        = string
  default     = "vmbr1"
}

variable "ip_address" {
  description = "Static IP address"
  type        = string
}

variable "gateway" {
  description = "Network gateway"
  type        = string
  default     = "*************"
}

variable "ssh_public_key" {
  description = "SSH public key"
  type        = string
}

variable "tags" {
  description = "VM tags"
  type        = string
  default     = "goad-blue"
}
```

### **Complete GOAD-Blue Infrastructure**

```hcl
# terraform/environments/proxmox/main.tf
module "splunk_server" {
  source = "../../modules/proxmox-vm"
  
  vm_name       = "goad-blue-splunk"
  target_node   = var.proxmox_node
  template_name = "ubuntu-22.04-template"
  
  cores  = 4
  memory = 8192
  disk_size = "100G"
  
  ip_address = "*************0"
  ssh_public_key = var.ssh_public_key
  
  tags = "goad-blue,siem,splunk"
}

module "security_onion_manager" {
  source = "../../modules/proxmox-vm"
  
  vm_name       = "goad-blue-so-manager"
  target_node   = var.proxmox_node
  template_name = "ubuntu-22.04-template"
  
  cores  = 8
  memory = 16384
  disk_size = "200G"
  
  ip_address = "**************"
  ssh_public_key = var.ssh_public_key
  
  tags = "goad-blue,monitoring,security-onion"
}

module "security_onion_sensor" {
  source = "../../modules/proxmox-vm"
  
  vm_count      = 2
  vm_name       = "goad-blue-so-sensor"
  target_node   = var.proxmox_node
  template_name = "ubuntu-22.04-template"
  
  cores  = 4
  memory = 8192
  disk_size = "100G"
  
  ip_address = "**************"  # Will increment for multiple VMs
  ssh_public_key = var.ssh_public_key
  
  tags = "goad-blue,monitoring,sensor"
}

module "velociraptor_server" {
  source = "../../modules/proxmox-vm"
  
  vm_name       = "goad-blue-velociraptor"
  target_node   = var.proxmox_node
  template_name = "ubuntu-22.04-template"
  
  cores  = 2
  memory = 4096
  disk_size = "50G"
  
  ip_address = "**************"
  ssh_public_key = var.ssh_public_key
  
  tags = "goad-blue,endpoint,velociraptor"
}

module "misp_server" {
  source = "../../modules/proxmox-vm"
  
  vm_name       = "goad-blue-misp"
  target_node   = var.proxmox_node
  template_name = "ubuntu-22.04-template"
  
  cores  = 2
  memory = 4096
  disk_size = "50G"
  
  ip_address = "**************"
  ssh_public_key = var.ssh_public_key
  
  tags = "goad-blue,intelligence,misp"
}

module "flare_vm" {
  source = "../../modules/proxmox-vm"
  
  vm_name       = "goad-blue-flare"
  target_node   = var.proxmox_node
  template_name = "windows-10-template"
  
  cores  = 4
  memory = 8192
  disk_size = "100G"
  
  network_bridge = "vmbr2"  # Isolated network
  ip_address = "*************0"
  gateway    = "*************"
  
  tags = "goad-blue,analysis,flare-vm"
}
```

## 🔧 Template Creation

### **Ubuntu 22.04 Template**

```bash
# Create VM for template
qm create 9000 --name ubuntu-22.04-template --memory 2048 --cores 2 --net0 virtio,bridge=vmbr0

# Add disk
qm importdisk 9000 /var/lib/vz/template/iso/ubuntu-22.04.3-live-server-amd64.iso local-lvm

# Configure VM
qm set 9000 --scsihw virtio-scsi-pci --scsi0 local-lvm:vm-9000-disk-0
qm set 9000 --ide2 local-lvm:cloudinit
qm set 9000 --boot c --bootdisk scsi0
qm set 9000 --serial0 socket --vga serial0

# Install Ubuntu (manual process)
qm start 9000

# After installation, prepare template
qm stop 9000

# Clean up for template
virt-sysprep -d 9000

# Convert to template
qm template 9000
```

### **Automated Template Creation Script**

```bash
#!/bin/bash
# scripts/create-proxmox-templates.sh

set -e

PROXMOX_NODE="pve"
STORAGE="local-lvm"
TEMPLATE_DIR="/var/lib/vz/template/iso"

create_ubuntu_template() {
    local VMID=9000
    local TEMPLATE_NAME="ubuntu-22.04-template"
    
    echo "Creating Ubuntu 22.04 template..."
    
    # Create VM
    qm create $VMID \
        --name $TEMPLATE_NAME \
        --memory 2048 \
        --cores 2 \
        --net0 virtio,bridge=vmbr0 \
        --scsihw virtio-scsi-pci
    
    # Add disk
    qm set $VMID --scsi0 $STORAGE:32
    
    # Add cloud-init drive
    qm set $VMID --ide2 $STORAGE:cloudinit
    
    # Configure boot
    qm set $VMID --boot c --bootdisk scsi0
    
    # Add serial console
    qm set $VMID --serial0 socket --vga serial0
    
    # Enable QEMU agent
    qm set $VMID --agent enabled=1
    
    # Download and attach Ubuntu ISO
    if [ ! -f "$TEMPLATE_DIR/ubuntu-22.04.3-live-server-amd64.iso" ]; then
        wget -O "$TEMPLATE_DIR/ubuntu-22.04.3-live-server-amd64.iso" \
            "https://releases.ubuntu.com/22.04/ubuntu-22.04.3-live-server-amd64.iso"
    fi
    
    qm set $VMID --cdrom $TEMPLATE_DIR/ubuntu-22.04.3-live-server-amd64.iso
    
    echo "Ubuntu template VM created. Please:"
    echo "1. Start VM: qm start $VMID"
    echo "2. Install Ubuntu with cloud-init support"
    echo "3. Run: qm template $VMID"
}

create_security_onion_template() {
    local VMID=9001
    local TEMPLATE_NAME="security-onion-template"
    
    echo "Creating Security Onion template..."
    
    # Create VM with more resources for Security Onion
    qm create $VMID \
        --name $TEMPLATE_NAME \
        --memory 8192 \
        --cores 4 \
        --net0 virtio,bridge=vmbr0 \
        --net1 virtio,bridge=vmbr1 \
        --scsihw virtio-scsi-pci
    
    # Add larger disk for Security Onion
    qm set $VMID --scsi0 $STORAGE:200
    
    # Download Security Onion ISO if not present
    if [ ! -f "$TEMPLATE_DIR/securityonion-2.4.60.iso" ]; then
        wget -O "$TEMPLATE_DIR/securityonion-2.4.60.iso" \
            "https://github.com/Security-Onion-Solutions/securityonion/releases/download/2.4.60/securityonion-2.4.60.iso"
    fi
    
    qm set $VMID --cdrom $TEMPLATE_DIR/securityonion-2.4.60.iso
    
    echo "Security Onion template VM created. Please install manually and convert to template."
}

# Main execution
case "${1:-all}" in
    ubuntu)
        create_ubuntu_template
        ;;
    security-onion)
        create_security_onion_template
        ;;
    all)
        create_ubuntu_template
        create_security_onion_template
        ;;
    *)
        echo "Usage: $0 [ubuntu|security-onion|all]"
        exit 1
        ;;
esac

echo "Template creation initiated. Complete manual installation steps as needed."
```

## 🎯 GOAD Integration on Proxmox

### **Network Integration**

```bash
# Configure network for GOAD integration
# Ensure GOAD VMs are on the same bridge as GOAD-Blue

# If GOAD uses different network, create routing
ip route add ************/24 via ************* dev vmbr1

# Make routing persistent
echo "************/24 via ************* dev vmbr1" >> /etc/network/interfaces
```

### **Proxmox API Integration**

```python
# scripts/proxmox-goad-integration.py
import proxmoxer
import json
from typing import List, Dict

class ProxmoxGoadIntegration:
    def __init__(self, proxmox_host: str, username: str, password: str):
        self.proxmox = proxmoxer.ProxmoxAPI(
            proxmox_host,
            user=username,
            password=password,
            verify_ssl=False
        )
        
    def discover_goad_vms(self) -> List[Dict]:
        """Discover GOAD VMs in Proxmox."""
        goad_vms = []
        
        for node in self.proxmox.nodes.get():
            node_name = node['node']
            
            for vm in self.proxmox.nodes(node_name).qemu.get():
                vm_config = self.proxmox.nodes(node_name).qemu(vm['vmid']).config.get()
                
                # Check if VM is part of GOAD
                if 'goad' in vm.get('name', '').lower() or 'goad' in vm_config.get('tags', ''):
                    vm_info = {
                        'vmid': vm['vmid'],
                        'name': vm['name'],
                        'node': node_name,
                        'status': vm['status'],
                        'ip_address': self._get_vm_ip(node_name, vm['vmid'])
                    }
                    goad_vms.append(vm_info)
                    
        return goad_vms
        
    def _get_vm_ip(self, node: str, vmid: int) -> str:
        """Get VM IP address from QEMU agent."""
        try:
            agent_info = self.proxmox.nodes(node).qemu(vmid).agent.get()
            # Parse network interfaces for IP
            # Implementation depends on QEMU agent data structure
            return "192.168.100.x"  # Placeholder
        except:
            return None
            
    def deploy_goad_blue_agents(self, goad_vms: List[Dict]):
        """Deploy GOAD-Blue monitoring agents to GOAD VMs."""
        for vm in goad_vms:
            print(f"Deploying agents to {vm['name']} ({vm['ip_address']})")
            # Implementation for agent deployment
            
if __name__ == "__main__":
    integration = ProxmoxGoadIntegration(
        "*************:8006",
        "root@pam",
        "your-password"
    )
    
    goad_vms = integration.discover_goad_vms()
    print(json.dumps(goad_vms, indent=2))
```

## 📊 Monitoring and Management

### **Proxmox Monitoring Integration**

```bash
# Install Prometheus node exporter on Proxmox
wget https://github.com/prometheus/node_exporter/releases/download/v1.6.1/node_exporter-1.6.1.linux-amd64.tar.gz
tar xvfz node_exporter-1.6.1.linux-amd64.tar.gz
sudo cp node_exporter-1.6.1.linux-amd64/node_exporter /usr/local/bin/

# Create systemd service
sudo tee /etc/systemd/system/node_exporter.service > /dev/null <<EOF
[Unit]
Description=Node Exporter
After=network.target

[Service]
User=node_exporter
Group=node_exporter
Type=simple
ExecStart=/usr/local/bin/node_exporter

[Install]
WantedBy=multi-user.target
EOF

# Start and enable service
sudo systemctl daemon-reload
sudo systemctl start node_exporter
sudo systemctl enable node_exporter
```

### **Backup Configuration**

```bash
# Configure automated backups
pvesh create /cluster/backup --schedule "daily" --storage "backup-storage" --mode "snapshot" --compress "lzo"

# Backup specific VMs
vzdump --mode snapshot --compress lzo --storage backup-storage 100 101 102

# Restore from backup
qmrestore /var/lib/vz/dump/vzdump-qemu-100-2024_01_15-10_30_00.vma.lzo 100
```

---

!!! info "Proxmox Resources"
    For additional Proxmox-specific information, see:
    
    - [Proxmox VE Documentation](https://pve.proxmox.com/pve-docs/)
    - [Terraform Proxmox Provider](https://registry.terraform.io/providers/Telmate/proxmox/latest/docs)
    - [Proxmox API Documentation](https://pve.proxmox.com/pve-docs/api-viewer/)

!!! tip "Performance Optimization"
    For optimal performance on Proxmox:
    
    - Use virtio drivers for network and storage
    - Enable CPU host passthrough for better performance
    - Use ZFS with SSD storage for best I/O performance
    - Configure appropriate CPU and memory allocation

!!! warning "Security Considerations"
    Secure your Proxmox deployment:
    
    - Change default passwords
    - Configure firewall rules
    - Use SSH keys for authentication
    - Regular security updates
    - Network segmentation for lab traffic
