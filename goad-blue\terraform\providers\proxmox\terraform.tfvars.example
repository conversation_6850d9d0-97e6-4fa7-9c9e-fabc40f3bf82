# Proxmox Provider Configuration Example
# Copy this file to terraform.tfvars and customize for your environment

# Proxmox Connection Configuration
proxmox_api_url      = "https://192.168.1.10:8006/api2/json"
proxmox_user         = "root@pam"
proxmox_password     = "your-proxmox-password"
proxmox_tls_insecure = true
proxmox_parallel     = 4
proxmox_timeout      = 300
proxmox_debug        = false

# Project Configuration
project_name = "goad-blue"
environment  = "production"
created_by   = "terraform"
name_prefix  = "goad-blue"
target_node  = "pve-node-1"

# Ubuntu Template (create this first using Packer or manually)
ubuntu_template_name = "ubuntu-22.04-template"

# VM Authentication
vm_user     = "ubuntu"
vm_password = "goadblue123!"
ssh_public_key = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC... your-public-key"

# Component Deployment Configuration
deploy_components = {
  splunk         = true
  security_onion = true
  velociraptor   = true
  misp          = true
}

security_onion_sensor_count = 1

# VM IDs (ensure these don't conflict with existing VMs)
vm_ids = {
  splunk                     = 200
  security_onion_manager     = 270
  security_onion_sensor_base = 271
  velociraptor              = 285
  misp                      = 230
}

# VM Resource Configurations
vm_configs = {
  splunk = {
    cores          = 8
    memory         = 16384  # 16GB
    disk_size      = "100G"
    data_disk_size = "500G"
  }
  security_onion_manager = {
    cores     = 16
    memory    = 32768  # 32GB
    disk_size = "1000G"
  }
  security_onion_sensor = {
    cores     = 8
    memory    = 16384  # 16GB
    disk_size = "500G"
  }
  velociraptor = {
    cores     = 4
    memory    = 8192  # 8GB
    disk_size = "200G"
  }
  misp = {
    cores     = 4
    memory    = 8192  # 8GB
    disk_size = "200G"
  }
}

# Network Configuration
network_bridges = {
  goad_blue = "vmbr2"  # GOAD-Blue network bridge
  goad      = "vmbr1"  # GOAD network bridge
  internet  = "vmbr0"  # Internet access bridge
}

network_vlans = {
  goad_blue = 100
  goad      = 56
}

network_cidrs = {
  goad_blue = "*************/24"
  goad      = "************/24"
}

network_gateway = "*************"
dns_servers     = ["*************", "*******"]

# VM IP Addresses
vm_ip_addresses = {
  splunk                           = "*************0"
  security_onion_manager           = "**************"
  security_onion_manager_monitor   = "*************"
  velociraptor                     = "**************"
  misp                            = "**************"
}

# Storage Configuration
storage_pools = {
  primary = "local-lvm"  # Primary storage pool
  data    = "local-lvm"  # Data storage pool (can be different for performance)
}

disk_format = "qcow2"  # qcow2, raw, or vmdk
enable_ssd  = true     # Enable SSD optimizations

# High Availability Configuration (requires cluster)
enable_ha        = false  # Set to true if you have a Proxmox cluster
ha_nofailback    = false
ha_max_restart   = 2
ha_max_relocate  = 2

# Backup Configuration
backup_schedule = {
  enabled  = true
  schedule = "daily"
  storage  = "backup-storage"
  compress = "lzo"
}

create_backup_storage   = false  # Set to true to create backup storage
backup_storage_path     = "/backup/goad-blue"
backup_storage_shared   = false

# Monitoring Configuration
monitoring_config = {
  enable_prometheus = true
  enable_grafana    = true
  metrics_retention = "30d"
}

# Security Configuration
security_config = {
  enable_firewall     = true
  allowed_ssh_sources = ["*************/24", "192.168.1.0/24"]
  enable_fail2ban     = true
}

enable_firewall     = false  # Proxmox firewall rules (optional)
allowed_ssh_sources = ["*************/24", "192.168.1.0/24"]

# Example configurations for different environments:

# Development Environment (smaller resources)
# vm_configs = {
#   splunk = {
#     cores          = 4
#     memory         = 8192
#     disk_size      = "50G"
#     data_disk_size = "100G"
#   }
#   security_onion_manager = {
#     cores     = 8
#     memory    = 16384
#     disk_size = "200G"
#   }
#   security_onion_sensor = {
#     cores     = 4
#     memory    = 8192
#     disk_size = "100G"
#   }
#   velociraptor = {
#     cores     = 2
#     memory    = 4096
#     disk_size = "50G"
#   }
#   misp = {
#     cores     = 2
#     memory    = 4096
#     disk_size = "50G"
#   }
# }

# Production Environment (larger resources)
# vm_configs = {
#   splunk = {
#     cores          = 16
#     memory         = 32768
#     disk_size      = "200G"
#     data_disk_size = "1000G"
#   }
#   security_onion_manager = {
#     cores     = 32
#     memory    = 65536
#     disk_size = "2000G"
#   }
#   security_onion_sensor = {
#     cores     = 16
#     memory    = 32768
#     disk_size = "1000G"
#   }
#   velociraptor = {
#     cores     = 8
#     memory    = 16384
#     disk_size = "500G"
#   }
#   misp = {
#     cores     = 8
#     memory    = 16384
#     disk_size = "500G"
#   }
# }

# Cluster Configuration (multiple nodes)
# target_node = "pve-node-1"  # Primary node
# enable_ha = true
# ha_nofailback = false
# 
# Additional nodes can be specified in the HA group configuration
# within the main.tf file

# Storage Configuration Examples:
# 
# Local storage only:
# storage_pools = {
#   primary = "local-lvm"
#   data    = "local-lvm"
# }
#
# Ceph distributed storage:
# storage_pools = {
#   primary = "ceph-pool"
#   data    = "ceph-pool"
# }
#
# NFS shared storage:
# storage_pools = {
#   primary = "nfs-storage"
#   data    = "nfs-storage"
# }
#
# Mixed storage (OS on local, data on shared):
# storage_pools = {
#   primary = "local-lvm"
#   data    = "ceph-pool"
# }
