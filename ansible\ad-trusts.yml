---
# Load datas
- import_playbook: data.yml
  vars:
    data_path: "../ad/{{domain_name}}/data/"
  tags: 'data'
# set AD trusts ==================================================================================================

- name: Trusts configuration prepare
  hosts: trust
  roles:
  - { role: 'settings/disable_nat_adapter' , tags: 'disable_nat_adapter'}
  - { role: 'dns_conditional_forwarder', tags: 'dns_conditional_forwarder' }
  vars:
    domain: "{{lab.hosts[dict_key].domain}}"
    remote_forest: "{{lab.domains[domain].trust}}"
    zone_name: "{{remote_forest}}"
    remote_dc: "{{lab.domains[remote_forest].dc}}"
    master_server: "{{hostvars[remote_dc].ansible_host}}"
    replication: "forest"

- name: Trusts configuration
  hosts: trust
  serial: 1 # add one trust at a time to avoid issues
  roles:
  - { role: 'trusts', tags: 'trust' }
  vars:
    domain: "{{lab.hosts[dict_key].domain}}"
    domain_username: "{{domain}}\\{{admin_user}}"
    domain_password: "{{lab.domains[domain].domain_password}}"
    remote_forest: "{{lab.domains[domain].trust}}"
    remote_admin: "Administrator@{{remote_forest}}"
    remote_admin_password: "{{lab.domains[remote_forest].domain_password}}"
    zone_name: "{{remote_forest}}"
    remote_dc: "{{lab.domains[remote_forest].dc}}"
    master_server: "{{hostvars[remote_dc].ansible_host}}"

- name: Trusts configuration end
  hosts: trust
  roles:
  - { role: 'settings/enable_nat_adapter', tags: 'enable_nat_adapter'}


# add DNS conditional forwarder on all trust dc for each other subdomains
- name: Adjust DNS conditional forwarded configuration
  hosts: trust
  roles:
  - { role: 'dc_dns_conditional_forwarder', tags: 'dns_conditional_forwarder' }
  vars:
    domain: "{{lab.hosts[dict_key].domain}}"
    replication: "forest"
    domain_username: "{{domain}}\\{{admin_user}}"
    domain_password: "{{lab.domains[domain].domain_password}}"
    parent_domain: "{{'.'.join(domain.split('.')[1:]) | default('')}}"
    trust: "{{lab.domains[domain].trust | default('')}}"
    lab: "{{lab}}"
    domains: "{{lab.domains.keys()}}"
