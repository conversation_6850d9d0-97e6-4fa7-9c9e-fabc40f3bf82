﻿<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (c)  Microsoft.  All Rights Reserved.  Licensed under the Apache License, Version 2.0.  See License.txt in the project root for license information. -->
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="Microsoft.Managed.Core.targets"/>

  <Target Name="CoreCompile"
          Inputs="$(MSBuildAllProjects);
                  @(Compile);
                  @(_CoreCompileResourceInputs);
                  $(ApplicationIcon);
                  $(AssemblyOriginatorKeyFile);
                  @(ReferencePathWithRefAssemblies);
                  @(CompiledLicenseFile);
                  @(LinkResource);
                  @(EmbeddedDocumentation);
                  $(Win32Resource);
                  $(Win32Manifest);
                  @(CustomAdditionalCompileInputs);
                  $(ResolvedCodeAnalysisRuleSet);
                  @(AdditionalFiles);
                  @(EmbeddedFiles)"
          Outputs="@(DocFileItem);
                   @(IntermediateAssembly);
                   @(IntermediateRefAssembly);
                   @(_DebugSymbolsIntermediatePath);
                   $(NonExistentFile);
                   @(CustomAdditionalCompileOutputs)"
          Returns="@(VbcCommandLineArgs)"
          DependsOnTargets="$(CoreCompileDependsOn);_BeforeVBCSCoreCompile">
    <PropertyGroup>
      <_NoWarnings Condition="'$(WarningLevel)' == '0'">true</_NoWarnings>
      <_NoWarnings Condition="'$(WarningLevel)' == '1'">false</_NoWarnings>
    </PropertyGroup>

    <PropertyGroup>
      <!-- If we are targeting winmdobj we want to specifically the pdbFile property since we do not want it to collide with the output of winmdexp-->
      <PdbFile Condition="'$(PdbFile)' == '' AND '$(OutputType)' == 'winmdobj' AND '$(DebugSymbols)' == 'true'">$(IntermediateOutputPath)$(TargetName).compile.pdb</PdbFile>
    </PropertyGroup>

    <!-- Condition is to filter out the _CoreCompileResourceInputs so that it doesn't pass in culture resources to the compiler -->
    <Vbc Condition="'%(_CoreCompileResourceInputs.WithCulture)' != 'true'"
         AdditionalLibPaths="$(AdditionalLibPaths)"
         AddModules="@(AddModules)"
         AdditionalFiles="@(AdditionalFiles)"
         Analyzers="@(Analyzer)"
         BaseAddress="$(BaseAddress)"
         ChecksumAlgorithm="$(ChecksumAlgorithm)"
         CodeAnalysisRuleSet="$(ResolvedCodeAnalysisRuleSet)"
         CodePage="$(CodePage)"
         DebugType="$(DebugType)"
         DefineConstants="$(FinalDefineConstants)"
         DelaySign="$(DelaySign)"
         DisabledWarnings="$(NoWarn)"
         DocumentationFile="@(DocFileItem)"
         EmbedAllSources="$(EmbedAllSources)"
         EmbeddedFiles="@(EmbeddedFiles)"
         EmitDebugInformation="$(DebugSymbols)"
         EnvironmentVariables="$(VbcEnvironment)"
         ErrorLog="$(ErrorLog)"
         ErrorReport="$(ErrorReport)"
         Features="$(Features)"
         FileAlignment="$(FileAlignment)"
         GenerateDocumentation="$(GenerateDocumentation)"
         HighEntropyVA="$(HighEntropyVA)"
         Imports="@(Import)"
         Instrument="$(Instrument)"
         KeyContainer="$(KeyContainerName)"
         KeyFile="$(KeyOriginatorFile)"
         LangVersion="$(LangVersion)"
         LinkResources="@(LinkResource)"
         MainEntryPoint="$(StartupObject)"
         ModuleAssemblyName="$(ModuleAssemblyName)"
         NoConfig="true"
         NoStandardLib="$(NoCompilerStandardLib)"
         NoVBRuntimeReference="$(NoVBRuntimeReference)"
         NoWarnings="$(_NoWarnings)"
         NoWin32Manifest="$(NoWin32Manifest)"
         Optimize="$(Optimize)"
         Deterministic="$(Deterministic)"
         PublicSign="$(PublicSign)"
         OptionCompare="$(OptionCompare)"
         OptionExplicit="$(OptionExplicit)"
         OptionInfer="$(OptionInfer)"
         OptionStrict="$(OptionStrict)"
         OptionStrictType="$(OptionStrictType)"
         OutputAssembly="@(IntermediateAssembly)"
         OutputRefAssembly="@(IntermediateRefAssembly)"
         PdbFile="$(PdbFile)"
         Platform="$(PlatformTarget)"
         Prefer32Bit="$(Prefer32Bit)"
         PreferredUILang="$(PreferredUILang)"
         ProvideCommandLineArgs="$(ProvideCommandLineArgs)"
         References="@(ReferencePathWithRefAssemblies)"
         RemoveIntegerChecks="$(RemoveIntegerChecks)"
         ReportAnalyzer="$(ReportAnalyzer)"
         Resources="@(_CoreCompileResourceInputs);@(CompiledLicenseFile)"
         ResponseFiles="$(CompilerResponseFile)"
         RootNamespace="$(RootNamespace)"
         RuntimeMetadataVersion="$(RuntimeMetadataVersion)"
         SdkPath="$(FrameworkPathOverride)"
         SharedCompilationId="$(SharedCompilationId)"
         SkipCompilerExecution="$(SkipCompilerExecution)"
         Sources="@(Compile)"
         SubsystemVersion="$(SubsystemVersion)"
         TargetCompactFramework="$(TargetCompactFramework)"
         TargetType="$(OutputType)"
         ToolExe="$(VbcToolExe)"
         ToolPath="$(VbcToolPath)"
         TreatWarningsAsErrors="$(TreatWarningsAsErrors)"
         UseHostCompilerIfAvailable="$(UseHostCompilerIfAvailable)"
         UseSharedCompilation="$(UseSharedCompilation)"
         Utf8Output="$(Utf8Output)"
         VBRuntimePath="$(VBRuntimePath)"
         Verbosity="$(VbcVerbosity)"
         VsSessionGuid="$(VsSessionGuid)"
         WarningsAsErrors="$(WarningsAsErrors)"
         WarningsNotAsErrors="$(WarningsNotAsErrors)"
         Win32Icon="$(ApplicationIcon)"
         Win32Manifest="$(Win32Manifest)"
         Win32Resource="$(Win32Resource)"
         VBRuntime="$(VBRuntime)"
         PathMap="$(PathMap)"
         SourceLink="$(SourceLink)">
      <Output TaskParameter="CommandLineArgs" ItemName="VbcCommandLineArgs" />
    </Vbc>
    <ItemGroup>
      <_CoreCompileResourceInputs Remove="@(_CoreCompileResourceInputs)" />
    </ItemGroup>

    <CallTarget Targets="$(TargetsTriggeredByCompilation)" Condition="'$(TargetsTriggeredByCompilation)' != ''" />
  </Target>
</Project>