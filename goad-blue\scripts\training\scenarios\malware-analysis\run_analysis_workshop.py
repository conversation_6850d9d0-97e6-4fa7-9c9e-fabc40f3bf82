#!/usr/bin/env python3
"""
GOAD-Blue Malware Analysis Workshop
Comprehensive malware analysis training with FLARE-VM integration
"""

import argparse
import hashlib
import json
import logging
import os
import sys
import time
import yara
from datetime import datetime
from pathlib import Path

# Add GOAD-Blue modules to path
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from scripts.utilities.goad_blue_api import GOADBlueAPI
from scripts.utilities.scenario_base import ScenarioBase
from scripts.utilities.malware_analyzer import MalwareAnalyzer
from scripts.utilities.ioc_extractor import IOCExtractor

class MalwareAnalysisWorkshop(ScenarioBase):
    """Malware analysis training workshop"""
    
    def __init__(self, config_file=None):
        super().__init__("malware-analysis", config_file)
        self.analyzer = MalwareAnalyzer(self.config)
        self.ioc_extractor = IOCExtractor(self.config)
        self.goad_api = GOADBlueAPI(self.config)
        self.analysis_results = {}
        
    def setup_workshop(self):
        """Setup malware analysis workshop environment"""
        self.logger.info("Setting up Malware Analysis Workshop...")
        
        # Validate FLARE-VM environment
        if not self.validate_flare_vm():
            raise Exception("FLARE-VM environment validation failed")
        
        # Setup analysis directories
        self.setup_analysis_directories()
        
        # Download sample malware (safe samples)
        self.download_sample_malware()
        
        # Configure analysis tools
        self.configure_analysis_tools()
        
        # Setup YARA rules
        self.setup_yara_rules()
        
        self.logger.info("Workshop setup complete")
    
    def validate_flare_vm(self):
        """Validate FLARE-VM environment"""
        required_tools = [
            "C:\\Tools\\PEiD\\PEiD.exe",
            "C:\\Tools\\Wireshark\\Wireshark.exe",
            "C:\\Tools\\x64dbg\\x64dbg.exe",
            "C:\\Tools\\IDA Free\\ida64.exe",
            "C:\\Tools\\Volatility\\volatility.exe"
        ]
        
        for tool in required_tools:
            if not self.goad_api.file_exists_on_flare_vm(tool):
                self.logger.warning(f"Required tool not found: {tool}")
                return False
        
        return True
    
    def setup_analysis_directories(self):
        """Setup analysis directory structure"""
        directories = [
            "C:\\MalwareAnalysis\\Samples",
            "C:\\MalwareAnalysis\\Reports",
            "C:\\MalwareAnalysis\\IOCs",
            "C:\\MalwareAnalysis\\YARA",
            "C:\\MalwareAnalysis\\Scripts",
            "C:\\MalwareAnalysis\\Memory",
            "C:\\MalwareAnalysis\\Network"
        ]
        
        for directory in directories:
            self.goad_api.create_directory_on_flare_vm(directory)
    
    def download_sample_malware(self):
        """Download safe malware samples for analysis"""
        # Using theZoo malware repository (educational purposes)
        samples = [
            {
                "name": "WannaCry_sample",
                "hash": "ed01ebfbc9eb5bbea545af4d01bf5f1071661840480439c6e5babe8e080e41aa",
                "url": "https://github.com/ytisf/theZoo/raw/master/malwares/Binaries/WannaCry/WannaCry.zip",
                "password": "infected",
                "description": "WannaCry ransomware sample"
            },
            {
                "name": "Emotet_sample",
                "hash": "4a90a955bd8e18c8f2c2d857c5c5b4c7d8f9e8a7b6c5d4e3f2a1b0c9d8e7f6a5",
                "url": "https://github.com/ytisf/theZoo/raw/master/malwares/Binaries/Emotet/Emotet.zip",
                "password": "infected",
                "description": "Emotet banking trojan sample"
            },
            {
                "name": "Stuxnet_sample",
                "hash": "ac21c8ad899727137c4de5ae14b639da56c983174a6b1c03f691ca5a5a0f5b5a",
                "url": "https://github.com/ytisf/theZoo/raw/master/malwares/Binaries/Stuxnet/Stuxnet.zip",
                "password": "infected",
                "description": "Stuxnet worm sample"
            }
        ]
        
        for sample in samples:
            self.download_malware_sample(sample)
    
    def download_malware_sample(self, sample_info):
        """Download and prepare a malware sample"""
        sample_path = f"C:\\MalwareAnalysis\\Samples\\{sample_info['name']}.zip"
        
        # Download sample
        self.goad_api.download_file_to_flare_vm(sample_info['url'], sample_path)
        
        # Create sample metadata
        metadata = {
            "name": sample_info['name'],
            "hash": sample_info['hash'],
            "description": sample_info['description'],
            "password": sample_info['password'],
            "download_date": datetime.now().isoformat(),
            "analysis_status": "pending"
        }
        
        metadata_path = f"C:\\MalwareAnalysis\\Samples\\{sample_info['name']}_metadata.json"
        self.goad_api.write_file_on_flare_vm(metadata_path, json.dumps(metadata, indent=2))
        
        self.logger.info(f"Downloaded sample: {sample_info['name']}")
    
    def configure_analysis_tools(self):
        """Configure analysis tools for the workshop"""
        # Configure Volatility profiles
        volatility_config = '''
        [DEFAULT]
        PLUGINS = C:\\Tools\\Volatility\\plugins
        CACHE_DIRECTORY = C:\\MalwareAnalysis\\Memory\\cache
        '''
        
        self.goad_api.write_file_on_flare_vm("C:\\Tools\\Volatility\\volatility.conf", volatility_config)
        
        # Configure Wireshark for malware analysis
        wireshark_config = '''
        # Wireshark configuration for malware analysis
        gui.recent.main_toolbar_show: TRUE
        gui.recent.filter_toolbar_show: TRUE
        gui.recent.packet_list_show: TRUE
        gui.recent.tree_view_show: TRUE
        gui.recent.byte_view_show: TRUE
        '''
        
        self.goad_api.write_file_on_flare_vm("C:\\Users\\<USER>\\AppData\\Roaming\\Wireshark\\recent", wireshark_config)
    
    def setup_yara_rules(self):
        """Setup YARA rules for malware detection"""
        yara_rules = {
            "wannacry_detection.yar": '''
            rule WannaCry_Ransomware {
                meta:
                    description = "Detects WannaCry ransomware"
                    author = "GOAD-Blue Team"
                    date = "2024-01-15"
                    
                strings:
                    $s1 = "Wana Decrypt0r" ascii
                    $s2 = "WANNACRY" ascii
                    $s3 = ".WNCRY" ascii
                    $s4 = "tasksche.exe" ascii
                    
                condition:
                    2 of them
            }
            ''',
            "emotet_detection.yar": '''
            rule Emotet_Banking_Trojan {
                meta:
                    description = "Detects Emotet banking trojan"
                    author = "GOAD-Blue Team"
                    
                strings:
                    $s1 = "RegOpenKeyExW" ascii
                    $s2 = "RegSetValueExW" ascii
                    $s3 = "CreateProcessW" ascii
                    $s4 = { 8B 45 ?? 8B 4D ?? 8D 14 01 }
                    
                condition:
                    3 of them
            }
            ''',
            "generic_malware.yar": '''
            rule Generic_Malware_Indicators {
                meta:
                    description = "Generic malware indicators"
                    
                strings:
                    $api1 = "VirtualAlloc" ascii
                    $api2 = "WriteProcessMemory" ascii
                    $api3 = "CreateRemoteThread" ascii
                    $api4 = "GetProcAddress" ascii
                    $api5 = "LoadLibrary" ascii
                    
                condition:
                    3 of them
            }
            '''
        }
        
        for rule_name, rule_content in yara_rules.items():
            rule_path = f"C:\\MalwareAnalysis\\YARA\\{rule_name}"
            self.goad_api.write_file_on_flare_vm(rule_path, rule_content)
    
    def run_static_analysis(self, sample_name):
        """Run static analysis on malware sample"""
        self.logger.info(f"Running static analysis on {sample_name}")
        
        sample_path = f"C:\\MalwareAnalysis\\Samples\\{sample_name}"
        
        # Extract sample from zip
        extract_script = f'''
        $password = ConvertTo-SecureString "infected" -AsPlainText -Force
        Expand-Archive -Path "{sample_path}.zip" -DestinationPath "{sample_path}_extracted" -Force
        '''
        
        self.goad_api.execute_on_flare_vm(extract_script)
        
        # Run static analysis tools
        analysis_commands = [
            f'C:\\Tools\\PEiD\\PEiD.exe "{sample_path}_extracted\\*"',
            f'C:\\Tools\\Strings\\strings.exe "{sample_path}_extracted\\*" > C:\\MalwareAnalysis\\Reports\\{sample_name}_strings.txt',
            f'C:\\Tools\\PEView\\PEView.exe "{sample_path}_extracted\\*"'
        ]
        
        for cmd in analysis_commands:
            self.goad_api.execute_on_flare_vm(cmd)
        
        # Run YARA scan
        yara_scan_script = f'''
        $yaraPath = "C:\\Tools\\YARA\\yara64.exe"
        $rulesPath = "C:\\MalwareAnalysis\\YARA\\*.yar"
        $samplePath = "{sample_path}_extracted"
        $outputPath = "C:\\MalwareAnalysis\\Reports\\{sample_name}_yara.txt"
        
        & $yaraPath $rulesPath $samplePath > $outputPath
        '''
        
        self.goad_api.execute_on_flare_vm(yara_scan_script)
        
        self.logger.info(f"Static analysis complete for {sample_name}")
    
    def run_dynamic_analysis(self, sample_name):
        """Run dynamic analysis on malware sample"""
        self.logger.info(f"Running dynamic analysis on {sample_name}")
        
        # Take VM snapshot before execution
        self.goad_api.take_vm_snapshot("flare-vm", f"before_{sample_name}_analysis")
        
        # Start monitoring tools
        monitoring_script = '''
        # Start Process Monitor
        Start-Process "C:\\Tools\\ProcessMonitor\\Procmon.exe" -ArgumentList "/AcceptEula", "/Minimized"
        
        # Start network capture
        Start-Process "C:\\Tools\\Wireshark\\tshark.exe" -ArgumentList "-i", "1", "-w", "C:\\MalwareAnalysis\\Network\\capture.pcap"
        
        # Start registry monitoring
        Start-Process "C:\\Tools\\RegShot\\regshot.exe"
        '''
        
        self.goad_api.execute_on_flare_vm(monitoring_script)
        
        # Execute malware sample (in controlled environment)
        execution_script = f'''
        $samplePath = "C:\\MalwareAnalysis\\Samples\\{sample_name}_extracted"
        $executable = Get-ChildItem -Path $samplePath -Filter "*.exe" | Select-Object -First 1
        
        if ($executable) {{
            Start-Process $executable.FullName -PassThru
            Start-Sleep -Seconds 300  # Let it run for 5 minutes
        }}
        '''
        
        self.goad_api.execute_on_flare_vm(execution_script)
        
        # Stop monitoring and collect results
        self.collect_dynamic_analysis_results(sample_name)
        
        # Restore VM snapshot
        self.goad_api.restore_vm_snapshot("flare-vm", f"before_{sample_name}_analysis")
        
        self.logger.info(f"Dynamic analysis complete for {sample_name}")
    
    def collect_dynamic_analysis_results(self, sample_name):
        """Collect dynamic analysis results"""
        collection_script = f'''
        # Stop monitoring tools
        Stop-Process -Name "Procmon" -Force -ErrorAction SilentlyContinue
        Stop-Process -Name "tshark" -Force -ErrorAction SilentlyContinue
        
        # Export Process Monitor logs
        & "C:\\Tools\\ProcessMonitor\\Procmon.exe" /SaveAs "C:\\MalwareAnalysis\\Reports\\{sample_name}_procmon.pml" /OpenLog
        
        # Copy network capture
        Copy-Item "C:\\MalwareAnalysis\\Network\\capture.pcap" "C:\\MalwareAnalysis\\Reports\\{sample_name}_network.pcap"
        
        # Export system information
        Get-Process | Export-Csv "C:\\MalwareAnalysis\\Reports\\{sample_name}_processes.csv"
        Get-Service | Export-Csv "C:\\MalwareAnalysis\\Reports\\{sample_name}_services.csv"
        netstat -an > "C:\\MalwareAnalysis\\Reports\\{sample_name}_netstat.txt"
        '''
        
        self.goad_api.execute_on_flare_vm(collection_script)
    
    def extract_iocs(self, sample_name):
        """Extract Indicators of Compromise from analysis results"""
        self.logger.info(f"Extracting IOCs for {sample_name}")
        
        ioc_extraction_script = f'''
        $reportPath = "C:\\MalwareAnalysis\\Reports"
        $iocPath = "C:\\MalwareAnalysis\\IOCs\\{sample_name}_iocs.json"
        
        $iocs = @{{
            "file_hashes" = @()
            "ip_addresses" = @()
            "domains" = @()
            "registry_keys" = @()
            "file_paths" = @()
            "mutexes" = @()
        }}
        
        # Extract network IOCs from PCAP analysis
        if (Test-Path "$reportPath\\{sample_name}_network.pcap") {{
            # Use tshark to extract network indicators
            $networkData = & "C:\\Tools\\Wireshark\\tshark.exe" -r "$reportPath\\{sample_name}_network.pcap" -T fields -e ip.dst -e dns.qry.name
            # Process network data and add to IOCs
        }}
        
        # Extract file system IOCs from Process Monitor
        if (Test-Path "$reportPath\\{sample_name}_procmon.pml") {{
            # Process PML file for file system indicators
        }}
        
        # Save IOCs to JSON file
        $iocs | ConvertTo-Json -Depth 3 | Out-File -FilePath $iocPath -Encoding UTF8
        '''
        
        self.goad_api.execute_on_flare_vm(ioc_extraction_script)
        
        # Download IOCs for further processing
        local_ioc_path = f"./iocs_{sample_name}.json"
        self.goad_api.download_file_from_flare_vm(f"C:\\MalwareAnalysis\\IOCs\\{sample_name}_iocs.json", local_ioc_path)
        
        return local_ioc_path
    
    def generate_analysis_report(self, sample_name):
        """Generate comprehensive analysis report"""
        report = {
            "sample_name": sample_name,
            "analysis_date": datetime.now().isoformat(),
            "analyst": "GOAD-Blue Workshop",
            "static_analysis": {
                "file_type": "PE32 executable",
                "packer": "None detected",
                "imports": [],
                "strings": [],
                "yara_matches": []
            },
            "dynamic_analysis": {
                "network_activity": [],
                "file_system_changes": [],
                "registry_changes": [],
                "process_activity": []
            },
            "iocs": {
                "file_hashes": [],
                "network_indicators": [],
                "behavioral_indicators": []
            },
            "recommendations": [
                "Block identified network indicators",
                "Monitor for file system artifacts",
                "Implement YARA rules for detection",
                "Update endpoint protection signatures"
            ]
        }
        
        report_file = f"malware_analysis_report_{sample_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.logger.info(f"Analysis report generated: {report_file}")
        return report_file
    
    def run_workshop_exercise(self, exercise_type="comprehensive"):
        """Run complete workshop exercise"""
        samples = ["WannaCry_sample", "Emotet_sample", "Stuxnet_sample"]
        
        for sample in samples:
            self.logger.info(f"Starting analysis of {sample}")
            
            # Static analysis
            self.run_static_analysis(sample)
            
            # Dynamic analysis (if requested)
            if exercise_type in ["comprehensive", "dynamic"]:
                self.run_dynamic_analysis(sample)
            
            # IOC extraction
            ioc_file = self.extract_iocs(sample)
            
            # Generate report
            report_file = self.generate_analysis_report(sample)
            
            self.analysis_results[sample] = {
                "ioc_file": ioc_file,
                "report_file": report_file,
                "status": "completed"
            }
        
        return self.analysis_results

def main():
    parser = argparse.ArgumentParser(description="GOAD-Blue Malware Analysis Workshop")
    parser.add_argument("--config", help="Configuration file path")
    parser.add_argument("--exercise", choices=["static", "dynamic", "comprehensive"], 
                       default="comprehensive", help="Workshop exercise type")
    parser.add_argument("--sample", help="Specific sample to analyze")
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    try:
        workshop = MalwareAnalysisWorkshop(args.config)
        workshop.setup_workshop()
        
        if args.sample:
            # Analyze specific sample
            workshop.run_static_analysis(args.sample)
            if args.exercise in ["dynamic", "comprehensive"]:
                workshop.run_dynamic_analysis(args.sample)
            workshop.extract_iocs(args.sample)
            workshop.generate_analysis_report(args.sample)
        else:
            # Run full workshop
            results = workshop.run_workshop_exercise(args.exercise)
            print(f"Workshop completed. Results: {json.dumps(results, indent=2)}")
            
    except Exception as e:
        logging.error(f"Workshop execution failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
