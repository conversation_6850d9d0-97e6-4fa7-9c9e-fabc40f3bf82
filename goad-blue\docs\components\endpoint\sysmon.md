# Sysmon

Sysmon (System Monitor) is a Windows system service and device driver that provides detailed information about process creation, network connections, and file operations. In GOAD-Blue, <PERSON><PERSON><PERSON> serves as a critical endpoint monitoring component, providing enhanced logging capabilities for security analysis.

## 🎯 Overview

Sysmon provides comprehensive system activity monitoring on Windows endpoints, generating high-quality logs that enable advanced threat detection, forensic analysis, and incident response across the GOAD environment.

```mermaid
graph TB
    subgraph "👁️ Sysmon Architecture"
        DRIVER[🔧 Sysmon Driver<br/>Kernel-level Monitoring<br/>System Call Interception]
        SERVICE[⚙️ Sysmon Service<br/>Event Processing<br/>Log Generation]
        CONFIG[📋 Configuration<br/>Event Filtering<br/>Rule Management]
        EVENTLOG[📝 Event Log<br/>Windows Event Log<br/>Structured Data]
    end

    subgraph "📊 Event Types"
        PROCESS[🔄 Process Events<br/>Creation & Termination<br/>Command Lines]
        NETWORK[🌐 Network Events<br/>TCP/UDP Connections<br/>DNS Queries]
        FILE[📁 File Events<br/>Creation & Modification<br/>Access Patterns]
        REGISTRY[🗃️ Registry Events<br/>Key Modifications<br/>Value Changes]
        IMAGE[📦 Image Events<br/>DLL Loading<br/>Process Injection]
    end

    subgraph "🔍 Analysis Capabilities"
        HUNTING[🔍 Threat Hunting<br/>Behavioral Analysis<br/>IOC Matching]
        FORENSICS[🕵️ Digital Forensics<br/>Timeline Reconstruction<br/>Evidence Collection]
        DETECTION[🚨 Threat Detection<br/>Anomaly Identification<br/>Attack Patterns]
        CORRELATION[🔗 Event Correlation<br/>Multi-event Analysis<br/>Attack Chains]
    end

    subgraph "🎮 GOAD Environment"
        GOAD_DC[👑 Domain Controllers<br/>Authentication Events<br/>Replication Activity]
        GOAD_SERVERS[⚔️ Member Servers<br/>Service Operations<br/>File Access]
        GOAD_WORKSTATIONS[🖥️ Workstations<br/>User Activity<br/>Process Execution]
    end

    GOAD_DC --> DRIVER
    GOAD_SERVERS --> DRIVER
    GOAD_WORKSTATIONS --> DRIVER

    DRIVER --> SERVICE
    SERVICE --> CONFIG
    SERVICE --> EVENTLOG

    EVENTLOG --> PROCESS
    EVENTLOG --> NETWORK
    EVENTLOG --> FILE
    EVENTLOG --> REGISTRY
    EVENTLOG --> IMAGE

    PROCESS --> HUNTING
    NETWORK --> FORENSICS
    FILE --> DETECTION
    REGISTRY --> CORRELATION
    IMAGE --> CORRELATION

    classDef sysmon fill:#0078d4,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef events fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef analysis fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef goad fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px

    class DRIVER,SERVICE,CONFIG,EVENTLOG sysmon
    class PROCESS,NETWORK,FILE,REGISTRY,IMAGE events
    class HUNTING,FORENSICS,DETECTION,CORRELATION analysis
    class GOAD_DC,GOAD_SERVERS,GOAD_WORKSTATIONS goad
```

## 🚀 Installation and Setup

### **Automated Installation**

```bash
# Install Sysmon using GOAD-Blue automation
python3 goad-blue.py install --component sysmon --target-network ************/24

# Deploy GOAD-optimized configuration
python3 goad-blue.py configure --component sysmon --config-template goad-blue

# Verify installation across GOAD systems
python3 goad-blue.py verify --component sysmon --check-all
```

### **Manual Installation**

```powershell
# Download Sysmon from Microsoft Sysinternals
Invoke-WebRequest -Uri "https://download.sysinternals.com/files/Sysmon.zip" -OutFile "Sysmon.zip"
Expand-Archive -Path "Sysmon.zip" -DestinationPath "C:\Tools\Sysmon"

# Install Sysmon with GOAD-Blue configuration
C:\Tools\Sysmon\Sysmon64.exe -accepteula -i sysmon-goad-blue.xml

# Verify installation
Get-Service Sysmon64
Get-WinEvent -LogName "Microsoft-Windows-Sysmon/Operational" -MaxEvents 10
```

### **GOAD-Blue Sysmon Configuration**

```xml
<!-- sysmon-goad-blue.xml - Optimized for GOAD environment -->
<Sysmon schemaversion="4.82">
  <EventFiltering>

    <!-- Event ID 1: Process Creation -->
    <RuleGroup name="" groupRelation="or">
      <ProcessCreate onmatch="exclude">
        <!-- Exclude common Windows processes to reduce noise -->
        <Image condition="is">C:\Windows\System32\svchost.exe</Image>
        <Image condition="is">C:\Windows\System32\dllhost.exe</Image>
        <Image condition="is">C:\Windows\System32\RuntimeBroker.exe</Image>
        <Image condition="is">C:\Windows\System32\SearchIndexer.exe</Image>
      </ProcessCreate>

      <ProcessCreate onmatch="include">
        <!-- Monitor PowerShell execution -->
        <Image condition="contains">powershell</Image>
        <CommandLine condition="contains">-ExecutionPolicy</CommandLine>
        <CommandLine condition="contains">-EncodedCommand</CommandLine>
        <CommandLine condition="contains">IEX</CommandLine>
        <CommandLine condition="contains">Invoke-Expression</CommandLine>

        <!-- Monitor command line tools -->
        <Image condition="contains">cmd.exe</Image>
        <Image condition="contains">wmic.exe</Image>
        <Image condition="contains">rundll32.exe</Image>
        <Image condition="contains">regsvr32.exe</Image>

        <!-- Monitor credential dumping tools -->
        <Image condition="contains">mimikatz</Image>
        <Image condition="contains">procdump</Image>
        <CommandLine condition="contains">sekurlsa</CommandLine>
        <CommandLine condition="contains">lsadump</CommandLine>

        <!-- Monitor lateral movement tools -->
        <Image condition="contains">psexec</Image>
        <Image condition="contains">winrs.exe</Image>
        <CommandLine condition="contains">net use</CommandLine>
        <CommandLine condition="contains">net share</CommandLine>

        <!-- Monitor persistence mechanisms -->
        <CommandLine condition="contains">schtasks</CommandLine>
        <CommandLine condition="contains">at.exe</CommandLine>
        <CommandLine condition="contains">sc.exe</CommandLine>

        <!-- Monitor processes from suspicious locations -->
        <Image condition="contains">:\Temp\</Image>
        <Image condition="contains">:\Users\Public\</Image>
        <Image condition="contains">:\ProgramData\</Image>
        <Image condition="contains">:\Windows\Temp\</Image>
      </ProcessCreate>
    </RuleGroup>

    <!-- Event ID 3: Network Connection -->
    <RuleGroup name="" groupRelation="or">
      <NetworkConnect onmatch="exclude">
        <!-- Exclude common Windows network activity -->
        <Image condition="is">C:\Windows\System32\svchost.exe</Image>
        <DestinationPort condition="is">53</DestinationPort>
        <DestinationPort condition="is">80</DestinationPort>
        <DestinationPort condition="is">443</DestinationPort>
      </NetworkConnect>

      <NetworkConnect onmatch="include">
        <!-- Monitor SMB/RDP connections -->
        <DestinationPort condition="is">445</DestinationPort>
        <DestinationPort condition="is">139</DestinationPort>
        <DestinationPort condition="is">3389</DestinationPort>

        <!-- Monitor WinRM connections -->
        <DestinationPort condition="is">5985</DestinationPort>
        <DestinationPort condition="is">5986</DestinationPort>

        <!-- Monitor uncommon ports -->
        <DestinationPort condition="is">4444</DestinationPort>
        <DestinationPort condition="is">8080</DestinationPort>
        <DestinationPort condition="is">9999</DestinationPort>

        <!-- Monitor external connections -->
        <Initiated condition="is">true</Initiated>
        <DestinationIp condition="begin with">192.168.</DestinationIp>
        <DestinationIp condition="begin with">10.</DestinationIp>
        <DestinationIp condition="begin with">172.</DestinationIp>
      </NetworkConnect>
    </RuleGroup>

    <!-- Event ID 7: Image/DLL Load -->
    <RuleGroup name="" groupRelation="or">
      <ImageLoad onmatch="include">
        <!-- Monitor suspicious DLL loads -->
        <ImageLoaded condition="contains">:\Temp\</ImageLoaded>
        <ImageLoaded condition="contains">:\Users\Public\</ImageLoaded>
        <ImageLoaded condition="contains">:\ProgramData\</ImageLoaded>

        <!-- Monitor reflective DLL loading -->
        <Signed condition="is">false</Signed>
        <SignatureStatus condition="is">Expired</SignatureStatus>

        <!-- Monitor specific suspicious DLLs -->
        <ImageLoaded condition="contains">mimikatz</ImageLoaded>
        <ImageLoaded condition="contains">powerkatz</ImageLoaded>
        <ImageLoaded condition="contains">invoke-mimikatz</ImageLoaded>
      </ImageLoad>
    </RuleGroup>

    <!-- Event ID 8: CreateRemoteThread -->
    <RuleGroup name="" groupRelation="or">
      <CreateRemoteThread onmatch="include">
        <!-- Monitor all process injection attempts -->
        <TargetImage condition="contains">lsass.exe</TargetImage>
        <TargetImage condition="contains">winlogon.exe</TargetImage>
        <TargetImage condition="contains">csrss.exe</TargetImage>
        <TargetImage condition="contains">explorer.exe</TargetImage>
      </CreateRemoteThread>
    </RuleGroup>

    <!-- Event ID 10: ProcessAccess -->
    <RuleGroup name="" groupRelation="or">
      <ProcessAccess onmatch="include">
        <!-- Monitor LSASS access for credential dumping -->
        <TargetImage condition="contains">lsass.exe</TargetImage>
        <GrantedAccess condition="is">0x1010</GrantedAccess>
        <GrantedAccess condition="is">0x1410</GrantedAccess>
        <GrantedAccess condition="is">0x147a</GrantedAccess>

        <!-- Monitor other critical process access -->
        <TargetImage condition="contains">winlogon.exe</TargetImage>
        <TargetImage condition="contains">csrss.exe</TargetImage>
      </ProcessAccess>
    </RuleGroup>

    <!-- Event ID 11: File Create -->
    <RuleGroup name="" groupRelation="or">
      <FileCreate onmatch="include">
        <!-- Monitor file creation in suspicious locations -->
        <TargetFilename condition="contains">:\Temp\</TargetFilename>
        <TargetFilename condition="contains">:\Users\Public\</TargetFilename>
        <TargetFilename condition="contains">:\ProgramData\</TargetFilename>
        <TargetFilename condition="contains">:\Windows\Temp\</TargetFilename>

        <!-- Monitor executable file creation -->
        <TargetFilename condition="end with">.exe</TargetFilename>
        <TargetFilename condition="end with">.dll</TargetFilename>
        <TargetFilename condition="end with">.scr</TargetFilename>
        <TargetFilename condition="end with">.bat</TargetFilename>
        <TargetFilename condition="end with">.cmd</TargetFilename>
        <TargetFilename condition="end with">.ps1</TargetFilename>
        <TargetFilename condition="end with">.vbs</TargetFilename>

        <!-- Monitor startup folder modifications -->
        <TargetFilename condition="contains">\Startup\</TargetFilename>
        <TargetFilename condition="contains">\Start Menu\</TargetFilename>
      </FileCreate>
    </RuleGroup>

    <!-- Event ID 12/13/14: Registry Events -->
    <RuleGroup name="" groupRelation="or">
      <RegistryEvent onmatch="include">
        <!-- Monitor Run keys for persistence -->
        <TargetObject condition="contains">CurrentVersion\Run</TargetObject>
        <TargetObject condition="contains">CurrentVersion\RunOnce</TargetObject>
        <TargetObject condition="contains">CurrentVersion\RunServices</TargetObject>

        <!-- Monitor service creation -->
        <TargetObject condition="contains">CurrentControlSet\Services</TargetObject>

        <!-- Monitor Windows Defender exclusions -->
        <TargetObject condition="contains">Windows Defender\Exclusions</TargetObject>

        <!-- Monitor security settings -->
        <TargetObject condition="contains">Policies\System</TargetObject>
        <TargetObject condition="contains">Control\SecurityProviders</TargetObject>
      </RegistryEvent>
    </RuleGroup>

    <!-- Event ID 15: FileCreateStreamHash -->
    <RuleGroup name="" groupRelation="or">
      <FileCreateStreamHash onmatch="include">
        <!-- Monitor alternate data streams -->
        <TargetFilename condition="contains">:</TargetFilename>
      </FileCreateStreamHash>
    </RuleGroup>

    <!-- Event ID 22: DNS Query -->
    <RuleGroup name="" groupRelation="or">
      <DnsQuery onmatch="include">
        <!-- Monitor suspicious DNS queries -->
        <QueryName condition="end with">.tk</QueryName>
        <QueryName condition="end with">.ml</QueryName>
        <QueryName condition="end with">.ga</QueryName>
        <QueryName condition="end with">.cf</QueryName>

        <!-- Monitor long DNS queries (potential tunneling) -->
        <QueryName condition="length">50</QueryName>

        <!-- Monitor base64-like DNS queries -->
        <QueryName condition="contains">==</QueryName>

        <!-- Monitor DGA-like domains -->
        <QueryName condition="contains">qwerty</QueryName>
        <QueryName condition="contains">asdfgh</QueryName>
      </DnsQuery>
    </RuleGroup>

  </EventFiltering>
</Sysmon>
```

## 📊 Sysmon Event Analysis

### **Event ID Reference for GOAD-Blue**

| Event ID | Event Type | Description | GOAD Use Cases |
|----------|------------|-------------|----------------|
| **1** | Process Creation | Process start with command line | Malware execution, lateral movement tools |
| **3** | Network Connection | TCP/UDP connections | C2 communication, lateral movement |
| **5** | Process Termination | Process end | Anti-forensics, process hiding |
| **7** | Image/DLL Load | Module loading | DLL injection, reflective loading |
| **8** | CreateRemoteThread | Process injection | Code injection, process hollowing |
| **10** | ProcessAccess | Process access | Credential dumping, process manipulation |
| **11** | File Create | File creation | Malware dropping, persistence |
| **12** | Registry Key/Value Create | Registry creation | Persistence mechanisms |
| **13** | Registry Value Set | Registry modification | Configuration changes |
| **14** | Registry Key/Value Rename | Registry rename | Evasion techniques |
| **15** | FileCreateStreamHash | Alternate data streams | Data hiding, evasion |
| **17** | Pipe Created | Named pipe creation | Inter-process communication |
| **18** | Pipe Connected | Named pipe connection | Lateral movement, C2 |
| **22** | DNS Query | DNS resolution | C2 domains, data exfiltration |

### **GOAD-Specific Detection Queries**

```sql
-- Splunk queries for Sysmon analysis

-- Lateral movement via SMB
index=goad_blue_windows source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=3
| where match(dest_port, "445|139")
| stats count by src_ip, dest_ip, Image
| where count > 5

-- Credential dumping detection
index=goad_blue_windows source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=10
| where match(TargetImage, "lsass.exe") AND match(GrantedAccess, "0x1010|0x1410|0x147a")
| table _time, Computer, SourceImage, TargetImage, GrantedAccess

-- PowerShell execution analysis
index=goad_blue_windows source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=1
| where match(Image, "powershell")
| where match(CommandLine, "ExecutionPolicy|EncodedCommand|IEX|Invoke-Expression")
| table _time, Computer, User, CommandLine

-- Persistence via registry
index=goad_blue_windows source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=13
| where match(TargetObject, "CurrentVersion\\\\Run")
| table _time, Computer, Image, TargetObject, Details

-- Suspicious file creation
index=goad_blue_windows source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=11
| where match(TargetFilename, "\\\\Temp\\\\|\\\\Public\\\\|\\\\ProgramData\\\\")
| where match(TargetFilename, "\\.exe$|\\.dll$|\\.scr$|\\.bat$|\\.ps1$")
| table _time, Computer, Image, TargetFilename

-- DNS tunneling detection
index=goad_blue_windows source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=22
| eval query_length=len(QueryName)
| where query_length > 50 OR match(QueryName, "==")
| table _time, Computer, Image, QueryName, query_length
```

### **Elasticsearch/Kibana Queries**

```json
{
  "query": {
    "bool": {
      "must": [
        {"term": {"winlog.event_id": "1"}},
        {"wildcard": {"winlog.event_data.Image": "*powershell*"}},
        {"wildcard": {"winlog.event_data.CommandLine": "*ExecutionPolicy*"}}
      ]
    }
  }
}
```

## 🔍 Threat Hunting with Sysmon

### **Advanced Hunting Techniques**

```powershell
# PowerShell script for Sysmon log analysis
function Hunt-LateralMovement {
    param(
        [string]$ComputerName = "localhost",
        [int]$Hours = 24
    )

    $StartTime = (Get-Date).AddHours(-$Hours)

    # Hunt for network connections to SMB/RDP ports
    $NetworkEvents = Get-WinEvent -FilterHashtable @{
        LogName = 'Microsoft-Windows-Sysmon/Operational'
        ID = 3
        StartTime = $StartTime
    } | Where-Object {
        $_.Message -match "DestinationPort: (445|139|3389)"
    }

    # Group by source process and destination
    $NetworkEvents | ForEach-Object {
        $Event = [xml]$_.ToXml()
        [PSCustomObject]@{
            TimeCreated = $_.TimeCreated
            Computer = $Event.Event.System.Computer
            ProcessId = $Event.Event.EventData.Data | Where-Object {$_.Name -eq "ProcessId"} | Select-Object -ExpandProperty '#text'
            Image = $Event.Event.EventData.Data | Where-Object {$_.Name -eq "Image"} | Select-Object -ExpandProperty '#text'
            DestinationIp = $Event.Event.EventData.Data | Where-Object {$_.Name -eq "DestinationIp"} | Select-Object -ExpandProperty '#text'
            DestinationPort = $Event.Event.EventData.Data | Where-Object {$_.Name -eq "DestinationPort"} | Select-Object -ExpandProperty '#text'
        }
    } | Group-Object Image, DestinationIp | Where-Object {$_.Count -gt 3}
}

function Hunt-ProcessInjection {
    param(
        [string]$ComputerName = "localhost",
        [int]$Hours = 24
    )

    $StartTime = (Get-Date).AddHours(-$Hours)

    # Hunt for CreateRemoteThread events
    $InjectionEvents = Get-WinEvent -FilterHashtable @{
        LogName = 'Microsoft-Windows-Sysmon/Operational'
        ID = 8
        StartTime = $StartTime
    }

    $InjectionEvents | ForEach-Object {
        $Event = [xml]$_.ToXml()
        [PSCustomObject]@{
            TimeCreated = $_.TimeCreated
            Computer = $Event.Event.System.Computer
            SourceImage = $Event.Event.EventData.Data | Where-Object {$_.Name -eq "SourceImage"} | Select-Object -ExpandProperty '#text'
            TargetImage = $Event.Event.EventData.Data | Where-Object {$_.Name -eq "TargetImage"} | Select-Object -ExpandProperty '#text'
            SourceProcessId = $Event.Event.EventData.Data | Where-Object {$_.Name -eq "SourceProcessId"} | Select-Object -ExpandProperty '#text'
            TargetProcessId = $Event.Event.EventData.Data | Where-Object {$_.Name -eq "TargetProcessId"} | Select-Object -ExpandProperty '#text'
        }
    }
}

function Hunt-CredentialAccess {
    param(
        [string]$ComputerName = "localhost",
        [int]$Hours = 24
    )

    $StartTime = (Get-Date).AddHours(-$Hours)

    # Hunt for LSASS access
    $AccessEvents = Get-WinEvent -FilterHashtable @{
        LogName = 'Microsoft-Windows-Sysmon/Operational'
        ID = 10
        StartTime = $StartTime
    } | Where-Object {
        $_.Message -match "TargetImage.*lsass.exe" -and
        $_.Message -match "GrantedAccess: 0x(1010|1410|147a)"
    }

    $AccessEvents | ForEach-Object {
        $Event = [xml]$_.ToXml()
        [PSCustomObject]@{
            TimeCreated = $_.TimeCreated
            Computer = $Event.Event.System.Computer
            SourceImage = $Event.Event.EventData.Data | Where-Object {$_.Name -eq "SourceImage"} | Select-Object -ExpandProperty '#text'
            TargetImage = $Event.Event.EventData.Data | Where-Object {$_.Name -eq "TargetImage"} | Select-Object -ExpandProperty '#text'
            GrantedAccess = $Event.Event.EventData.Data | Where-Object {$_.Name -eq "GrantedAccess"} | Select-Object -ExpandProperty '#text'
            CallTrace = $Event.Event.EventData.Data | Where-Object {$_.Name -eq "CallTrace"} | Select-Object -ExpandProperty '#text'
        }
    }
}
```

### **Automated Threat Detection**

```python
# Python script for automated Sysmon analysis
import xml.etree.ElementTree as ET
import re
from datetime import datetime, timedelta
import win32evtlog

class SysmonThreatDetector:
    def __init__(self):
        self.suspicious_processes = [
            'mimikatz', 'procdump', 'pwdump', 'fgdump',
            'wce.exe', 'gsecdump', 'cachedump'
        ]

        self.lateral_movement_tools = [
            'psexec', 'winrs', 'wmic', 'schtasks',
            'at.exe', 'net.exe'
        ]

        self.persistence_locations = [
            r'CurrentVersion\\Run',
            r'CurrentVersion\\RunOnce',
            r'CurrentControlSet\\Services'
        ]

    def detect_credential_dumping(self, hours=24):
        """Detect credential dumping activities"""
        events = self.get_sysmon_events(event_id=10, hours=hours)
        alerts = []

        for event in events:
            if self.is_lsass_access(event):
                alerts.append({
                    'type': 'credential_dumping',
                    'severity': 'high',
                    'timestamp': event['timestamp'],
                    'computer': event['computer'],
                    'source_image': event['source_image'],
                    'details': f"LSASS access detected from {event['source_image']}"
                })

        return alerts

    def detect_lateral_movement(self, hours=24):
        """Detect lateral movement activities"""
        # Check for network connections to SMB/RDP ports
        network_events = self.get_sysmon_events(event_id=3, hours=hours)
        process_events = self.get_sysmon_events(event_id=1, hours=hours)

        alerts = []

        # Analyze network connections
        smb_connections = {}
        for event in network_events:
            if event.get('destination_port') in ['445', '139', '3389']:
                key = (event['source_ip'], event['image'])
                if key not in smb_connections:
                    smb_connections[key] = []
                smb_connections[key].append(event['destination_ip'])

        # Alert on multiple destinations
        for (source_ip, image), destinations in smb_connections.items():
            if len(set(destinations)) > 3:
                alerts.append({
                    'type': 'lateral_movement',
                    'severity': 'high',
                    'source_ip': source_ip,
                    'image': image,
                    'destination_count': len(set(destinations)),
                    'details': f"Multiple SMB/RDP connections from {image}"
                })

        # Analyze process creation for lateral movement tools
        for event in process_events:
            if any(tool in event.get('image', '').lower() for tool in self.lateral_movement_tools):
                alerts.append({
                    'type': 'lateral_movement_tool',
                    'severity': 'medium',
                    'timestamp': event['timestamp'],
                    'computer': event['computer'],
                    'image': event['image'],
                    'command_line': event.get('command_line', ''),
                    'details': f"Lateral movement tool detected: {event['image']}"
                })

        return alerts

    def detect_persistence(self, hours=24):
        """Detect persistence mechanisms"""
        registry_events = self.get_sysmon_events(event_id=13, hours=hours)
        file_events = self.get_sysmon_events(event_id=11, hours=hours)

        alerts = []

        # Check registry persistence
        for event in registry_events:
            target_object = event.get('target_object', '')
            if any(location in target_object for location in self.persistence_locations):
                alerts.append({
                    'type': 'registry_persistence',
                    'severity': 'medium',
                    'timestamp': event['timestamp'],
                    'computer': event['computer'],
                    'image': event['image'],
                    'target_object': target_object,
                    'details': event.get('details', ''),
                    'description': f"Registry persistence detected in {target_object}"
                })

        # Check startup folder modifications
        for event in file_events:
            target_filename = event.get('target_filename', '')
            if 'startup' in target_filename.lower() or 'start menu' in target_filename.lower():
                alerts.append({
                    'type': 'startup_persistence',
                    'severity': 'medium',
                    'timestamp': event['timestamp'],
                    'computer': event['computer'],
                    'image': event['image'],
                    'target_filename': target_filename,
                    'description': f"Startup folder modification: {target_filename}"
                })

        return alerts

    def is_lsass_access(self, event):
        """Check if event represents LSASS access for credential dumping"""
        target_image = event.get('target_image', '').lower()
        granted_access = event.get('granted_access', '')

        return ('lsass.exe' in target_image and
                granted_access in ['0x1010', '0x1410', '0x147a'])

    def get_sysmon_events(self, event_id, hours=24):
        """Retrieve Sysmon events from Windows Event Log"""
        # Implementation would use win32evtlog or similar
        # This is a simplified example
        events = []

        # Query Windows Event Log for Sysmon events
        # Parse XML and extract relevant fields

        return events

## 🚨 Alerting and Response

### **Real-time Monitoring**

```powershell
# PowerShell script for real-time Sysmon monitoring
function Start-SysmonMonitoring {
    param(
        [string[]]$AlertTypes = @("credential_dumping", "lateral_movement", "persistence"),
        [string]$OutputPath = "C:\Logs\SysmonAlerts.log"
    )

    # Register for Sysmon events
    Register-WmiEvent -Query "SELECT * FROM Win32_VolumeChangeEvent WHERE EventType = 2" -Action {
        $Event = $Event.SourceEventArgs.NewEvent
        Write-Host "Sysmon Event Detected: $($Event.EventType)"
    }

    # Monitor specific event IDs
    $EventIDs = @(1, 3, 7, 8, 10, 11, 13, 22)

    foreach ($EventID in $EventIDs) {
        Register-ObjectEvent -InputObject (Get-WinEvent -ListLog "Microsoft-Windows-Sysmon/Operational") -EventName EntryWritten -Action {
            $Entry = $Event.SourceEventArgs.Entry

            if ($Entry.Id -in $EventIDs) {
                $Alert = Analyze-SysmonEvent -Event $Entry
                if ($Alert) {
                    Write-SysmonAlert -Alert $Alert -OutputPath $OutputPath
                    Send-SysmonAlert -Alert $Alert
                }
            }
        }
    }
}

function Analyze-SysmonEvent {
    param($Event)

    $EventXml = [xml]$Event.ToXml()
    $EventData = @{}

    foreach ($Data in $EventXml.Event.EventData.Data) {
        $EventData[$Data.Name] = $Data.'#text'
    }

    # Analyze based on event type
    switch ($Event.Id) {
        1 { return Analyze-ProcessCreation -EventData $EventData }
        3 { return Analyze-NetworkConnection -EventData $EventData }
        8 { return Analyze-ProcessInjection -EventData $EventData }
        10 { return Analyze-ProcessAccess -EventData $EventData }
        11 { return Analyze-FileCreation -EventData $EventData }
        13 { return Analyze-RegistryModification -EventData $EventData }
        22 { return Analyze-DNSQuery -EventData $EventData }
    }

    return $null
}
```

### **Integration with SIEM**

```yaml
# Logstash configuration for Sysmon data processing
input {
  beats {
    port => 5044
    type => "winlogbeat"
  }
}

filter {
  if [winlog][channel] == "Microsoft-Windows-Sysmon/Operational" {

    # Add GOAD-Blue specific tags
    mutate {
      add_field => { "data_source" => "sysmon" }
      add_field => { "environment" => "goad-blue" }
    }

    # Parse event based on Event ID
    if [winlog][event_id] == 1 {
      mutate { add_field => { "event_category" => "process_creation" } }

      # Extract command line arguments
      if [winlog][event_data][CommandLine] {
        mutate {
          add_field => { "process_command_line" => "%{[winlog][event_data][CommandLine]}" }
        }
      }

      # Detect suspicious processes
      if [winlog][event_data][Image] =~ /(?i)(mimikatz|procdump|pwdump)/ {
        mutate {
          add_field => { "threat_category" => "credential_access" }
          add_field => { "severity" => "high" }
        }
      }
    }

    if [winlog][event_id] == 3 {
      mutate { add_field => { "event_category" => "network_connection" } }

      # Detect lateral movement
      if [winlog][event_data][DestinationPort] in ["445", "139", "3389"] {
        mutate {
          add_field => { "threat_category" => "lateral_movement" }
          add_field => { "severity" => "medium" }
        }
      }
    }

    if [winlog][event_id] == 10 {
      mutate { add_field => { "event_category" => "process_access" } }

      # Detect LSASS access
      if [winlog][event_data][TargetImage] =~ /lsass\.exe/ and
         [winlog][event_data][GrantedAccess] in ["0x1010", "0x1410", "0x147a"] {
        mutate {
          add_field => { "threat_category" => "credential_access" }
          add_field => { "severity" => "high" }
        }
      }
    }

    # GeoIP enrichment for external connections
    if [winlog][event_data][DestinationIp] and
       [winlog][event_data][DestinationIp] !~ /^(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)/ {
      geoip {
        source => "[winlog][event_data][DestinationIp]"
        target => "geoip"
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["goad-blue-elasticsearch:9200"]
    index => "goad-blue-sysmon-%{+YYYY.MM.dd}"
  }

  # Send high-severity alerts to SOAR
  if [severity] == "high" {
    http {
      url => "https://soar.goad-blue.local/api/alerts"
      http_method => "post"
      headers => {
        "Authorization" => "Bearer YOUR_API_TOKEN"
        "Content-Type" => "application/json"
      }
    }
  }
}
```

## 📈 Performance Optimization

### **Configuration Tuning**

```xml
<!-- Optimized Sysmon configuration for performance -->
<Sysmon schemaversion="4.82">
  <HashAlgorithms>md5,sha256</HashAlgorithms>
  <CheckRevocation>false</CheckRevocation>
  <CopyOnDeletePE>false</CopyOnDeletePE>
  <DnsLookup>false</DnsLookup>

  <EventFiltering>
    <!-- Use specific exclusions to reduce noise -->
    <RuleGroup name="Performance Optimizations" groupRelation="or">
      <ProcessCreate onmatch="exclude">
        <!-- Exclude high-volume, low-value processes -->
        <Image condition="is">C:\Windows\System32\backgroundTaskHost.exe</Image>
        <Image condition="is">C:\Windows\System32\taskhostw.exe</Image>
        <Image condition="is">C:\Windows\System32\dwm.exe</Image>
        <ParentImage condition="is">C:\Windows\System32\services.exe</ParentImage>

        <!-- Exclude based on command line patterns -->
        <CommandLine condition="contains">-Embedding</CommandLine>
        <CommandLine condition="contains">-ServerName:</CommandLine>
      </ProcessCreate>

      <NetworkConnect onmatch="exclude">
        <!-- Exclude local network traffic -->
        <DestinationIp condition="is">127.0.0.1</DestinationIp>
        <DestinationIp condition="is">::1</DestinationIp>
        <SourceIp condition="is">127.0.0.1</SourceIp>

        <!-- Exclude common Windows services -->
        <Image condition="is">C:\Windows\System32\svchost.exe</Image>
        <Image condition="is">C:\Windows\System32\spoolsv.exe</Image>
      </NetworkConnect>
    </RuleGroup>
  </EventFiltering>
</Sysmon>
```

### **Log Management**

```powershell
# PowerShell script for Sysmon log management
function Manage-SysmonLogs {
    param(
        [int]$MaxSizeMB = 1024,
        [int]$RetentionDays = 30
    )

    # Configure log size and retention
    $LogName = "Microsoft-Windows-Sysmon/Operational"

    # Set maximum log size
    wevtutil sl $LogName /ms:$($MaxSizeMB * 1024 * 1024)

    # Set log retention policy
    wevtutil sl $LogName /rt:false

    # Archive old logs
    $ArchivePath = "C:\Logs\SysmonArchive"
    if (!(Test-Path $ArchivePath)) {
        New-Item -ItemType Directory -Path $ArchivePath -Force
    }

    $CutoffDate = (Get-Date).AddDays(-$RetentionDays)
    wevtutil epl $LogName "$ArchivePath\Sysmon-$(Get-Date -Format 'yyyyMMdd').evtx" "/q:*[System[TimeCreated[@SystemTime<='$($CutoffDate.ToString('yyyy-MM-ddTHH:mm:ss.fffZ'))']]]"

    # Clear old events
    wevtutil cl $LogName
}

# Schedule log management
$Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\Scripts\Manage-SysmonLogs.ps1"
$Trigger = New-ScheduledTaskTrigger -Daily -At "02:00AM"
$Settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
Register-ScheduledTask -TaskName "SysmonLogManagement" -Action $Action -Trigger $Trigger -Settings $Settings -RunLevel Highest
```

## 🎓 Training Scenarios

### **Sysmon Analysis Fundamentals**

1. **Event Correlation**
   - Link process creation to network connections
   - Correlate file creation with process execution
   - Track parent-child process relationships

2. **Threat Detection**
   - Identify credential dumping attempts
   - Detect lateral movement patterns
   - Recognize persistence mechanisms

3. **Performance Tuning**
   - Optimize configuration for environment
   - Balance detection coverage with performance
   - Implement effective log management

### **Advanced Sysmon Techniques**

1. **Custom Rule Development**
   - Create environment-specific filters
   - Develop threat-specific detection rules
   - Implement performance optimizations

2. **Integration and Automation**
   - SIEM integration best practices
   - Automated alert response
   - Custom analysis tools development

3. **Forensic Analysis**
   - Timeline reconstruction using Sysmon data
   - Attack path analysis
   - Evidence collection and preservation

## 🔗 Integration Points

### **GOAD Environment Integration**

```yaml
# Ansible playbook for GOAD Sysmon deployment
---
- name: Deploy Sysmon to GOAD Environment
  hosts: goad_windows
  vars:
    sysmon_config_url: "https://raw.githubusercontent.com/GOAD-Blue/configs/main/sysmon-goad-blue.xml"
    sysmon_download_url: "https://download.sysinternals.com/files/Sysmon.zip"

  tasks:
    - name: Create Sysmon directory
      win_file:
        path: C:\Tools\Sysmon
        state: directory

    - name: Download Sysmon
      win_get_url:
        url: "{{ sysmon_download_url }}"
        dest: C:\Tools\Sysmon\Sysmon.zip

    - name: Extract Sysmon
      win_unzip:
        src: C:\Tools\Sysmon\Sysmon.zip
        dest: C:\Tools\Sysmon

    - name: Download GOAD-Blue configuration
      win_get_url:
        url: "{{ sysmon_config_url }}"
        dest: C:\Tools\Sysmon\sysmon-goad-blue.xml

    - name: Install Sysmon
      win_command: C:\Tools\Sysmon\Sysmon64.exe -accepteula -i C:\Tools\Sysmon\sysmon-goad-blue.xml
      register: sysmon_install

    - name: Verify Sysmon service
      win_service:
        name: Sysmon64
        state: started
        start_mode: auto

    - name: Configure Windows Event Log forwarding
      win_shell: |
        wecutil cs C:\Tools\Sysmon\subscription.xml
      when: configure_forwarding | default(false)
```

### **Threat Intelligence Integration**

```python
# MISP integration for Sysmon IOC enrichment
from pymisp import PyMISP
import re
import hashlib

class SysmonThreatIntelligence:
    def __init__(self, misp_url, misp_key):
        self.misp = PyMISP(misp_url, misp_key)

    def enrich_sysmon_event(self, event_data):
        """Enrich Sysmon event with threat intelligence"""
        enrichment = {}

        # Extract IOCs from event
        iocs = self.extract_iocs(event_data)

        for ioc_type, ioc_value in iocs.items():
            # Search MISP for IOC
            result = self.misp.search(value=ioc_value, type_attribute=ioc_type)

            if result:
                enrichment[ioc_type] = {
                    'value': ioc_value,
                    'threat_level': result[0].get('threat_level_id'),
                    'tags': [tag.name for tag in result[0].tags],
                    'first_seen': result[0].get('timestamp'),
                    'info': result[0].get('info')
                }

        return enrichment

    def extract_iocs(self, event_data):
        """Extract IOCs from Sysmon event data"""
        iocs = {}

        # Extract file hashes
        if 'Hashes' in event_data:
            hashes = event_data['Hashes']
            if 'SHA256=' in hashes:
                sha256 = hashes.split('SHA256=')[1].split(',')[0]
                iocs['sha256'] = sha256
            if 'MD5=' in hashes:
                md5 = hashes.split('MD5=')[1].split(',')[0]
                iocs['md5'] = md5

        # Extract IP addresses
        if 'DestinationIp' in event_data:
            ip = event_data['DestinationIp']
            if self.is_external_ip(ip):
                iocs['ip-dst'] = ip

        # Extract domain names
        if 'QueryName' in event_data:
            domain = event_data['QueryName']
            if self.is_suspicious_domain(domain):
                iocs['domain'] = domain

        return iocs

    def is_external_ip(self, ip):
        """Check if IP is external (not RFC 1918)"""
        private_ranges = [
            r'^10\.',
            r'^172\.(1[6-9]|2[0-9]|3[01])\.',
            r'^192\.168\.',
            r'^127\.',
            r'^169\.254\.'
        ]

        return not any(re.match(pattern, ip) for pattern in private_ranges)

    def is_suspicious_domain(self, domain):
        """Check if domain appears suspicious"""
        suspicious_tlds = ['.tk', '.ml', '.ga', '.cf']
        return any(domain.endswith(tld) for tld in suspicious_tlds) or len(domain) > 50
```

---

!!! tip "Sysmon Best Practices"
    - Start with a conservative configuration and gradually expand coverage
    - Regularly review and tune filters to reduce false positives
    - Monitor log volume and system performance impact
    - Integrate with SIEM for centralized analysis and alerting
    - Keep Sysmon and configuration files updated

!!! warning "Performance Impact"
    Sysmon can generate large volumes of logs and impact system performance. Carefully tune your configuration based on your environment's needs and monitoring capabilities.

!!! info "Learning Resources"
    - Microsoft Sysinternals Sysmon documentation
    - SwiftOnSecurity Sysmon configuration templates
    - GOAD-Blue specific detection use cases and scenarios
    - Community-contributed configurations and analysis tools
```