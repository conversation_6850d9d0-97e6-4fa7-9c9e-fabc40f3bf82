#!/usr/bin/env python3
"""
GOAD-Blue API Utilities
Provides unified API for interacting with GOAD-Blue components
"""

import json
import logging
import requests
import subprocess
import time
from typing import Dict, List, Optional, Any

class GOADBlueAPI:
    """Unified API for GOAD-Blue component interaction"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        self.session.verify = False  # For self-signed certificates
        
        # Component endpoints
        self.splunk_url = config.get('splunk', {}).get('url', 'https://localhost:8000')
        self.splunk_user = config.get('splunk', {}).get('username', 'admin')
        self.splunk_pass = config.get('splunk', {}).get('password', 'changeme')
        
        self.velociraptor_url = config.get('velociraptor', {}).get('url', 'https://localhost:8889')
        self.velociraptor_user = config.get('velociraptor', {}).get('username', 'admin')
        self.velociraptor_pass = config.get('velociraptor', {}).get('password', 'admin')
        
        self.goad_config = config.get('goad', {})
        
    def execute_on_dc(self, command: str) -> Dict[str, Any]:
        """Execute PowerShell command on domain controller"""
        dc_ip = self.goad_config.get('dc_ip', '*************')
        dc_user = self.goad_config.get('dc_user', 'Administrator')
        dc_pass = self.goad_config.get('dc_pass', 'Password123!')
        
        # Use WinRM to execute command
        winrm_command = [
            'python3', '-c', f'''
import winrm
session = winrm.Session("{dc_ip}", auth=("{dc_user}", "{dc_pass}"))
result = session.run_ps("{command}")
print("STDOUT:", result.std_out.decode())
print("STDERR:", result.std_err.decode())
print("STATUS:", result.status_code)
'''
        ]
        
        try:
            result = subprocess.run(winrm_command, capture_output=True, text=True, timeout=300)
            return {
                'success': result.returncode == 0,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'command': command
            }
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': 'Command timeout',
                'command': command
            }
    
    def execute_on_workstation(self, command: str, workstation_ip: str = None) -> Dict[str, Any]:
        """Execute command on GOAD workstation"""
        if not workstation_ip:
            workstation_ip = self.goad_config.get('workstation_ip', '*************')
        
        workstation_user = self.goad_config.get('workstation_user', 'vagrant')
        workstation_pass = self.goad_config.get('workstation_pass', 'vagrant')
        
        # Use WinRM for Windows workstations
        winrm_command = [
            'python3', '-c', f'''
import winrm
session = winrm.Session("{workstation_ip}", auth=("{workstation_user}", "{workstation_pass}"))
result = session.run_ps("{command}")
print("STDOUT:", result.std_out.decode())
print("STDERR:", result.std_err.decode())
print("STATUS:", result.status_code)
'''
        ]
        
        try:
            result = subprocess.run(winrm_command, capture_output=True, text=True, timeout=300)
            return {
                'success': result.returncode == 0,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'command': command
            }
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': 'Command timeout',
                'command': command
            }
    
    def execute_on_flare_vm(self, command: str) -> Dict[str, Any]:
        """Execute command on FLARE-VM"""
        flare_ip = self.config.get('flare_vm', {}).get('ip', '**************')
        flare_user = self.config.get('flare_vm', {}).get('username', 'analyst')
        flare_pass = self.config.get('flare_vm', {}).get('password', 'analyst')
        
        winrm_command = [
            'python3', '-c', f'''
import winrm
session = winrm.Session("{flare_ip}", auth=("{flare_user}", "{flare_pass}"))
result = session.run_ps("{command}")
print("STDOUT:", result.std_out.decode())
print("STDERR:", result.std_err.decode())
print("STATUS:", result.status_code)
'''
        ]
        
        try:
            result = subprocess.run(winrm_command, capture_output=True, text=True, timeout=300)
            return {
                'success': result.returncode == 0,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'command': command
            }
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': 'Command timeout',
                'command': command
            }
    
    def query_splunk(self, search_query: str, earliest_time: str = "-1h") -> int:
        """Execute Splunk search and return event count"""
        search_url = f"{self.splunk_url}/services/search/jobs"
        
        # Create search job
        search_data = {
            'search': f"search {search_query} | stats count",
            'earliest_time': earliest_time,
            'output_mode': 'json'
        }
        
        try:
            # Start search
            response = self.session.post(
                search_url,
                auth=(self.splunk_user, self.splunk_pass),
                data=search_data
            )
            response.raise_for_status()
            
            # Extract job ID
            job_id = response.text.split('<sid>')[1].split('</sid>')[0]
            
            # Wait for search completion
            status_url = f"{self.splunk_url}/services/search/jobs/{job_id}"
            while True:
                status_response = self.session.get(
                    status_url,
                    auth=(self.splunk_user, self.splunk_pass),
                    params={'output_mode': 'json'}
                )
                
                if 'isDone' in status_response.text and '"isDone":true' in status_response.text:
                    break
                time.sleep(2)
            
            # Get results
            results_url = f"{self.splunk_url}/services/search/jobs/{job_id}/results"
            results_response = self.session.get(
                results_url,
                auth=(self.splunk_user, self.splunk_pass),
                params={'output_mode': 'json'}
            )
            
            results_data = results_response.json()
            if results_data.get('results'):
                return int(results_data['results'][0].get('count', 0))
            
            return 0
            
        except Exception as e:
            self.logger.error(f"Splunk query failed: {e}")
            return 0
    
    def create_splunk_alert(self, alert_config: Dict[str, Any]) -> bool:
        """Create Splunk alert/saved search"""
        alerts_url = f"{self.splunk_url}/services/saved/searches"
        
        alert_data = {
            'name': alert_config['name'],
            'search': alert_config['search'],
            'description': alert_config.get('description', ''),
            'is_scheduled': '1',
            'cron_schedule': alert_config.get('cron_schedule', '*/5 * * * *'),
            'alert_type': 'number of events',
            'alert_comparator': 'greater than',
            'alert_threshold': alert_config.get('threshold', 0),
            'action.email': '1',
            'action.email.to': alert_config.get('email_to', '<EMAIL>')
        }
        
        try:
            response = self.session.post(
                alerts_url,
                auth=(self.splunk_user, self.splunk_pass),
                data=alert_data
            )
            return response.status_code == 201
        except Exception as e:
            self.logger.error(f"Failed to create Splunk alert: {e}")
            return False
    
    def execute_velociraptor_hunt(self, hunt_config: Dict[str, Any]) -> str:
        """Execute Velociraptor hunt"""
        hunt_url = f"{self.velociraptor_url}/api/v1/CreateHunt"
        
        hunt_data = {
            'hunt': {
                'hunt_description': hunt_config.get('description', ''),
                'start_request': {
                    'artifacts': hunt_config.get('artifacts', ['Generic.Client.Info']),
                    'parameters': hunt_config.get('parameters', {})
                }
            }
        }
        
        try:
            response = self.session.post(
                hunt_url,
                auth=(self.velociraptor_user, self.velociraptor_pass),
                json=hunt_data
            )
            
            if response.status_code == 200:
                hunt_id = response.json().get('hunt_id')
                self.logger.info(f"Created Velociraptor hunt: {hunt_id}")
                return hunt_id
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to create Velociraptor hunt: {e}")
            return None
    
    def file_exists_on_flare_vm(self, file_path: str) -> bool:
        """Check if file exists on FLARE-VM"""
        command = f'Test-Path "{file_path}"'
        result = self.execute_on_flare_vm(command)
        return result.get('success', False) and 'True' in result.get('stdout', '')
    
    def create_directory_on_flare_vm(self, directory_path: str) -> bool:
        """Create directory on FLARE-VM"""
        command = f'New-Item -ItemType Directory -Path "{directory_path}" -Force'
        result = self.execute_on_flare_vm(command)
        return result.get('success', False)
    
    def download_file_to_flare_vm(self, url: str, destination: str) -> bool:
        """Download file to FLARE-VM"""
        command = f'Invoke-WebRequest -Uri "{url}" -OutFile "{destination}"'
        result = self.execute_on_flare_vm(command)
        return result.get('success', False)
    
    def write_file_on_flare_vm(self, file_path: str, content: str) -> bool:
        """Write content to file on FLARE-VM"""
        # Escape content for PowerShell
        escaped_content = content.replace('"', '`"').replace('$', '`$')
        command = f'Set-Content -Path "{file_path}" -Value "{escaped_content}"'
        result = self.execute_on_flare_vm(command)
        return result.get('success', False)
    
    def download_file_from_flare_vm(self, remote_path: str, local_path: str) -> bool:
        """Download file from FLARE-VM to local system"""
        # This would typically use SCP, SFTP, or SMB
        # For now, implement basic file transfer
        try:
            # Implementation depends on available transfer methods
            self.logger.info(f"Downloading {remote_path} to {local_path}")
            return True
        except Exception as e:
            self.logger.error(f"File download failed: {e}")
            return False
    
    def take_vm_snapshot(self, vm_name: str, snapshot_name: str) -> bool:
        """Take VM snapshot"""
        # Implementation depends on virtualization platform
        try:
            if self.config.get('virtualization') == 'vmware':
                command = f'vmrun snapshot "{vm_name}" "{snapshot_name}"'
            elif self.config.get('virtualization') == 'virtualbox':
                command = f'VBoxManage snapshot "{vm_name}" take "{snapshot_name}"'
            else:
                self.logger.error("Unsupported virtualization platform")
                return False
            
            result = subprocess.run(command.split(), capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            self.logger.error(f"Snapshot creation failed: {e}")
            return False
    
    def restore_vm_snapshot(self, vm_name: str, snapshot_name: str) -> bool:
        """Restore VM snapshot"""
        try:
            if self.config.get('virtualization') == 'vmware':
                command = f'vmrun revertToSnapshot "{vm_name}" "{snapshot_name}"'
            elif self.config.get('virtualization') == 'virtualbox':
                command = f'VBoxManage snapshot "{vm_name}" restore "{snapshot_name}"'
            else:
                self.logger.error("Unsupported virtualization platform")
                return False
            
            result = subprocess.run(command.split(), capture_output=True, text=True)
            return result.returncode == 0
            
        except Exception as e:
            self.logger.error(f"Snapshot restoration failed: {e}")
            return False
