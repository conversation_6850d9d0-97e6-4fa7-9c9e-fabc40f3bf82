---
# GOAD-Blue SIEM Deployment Playbook
# Deploys and configures SIEM components (Splunk/Elastic Stack)

- name: Deploy SIEM Infrastructure
  hosts: siem_servers
  become: yes
  vars_files:
    - vars/goad-blue-config.yml
    - vars/siem-config.yml
  
  pre_tasks:
    - name: Validate SIEM configuration
      assert:
        that:
          - goad_blue_config.siem.type in ['splunk', 'elastic']
          - goad_blue_config.siem.version is defined
        fail_msg: "Invalid SIEM configuration"

  tasks:
    - name: Deploy Splunk Enterprise
      include_tasks: tasks/siem/deploy_splunk.yml
      when: goad_blue_config.siem.type == 'splunk'

    - name: Deploy Elastic Stack
      include_tasks: tasks/siem/deploy_elastic.yml
      when: goad_blue_config.siem.type == 'elastic'

    - name: Configure SIEM for GOAD-Blue
      include_tasks: tasks/siem/configure_siem.yml

    - name: Install GOAD-Blue apps and dashboards
      include_tasks: tasks/siem/install_apps.yml

    - name: Configure log forwarding
      include_tasks: tasks/siem/configure_forwarding.yml

- name: Deploy Universal Forwarders
  hosts: forwarder_targets
  become: yes
  vars_files:
    - vars/goad-blue-config.yml
    - vars/siem-config.yml
  
  tasks:
    - name: Install Splunk Universal Forwarder
      include_tasks: tasks/siem/install_forwarder.yml
      when: goad_blue_config.siem.type == 'splunk'

    - name: Install Elastic Beats
      include_tasks: tasks/siem/install_beats.yml
      when: goad_blue_config.siem.type == 'elastic'

    - name: Configure log collection
      include_tasks: tasks/siem/configure_log_collection.yml

- name: Configure SIEM Integration
  hosts: siem_servers
  become: yes
  vars_files:
    - vars/goad-blue-config.yml
  
  tasks:
    - name: Create GOAD-Blue indexes/indices
      include_tasks: tasks/siem/create_indexes.yml

    - name: Install Technical Add-ons
      include_tasks: tasks/siem/install_technical_addons.yml

    - name: Configure data models
      include_tasks: tasks/siem/configure_data_models.yml

    - name: Create saved searches and alerts
      include_tasks: tasks/siem/create_searches_alerts.yml

    - name: Configure user roles and permissions
      include_tasks: tasks/siem/configure_users.yml

    - name: Validate SIEM deployment
      include_tasks: tasks/siem/validate_siem.yml

  post_tasks:
    - name: Generate SIEM access information
      template:
        src: templates/siem_access_info.j2
        dest: "{{ playbook_dir }}/output/siem_access.yml"
      delegate_to: localhost

    - name: Display SIEM deployment summary
      debug:
        msg: |
          SIEM Deployment Complete:
          Type: {{ goad_blue_config.siem.type }}
          URL: {{ siem_web_url }}
          Admin User: {{ siem_admin_user }}
          Status: {{ siem_deployment_status }}
