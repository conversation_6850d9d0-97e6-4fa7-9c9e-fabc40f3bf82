# Red vs Blue Exercises

Red vs Blue exercises are the cornerstone of GOAD-Blue training, providing realistic adversarial scenarios where red teams simulate attacks while blue teams practice detection and response using the comprehensive GOAD-Blue security stack.

## 🎯 Overview

GOAD-Blue Red vs Blue exercises create immersive cybersecurity training environments where participants experience real-world attack and defense scenarios in a controlled, educational setting.

```mermaid
graph TB
    subgraph "🔴 Red Team Activities"
        RECON[🔍 Reconnaissance<br/>Network Discovery<br/>Service Enumeration]
        INITIAL[🚪 Initial Access<br/>Exploitation<br/>Credential Harvesting]
        LATERAL[↔️ Lateral Movement<br/>Privilege Escalation<br/>Persistence]
        OBJECTIVES[🎯 Objectives<br/>Data Exfiltration<br/>Domain Compromise]
    end
    
    subgraph "🔵 Blue Team Activities"
        MONITORING[👁️ Monitoring<br/>SIEM Analysis<br/>Alert Triage]
        DETECTION[🚨 Detection<br/>Threat Hunting<br/>IOC Analysis]
        INVESTIGATION[🔍 Investigation<br/>Forensic Analysis<br/>Timeline Reconstruction]
        RESPONSE[🛡️ Response<br/>Containment<br/>Remediation]
    end
    
    subgraph "🎮 GOAD Environment"
        GOAD_INFRA[🏰 GOAD Infrastructure<br/>Domain Controllers<br/>Member Servers<br/>Workstations]
        GOAD_USERS[👥 Simulated Users<br/>Service Accounts<br/>Admin Accounts<br/>Regular Users]
        GOAD_DATA[📊 Realistic Data<br/>File Shares<br/>Databases<br/>Applications]
    end
    
    subgraph "🛠️ GOAD-Blue Tools"
        SIEM[📊 SIEM Platforms<br/>Splunk/Elastic<br/>Real-time Analysis]
        EDR[🛡️ EDR Solutions<br/>Velociraptor<br/>Endpoint Monitoring]
        NETWORK[🌐 Network Security<br/>Security Onion<br/>Traffic Analysis]
        INTEL[🧠 Threat Intelligence<br/>MISP<br/>IOC Management]
        ANALYSIS[🔬 Malware Analysis<br/>FLARE-VM<br/>Reverse Engineering]
    end
    
    RECON --> INITIAL
    INITIAL --> LATERAL
    LATERAL --> OBJECTIVES
    
    MONITORING --> DETECTION
    DETECTION --> INVESTIGATION
    INVESTIGATION --> RESPONSE
    
    GOAD_INFRA --> RECON
    GOAD_USERS --> INITIAL
    GOAD_DATA --> OBJECTIVES
    
    OBJECTIVES --> MONITORING
    LATERAL --> DETECTION
    INITIAL --> INVESTIGATION
    RECON --> RESPONSE
    
    SIEM --> MONITORING
    EDR --> DETECTION
    NETWORK --> INVESTIGATION
    INTEL --> RESPONSE
    ANALYSIS --> INVESTIGATION
    
    classDef red fill:#ff5252,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef blue fill:#2196f3,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef goad fill:#4caf50,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef tools fill:#ff9800,stroke:#ffffff,stroke-width:2px,color:#fff
    
    class RECON,INITIAL,LATERAL,OBJECTIVES red
    class MONITORING,DETECTION,INVESTIGATION,RESPONSE blue
    class GOAD_INFRA,GOAD_USERS,GOAD_DATA goad
    class SIEM,EDR,NETWORK,INTEL,ANALYSIS tools
```

## 🏗️ Exercise Framework

### **Exercise Structure**

```yaml
# Red vs Blue Exercise Template
exercise_framework:
  duration: "8 hours"
  participants: "12-20 (6-10 per team)"
  
  phases:
    preparation:
      duration: "1 hour"
      activities:
        - Environment setup and verification
        - Team briefings and role assignments
        - Tool access and credential distribution
        - Rules of engagement review
    
    execution:
      duration: "6 hours"
      activities:
        - Red team attack execution
        - Blue team monitoring and response
        - Real-time scenario progression
        - Instructor observation and guidance
    
    debrief:
      duration: "1 hour"
      activities:
        - Attack timeline reconstruction
        - Detection effectiveness analysis
        - Lessons learned discussion
        - Improvement recommendations

  scoring:
    red_team:
      - Initial access achieved (20 points)
      - Lateral movement success (25 points)
      - Privilege escalation (25 points)
      - Objective completion (30 points)
    
    blue_team:
      - Alert detection speed (25 points)
      - Investigation accuracy (25 points)
      - Containment effectiveness (25 points)
      - Recovery completeness (25 points)
```

### **Role Definitions**

#### **Red Team Roles**
- **Team Lead**: Coordinates attack strategy and timeline
- **Initial Access Specialist**: Focuses on gaining initial foothold
- **Lateral Movement Expert**: Handles privilege escalation and movement
- **Persistence Specialist**: Maintains access and establishes backdoors
- **Data Exfiltration Expert**: Focuses on objective completion

#### **Blue Team Roles**
- **SOC Manager**: Coordinates defense strategy and resource allocation
- **Tier 1 Analyst**: Monitors alerts and performs initial triage
- **Tier 2 Analyst**: Conducts detailed investigations and analysis
- **Threat Hunter**: Proactively searches for threats and IOCs
- **Incident Responder**: Leads containment and remediation efforts

## 🎯 Exercise Scenarios

### **Scenario 1: APT-Style Attack Campaign**

```yaml
scenario_apt_campaign:
  name: "Operation Shadow Kingdom"
  difficulty: "Advanced"
  duration: "8 hours"
  
  red_team_objectives:
    primary:
      - Compromise domain administrator account
      - Access sensitive financial data
      - Establish persistent backdoor
    
    secondary:
      - Avoid detection for >4 hours
      - Compromise >5 workstations
      - Exfiltrate >100MB of data
  
  blue_team_objectives:
    primary:
      - Detect initial compromise within 2 hours
      - Identify lateral movement within 4 hours
      - Contain attack before domain compromise
    
    secondary:
      - Identify all compromised systems
      - Recover all exfiltrated data
      - Implement preventive measures
  
  attack_chain:
    initial_access:
      method: "Spear phishing email"
      target: "Finance department users"
      payload: "Macro-enabled document"
      
    persistence:
      method: "Scheduled task creation"
      location: "Compromised workstation"
      
    lateral_movement:
      method: "Credential dumping + Pass-the-Hash"
      tools: ["Mimikatz", "PsExec"]
      
    privilege_escalation:
      method: "Kerberoasting attack"
      target: "Service accounts"
      
    data_exfiltration:
      method: "DNS tunneling"
      target: "Financial databases"
  
  detection_opportunities:
    - Suspicious email attachment execution
    - Unusual process creation (Office spawning cmd.exe)
    - Credential dumping tool execution
    - Lateral movement via SMB
    - Kerberos ticket requests for service accounts
    - Large DNS queries (tunneling)
    - Unusual data access patterns
```

### **Scenario 2: Ransomware Attack Simulation**

```yaml
scenario_ransomware:
  name: "CryptoLocker Simulation"
  difficulty: "Intermediate"
  duration: "6 hours"
  
  red_team_objectives:
    primary:
      - Deploy ransomware to >10 systems
      - Encrypt critical file shares
      - Disable backup systems
    
    secondary:
      - Avoid detection during deployment
      - Establish command and control
      - Simulate ransom demands
  
  blue_team_objectives:
    primary:
      - Detect ransomware deployment
      - Isolate infected systems
      - Prevent spread to critical systems
    
    secondary:
      - Identify patient zero
      - Recover encrypted files
      - Implement protective measures
  
  attack_progression:
    hour_1:
      - Initial compromise via RDP brute force
      - Reconnaissance and network mapping
      
    hour_2:
      - Credential harvesting
      - Lateral movement to file servers
      
    hour_3:
      - Backup system compromise
      - Ransomware deployment preparation
      
    hour_4:
      - Mass ransomware deployment
      - File encryption begins
      
    hour_5:
      - Ransom note deployment
      - C2 communication establishment
      
    hour_6:
      - Cleanup and persistence
```

## 🛠️ Technical Implementation

### **Red Team Toolkit**

```bash
#!/bin/bash
# Red Team automation script for GOAD-Blue exercises

# Initial reconnaissance
function perform_recon() {
    echo "[+] Starting reconnaissance phase..."
    
    # Network discovery
    nmap -sn ************/24 > recon/network_discovery.txt
    
    # Service enumeration
    nmap -sV -sC -p- *************-15 > recon/service_enum.txt
    
    # SMB enumeration
    enum4linux -a ************* > recon/smb_enum.txt
    
    # DNS enumeration
    dnsrecon -d sevenkingdoms.local -t std > recon/dns_enum.txt
    
    echo "[+] Reconnaissance completed. Results in recon/ directory."
}

# Initial access via phishing simulation
function simulate_phishing() {
    echo "[+] Simulating phishing attack..."
    
    # Create malicious document (simulation)
    echo "Creating simulated malicious document..."
    
    # Log phishing attempt for blue team detection
    logger -p local0.info "PHISHING_SIM: Malicious document opened by user"
    
    # Simulate payload execution
    powershell.exe -ExecutionPolicy Bypass -WindowStyle Hidden -Command "
        # Simulate malware beacon
        while (\$true) {
            try {
                \$response = Invoke-WebRequest -Uri 'http://*************0:8080/beacon' -Method POST
                Start-Sleep 300
            } catch {
                Start-Sleep 60
            }
        }
    " &
    
    echo "[+] Phishing simulation deployed."
}

# Credential harvesting
function harvest_credentials() {
    echo "[+] Starting credential harvesting..."
    
    # Simulate Mimikatz execution (logged for detection)
    logger -p local0.warning "CREDENTIAL_HARVEST: Mimikatz-like activity detected"
    
    # Extract simulated credentials
    echo "DOMAIN\\user1:password123" > /tmp/harvested_creds.txt
    echo "DOMAIN\\service_account:ServicePass456" >> /tmp/harvested_creds.txt
    
    echo "[+] Credentials harvested (simulated)."
}

# Lateral movement
function lateral_movement() {
    echo "[+] Performing lateral movement..."
    
    # Simulate PsExec usage
    logger -p local0.warning "LATERAL_MOVEMENT: PsExec-like activity to multiple hosts"
    
    # Simulate SMB connections
    for host in ************* ************* *************; do
        echo "Connecting to $host via SMB..."
        # smbclient simulation would go here
        logger -p local0.info "SMB_CONNECTION: Connection to $host established"
    done
    
    echo "[+] Lateral movement completed."
}

# Main execution
case "$1" in
    recon)
        perform_recon
        ;;
    phishing)
        simulate_phishing
        ;;
    harvest)
        harvest_credentials
        ;;
    lateral)
        lateral_movement
        ;;
    full)
        perform_recon
        simulate_phishing
        sleep 300
        harvest_credentials
        sleep 180
        lateral_movement
        ;;
    *)
        echo "Usage: $0 {recon|phishing|harvest|lateral|full}"
        exit 1
        ;;
esac
```

### **Blue Team Response Playbook**

```yaml
# Blue Team Incident Response Playbook
incident_response_playbook:
  phase_1_detection:
    duration: "30 minutes"
    activities:
      - Monitor SIEM dashboards for alerts
      - Triage high-priority alerts
      - Validate true positives
      - Escalate confirmed incidents
    
    tools:
      - Splunk/Elastic SIEM
      - Security Onion alerts
      - Velociraptor hunts
    
    success_criteria:
      - Alert detected within 30 minutes
      - Initial triage completed
      - Incident declared if confirmed
  
  phase_2_investigation:
    duration: "60 minutes"
    activities:
      - Collect additional evidence
      - Analyze attack timeline
      - Identify affected systems
      - Determine attack scope
    
    tools:
      - Velociraptor forensics
      - Sysmon logs analysis
      - Network traffic analysis
      - Memory analysis (if needed)
    
    success_criteria:
      - Attack vector identified
      - Timeline established
      - Scope determined
      - IOCs extracted
  
  phase_3_containment:
    duration: "45 minutes"
    activities:
      - Isolate affected systems
      - Block malicious IPs/domains
      - Disable compromised accounts
      - Prevent lateral movement
    
    tools:
      - Firewall rule updates
      - AD account management
      - Network segmentation
      - Endpoint isolation
    
    success_criteria:
      - Threat contained
      - No further spread
      - Systems isolated
      - Accounts secured
  
  phase_4_eradication:
    duration: "90 minutes"
    activities:
      - Remove malware/backdoors
      - Patch vulnerabilities
      - Update security controls
      - Strengthen defenses
    
    tools:
      - Antimalware tools
      - Patch management
      - Configuration updates
      - Security hardening
    
    success_criteria:
      - Threats removed
      - Vulnerabilities patched
      - Controls updated
      - Systems hardened
  
  phase_5_recovery:
    duration: "60 minutes"
    activities:
      - Restore systems from clean backups
      - Verify system integrity
      - Resume normal operations
      - Monitor for reinfection
    
    tools:
      - Backup systems
      - Integrity checking
      - Monitoring tools
      - Performance metrics
    
    success_criteria:
      - Systems restored
      - Operations resumed
      - Monitoring active
      - No reinfection
```

## 📊 Assessment and Scoring

### **Performance Metrics**

```python
# Exercise scoring and assessment system
class ExerciseAssessment:
    def __init__(self):
        self.red_team_score = 0
        self.blue_team_score = 0
        self.timeline = []
        
    def score_red_team_activity(self, activity, success, timestamp):
        """Score red team activities"""
        scoring = {
            'initial_access': 20,
            'persistence': 15,
            'lateral_movement': 25,
            'privilege_escalation': 25,
            'data_exfiltration': 30,
            'stealth_bonus': 10  # Avoiding detection
        }
        
        if success:
            points = scoring.get(activity, 0)
            self.red_team_score += points
            
            self.timeline.append({
                'timestamp': timestamp,
                'team': 'red',
                'activity': activity,
                'points': points,
                'success': True
            })
    
    def score_blue_team_activity(self, activity, effectiveness, timestamp):
        """Score blue team activities"""
        scoring = {
            'alert_detection': 25,
            'investigation': 25,
            'containment': 25,
            'recovery': 25,
            'speed_bonus': 15  # Fast response
        }
        
        base_points = scoring.get(activity, 0)
        points = int(base_points * effectiveness)  # 0.0 to 1.0
        self.blue_team_score += points
        
        self.timeline.append({
            'timestamp': timestamp,
            'team': 'blue',
            'activity': activity,
            'points': points,
            'effectiveness': effectiveness
        })
    
    def calculate_final_scores(self):
        """Calculate final exercise scores"""
        # Time-based bonuses/penalties
        detection_time = self.get_detection_time()
        if detection_time:
            if detection_time < 1800:  # 30 minutes
                self.blue_team_score += 20
            elif detection_time > 7200:  # 2 hours
                self.red_team_score += 15
        
        return {
            'red_team': self.red_team_score,
            'blue_team': self.blue_team_score,
            'winner': 'red' if self.red_team_score > self.blue_team_score else 'blue'
        }
    
    def generate_report(self):
        """Generate exercise assessment report"""
        scores = self.calculate_final_scores()
        
        report = f"""
        GOAD-Blue Red vs Blue Exercise Assessment Report
        ===============================================
        
        Final Scores:
        - Red Team: {scores['red_team']} points
        - Blue Team: {scores['blue_team']} points
        - Winner: {scores['winner'].upper()} TEAM
        
        Timeline Analysis:
        """
        
        for event in sorted(self.timeline, key=lambda x: x['timestamp']):
            report += f"\n{event['timestamp']}: {event['team'].upper()} - {event['activity']} ({event['points']} points)"
        
        return report
```

## 🎓 Learning Objectives

### **Red Team Learning Outcomes**
- **Attack Planning**: Develop systematic approach to penetration testing
- **Tool Proficiency**: Master offensive security tools and techniques
- **Evasion Techniques**: Learn to avoid detection by security controls
- **Objective Achievement**: Focus on business impact and risk assessment

### **Blue Team Learning Outcomes**
- **Threat Detection**: Improve ability to identify malicious activities
- **Investigation Skills**: Develop forensic analysis capabilities
- **Response Coordination**: Practice incident response procedures
- **Tool Integration**: Learn to leverage multiple security tools effectively

### **Shared Learning Outcomes**
- **Threat Landscape**: Understand current attack trends and techniques
- **Risk Assessment**: Evaluate security posture and vulnerabilities
- **Communication**: Improve technical communication and reporting
- **Continuous Improvement**: Develop iterative security enhancement mindset

---

!!! tip "Exercise Best Practices"
    - Establish clear rules of engagement before starting
    - Maintain realistic but safe attack scenarios
    - Provide real-time coaching and guidance
    - Focus on learning over winning
    - Conduct thorough debriefs after each exercise

!!! warning "Safety Considerations"
    - Ensure all attacks remain within designated test environment
    - Have rollback procedures ready for system recovery
    - Monitor exercise progression to prevent real damage
    - Maintain clear communication channels between teams

!!! info "Customization Options"
    - Adjust difficulty based on participant skill levels
    - Modify scenarios to match organizational threats
    - Include compliance and regulatory requirements
    - Add industry-specific attack vectors and defenses
