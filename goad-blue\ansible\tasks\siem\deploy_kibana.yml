---
# Deploy <PERSON><PERSON> for GOAD-Blue

- name: Create kibana user
  user:
    name: kibana
    system: yes
    shell: /bin/false
    home: /var/lib/kibana
    create_home: yes

- name: Download Kibana
  get_url:
    url: "{{ kibana.download_url }}"
    dest: "/tmp/kibana-{{ kibana.version }}.tar.gz"
    mode: '0644'

- name: Extract Kibana
  unarchive:
    src: "/tmp/kibana-{{ kibana.version }}.tar.gz"
    dest: /opt
    remote_src: yes
    owner: kibana
    group: kibana
    creates: "/opt/kibana-{{ kibana.version }}"

- name: Create Kibana symlink
  file:
    src: "/opt/kibana-{{ kibana.version }}"
    dest: "{{ kibana.install_path }}"
    state: link
    owner: kibana
    group: kibana

- name: Create Kibana directories
  file:
    path: "{{ item }}"
    state: directory
    owner: kibana
    group: kibana
    mode: '0755'
  loop:
    - /etc/kibana
    - /var/log/kibana
    - /var/lib/kibana

- name: Configure Kibana
  template:
    src: kibana.yml.j2
    dest: /etc/kibana/kibana.yml
    owner: kibana
    group: kibana
    mode: '0644'
    backup: yes
  notify: restart kibana

- name: Create Kibana systemd service
  template:
    src: kibana.service.j2
    dest: /etc/systemd/system/kibana.service
    mode: '0644'
  notify:
    - reload systemd
    - restart kibana

- name: Start and enable Kibana
  systemd:
    name: kibana
    state: started
    enabled: yes
    daemon_reload: yes

- name: Wait for Kibana to be ready
  uri:
    url: "http://{{ kibana.server.host }}:{{ kibana.server.port }}/api/status"
    method: GET
    status_code: 200
  register: kibana_health
  until: kibana_health.status == 200
  retries: 30
  delay: 10

- name: Create GOAD-Blue index patterns
  uri:
    url: "http://{{ kibana.server.host }}:{{ kibana.server.port }}/api/saved_objects/index-pattern/{{ item.id }}"
    method: POST
    body_format: json
    headers:
      kbn-xsrf: true
      Content-Type: application/json
    body:
      attributes:
        title: "{{ item.pattern }}"
        timeFieldName: "@timestamp"
    status_code: [200, 409]  # 409 if already exists
  loop:
    - id: "goad-blue-windows"
      pattern: "goad-blue-windows-*"
    - id: "goad-blue-linux"
      pattern: "goad-blue-linux-*"
    - id: "goad-blue-network"
      pattern: "goad-blue-network-*"
    - id: "goad-blue-security"
      pattern: "goad-blue-security-*"

- name: Import GOAD-Blue Kibana dashboards
  uri:
    url: "http://{{ kibana.server.host }}:{{ kibana.server.port }}/api/saved_objects/_import"
    method: POST
    headers:
      kbn-xsrf: true
    body: "{{ lookup('file', 'files/kibana/goad-blue-dashboards.ndjson') }}"
    status_code: [200, 409]
  ignore_errors: yes

- name: Create GOAD-Blue visualizations
  uri:
    url: "http://{{ kibana.server.host }}:{{ kibana.server.port }}/api/saved_objects/visualization/{{ item.id }}"
    method: POST
    body_format: json
    headers:
      kbn-xsrf: true
      Content-Type: application/json
    body:
      attributes:
        title: "{{ item.title }}"
        visState: "{{ item.vis_state | to_json }}"
        uiStateJSON: "{}"
        kibanaSavedObjectMeta:
          searchSourceJSON: "{{ item.search_source | to_json }}"
    status_code: [200, 409]
  loop:
    - id: "goad-blue-event-timeline"
      title: "GOAD-Blue Event Timeline"
      vis_state:
        title: "GOAD-Blue Event Timeline"
        type: "histogram"
        params:
          grid:
            categoryLines: false
            style:
              color: "#eee"
          categoryAxes:
            - id: "CategoryAxis-1"
              type: "category"
              position: "bottom"
              show: true
              style: {}
              scale:
                type: "linear"
              labels:
                show: true
                truncate: 100
              title: {}
          valueAxes:
            - id: "ValueAxis-1"
              name: "LeftAxis-1"
              type: "value"
              position: "left"
              show: true
              style: {}
              scale:
                type: "linear"
                mode: "normal"
              labels:
                show: true
                rotate: 0
                filter: false
                truncate: 100
              title:
                text: "Count"
          seriesParams:
            - show: true
              type: "histogram"
              mode: "stacked"
              data:
                label: "Count"
                id: "1"
              valueAxis: "ValueAxis-1"
              drawLinesBetweenPoints: true
              showCircles: true
          addTooltip: true
          addLegend: true
          legendPosition: "right"
          times: []
          addTimeMarker: false
        aggs:
          - id: "1"
            enabled: true
            type: "count"
            schema: "metric"
            params: {}
          - id: "2"
            enabled: true
            type: "date_histogram"
            schema: "segment"
            params:
              field: "@timestamp"
              interval: "auto"
              customInterval: "2h"
              min_doc_count: 1
              extended_bounds: {}
      search_source:
        index: "goad-blue-*"
        query:
          match_all: {}
        filter: []

- name: Set default index pattern
  uri:
    url: "http://{{ kibana.server.host }}:{{ kibana.server.port }}/api/kibana/settings/defaultIndex"
    method: POST
    body_format: json
    headers:
      kbn-xsrf: true
      Content-Type: application/json
    body:
      value: "goad-blue-windows"
    status_code: [200, 400]

- name: Configure Kibana security settings
  uri:
    url: "http://{{ kibana.server.host }}:{{ kibana.server.port }}/api/kibana/settings"
    method: POST
    body_format: json
    headers:
      kbn-xsrf: true
      Content-Type: application/json
    body:
      changes:
        "xpack.security.enabled": false
        "xpack.monitoring.enabled": true
        "telemetry.enabled": false
        "telemetry.optIn": false
    status_code: [200, 400]

- name: Create Kibana log rotation configuration
  template:
    src: kibana-logrotate.j2
    dest: /etc/logrotate.d/kibana
    mode: '0644'

- name: Display Kibana access information
  debug:
    msg: |
      Kibana deployment completed!
      Kibana URL: http://{{ kibana.server.host }}:{{ kibana.server.port }}
      Index Patterns Created:
      - goad-blue-windows-*
      - goad-blue-linux-*
      - goad-blue-network-*
      - goad-blue-security-*
