- name: "disable the registration of the {{nat_adapter}} interface (NAT address) in DNS"
  ansible.windows.win_shell: Get-NetAdapter {{nat_adapter}} | Set-DNSClient -RegisterThisConnectionsAddress $False
  when: two_adapters

- name: "Set configure dns to {{dns_domain}}"
  win_dns_client:
    adapter_names: "{{domain_adapter}}"
    ipv4_addresses:
      - "{{hostvars[dns_domain].ansible_host}}"
    log_path: C:\dns_log.txt

- name: Install windows features - AD Domain Services
  win_feature:
    name: AD-Domain-Services
    state: present
    include_management_tools: yes
  register: features

- name: Install windows features - RSAT-ADDS
  win_feature:
    name: RSAT-ADDS
    state: present
    include_management_tools: yes
  register: features

# DNSCHANGE
# - name: "disable interface {{nat_adapter}} before join domain"
#   win_shell: netsh interface set interface "{{nat_adapter}}" disable

- name: add child domain to parent domain
  ansible.windows.win_powershell:
    script: |
      [CmdletBinding()]
      param (
          [String]
          $Password,

          [String]
          $DomainAdmin,

          [String]
          $ParentDomainName,

          [String]
          $NewDomainNetbiosName,

          [String]
          $ReplicationSourceDC,

          [String]
          $NewDomainName,

          [String]
          $DomSafePassword
      )
      try {
          $child_domain = Get-ADDomain -Identity $NewDomainName
          $domainExist=$true
      } catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
          $domainExist=$false
      }
      if (-not $domainExist) {
        $Ansible.Changed = $true
        $pass = ConvertTo-SecureString $Password -AsPlainText -Force
        $Cred = New-Object System.Management.Automation.PSCredential ($DomainAdmin, $pass)
        $safePassword = ConvertTo-SecureString $DomSafePassword -AsPlainText -Force
        Install-ADDSDomain -Credential $Cred -SkipPreChecks -NewDomainName $NewDomainName -NewDomainNetbiosName $NewDomainNetbiosName -ParentDomainName $ParentDomainName -ReplicationSourceDC $ReplicationSourceDC -DatabasePath "C:\Windows\NTDS" -SYSVOLPath "C:\Windows\SYSVOL" -LogPath "C:\Windows\Logs" -SafeModeAdministratorPassword $safePassword -Force -NoRebootOnCompletion
      } else {
        $Ansible.Changed = $false
      }
    parameters:
      Password: "{{parent_domain_password}}"
      DomSafePassword: "{{domain_password}}"
      DomainAdmin: "{{parent_domain_user}}"
      ParentDomainName: "{{parent_domain}}"
      ReplicationSourceDC: "{{source_dc}}"
      NewDomainNetbiosName: "{{netbios_name}}"
      NewDomainName: "{{domain.split('.')[0]}}"
  register: child_result

- name: Reboot
  win_reboot:
    reboot_timeout: 900
    post_reboot_delay: 100
  when: child_result.changed

# - name: "enable interface {{nat_adapter}} after domain joined"
#   win_shell: netsh interface set interface "{{nat_adapter}}" enable
#   register: enable_interface
#   until: "enable_interface is not failed"
#   retries: 3
#   delay: 120

- name: enable the {{domain_adapter}} interface (local) for DNS client requests
  ansible.windows.win_shell: dnscmd . /resetlistenaddresses {{ hostvars[dict_key].ansible_host }}
  when: two_adapters

- name: Check for xDnsServer Powershell module
  win_psmodule:
    name: xDnsServer
    state: present

# add external dns forwarder
- name: Configure DNS Forwarders
  win_dsc:
    resource_name: xDnsServerForwarder
    IsSingleInstance: "yes"
    UseRootHint: false
    IPAddresses:
      - "{{dns_server_forwarder}}"

- name: "Install XactiveDirectory"
  win_psmodule:
    name: ActiveDirectoryDSC
    state: present

# issue 156
- name: enable the Active directory web services if not enabled
  ansible.windows.win_shell: Set-Service -Name adws -StartupType Automatic
