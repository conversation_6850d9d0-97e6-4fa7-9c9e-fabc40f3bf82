<?php
/**
 * MISP Database Configuration
 * Generated by GOAD-Blue Ansible
 */

class DATABASE_CONFIG {
    public $default = array(
        'datasource' => 'Database/Mysql',
        'persistent' => false,
        'host' => 'localhost',
        'login' => '{{ misp.database.user }}',
        'password' => '{{ misp.database.password }}',
        'database' => '{{ misp.database.name }}',
        'prefix' => '',
        'encoding' => 'utf8mb4',
        'port' => 3306,
    );
}
