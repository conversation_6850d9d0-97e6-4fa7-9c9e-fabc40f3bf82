- name: Refresh
  ansible.windows.win_command: gpupdate /force

- name: Install ADCSTemplate Module
  win_copy:
    src: files/ADCSTemplate
    dest: "C:\\Program Files\\WindowsPowerShell\\Modules"

- name: create a directory for templates
  win_file: 
    path: c:\setup
    state: directory

- name: Copy templates json
  win_copy:
    src: "files/{{item}}.json"
    dest: "C:\\setup\\{{item}}.json"
  with_items:
    - ESC1
    - ESC2
    - ESC3
    - ESC3-CRA
    - ESC4
    - ESC9
    - ESC13

- name: Install templates
  win_shell: |
    if (-not(Get-ADCSTemplate -DisplayName "{{item}}")) { New-ADCSTemplate -DisplayName "{{item}}" -JSON (Get-Content c:\setup\{{item}}.json -Raw) -Identity "{{domain_name}}\Domain Users" -Publish }
  vars:
    ansible_become: yes
    ansible_become_method: runas
    domain_name: "{{domain}}"
    ansible_become_user: "{{domain_username}}"
    ansible_become_password: "{{domain_password}}"
  with_items:
    - ESC1
    - ESC2
    - ESC3
    - ESC3-CRA
    - ESC4
    - ESC9
    - ESC13
