- name: "Update cache"
  apt:
    update_cache: true
    cache_valid_time: 86400

- name: Add required dependencies.
  apt:
    name:
      - apt-transport-https
      - gnupg2
    state: present
    update_cache: yes

- name: Add Elasticsearch apt key.
  apt_key:
    url: https://artifacts.elastic.co/GPG-KEY-elasticsearch
    state: present

- name: Add Elasticsearch repository.
  apt_repository:
    repo: 'deb https://artifacts.elastic.co/packages/{{ elasticsearch_version }}/apt stable main'
    state: present
    update_cache: true

- name: Install logstash
  apt:
    name: logstash
    state: present

- name: Install java
  apt:
    name: openjdk-11-jre
    state: present

- name: Install elasticsearch
  apt:
    name: elasticsearch
    state: present

- name: Install kibana
  apt:
    name: kibana
    state: present

- name: copy kibana config
  copy:
    src: kibana.yml
    dest: /etc/kibana/kibana.yml
    owner: "root"
    group: "kibana"
    mode: 0660

- name: elasticsearch change start timeout to 3min
  lineinfile:
    destfile: /usr/lib/systemd/system/elasticsearch.service
    regexp: 'TimeoutStartSec='
    line: 'TimeoutStartSec=180'

- name: copy elasticsearch config
  copy:
    src: elasticsearch.yml
    dest: /etc/elasticsearch/elasticsearch.yml
    owner: "root"
    group: "elasticsearch"
    mode: 0660

- name: enable logstash
  service:
    name: logstash
    enabled: yes

- name: enable elasticsearch
  service:
    name: elasticsearch
    enabled: yes

- name: enable kibana
  service:
    name: kibana
    enabled: yes

- name: start logstash
  service:
    name: logstash
    state: started

- name: start elasticsearch
  service:
    name: elasticsearch
    state: started

- name: start kibana
  service:
    name: kibana
    state: started
