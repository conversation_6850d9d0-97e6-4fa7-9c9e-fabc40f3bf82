# GOAD-Blue Use Cases & Scenarios

## Overview

GOAD-Blue provides a comprehensive platform for cybersecurity training, testing, and research. This document outlines practical use cases and step-by-step scenarios for getting the most out of your GOAD-Blue environment.

## 🎯 Primary Use Cases

### 1. Red Team vs Blue Team Exercises

**Objective:** Conduct realistic attack-defense scenarios

**Participants:**
- Red Team: Uses GOAD environment to execute attacks
- Blue Team: Uses GOAD-Blue tools to detect and respond

**Benefits:**
- Realistic attack simulation
- Hands-on detection experience
- Incident response practice
- Tool evaluation and tuning

### 2. Security Operations Center (SOC) Training

**Objective:** Train SOC analysts on real-world scenarios

**Components Used:**
- SIEM (Splunk/Elastic) for log analysis
- Security Onion for network monitoring
- Velociraptor for endpoint investigation
- MISP for threat intelligence

**Training Areas:**
- Log analysis and correlation
- Alert triage and investigation
- Threat hunting techniques
- Incident response workflows

### 3. Threat Hunting Workshops

**Objective:** Develop advanced threat hunting skills

**Focus Areas:**
- Behavioral analysis
- Anomaly detection
- IOC development
- Hunt hypothesis testing

### 4. Security Tool Evaluation

**Objective:** Test and compare security tools

**Evaluation Criteria:**
- Detection accuracy
- False positive rates
- Performance impact
- Integration capabilities

### 5. Compliance and Audit Preparation

**Objective:** Prepare for security audits and compliance requirements

**Use Cases:**
- Log retention and analysis
- Incident response procedures
- Security control validation
- Documentation and reporting

## 📋 Detailed Scenarios

### Scenario 1: Kerberoasting Attack Detection

**Duration:** 2-3 hours  
**Skill Level:** Intermediate  
**Components:** GOAD, Splunk, Security Onion, Velociraptor

#### Setup Phase (30 minutes)

1. **Verify Environment:**
   ```bash
   python3 goad-blue.py -t status
   ```

2. **Access SIEM Dashboard:**
   - Navigate to Splunk: https://19************:8000
   - Login with admin credentials
   - Verify data ingestion from GOAD VMs

3. **Baseline Network Activity:**
   - Check Security Onion dashboard
   - Review normal Kerberos traffic patterns
   - Document baseline metrics

#### Attack Phase (45 minutes)

1. **Execute Kerberoasting Attack:**
   ```powershell
   # On GOAD environment
   Import-Module .\PowerView.ps1
   Get-DomainUser -SPN | Get-DomainSPNTicket -Format Hashcat
   ```

2. **Escalate Attack:**
   ```powershell
   # Request multiple service tickets
   Get-DomainUser -SPN | ForEach-Object { Get-DomainSPNTicket $_.samaccountname }
   ```

#### Detection Phase (60 minutes)

1. **SIEM Analysis:**
   ```spl
   # Splunk search for Kerberoasting
   index=goad_blue_windows EventCode=4769 
   | where Ticket_Encryption_Type="0x17" 
   | stats count by Account_Name, Service_Name 
   | where count > 10
   ```

2. **Network Analysis:**
   - Review Suricata alerts for Kerberos anomalies
   - Analyze Zeek logs for unusual TGS requests
   - Correlate network and endpoint data

3. **Endpoint Investigation:**
   - Use Velociraptor to hunt for PowerShell activity
   - Collect process creation events
   - Analyze command line arguments

#### Response Phase (45 minutes)

1. **Incident Documentation:**
   - Create incident ticket in MISP
   - Document attack timeline
   - Identify affected accounts

2. **Containment Actions:**
   - Reset compromised service account passwords
   - Implement additional monitoring
   - Update detection rules

3. **Lessons Learned:**
   - Review detection gaps
   - Improve monitoring coverage
   - Update playbooks

### Scenario 2: Lateral Movement Detection

**Duration:** 3-4 hours  
**Skill Level:** Advanced  
**Components:** GOAD, Elastic Stack, Velociraptor, MISP

#### Attack Simulation

1. **Initial Compromise:**
   - Simulate phishing attack
   - Establish initial foothold
   - Enumerate domain environment

2. **Credential Harvesting:**
   - Extract credentials from memory
   - Crack password hashes
   - Identify high-value targets

3. **Lateral Movement:**
   - Use stolen credentials
   - Move between systems
   - Escalate privileges

#### Detection and Analysis

1. **Behavioral Analysis:**
   - Identify unusual logon patterns
   - Detect abnormal process execution
   - Correlate user and system behavior

2. **Network Monitoring:**
   - Track lateral movement patterns
   - Identify command and control traffic
   - Monitor file transfers

3. **Threat Intelligence:**
   - Correlate with known TTPs
   - Update IOC database
   - Share intelligence with team

### Scenario 3: Malware Analysis Workshop

**Duration:** 4-6 hours  
**Skill Level:** Advanced  
**Components:** FLARE-VM, MISP, Velociraptor

#### Malware Collection

1. **Sample Acquisition:**
   - Download samples from threat feeds
   - Use MISP to track sample metadata
   - Ensure proper isolation

2. **Initial Triage:**
   - Static analysis with PEStudio
   - String analysis and entropy calculation
   - Identify packing and obfuscation

#### Dynamic Analysis

1. **Sandbox Execution:**
   - Execute malware in FLARE-VM
   - Monitor system changes
   - Capture network traffic

2. **Behavioral Analysis:**
   - Document file system changes
   - Track registry modifications
   - Analyze network communications

#### Intelligence Development

1. **IOC Extraction:**
   - Identify file hashes
   - Extract network indicators
   - Document behavioral patterns

2. **MISP Integration:**
   - Create threat event in MISP
   - Add extracted IOCs
   - Share with security team

3. **Detection Rule Creation:**
   - Develop YARA rules
   - Create Suricata signatures
   - Implement SIEM detections

## 🎓 Training Curricula

### SOC Analyst Level 1 (40 hours)

**Week 1: Fundamentals**
- SIEM basics and log analysis
- Windows Event Log interpretation
- Network traffic analysis
- Basic incident response

**Week 2: Hands-on Practice**
- Alert triage and investigation
- False positive identification
- Basic threat hunting
- Documentation and reporting

### SOC Analyst Level 2 (60 hours)

**Week 1-2: Advanced Analysis**
- Complex log correlation
- Advanced threat hunting
- Malware analysis basics
- Threat intelligence utilization

**Week 3: Specialized Skills**
- Memory forensics
- Network forensics
- Advanced persistent threat detection
- Custom detection development

### Threat Hunter Certification (80 hours)

**Module 1: Foundations (20 hours)**
- Threat hunting methodology
- Hypothesis development
- Data analysis techniques
- Tool proficiency

**Module 2: Advanced Techniques (30 hours)**
- Behavioral analysis
- Anomaly detection
- Machine learning applications
- Custom tool development

**Module 3: Practical Application (30 hours)**
- Real-world hunt scenarios
- Capstone project
- Peer review and feedback
- Certification assessment

## 🔧 Customization Examples

### Custom Detection Rules

**Splunk Saved Search:**
```spl
# Detect potential Golden Ticket usage
index=goad_blue_windows EventCode=4624 Logon_Type=3 
| eval age=now()-strptime(Account_Domain, "%Y-%m-%d %H:%M:%S")
| where age > 86400*30  # Tickets older than 30 days
| stats count by Account_Name, Computer_Name
```

**Suricata Rule:**
```
# Detect Kerberoasting activity
alert kerberos any any -> any any (
    msg:"GOAD-Blue Potential Kerberoasting Activity"; 
    content:"|a0 03 02 01 17|"; 
    threshold:type both, track by_src, count 10, seconds 300;
    sid:1000001;
)
```

### Custom Dashboards

**Kibana Dashboard Components:**
- Real-time attack timeline
- Geographic threat visualization
- User behavior analytics
- System health monitoring

**Splunk Dashboard Panels:**
- Top attacked services
- Credential usage patterns
- Network communication flows
- Threat intelligence feeds

### Integration Scripts

**MISP to SIEM Integration:**
```python
# Automated IOC synchronization
import requests
import json

def sync_misp_to_splunk():
    # Fetch IOCs from MISP
    misp_iocs = get_misp_indicators()
    
    # Format for Splunk
    splunk_data = format_for_splunk(misp_iocs)
    
    # Push to Splunk
    upload_to_splunk(splunk_data)
```

## 📊 Assessment and Metrics

### Performance Indicators

**Detection Metrics:**
- Mean Time to Detection (MTTD)
- Mean Time to Response (MTTR)
- False Positive Rate
- Coverage Percentage

**Training Metrics:**
- Skill Assessment Scores
- Scenario Completion Time
- Knowledge Retention Rate
- Practical Application Success

### Reporting Templates

**Executive Summary:**
- High-level findings
- Risk assessment
- Recommendations
- Resource requirements

**Technical Report:**
- Detailed analysis
- Evidence documentation
- Tool performance
- Improvement suggestions

## 🚀 Advanced Scenarios

### Multi-Stage Attack Campaign

**Objective:** Simulate advanced persistent threat (APT)

**Duration:** 1-2 weeks  
**Participants:** Multiple teams

**Phases:**
1. Reconnaissance and initial access
2. Persistence and privilege escalation
3. Lateral movement and data collection
4. Exfiltration and cleanup

### Purple Team Exercise

**Objective:** Collaborative red/blue team exercise

**Structure:**
- Joint planning session
- Coordinated attack execution
- Real-time feedback and adjustment
- Collaborative improvement

### Compliance Audit Simulation

**Objective:** Prepare for regulatory audit

**Components:**
- Log retention verification
- Incident response testing
- Control effectiveness validation
- Documentation review

## 📚 Additional Resources

### Documentation
- [Component Configuration Guides](components/)
- [Troubleshooting Guide](troubleshooting.md)
- [API Reference](api-reference.md)

### External Resources
- MITRE ATT&CK Framework
- NIST Cybersecurity Framework
- SANS Training Materials
- Industry Threat Intelligence Feeds

### Community
- GOAD-Blue User Forum
- Security Community Discord
- Monthly Virtual Meetups
- Annual Conference
