# SOC Training

Security Operations Center (SOC) training in GOAD-Blue provides comprehensive education for security analysts, covering everything from basic alert triage to advanced threat hunting using real-world scenarios and enterprise-grade security tools.

## 🎯 Overview

GOAD-Blue SOC training creates realistic security operations environments where participants learn to detect, analyze, and respond to cyber threats using industry-standard tools and procedures.

```mermaid
graph TB
    subgraph "🎓 SOC Training Curriculum"
        FUNDAMENTALS[📚 SOC Fundamentals<br/>Security Concepts<br/>Threat Landscape<br/>Tool Overview]
        OPERATIONS[⚙️ SOC Operations<br/>Alert Triage<br/>Incident Handling<br/>Escalation Procedures]
        ANALYSIS[🔍 Threat Analysis<br/>Log Analysis<br/>Forensic Techniques<br/>IOC Development]
        HUNTING[🎯 Threat Hunting<br/>Proactive Detection<br/>Hypothesis Development<br/>Advanced Analytics]
    end
    
    subgraph "🛠️ Training Tools"
        SIEM_TRAINING[📊 SIEM Training<br/>Splunk/Elastic<br/>Query Development<br/>Dashboard Creation]
        EDR_TRAINING[🛡️ EDR Training<br/>Velociraptor<br/>Endpoint Analysis<br/>Response Actions]
        NETWORK_TRAINING[🌐 Network Training<br/>Security Onion<br/>Traffic Analysis<br/>IDS/IPS Management]
        INTEL_TRAINING[🧠 Intelligence Training<br/>MISP<br/>IOC Management<br/>Threat Research]
    end
    
    subgraph "📋 Skill Levels"
        TIER1[🥉 Tier 1 Analyst<br/>Alert Monitoring<br/>Initial Triage<br/>Basic Investigation]
        TIER2[🥈 Tier 2 Analyst<br/>Deep Analysis<br/>Incident Response<br/>Tool Expertise]
        TIER3[🥇 Tier 3 Analyst<br/>Threat Hunting<br/>Advanced Forensics<br/>Tool Development]
        SPECIALIST[🏆 Specialist<br/>Subject Matter Expert<br/>Research & Development<br/>Training & Mentoring]
    end
    
    subgraph "🎮 GOAD Scenarios"
        REALISTIC_ATTACKS[⚔️ Realistic Attacks<br/>APT Campaigns<br/>Ransomware<br/>Insider Threats]
        LIVE_ENVIRONMENT[🏰 Live Environment<br/>Active Directory<br/>Real Applications<br/>Authentic Traffic]
        CONTINUOUS_LEARNING[🔄 Continuous Learning<br/>New Scenarios<br/>Updated TTPs<br/>Evolving Threats]
    end
    
    FUNDAMENTALS --> TIER1
    OPERATIONS --> TIER2
    ANALYSIS --> TIER3
    HUNTING --> SPECIALIST
    
    SIEM_TRAINING --> FUNDAMENTALS
    EDR_TRAINING --> OPERATIONS
    NETWORK_TRAINING --> ANALYSIS
    INTEL_TRAINING --> HUNTING
    
    TIER1 --> REALISTIC_ATTACKS
    TIER2 --> LIVE_ENVIRONMENT
    TIER3 --> CONTINUOUS_LEARNING
    SPECIALIST --> CONTINUOUS_LEARNING
    
    classDef curriculum fill:#2196f3,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef tools fill:#4caf50,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef levels fill:#ff9800,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef scenarios fill:#9c27b0,stroke:#ffffff,stroke-width:2px,color:#fff
    
    class FUNDAMENTALS,OPERATIONS,ANALYSIS,HUNTING curriculum
    class SIEM_TRAINING,EDR_TRAINING,NETWORK_TRAINING,INTEL_TRAINING tools
    class TIER1,TIER2,TIER3,SPECIALIST levels
    class REALISTIC_ATTACKS,LIVE_ENVIRONMENT,CONTINUOUS_LEARNING scenarios
```

## 📚 Training Curriculum

### **Tier 1 Analyst Training**

```yaml
tier1_curriculum:
  duration: "40 hours (1 week intensive)"
  prerequisites: "Basic IT knowledge, Security+ recommended"
  
  modules:
    module_1_fundamentals:
      title: "SOC Fundamentals"
      duration: "8 hours"
      topics:
        - SOC roles and responsibilities
        - Threat landscape overview
        - Security frameworks (NIST, MITRE ATT&CK)
        - Incident classification and severity
      
      hands_on:
        - GOAD environment tour
        - Tool interface familiarization
        - Basic navigation exercises
    
    module_2_alert_triage:
      title: "Alert Monitoring and Triage"
      duration: "12 hours"
      topics:
        - SIEM dashboard navigation
        - Alert prioritization techniques
        - False positive identification
        - Escalation procedures
      
      hands_on:
        - Live alert monitoring
        - Triage decision making
        - Documentation practices
        - Escalation simulations
    
    module_3_basic_investigation:
      title: "Basic Investigation Techniques"
      duration: "12 hours"
      topics:
        - Log analysis fundamentals
        - Timeline reconstruction
        - Evidence collection
        - Initial response actions
      
      hands_on:
        - Guided investigations
        - Log correlation exercises
        - Evidence documentation
        - Response procedures
    
    module_4_tools_training:
      title: "SOC Tools Training"
      duration: "8 hours"
      topics:
        - SIEM query basics
        - Endpoint tool usage
        - Network monitoring tools
        - Ticketing systems
      
      hands_on:
        - Query writing practice
        - Tool integration exercises
        - Workflow automation
        - Reporting procedures
  
  assessment:
    practical_exam:
      duration: "4 hours"
      scenarios:
        - Malware infection detection
        - Phishing email analysis
        - Unauthorized access investigation
        - Data exfiltration attempt
    
    certification_requirements:
      - 80% score on practical exam
      - Successful completion of all modules
      - Demonstration of core competencies
      - Peer evaluation feedback
```

### **Tier 2 Analyst Training**

```yaml
tier2_curriculum:
  duration: "60 hours (1.5 weeks intensive)"
  prerequisites: "Tier 1 certification or equivalent experience"
  
  modules:
    module_1_advanced_analysis:
      title: "Advanced Threat Analysis"
      duration: "16 hours"
      topics:
        - Advanced log correlation
        - Behavioral analysis techniques
        - Malware analysis basics
        - Attribution methodologies
      
      hands_on:
        - Complex incident analysis
        - Multi-vector attack investigation
        - Malware sample analysis
        - Threat actor profiling
    
    module_2_incident_response:
      title: "Incident Response Leadership"
      duration: "16 hours"
      topics:
        - IR methodology and frameworks
        - Containment strategies
        - Evidence preservation
        - Communication protocols
      
      hands_on:
        - IR team leadership
        - Containment simulations
        - Forensic evidence handling
        - Stakeholder communication
    
    module_3_forensics:
      title: "Digital Forensics"
      duration: "16 hours"
      topics:
        - Memory analysis techniques
        - Disk forensics
        - Network forensics
        - Mobile device analysis
      
      hands_on:
        - Volatility memory analysis
        - Disk imaging and analysis
        - Network packet analysis
        - Mobile forensics tools
    
    module_4_automation:
      title: "SOC Automation and Orchestration"
      duration: "12 hours"
      topics:
        - SOAR platform usage
        - Playbook development
        - API integration
        - Custom tool development
      
      hands_on:
        - Playbook creation
        - API scripting
        - Workflow automation
        - Tool integration projects
  
  capstone_project:
    title: "Complex Incident Investigation"
    duration: "20 hours"
    description: "Lead investigation of simulated APT campaign"
    deliverables:
      - Comprehensive incident report
      - Timeline reconstruction
      - IOC package
      - Remediation recommendations
```

### **Tier 3 Analyst Training**

```yaml
tier3_curriculum:
  duration: "80 hours (2 weeks intensive)"
  prerequisites: "Tier 2 certification + 1 year experience"
  
  modules:
    module_1_threat_hunting:
      title: "Advanced Threat Hunting"
      duration: "20 hours"
      topics:
        - Hypothesis-driven hunting
        - Advanced analytics
        - Machine learning applications
        - Custom detection development
      
      hands_on:
        - Hunting methodology development
        - Statistical analysis techniques
        - ML model training
        - Custom rule creation
    
    module_2_research:
      title: "Threat Research and Intelligence"
      duration: "20 hours"
      topics:
        - Threat intelligence analysis
        - Research methodologies
        - Open source intelligence
        - Attribution techniques
      
      hands_on:
        - Intelligence report writing
        - OSINT investigations
        - Threat actor research
        - Campaign analysis
    
    module_3_advanced_tools:
      title: "Advanced Tool Mastery"
      duration: "20 hours"
      topics:
        - Custom tool development
        - Advanced SIEM queries
        - Scripting and automation
        - Integration techniques
      
      hands_on:
        - Tool development projects
        - Advanced query optimization
        - Automation scripting
        - API development
    
    module_4_leadership:
      title: "Technical Leadership"
      duration: "20 hours"
      topics:
        - Team leadership
        - Knowledge transfer
        - Process improvement
        - Strategic planning
      
      hands_on:
        - Team mentoring
        - Training delivery
        - Process documentation
        - Strategic presentations
  
  research_project:
    title: "Original Research Project"
    duration: "40 hours"
    description: "Conduct original security research"
    deliverables:
      - Research paper
      - Tool or technique development
      - Conference presentation
      - Community contribution
```

## 🛠️ Hands-On Training Scenarios

### **Scenario 1: Phishing Investigation**

```python
# Phishing investigation training scenario
class PhishingInvestigationScenario:
    def __init__(self):
        self.scenario_name = "Corporate Phishing Campaign"
        self.difficulty = "Beginner"
        self.estimated_time = "2 hours"
        
    def setup_scenario(self):
        """Set up phishing investigation scenario"""
        scenario_data = {
            'initial_alert': {
                'timestamp': '2024-01-15 09:15:23',
                'source': 'Email Security Gateway',
                'severity': 'Medium',
                'description': 'Suspicious email with attachment detected',
                'affected_user': '<EMAIL>',
                'sender': '<EMAIL>',
                'subject': 'Urgent: Employee Benefits Update Required',
                'attachment': 'benefits_update.docm'
            },
            
            'investigation_steps': [
                {
                    'step': 1,
                    'task': 'Analyze email headers and metadata',
                    'tools': ['Email analysis tools', 'SIEM queries'],
                    'expected_findings': [
                        'Spoofed sender domain',
                        'Suspicious originating IP',
                        'Macro-enabled document'
                    ]
                },
                {
                    'step': 2,
                    'task': 'Check for similar emails in environment',
                    'tools': ['SIEM correlation', 'Email logs'],
                    'expected_findings': [
                        'Multiple recipients targeted',
                        'Campaign pattern identified',
                        'Additional malicious emails'
                    ]
                },
                {
                    'step': 3,
                    'task': 'Analyze attachment for malicious content',
                    'tools': ['FLARE-VM', 'Sandbox analysis'],
                    'expected_findings': [
                        'Malicious macro code',
                        'C2 communication attempts',
                        'Credential harvesting payload'
                    ]
                },
                {
                    'step': 4,
                    'task': 'Determine if users opened attachment',
                    'tools': ['Endpoint logs', 'Process monitoring'],
                    'expected_findings': [
                        'Document execution events',
                        'Suspicious process creation',
                        'Network connections'
                    ]
                },
                {
                    'step': 5,
                    'task': 'Implement containment measures',
                    'tools': ['Email quarantine', 'Endpoint isolation'],
                    'expected_actions': [
                        'Quarantine remaining emails',
                        'Isolate affected endpoints',
                        'Block malicious domains/IPs'
                    ]
                }
            ],
            
            'learning_objectives': [
                'Email threat analysis techniques',
                'Phishing campaign identification',
                'Malware analysis basics',
                'Containment procedures',
                'Documentation and reporting'
            ]
        }
        
        return scenario_data
    
    def generate_evidence(self):
        """Generate realistic evidence for investigation"""
        evidence = {
            'email_headers': '''
Received: from mail.sevenkingdoms-corp.com (**************)
    by mx.sevenkingdoms.local (*************)
From: <EMAIL>
To: <EMAIL>
Subject: Urgent: Employee Benefits Update Required
Date: Mon, 15 Jan 2024 09:10:15 +0000
X-Originating-IP: **************
X-Spam-Score: 7.5
            ''',
            
            'siem_logs': [
                {
                    'timestamp': '2024-01-15T09:15:23Z',
                    'source': 'email_gateway',
                    'event': 'email_received',
                    'sender': '<EMAIL>',
                    'recipient': '<EMAIL>',
                    'attachment_hash': 'a1b2c3d4e5f6...'
                },
                {
                    'timestamp': '2024-01-15T09:16:45Z',
                    'source': 'endpoint_agent',
                    'event': 'process_creation',
                    'process': 'WINWORD.EXE',
                    'command_line': '"C:\\Program Files\\Microsoft Office\\WINWORD.EXE" benefits_update.docm',
                    'user': 'john.doe'
                }
            ],
            
            'network_logs': [
                {
                    'timestamp': '2024-01-15T09:17:30Z',
                    'source_ip': '*************',
                    'dest_ip': '**************',
                    'dest_port': 443,
                    'protocol': 'HTTPS',
                    'bytes_out': 1024,
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
                }
            ]
        }
        
        return evidence
    
    def validate_investigation(self, student_findings):
        """Validate student investigation results"""
        required_findings = [
            'spoofed_domain_identified',
            'malicious_ip_found',
            'macro_analysis_completed',
            'affected_users_identified',
            'containment_implemented'
        ]
        
        score = 0
        feedback = []
        
        for finding in required_findings:
            if finding in student_findings:
                score += 20
                feedback.append(f"✓ {finding.replace('_', ' ').title()}")
            else:
                feedback.append(f"✗ Missing: {finding.replace('_', ' ').title()}")
        
        return {
            'score': score,
            'feedback': feedback,
            'passed': score >= 80
        }
```

### **Scenario 2: Lateral Movement Detection**

```yaml
lateral_movement_scenario:
  name: "Advanced Persistent Threat - Lateral Movement"
  difficulty: "Intermediate"
  duration: "4 hours"
  
  background:
    "An attacker has gained initial access to a workstation and is attempting to move laterally through the GOAD domain to reach high-value targets."
  
  initial_indicators:
    - Unusual SMB traffic patterns
    - Multiple failed authentication attempts
    - Suspicious PowerShell execution
    - Abnormal service account activity
  
  investigation_phases:
    phase_1_detection:
      duration: "45 minutes"
      objectives:
        - Identify initial compromise indicators
        - Correlate related events
        - Determine attack timeline
      
      tools_used:
        - Splunk/Elastic SIEM
        - Sysmon logs
        - Windows Event Logs
      
      expected_findings:
        - Initial compromise vector
        - Credential harvesting evidence
        - First lateral movement attempt
    
    phase_2_analysis:
      duration: "90 minutes"
      objectives:
        - Map attack progression
        - Identify compromised accounts
        - Determine attacker objectives
      
      tools_used:
        - Velociraptor hunts
        - Network traffic analysis
        - Memory forensics
      
      expected_findings:
        - Complete attack timeline
        - List of compromised systems
        - Attacker tools and techniques
    
    phase_3_containment:
      duration: "60 minutes"
      objectives:
        - Isolate compromised systems
        - Disable compromised accounts
        - Block attacker infrastructure
      
      tools_used:
        - Active Directory management
        - Firewall configuration
        - Endpoint isolation tools
      
      expected_actions:
        - Systems quarantined
        - Accounts disabled
        - Network access blocked
    
    phase_4_eradication:
      duration: "45 minutes"
      objectives:
        - Remove attacker presence
        - Patch vulnerabilities
        - Strengthen defenses
      
      tools_used:
        - Antimalware tools
        - Patch management
        - Security hardening
      
      expected_outcomes:
        - Malware removed
        - Systems patched
        - Controls enhanced
  
  assessment_criteria:
    technical_skills:
      - Log analysis proficiency (25%)
      - Tool usage effectiveness (25%)
      - Investigation methodology (25%)
      - Evidence documentation (25%)
    
    soft_skills:
      - Communication clarity
      - Time management
      - Decision making
      - Stress handling
  
  learning_outcomes:
    - Advanced log correlation techniques
    - Lateral movement detection methods
    - Incident response coordination
    - Forensic evidence handling
```

## 📊 Performance Assessment

### **Competency Framework**

```python
# SOC analyst competency assessment framework
class SOCCompetencyAssessment:
    def __init__(self):
        self.competencies = {
            'tier1': {
                'technical_skills': {
                    'siem_navigation': {'weight': 20, 'required_score': 80},
                    'alert_triage': {'weight': 25, 'required_score': 85},
                    'basic_investigation': {'weight': 20, 'required_score': 75},
                    'documentation': {'weight': 15, 'required_score': 80},
                    'tool_usage': {'weight': 20, 'required_score': 75}
                },
                'soft_skills': {
                    'communication': {'weight': 30, 'required_score': 80},
                    'attention_to_detail': {'weight': 25, 'required_score': 85},
                    'time_management': {'weight': 25, 'required_score': 75},
                    'teamwork': {'weight': 20, 'required_score': 80}
                }
            },
            'tier2': {
                'technical_skills': {
                    'advanced_analysis': {'weight': 25, 'required_score': 85},
                    'incident_response': {'weight': 25, 'required_score': 80},
                    'forensics': {'weight': 20, 'required_score': 75},
                    'automation': {'weight': 15, 'required_score': 70},
                    'threat_intelligence': {'weight': 15, 'required_score': 75}
                },
                'soft_skills': {
                    'leadership': {'weight': 30, 'required_score': 80},
                    'critical_thinking': {'weight': 25, 'required_score': 85},
                    'mentoring': {'weight': 25, 'required_score': 75},
                    'presentation': {'weight': 20, 'required_score': 80}
                }
            },
            'tier3': {
                'technical_skills': {
                    'threat_hunting': {'weight': 30, 'required_score': 85},
                    'research': {'weight': 25, 'required_score': 80},
                    'tool_development': {'weight': 20, 'required_score': 75},
                    'advanced_forensics': {'weight': 15, 'required_score': 80},
                    'strategic_analysis': {'weight': 10, 'required_score': 75}
                },
                'soft_skills': {
                    'innovation': {'weight': 30, 'required_score': 80},
                    'strategic_thinking': {'weight': 25, 'required_score': 85},
                    'knowledge_transfer': {'weight': 25, 'required_score': 80},
                    'industry_engagement': {'weight': 20, 'required_score': 75}
                }
            }
        }
    
    def assess_analyst(self, tier, scores):
        """Assess analyst competency based on scores"""
        tier_competencies = self.competencies[tier]
        results = {'technical': {}, 'soft_skills': {}, 'overall': {}}
        
        # Assess technical skills
        tech_total_weight = sum(skill['weight'] for skill in tier_competencies['technical_skills'].values())
        tech_weighted_score = 0
        tech_passed = True
        
        for skill, config in tier_competencies['technical_skills'].items():
            score = scores.get(skill, 0)
            weighted_score = (score * config['weight']) / 100
            tech_weighted_score += weighted_score
            
            passed = score >= config['required_score']
            tech_passed = tech_passed and passed
            
            results['technical'][skill] = {
                'score': score,
                'required': config['required_score'],
                'passed': passed,
                'weight': config['weight']
            }
        
        results['technical']['overall_score'] = (tech_weighted_score / tech_total_weight) * 100
        results['technical']['passed'] = tech_passed
        
        # Assess soft skills
        soft_total_weight = sum(skill['weight'] for skill in tier_competencies['soft_skills'].values())
        soft_weighted_score = 0
        soft_passed = True
        
        for skill, config in tier_competencies['soft_skills'].items():
            score = scores.get(skill, 0)
            weighted_score = (score * config['weight']) / 100
            soft_weighted_score += weighted_score
            
            passed = score >= config['required_score']
            soft_passed = soft_passed and passed
            
            results['soft_skills'][skill] = {
                'score': score,
                'required': config['required_score'],
                'passed': passed,
                'weight': config['weight']
            }
        
        results['soft_skills']['overall_score'] = (soft_weighted_score / soft_total_weight) * 100
        results['soft_skills']['passed'] = soft_passed
        
        # Overall assessment
        overall_score = (results['technical']['overall_score'] + results['soft_skills']['overall_score']) / 2
        overall_passed = tech_passed and soft_passed
        
        results['overall'] = {
            'score': overall_score,
            'passed': overall_passed,
            'certification_eligible': overall_passed and overall_score >= 80
        }
        
        return results
    
    def generate_development_plan(self, assessment_results):
        """Generate personalized development plan"""
        plan = {'focus_areas': [], 'recommended_training': [], 'timeline': ''}
        
        # Identify areas needing improvement
        for category in ['technical', 'soft_skills']:
            for skill, result in assessment_results[category].items():
                if skill != 'overall_score' and skill != 'passed' and not result['passed']:
                    plan['focus_areas'].append({
                        'skill': skill,
                        'current_score': result['score'],
                        'target_score': result['required'],
                        'gap': result['required'] - result['score']
                    })
        
        # Recommend training based on gaps
        for area in plan['focus_areas']:
            if area['gap'] > 20:
                plan['recommended_training'].append(f"Intensive {area['skill']} training (40 hours)")
            elif area['gap'] > 10:
                plan['recommended_training'].append(f"Focused {area['skill']} workshop (16 hours)")
            else:
                plan['recommended_training'].append(f"Refresher {area['skill']} session (8 hours)")
        
        return plan
```

---

!!! tip "SOC Training Best Practices"
    - Use realistic scenarios based on current threat landscape
    - Provide hands-on experience with actual security tools
    - Include both technical and soft skills development
    - Implement continuous assessment and feedback
    - Create clear career progression pathways

!!! warning "Training Considerations"
    - Ensure training environment isolation from production
    - Maintain up-to-date threat scenarios and techniques
    - Balance theoretical knowledge with practical skills
    - Consider different learning styles and paces
    - Provide adequate mentoring and support

!!! info "Certification Benefits"
    - Standardized skill validation across the organization
    - Clear career progression and development paths
    - Improved job satisfaction and retention
    - Enhanced security operations effectiveness
    - Industry recognition and credibility
