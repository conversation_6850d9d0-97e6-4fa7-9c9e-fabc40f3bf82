# Kerberoasting Detection Scenario

This scenario teaches detection and analysis of Kerberoasting attacks, a common Active Directory attack technique that targets service accounts to extract and crack their passwords offline.

## 🎯 Scenario Overview

**Difficulty:** Intermediate  
**Duration:** 3-4 hours  
**Skills Focus:** SIEM Analysis, Active Directory Security, Log Correlation  
**Tools Required:** Splunk/Elastic, Wireshark, PowerShell, Rubeus

### **Learning Objectives**
- Understand Kerberos authentication protocol and vulnerabilities
- Detect Kerberoasting attacks using SIEM platforms
- Analyze Kerberos logs and network traffic
- Develop effective detection rules and alerts
- Implement defensive countermeasures

## 📚 Background Knowledge

### **Kerberos Protocol Fundamentals**

```mermaid
sequenceDiagram
    participant Client
    participant KDC as Key Distribution Center
    participant Service
    
    Note over Client,Service: Normal Kerberos Authentication
    Client->>KDC: 1. Authentication Request (AS-REQ)
    KDC->>Client: 2. Ticket Granting Ticket (TGT)
    Client->>KDC: 3. Service Ticket Request (TGS-REQ)
    KDC->>Client: 4. Service Ticket (TGS-REP)
    Client->>Service: 5. Service Request with Ticket
    
    Note over Client,Service: Kerberoasting Attack
    Client->>KDC: 3. TGS-REQ for Service Account
    KDC->>Client: 4. Encrypted TGS-REP
    Note over Client: Offline Password Cracking
```

### **Attack Methodology**
1. **Service Discovery** - Enumerate service accounts with SPNs
2. **Ticket Requests** - Request TGS tickets for service accounts
3. **Ticket Extraction** - Extract encrypted tickets from memory
4. **Offline Cracking** - Attempt to crack service account passwords

## 🎮 Scenario Setup

### **Environment Configuration**

```yaml
scenario_environment:
  domain: "sevenkingdoms.local"
  domain_controller: "winterfell.sevenkingdoms.local"
  
  service_accounts:
    - name: "svc_sql"
      spn: "MSSQLSvc/winterfell.sevenkingdoms.local:1433"
      password: "MyStr0ngP@ssw0rd123"
      description: "SQL Server service account"
    
    - name: "svc_web"
      spn: "HTTP/kingslanding.sevenkingdoms.local"
      password: "WebService2024!"
      description: "Web application service account"
    
    - name: "svc_backup"
      spn: "BackupSvc/winterfell.sevenkingdoms.local"
      password: "BackupP@ss123"
      description: "Backup service account"
  
  attacker_workstation: "*************"
  monitoring_tools:
    - splunk_server: "*************"
    - wireshark_capture: "enabled"
    - sysmon_logging: "enabled"
```

### **Pre-Attack Baseline**

```powershell
# Establish baseline Kerberos activity
# Run this script to understand normal TGS request patterns

# Get baseline TGS request frequency
Get-WinEvent -FilterHashtable @{LogName='Security'; ID=4769} -MaxEvents 1000 | 
    Group-Object {$_.TimeCreated.Hour} | 
    Select-Object Name, Count | 
    Sort-Object Name

# Identify legitimate service accounts
Get-ADUser -Filter {ServicePrincipalName -like "*"} -Properties ServicePrincipalName | 
    Select-Object Name, ServicePrincipalName

# Document normal authentication patterns
Get-WinEvent -FilterHashtable @{LogName='Security'; ID=4624} -MaxEvents 500 | 
    Where-Object {$_.Properties[8].Value -eq 3} | 
    Group-Object {$_.Properties[5].Value} | 
    Select-Object Name, Count
```

## ⚔️ Attack Simulation

### **Phase 1: Service Account Discovery**

```powershell
# Attacker discovers service accounts with SPNs
# This simulates the reconnaissance phase

# Method 1: Using built-in tools
setspn -Q */*

# Method 2: Using PowerShell
Get-ADUser -Filter {ServicePrincipalName -like "*"} -Properties ServicePrincipalName | 
    Select-Object Name, ServicePrincipalName

# Method 3: Using LDAP queries
([adsisearcher]"(&(objectClass=user)(servicePrincipalName=*))").FindAll() | 
    ForEach-Object {$_.Properties.samaccountname}
```

### **Phase 2: Kerberoasting Attack Execution**

```powershell
# Simulate Kerberoasting attack using Rubeus
# This generates the detection events we'll analyze

# Request TGS tickets for all service accounts
.\Rubeus.exe kerberoast /outfile:tickets.txt

# Alternative method using PowerShell
Add-Type -AssemblyName System.IdentityModel
New-Object System.IdentityModel.Tokens.KerberosRequestorSecurityToken -ArgumentList "MSSQLSvc/winterfell.sevenkingdoms.local:1433"

# Extract tickets from memory
.\Rubeus.exe dump /service:krbtgt /outfile:extracted_tickets.txt
```

### **Phase 3: Attack Artifacts Generation**

```python
# Python script to generate realistic attack artifacts
import datetime
import random

def generate_kerberoasting_logs():
    """Generate realistic Kerberoasting attack logs"""
    
    service_accounts = [
        "svc_sql",
        "svc_web", 
        "svc_backup"
    ]
    
    attacker_ip = "*************"
    dc_ip = "*************"
    
    # Generate TGS request events (Event ID 4769)
    for i in range(50):  # Burst of requests
        timestamp = datetime.datetime.now() + datetime.timedelta(seconds=i*2)
        service_account = random.choice(service_accounts)
        
        log_entry = {
            "timestamp": timestamp.isoformat(),
            "event_id": 4769,
            "source_ip": attacker_ip,
            "target_service": service_account,
            "ticket_encryption_type": "0x17",  # RC4-HMAC
            "failure_code": "0x0",
            "pre_authentication": "Yes"
        }
        
        print(f"TGS Request: {log_entry}")
    
    # Generate suspicious PowerShell execution
    powershell_events = [
        {
            "timestamp": datetime.datetime.now().isoformat(),
            "event_id": 4688,
            "process_name": "powershell.exe",
            "command_line": "Get-ADUser -Filter {ServicePrincipalName -like '*'}",
            "parent_process": "cmd.exe",
            "user": "SEVENKINGDOMS\\jon.snow"
        }
    ]
    
    return log_entry, powershell_events

# Generate attack artifacts
generate_kerberoasting_logs()
```

## 🔍 Detection and Analysis

### **Task 1: SIEM Analysis (45 minutes)**

#### **Splunk Detection Queries**

```sql
-- Query 1: Detect high volume of TGS requests
index=windows EventCode=4769 
| bucket _time span=5m 
| stats count by _time, Account_Name, Service_Name 
| where count > 10
| eval risk_score = count * 2
| where risk_score > 20

-- Query 2: Identify RC4 encryption usage (weak encryption)
index=windows EventCode=4769 Ticket_Encryption_Type=0x17
| stats count by Account_Name, Service_Name, Client_Address
| where count > 5
| sort - count

-- Query 3: Detect service account enumeration
index=windows EventCode=4662 Object_Type="user" 
| where match(Properties, ".*servicePrincipalName.*")
| stats count by Account_Name, Object_Name
| where count > 10

-- Query 4: Correlate with PowerShell activity
index=windows (EventCode=4688 OR EventCode=4103)
| where match(Process_Command_Line, "(?i)(setspn|Get-ADUser.*ServicePrincipalName|kerberoast)")
| table _time, Account_Name, Process_Command_Line, Parent_Process_Name
```

#### **Elastic Stack Detection Queries**

```json
{
  "query": {
    "bool": {
      "must": [
        {"term": {"winlog.event_id": "4769"}},
        {"range": {"@timestamp": {"gte": "now-1h"}}},
        {"term": {"winlog.event_data.TicketEncryptionType": "0x17"}}
      ]
    }
  },
  "aggs": {
    "accounts": {
      "terms": {
        "field": "winlog.event_data.TargetUserName.keyword",
        "size": 10
      },
      "aggs": {
        "request_count": {
          "value_count": {
            "field": "winlog.event_data.TargetUserName.keyword"
          }
        }
      }
    }
  }
}
```

### **Task 2: Network Traffic Analysis (30 minutes)**

#### **Wireshark Analysis**

```bash
# Wireshark display filters for Kerberos analysis

# Filter 1: All Kerberos traffic
kerberos

# Filter 2: TGS requests specifically
kerberos.msg_type == 12

# Filter 3: TGS responses
kerberos.msg_type == 13

# Filter 4: Focus on service ticket requests
kerberos.req_body.sname contains "svc_"

# Filter 5: Identify RC4 encryption
kerberos.etype == 23
```

#### **Network Analysis Tasks**

1. **Identify TGS Request Patterns**
   - Look for burst of TGS requests from single source
   - Analyze timing patterns (rapid succession)
   - Identify targeted service accounts

2. **Encryption Analysis**
   - Identify weak encryption types (RC4-HMAC)
   - Compare with baseline encryption usage
   - Document encryption downgrade attempts

3. **Source Attribution**
   - Identify attacking workstation
   - Correlate with user authentication events
   - Map attack timeline

### **Task 3: PowerShell Log Analysis (30 minutes)**

```powershell
# Analyze PowerShell logs for reconnaissance activity

# Check PowerShell command history
Get-WinEvent -FilterHashtable @{LogName='Microsoft-Windows-PowerShell/Operational'; ID=4103} | 
    Where-Object {$_.Message -match "(setspn|Get-ADUser|ServicePrincipalName|kerberoast)"} |
    Select-Object TimeCreated, UserId, Message

# Analyze PowerShell module loading
Get-WinEvent -FilterHashtable @{LogName='Microsoft-Windows-PowerShell/Operational'; ID=4103} |
    Where-Object {$_.Message -match "System.IdentityModel"} |
    Format-Table TimeCreated, UserId, Message -Wrap

# Check for Rubeus execution
Get-WinEvent -FilterHashtable @{LogName='Security'; ID=4688} |
    Where-Object {$_.Properties[5].Value -match "rubeus"} |
    Select-Object TimeCreated, @{Name="CommandLine";Expression={$_.Properties[8].Value}}
```

## 📊 Analysis Questions

### **Detection Analysis**
1. **What indicators suggest a Kerberoasting attack is occurring?**
   - High volume of TGS requests
   - Requests for multiple service accounts
   - Use of RC4 encryption
   - Rapid succession of requests

2. **How can you differentiate between legitimate and malicious TGS requests?**
   - Request frequency and timing
   - Source IP analysis
   - Service account targeting patterns
   - Encryption type preferences

3. **What reconnaissance activities preceded the attack?**
   - Service account enumeration
   - SPN discovery commands
   - LDAP queries for service accounts
   - PowerShell reconnaissance scripts

### **Investigation Tasks**
1. **Timeline Reconstruction**
   - Map attack progression from reconnaissance to ticket requests
   - Identify all affected service accounts
   - Correlate with user authentication events

2. **Impact Assessment**
   - Determine which service accounts were targeted
   - Assess potential for password compromise
   - Evaluate business impact of compromised services

3. **Attribution Analysis**
   - Identify attacking user account
   - Trace attack source workstation
   - Determine attack tools used

## 🛡️ Detection Rule Development

### **Splunk Detection Rules**

```sql
-- High-confidence Kerberoasting detection
index=windows EventCode=4769
| bucket _time span=1m
| stats count dc(Service_Name) as unique_services by _time, Account_Name, Client_Address
| where count > 5 AND unique_services > 2
| eval severity = case(
    count > 20, "critical",
    count > 10, "high", 
    count > 5, "medium",
    1=1, "low"
)
| where severity != "low"

-- RC4 encryption usage alert
index=windows EventCode=4769 Ticket_Encryption_Type=0x17
| stats count by Account_Name, Service_Name, Client_Address
| where count > 3
| eval alert_message = "Potential Kerberoasting: RC4 encryption used for " . Service_Name
```

### **Sigma Rules**

```yaml
title: Kerberoasting Attack Detection
id: 87e3c4e8-a6a8-4ad4-b53c-b2a7a8b1c4d5
description: Detects potential Kerberoasting attacks based on TGS request patterns
author: GOAD-Blue Team
date: 2024/01/15
references:
    - https://attack.mitre.org/techniques/T1558/003/
logsource:
    product: windows
    service: security
detection:
    selection:
        EventID: 4769
        TicketEncryptionType: '0x17'
    timeframe: 5m
    condition: selection | count() > 10
falsepositives:
    - Legitimate service account usage
    - Automated service authentication
level: medium
tags:
    - attack.credential_access
    - attack.t1558.003
```

## 🔧 Defensive Countermeasures

### **Immediate Response Actions**

```powershell
# Incident response script for Kerberoasting detection

# 1. Identify affected service accounts
$AffectedAccounts = @("svc_sql", "svc_web", "svc_backup")

# 2. Force password reset for service accounts
foreach ($Account in $AffectedAccounts) {
    $NewPassword = -join ((65..90) + (97..122) + (48..57) | Get-Random -Count 20 | % {[char]$_})
    Set-ADAccountPassword -Identity $Account -NewPassword (ConvertTo-SecureString $NewPassword -AsPlainText -Force) -Reset
    Write-Host "Password reset for $Account"
}

# 3. Enable AES encryption for Kerberos
foreach ($Account in $AffectedAccounts) {
    Set-ADUser -Identity $Account -KerberosEncryptionType AES128,AES256
    Write-Host "Enabled AES encryption for $Account"
}

# 4. Audit service account permissions
foreach ($Account in $AffectedAccounts) {
    Get-ADUser -Identity $Account -Properties MemberOf | 
        Select-Object Name, @{Name="Groups";Expression={$_.MemberOf -join "; "}}
}
```

### **Long-term Security Improvements**

1. **Service Account Hardening**
   - Use Managed Service Accounts (MSA) where possible
   - Implement strong, complex passwords
   - Enable AES encryption for Kerberos
   - Regular password rotation

2. **Monitoring Enhancements**
   - Deploy advanced Kerberos monitoring
   - Implement behavioral analytics
   - Create custom detection rules
   - Enable detailed audit logging

3. **Network Segmentation**
   - Isolate service accounts
   - Implement least privilege access
   - Monitor service account usage
   - Restrict lateral movement

## 📋 Scenario Assessment

### **Deliverables**
1. **Detection Report** (30 points)
   - Timeline of attack events
   - Analysis of detection methods
   - False positive assessment
   - Confidence scoring

2. **Investigation Summary** (25 points)
   - Root cause analysis
   - Impact assessment
   - Attribution findings
   - Evidence documentation

3. **Detection Rules** (25 points)
   - Splunk/Elastic queries
   - Sigma rule development
   - Tuning recommendations
   - Performance considerations

4. **Response Plan** (20 points)
   - Immediate containment actions
   - Recovery procedures
   - Long-term improvements
   - Lessons learned

### **Assessment Criteria**

| Criterion | Excellent (90-100%) | Good (80-89%) | Satisfactory (70-79%) | Needs Improvement (<70%) |
|-----------|-------------------|---------------|---------------------|------------------------|
| **Detection Speed** | <15 minutes | 15-30 minutes | 30-60 minutes | >60 minutes |
| **Analysis Accuracy** | All indicators identified | Most indicators found | Some indicators missed | Major gaps in analysis |
| **Rule Quality** | Low false positives, high detection rate | Good balance | Some tuning needed | High false positive rate |
| **Documentation** | Comprehensive and clear | Good detail | Adequate | Insufficient detail |

---

!!! tip "Success Tips"
    - Focus on understanding the Kerberos protocol fundamentals
    - Practice correlating events across multiple log sources
    - Pay attention to timing patterns and frequency analysis
    - Consider both technical and business impact in your analysis
    - Test your detection rules thoroughly to minimize false positives

!!! warning "Common Pitfalls"
    - Don't rely solely on single indicators
    - Be aware of legitimate service account activity
    - Consider encrypted network traffic limitations
    - Account for different attack tool variations
    - Validate findings with multiple data sources

!!! info "Additional Resources"
    - MITRE ATT&CK T1558.003: Kerberoasting
    - Microsoft Kerberos documentation
    - Rubeus tool documentation
    - Active Directory security best practices
    - Kerberos attack detection research papers
