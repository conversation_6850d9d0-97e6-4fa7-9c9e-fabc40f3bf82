- name: Enable update service
  ansible.windows.win_service:
    name: Windows Update
    state: started
    start_mode: auto

- name: Install IIS Management Features
  win_feature:
    name: Web-Mgmt-<PERSON><PERSON>, 
          Web-Mgmt-Console, 
          Web-Scripting-Tools, 
          Web-Mgmt-Service
    state: present

- name: Add SYSTEM allow rights to machine keys (required for installation of IIS 6 components)
  win_acl:
    path: C:\ProgramData\Microsoft\Crypto\RSA\MachineKeys\
    user: SYSTEM
    rights: FullControl
    type: allow
    state: present
    inherit: ContainerInherit, ObjectInherit
    propagation: 'InheritOnly'

- name: Install IIS 6 Compatibility Features 
  win_feature:
    name: Web-Mgmt-Compat,
          Web-Metabase,
          Web-Lgcy-Mgmt-Console,
          Web-Lgcy-Scripting,
          Web-WMI
    state: present

- name: Install IIS Web-Server with sub features and management tools
  win_feature:
    name: Web-Server
    state: present
    include_sub_features: yes
    include_management_tools: yes
  register: win_feature

- name: Create directory
  win_file:
    path: C:\\inetpub
    state: directory

- name: Create directory
  win_file:
    path: C:\\inetpub\\wwwroot
    state: directory

- name: default-website-index
  win_copy:
    src: files/index.html
    dest: "C:\\inetpub\\wwwroot\\index.html"

- name: Reboot if installing Web-Server feature requires it
  ansible.windows.win_reboot:
  when: win_feature.reboot_required

