{"builders": [{"boot_wait": "2m", "communicator": "winrm", "cpus": 2, "disk_adapter_type": "lsisas1068", "disk_size": "{{user `disk_size`}}", "disk_type_id": "{{user `disk_type_id`}}", "floppy_files": ["{{user `autounattend`}}", "./scripts/disable-screensaver.ps1", "./scripts/disable-winrm.ps1", "./scripts/enable-winrm.ps1", "./scripts/microsoft-updates.bat", "./scripts/win-updates.ps1", "./scripts/unattend.xml", "./scripts/sysprep.bat"], "guest_os_type": "windows9srv-64", "headless": "{{user `headless`}}", "iso_checksum": "{{user `iso_checksum`}}", "iso_url": "{{user `iso_url`}}", "memory": "{{user `memory`}}", "shutdown_command": "a:/sysprep.bat", "type": "vmware-iso", "version": "{{user `vmx_version`}}", "vm_name": "WindowsServer2022", "vmx_data": {"RemoteDisplay.vnc.enabled": "false", "RemoteDisplay.vnc.port": "5900"}, "vmx_remove_ethernet_interfaces": true, "vnc_port_max": 5980, "vnc_port_min": 5900, "winrm_password": "vagrant", "winrm_timeout": "{{user `winrm_timeout`}}", "winrm_username": "vagrant"}, {"boot_wait": "2m", "communicator": "winrm", "cpus": 2, "disk_size": "{{user `disk_size`}}", "floppy_files": ["{{user `autounattend`}}", "./scripts/disable-screensaver.ps1", "./scripts/disable-winrm.ps1", "./scripts/enable-winrm.ps1", "./scripts/microsoft-updates.bat", "./scripts/win-updates.ps1", "./scripts/unattend.xml", "./scripts/sysprep.bat"], "guest_additions_mode": "disable", "guest_os_type": "Windows2016_64", "headless": "{{user `headless`}}", "iso_checksum": "{{user `iso_checksum`}}", "iso_url": "{{user `iso_url`}}", "memory": "{{user `memory`}}", "shutdown_command": "a:/sysprep.bat", "type": "virtualbox-iso", "vm_name": "WindowsServer2022", "winrm_password": "vagrant", "winrm_timeout": "{{user `winrm_timeout`}}", "winrm_username": "vagrant"}], "post-processors": [{"keep_input_artifact": false, "output": "windows_2022_{{.Provider}}.box", "type": "vagrant", "vagrantfile_template": "vagrantfile-windows_2016.template"}], "provisioners": [{"execute_command": "{{ .Vars }} cmd /c \"{{ .Path }}\"", "scripts": ["./scripts/enable-rdp.bat"], "type": "windows-shell"}, {"scripts": ["./scripts/vm-guest-tools.ps1"], "type": "powershell"}, {"restart_timeout": "{{user `restart_timeout`}}", "type": "windows-restart"}, {"execute_command": "{{ .Vars }} cmd /c \"{{ .Path }}\"", "scripts": ["./scripts/set-winrm-automatic.bat", "./scripts/uac-enable.bat", "./scripts/compile-dotnet-assemblies.bat", "./scripts/dis-updates.bat"], "type": "windows-shell"}], "variables": {"autounattend": "./answer_files/2022/Autounattend.xml", "disk_size": "61440", "disk_type_id": "1", "memory": "2048", "headless": "false", "hyperv_switchname": "{{env `hyperv_switchname`}}", "iso_checksum": "sha256:3e4fa6d8507b554856fc9ca6079cc402df11a8b79344871669f0251535255325", "iso_url": "https://software-static.download.prss.microsoft.com/sg/download/888969d5-f34g-4e03-ac9d-1f9786c66749/SERVER_EVAL_x64FRE_en-us.iso", "manually_download_iso_from": "https://www.microsoft.com/en-us/evalcenter/evaluate-windows-server-2022", "restart_timeout": "5m", "virtio_win_iso": "~/virtio-win.iso", "winrm_timeout": "2h", "vmx_version": "14"}}