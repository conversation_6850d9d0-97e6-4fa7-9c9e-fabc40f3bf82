# Malware Analysis Workshop

This comprehensive scenario provides hands-on experience with malware analysis using FLARE-VM, covering static analysis, dynamic analysis, and reverse engineering techniques to extract IOCs and develop detection signatures.

## 🎯 Scenario Overview

**Difficulty:** Intermediate to Advanced
**Duration:** 6-8 hours
**Skills Focus:** Reverse Engineering, Forensics, IOC Extraction
**Tools Required:** FLARE-VM, IDA Pro/Ghidra, Wireshark, Volatility

### **Learning Objectives**
- Master static and dynamic malware analysis techniques
- Extract and validate indicators of compromise (IOCs)
- Develop YARA rules and detection signatures
- Understand malware behavior and persistence mechanisms
- Create comprehensive analysis reports for threat intelligence

## 📚 Background Knowledge

### **Malware Analysis Methodology**

```mermaid
graph TB
    subgraph "🔬 Analysis Workflow"
        TRIAGE[🎯 Initial Triage<br/>File Properties<br/>Basic Scanning<br/>Risk Assessment]
        STATIC[📄 Static Analysis<br/>Code Structure<br/>String Analysis<br/>Import/Export Tables]
        DYNAMIC[⚡ Dynamic Analysis<br/>Behavioral Monitoring<br/>Network Activity<br/>System Changes]
        REVERSE[🔍 Reverse Engineering<br/>Disassembly<br/>Code Analysis<br/>Algorithm Understanding]
    end

    subgraph "🛠️ Analysis Tools"
        BASIC_TOOLS[🔧 Basic Tools<br/>File<br/>Strings<br/>PEStudio<br/>Detect It Easy]
        DISASSEMBLERS[⚙️ Disassemblers<br/>IDA Pro<br/>Ghidra<br/>x64dbg<br/>Radare2]
        SANDBOXES[📦 Sandboxes<br/>Cuckoo<br/>Joe Sandbox<br/>Any.run<br/>Hybrid Analysis]
        MONITORS[👁️ Monitors<br/>Process Monitor<br/>Process Hacker<br/>API Monitor<br/>Wireshark]
    end

    subgraph "📊 Analysis Outputs"
        IOCS[🔍 IOCs<br/>File Hashes<br/>Network Indicators<br/>Registry Keys<br/>File Paths]
        SIGNATURES[📝 Signatures<br/>YARA Rules<br/>Snort Rules<br/>Sigma Rules<br/>Custom Detections]
        REPORTS[📋 Reports<br/>Technical Analysis<br/>Executive Summary<br/>Threat Assessment<br/>Recommendations]
        INTELLIGENCE[🧠 Threat Intelligence<br/>Attribution<br/>Campaign Analysis<br/>TTP Mapping<br/>Family Classification]
    end

    TRIAGE --> STATIC
    STATIC --> DYNAMIC
    DYNAMIC --> REVERSE
    REVERSE --> TRIAGE

    BASIC_TOOLS --> TRIAGE
    DISASSEMBLERS --> STATIC
    SANDBOXES --> DYNAMIC
    MONITORS --> REVERSE

    STATIC --> IOCS
    DYNAMIC --> SIGNATURES
    REVERSE --> REPORTS
    TRIAGE --> INTELLIGENCE

    classDef workflow fill:#2196f3,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef tools fill:#4caf50,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef outputs fill:#ff9800,stroke:#ffffff,stroke-width:2px,color:#fff

    class TRIAGE,STATIC,DYNAMIC,REVERSE workflow
    class BASIC_TOOLS,DISASSEMBLERS,SANDBOXES,MONITORS tools
    class IOCS,SIGNATURES,REPORTS,INTELLIGENCE outputs
```

### **Malware Categories and Techniques**

| Category | Description | Common Techniques | Detection Challenges |
|----------|-------------|-------------------|---------------------|
| **Trojans** | Disguised malicious software | Social engineering, legitimate-looking files | Polymorphism, encryption |
| **Ransomware** | File encryption for extortion | Crypto APIs, file enumeration | Rapid encryption, legitimate crypto usage |
| **Backdoors** | Remote access tools | Network communication, persistence | Legitimate remote tools, encrypted C2 |
| **Rootkits** | System-level hiding | Kernel modification, API hooking | Deep system integration, stealth |
| **Botnets** | Coordinated malware networks | C2 communication, peer-to-peer | Domain generation algorithms, encryption |

## 🎮 Scenario Setup

### **Sample Collection**

```yaml
malware_samples:
  sample_1:
    name: "TrojanBanker.exe"
    family: "Banking Trojan"
    hash_md5: "d41d8cd98f00b204e9800998ecf8427e"
    hash_sha256: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
    size: "2.5 MB"
    file_type: "PE32 executable"
    packer: "UPX"
    difficulty: "Intermediate"

  sample_2:
    name: "RansomCrypt.dll"
    family: "Ransomware"
    hash_md5: "5d41402abc4b2a76b9719d911017c592"
    hash_sha256: "2c26b46b68ffc68ff99b453c1d30413413422d706483bfa0f98a5e886266e7ae"
    size: "1.8 MB"
    file_type: "PE32+ DLL"
    packer: "Custom"
    difficulty: "Advanced"

  sample_3:
    name: "BackdoorRAT.bin"
    family: "Remote Access Trojan"
    hash_md5: "098f6bcd4621d373cade4e832627b4f6"
    hash_sha256: "9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08"
    size: "850 KB"
    file_type: "PE32 executable"
    packer: "None"
    difficulty: "Beginner"

analysis_environment:
  vm_platform: "VMware Workstation"
  base_image: "FLARE-VM Windows 10"
  snapshot_name: "Clean_Analysis_Environment"
  network_mode: "Host-only (isolated)"
  monitoring_tools:
    - "Process Monitor"
    - "Process Hacker"
    - "API Monitor"
    - "Wireshark"
    - "Volatility"
```

### **Environment Preparation**

```powershell
# FLARE-VM environment preparation script
# Prepare analysis environment for malware workshop

# Create analysis directories
New-Item -ItemType Directory -Path "C:\MalwareAnalysis" -Force
New-Item -ItemType Directory -Path "C:\MalwareAnalysis\Samples" -Force
New-Item -ItemType Directory -Path "C:\MalwareAnalysis\Reports" -Force
New-Item -ItemType Directory -Path "C:\MalwareAnalysis\IOCs" -Force
New-Item -ItemType Directory -Path "C:\MalwareAnalysis\Artifacts" -Force

# Configure Windows Defender exclusions
Add-MpPreference -ExclusionPath "C:\MalwareAnalysis"
Add-MpPreference -ExclusionExtension ".exe"
Add-MpPreference -ExclusionExtension ".dll"
Add-MpPreference -ExclusionExtension ".bin"

# Start monitoring tools
Start-Process "C:\Tools\SysinternalsSuite\Procmon.exe" -ArgumentList "/AcceptEula", "/Minimized"
Start-Process "C:\Tools\ProcessHacker\ProcessHacker.exe" -ArgumentList "-hide"

# Create VM snapshot
Write-Host "Creating clean analysis snapshot..."
# VMware snapshot creation would be done via vmrun or PowerCLI

# Network isolation verification
$NetworkAdapters = Get-NetAdapter | Where-Object {$_.Status -eq "Up"}
foreach ($Adapter in $NetworkAdapters) {
    Write-Host "Network Adapter: $($Adapter.Name) - Status: $($Adapter.Status)"
}

Write-Host "Analysis environment prepared successfully!"
```

## 🔬 Analysis Tasks

### **Task 1: Static Analysis (90 minutes)**

#### **Basic File Analysis**

```powershell
# Static analysis automation script
param(
    [Parameter(Mandatory=$true)]
    [string]$SamplePath
)

function Perform-StaticAnalysis {
    param($FilePath)

    $FileName = Split-Path $FilePath -Leaf
    $ReportPath = "C:\MalwareAnalysis\Reports\$FileName-static-report.txt"

    Write-Host "Starting static analysis of $FileName..."

    # File properties
    "=== BASIC FILE INFORMATION ===" | Out-File $ReportPath
    $FileInfo = Get-ItemProperty $FilePath
    "File Name: $($FileInfo.Name)" | Out-File $ReportPath -Append
    "File Size: $($FileInfo.Length) bytes" | Out-File $ReportPath -Append
    "Creation Time: $($FileInfo.CreationTime)" | Out-File $ReportPath -Append
    "Last Write Time: $($FileInfo.LastWriteTime)" | Out-File $ReportPath -Append

    # File hashes
    "=== FILE HASHES ===" | Out-File $ReportPath -Append
    $MD5 = Get-FileHash $FilePath -Algorithm MD5
    $SHA1 = Get-FileHash $FilePath -Algorithm SHA1
    $SHA256 = Get-FileHash $FilePath -Algorithm SHA256

    "MD5:    $($MD5.Hash)" | Out-File $ReportPath -Append
    "SHA1:   $($SHA1.Hash)" | Out-File $ReportPath -Append
    "SHA256: $($SHA256.Hash)" | Out-File $ReportPath -Append

    # File type detection
    "=== FILE TYPE ANALYSIS ===" | Out-File $ReportPath -Append
    & "C:\Tools\file\file.exe" $FilePath | Out-File $ReportPath -Append

    # PE analysis (if PE file)
    if ($FilePath -match '\.(exe|dll|sys)$') {
        "=== PE ANALYSIS ===" | Out-File $ReportPath -Append
        & "C:\Tools\PEStudio\pestudio.exe" /export $FilePath | Out-File $ReportPath -Append
    }

    # Strings analysis
    "=== STRINGS ANALYSIS ===" | Out-File $ReportPath -Append
    & "C:\Tools\SysinternalsSuite\strings.exe" -n 8 $FilePath | Out-File $ReportPath -Append

    # Entropy analysis
    "=== ENTROPY ANALYSIS ===" | Out-File $ReportPath -Append
    & "C:\Tools\densityscout\densityscout.exe" $FilePath | Out-File $ReportPath -Append

    Write-Host "Static analysis completed. Report: $ReportPath"
    return $ReportPath
}

# Execute static analysis
$StaticReport = Perform-StaticAnalysis -FilePath $SamplePath
```

#### **Advanced Static Analysis Tasks**

1. **PE Structure Analysis**
   - Examine PE headers and sections
   - Analyze import/export tables
   - Identify packing or obfuscation
   - Check for digital signatures

2. **String Analysis**
   - Extract ASCII and Unicode strings
   - Identify URLs, IP addresses, file paths
   - Look for encryption keys or passwords
   - Find error messages and debug information

3. **Disassembly Analysis**
   - Load sample in IDA Pro or Ghidra
   - Identify main function and entry points
   - Analyze control flow and function calls
   - Look for anti-analysis techniques

### **Task 2: Dynamic Analysis (120 minutes)**

#### **Behavioral Analysis Setup**

```powershell
# Dynamic analysis automation script
param(
    [Parameter(Mandatory=$true)]
    [string]$SamplePath,

    [Parameter(Mandatory=$false)]
    [int]$AnalysisTime = 300  # 5 minutes
)

function Start-DynamicAnalysis {
    param($FilePath, $Duration)

    $FileName = Split-Path $FilePath -Leaf
    $ReportPath = "C:\MalwareAnalysis\Reports\$FileName-dynamic-report.txt"

    Write-Host "Starting dynamic analysis of $FileName for $Duration seconds..."

    # Start monitoring tools
    $ProcMon = Start-Process "C:\Tools\SysinternalsSuite\Procmon.exe" -ArgumentList "/BackingFile", "C:\MalwareAnalysis\Artifacts\$FileName-procmon.pml", "/Quiet" -PassThru
    $NetCapture = Start-Process "C:\Tools\Wireshark\dumpcap.exe" -ArgumentList "-i", "1", "-w", "C:\MalwareAnalysis\Artifacts\$FileName-network.pcap" -PassThru

    # Take baseline snapshot
    $BaselineProcesses = Get-Process | Select-Object Name, Id, CPU
    $BaselineServices = Get-Service | Where-Object {$_.Status -eq "Running"}
    $BaselineFiles = Get-ChildItem -Path "C:\Windows\System32" -File | Select-Object Name, LastWriteTime

    # Execute malware sample
    Write-Host "Executing malware sample..."
    $MalwareProcess = Start-Process $FilePath -PassThru -ErrorAction SilentlyContinue

    # Monitor for specified duration
    Start-Sleep -Seconds $Duration

    # Stop monitoring
    Stop-Process -Id $ProcMon.Id -Force -ErrorAction SilentlyContinue
    Stop-Process -Id $NetCapture.Id -Force -ErrorAction SilentlyContinue

    # Kill malware if still running
    if ($MalwareProcess -and !$MalwareProcess.HasExited) {
        Stop-Process -Id $MalwareProcess.Id -Force
    }

    # Analyze changes
    "=== DYNAMIC ANALYSIS REPORT ===" | Out-File $ReportPath
    "Sample: $FileName" | Out-File $ReportPath -Append
    "Analysis Duration: $Duration seconds" | Out-File $ReportPath -Append
    "Analysis Date: $(Get-Date)" | Out-File $ReportPath -Append
    "" | Out-File $ReportPath -Append

    # Process analysis
    "=== NEW PROCESSES ===" | Out-File $ReportPath -Append
    $NewProcesses = Get-Process | Where-Object {$_.Id -notin $BaselineProcesses.Id}
    $NewProcesses | Format-Table Name, Id, CPU | Out-File $ReportPath -Append

    # Service analysis
    "=== NEW SERVICES ===" | Out-File $ReportPath -Append
    $NewServices = Get-Service | Where-Object {$_.Status -eq "Running" -and $_.Name -notin $BaselineServices.Name}
    $NewServices | Format-Table Name, Status | Out-File $ReportPath -Append

    # File system changes
    "=== FILE SYSTEM CHANGES ===" | Out-File $ReportPath -Append
    $ModifiedFiles = Get-ChildItem -Path "C:\Windows\System32" -File | Where-Object {$_.LastWriteTime -gt (Get-Date).AddSeconds(-$Duration)}
    $ModifiedFiles | Format-Table Name, LastWriteTime | Out-File $ReportPath -Append

    Write-Host "Dynamic analysis completed. Report: $ReportPath"
    return $ReportPath
}

# Execute dynamic analysis
$DynamicReport = Start-DynamicAnalysis -FilePath $SamplePath -Duration $AnalysisTime
```

#### **Network Traffic Analysis**

```python
# Network traffic analysis for malware communication
import pyshark
import json
from datetime import datetime

class MalwareNetworkAnalysis:
    def __init__(self, pcap_file):
        self.pcap_file = pcap_file
        self.analysis_results = {}

    def analyze_dns_requests(self):
        """Analyze DNS requests for C2 domains"""
        capture = pyshark.FileCapture(self.pcap_file, display_filter='dns')

        dns_requests = []
        for packet in capture:
            if hasattr(packet, 'dns') and hasattr(packet.dns, 'qry_name'):
                dns_requests.append({
                    'timestamp': packet.sniff_time.isoformat(),
                    'domain': packet.dns.qry_name,
                    'query_type': packet.dns.qry_type,
                    'source_ip': packet.ip.src
                })

        # Identify suspicious domains
        suspicious_domains = []
        for request in dns_requests:
            domain = request['domain']
            if self.is_suspicious_domain(domain):
                suspicious_domains.append(request)

        self.analysis_results['dns'] = {
            'total_requests': len(dns_requests),
            'unique_domains': len(set([r['domain'] for r in dns_requests])),
            'suspicious_domains': suspicious_domains
        }

        return suspicious_domains

    def analyze_http_traffic(self):
        """Analyze HTTP traffic for C2 communication"""
        capture = pyshark.FileCapture(self.pcap_file, display_filter='http')

        http_requests = []
        for packet in capture:
            if hasattr(packet, 'http'):
                http_requests.append({
                    'timestamp': packet.sniff_time.isoformat(),
                    'method': getattr(packet.http, 'request_method', ''),
                    'host': getattr(packet.http, 'host', ''),
                    'uri': getattr(packet.http, 'request_uri', ''),
                    'user_agent': getattr(packet.http, 'user_agent', ''),
                    'source_ip': packet.ip.src,
                    'dest_ip': packet.ip.dst
                })

        # Identify C2 communication patterns
        c2_indicators = []
        for request in http_requests:
            if self.is_c2_communication(request):
                c2_indicators.append(request)

        self.analysis_results['http'] = {
            'total_requests': len(http_requests),
            'c2_indicators': c2_indicators
        }

        return c2_indicators

    def is_suspicious_domain(self, domain):
        """Check if domain appears suspicious"""
        suspicious_indicators = [
            len(domain) > 50,  # Very long domains
            domain.count('.') > 5,  # Many subdomains
            any(tld in domain for tld in ['.tk', '.ml', '.ga']),  # Suspicious TLDs
            sum(c.isdigit() for c in domain) > len(domain) * 0.3  # Many numbers
        ]

        return any(suspicious_indicators)

    def is_c2_communication(self, request):
        """Identify potential C2 communication"""
        c2_indicators = [
            'bot' in request.get('user_agent', '').lower(),
            request.get('uri', '').startswith('/api/'),
            len(request.get('uri', '')) > 100,  # Very long URIs
            request.get('method') == 'POST' and 'data=' in request.get('uri', '')
        ]

        return any(c2_indicators)

    def generate_iocs(self):
        """Generate IOCs from network analysis"""
        iocs = []

        # Domain IOCs
        if 'dns' in self.analysis_results:
            for domain_request in self.analysis_results['dns']['suspicious_domains']:
                iocs.append({
                    'type': 'domain',
                    'value': domain_request['domain'],
                    'description': 'Suspicious domain from malware DNS request',
                    'confidence': 'medium'
                })

        # IP IOCs
        if 'http' in self.analysis_results:
            for c2_request in self.analysis_results['http']['c2_indicators']:
                iocs.append({
                    'type': 'ip',
                    'value': c2_request['dest_ip'],
                    'description': 'C2 server IP from HTTP communication',
                    'confidence': 'high'
                })

        return iocs

# Example usage
def analyze_malware_network_traffic(pcap_file):
    analyzer = MalwareNetworkAnalysis(pcap_file)

    # Analyze DNS requests
    suspicious_dns = analyzer.analyze_dns_requests()
    print(f"Found {len(suspicious_dns)} suspicious DNS requests")

    # Analyze HTTP traffic
    c2_traffic = analyzer.analyze_http_traffic()
    print(f"Found {len(c2_traffic)} potential C2 communications")

    # Generate IOCs
    iocs = analyzer.generate_iocs()
    print(f"Generated {len(iocs)} IOCs")

    return analyzer.analysis_results, iocs

### **Task 3: Memory Analysis (90 minutes)**

#### **Memory Dump Analysis with Volatility**

```python
# Memory analysis automation for malware investigation
import subprocess
import json
from datetime import datetime

class MalwareMemoryAnalysis:
    def __init__(self, memory_dump_path):
        self.memory_dump = memory_dump_path
        self.profile = None
        self.analysis_results = {}

    def identify_profile(self):
        """Identify Volatility profile for memory dump"""
        cmd = f"volatility -f {self.memory_dump} imageinfo"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        # Parse profile from output
        if "Win10x64" in result.stdout:
            self.profile = "Win10x64_19041"
        elif "Win7SP1x64" in result.stdout:
            self.profile = "Win7SP1x64"
        else:
            self.profile = "Win10x64_19041"  # Default

        return self.profile

    def analyze_malicious_processes(self):
        """Identify malicious processes in memory"""
        cmd = f"volatility -f {self.memory_dump} --profile={self.profile} pslist"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        processes = self.parse_process_list(result.stdout)

        # Identify suspicious processes
        suspicious_processes = []
        for process in processes:
            if self.is_malicious_process(process):
                suspicious_processes.append(process)

        self.analysis_results['malicious_processes'] = suspicious_processes
        return suspicious_processes

    def extract_network_artifacts(self):
        """Extract network connections from memory"""
        cmd = f"volatility -f {self.memory_dump} --profile={self.profile} netscan"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        connections = self.parse_network_connections(result.stdout)

        # Filter for suspicious connections
        suspicious_connections = []
        for conn in connections:
            if self.is_suspicious_connection(conn):
                suspicious_connections.append(conn)

        self.analysis_results['network_connections'] = suspicious_connections
        return suspicious_connections

    def detect_code_injection(self):
        """Detect code injection techniques"""
        # Check for process hollowing
        cmd = f"volatility -f {self.memory_dump} --profile={self.profile} hollowfind"
        hollowing_result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        # Check for DLL injection
        cmd = f"volatility -f {self.memory_dump} --profile={self.profile} malfind"
        injection_result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        injection_artifacts = {
            'process_hollowing': self.parse_hollowing_results(hollowing_result.stdout),
            'dll_injection': self.parse_injection_results(injection_result.stdout)
        }

        self.analysis_results['code_injection'] = injection_artifacts
        return injection_artifacts

    def extract_malware_config(self):
        """Extract malware configuration from memory"""
        # Use YARA rules to find configuration patterns
        cmd = f"volatility -f {self.memory_dump} --profile={self.profile} yarascan -Y /path/to/malware_config.yar"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        config_data = self.parse_yara_results(result.stdout)

        self.analysis_results['malware_config'] = config_data
        return config_data

    def is_malicious_process(self, process):
        """Determine if process is likely malicious"""
        malicious_indicators = [
            process['name'].lower() in ['svchost.exe', 'explorer.exe'] and process['ppid'] != 'expected_parent',
            'temp' in process['path'].lower(),
            process['name'].endswith('.tmp.exe'),
            len(process['name']) == 8 and process['name'].isalnum()  # Random 8-char names
        ]

        return any(malicious_indicators)

# Memory analysis YARA rules
malware_config_yara = """
rule MalwareConfig {
    meta:
        description = "Detects malware configuration patterns"
        author = "GOAD-Blue Team"

    strings:
        $c2_1 = /https?:\/\/[a-zA-Z0-9.-]+\/[a-zA-Z0-9\/]+/ ascii
        $c2_2 = /[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}:[0-9]+/ ascii
        $key_1 = /[A-Za-z0-9+\/]{20,}={0,2}/ ascii  // Base64 patterns
        $mutex = /Global\\[A-Za-z0-9]{8,16}/ ascii

    condition:
        any of them
}

rule BankingTrojanConfig {
    meta:
        description = "Banking trojan configuration patterns"

    strings:
        $bank_1 = "bank" nocase
        $bank_2 = "login" nocase
        $bank_3 = "password" nocase
        $inject = "inject" nocase

    condition:
        2 of ($bank_*) and $inject
}
"""
```

### **Task 4: Reverse Engineering (120 minutes)**

#### **Disassembly Analysis**

```python
# Reverse engineering analysis framework
class ReverseEngineeringAnalysis:
    def __init__(self, sample_path):
        self.sample_path = sample_path
        self.analysis_results = {}

    def analyze_entry_point(self):
        """Analyze malware entry point and initialization"""
        # This would typically be done in IDA Pro or Ghidra
        entry_point_analysis = {
            'entry_point_address': '0x401000',
            'initialization_functions': [
                'sub_401020',  # Anti-debugging checks
                'sub_401050',  # Mutex creation
                'sub_401080',  # Configuration decryption
                'sub_4010B0'   # C2 initialization
            ],
            'anti_analysis_techniques': [
                'IsDebuggerPresent check',
                'Timing checks',
                'VM detection'
            ]
        }

        self.analysis_results['entry_point'] = entry_point_analysis
        return entry_point_analysis

    def analyze_string_obfuscation(self):
        """Analyze string obfuscation techniques"""
        obfuscation_analysis = {
            'encryption_method': 'XOR with rotating key',
            'key_derivation': 'Hardcoded 16-byte key',
            'encrypted_strings': [
                {'offset': '0x402000', 'decrypted': 'http://malware-c2.com/api'},
                {'offset': '0x402020', 'decrypted': 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run'},
                {'offset': '0x402040', 'decrypted': 'Global\\MalwareMutex2024'}
            ],
            'decryption_function': 'sub_401200'
        }

        self.analysis_results['string_obfuscation'] = obfuscation_analysis
        return obfuscation_analysis

    def analyze_api_resolution(self):
        """Analyze dynamic API resolution techniques"""
        api_analysis = {
            'resolution_method': 'Hash-based API resolution',
            'hash_algorithm': 'CRC32',
            'resolved_apis': [
                {'hash': '0x12345678', 'api': 'CreateFileA'},
                {'hash': '0x87654321', 'api': 'WriteFile'},
                {'hash': '0xABCDEF00', 'api': 'RegSetValueExA'},
                {'hash': '0x11223344', 'api': 'InternetOpenA'}
            ],
            'resolution_function': 'sub_401300'
        }

        self.analysis_results['api_resolution'] = api_analysis
        return api_analysis

    def analyze_persistence_mechanism(self):
        """Analyze malware persistence techniques"""
        persistence_analysis = {
            'techniques': [
                {
                    'method': 'Registry Run Key',
                    'location': 'HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run',
                    'value_name': 'SecurityUpdate',
                    'implementation_function': 'sub_401400'
                },
                {
                    'method': 'Scheduled Task',
                    'task_name': 'SystemMaintenance',
                    'trigger': 'Daily at startup',
                    'implementation_function': 'sub_401450'
                }
            ],
            'evasion_techniques': [
                'Legitimate-looking names',
                'System directory installation',
                'File attribute manipulation'
            ]
        }

        self.analysis_results['persistence'] = persistence_analysis
        return persistence_analysis

    def analyze_c2_communication(self):
        """Analyze command and control communication"""
        c2_analysis = {
            'protocol': 'HTTPS',
            'encryption': 'AES-256-CBC',
            'key_exchange': 'RSA-2048',
            'communication_pattern': {
                'beacon_interval': '300 seconds',
                'jitter': '10-30%',
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            'command_structure': {
                'header': '4 bytes - Command ID',
                'length': '4 bytes - Data length',
                'data': 'Variable - Encrypted payload',
                'checksum': '4 bytes - CRC32'
            },
            'supported_commands': [
                {'id': 0x01, 'name': 'HEARTBEAT', 'description': 'Keep-alive beacon'},
                {'id': 0x02, 'name': 'DOWNLOAD', 'description': 'Download and execute file'},
                {'id': 0x03, 'name': 'UPLOAD', 'description': 'Upload file to C2'},
                {'id': 0x04, 'name': 'SHELL', 'description': 'Execute shell command'},
                {'id': 0x05, 'name': 'UNINSTALL', 'description': 'Remove malware'}
            ]
        }

        self.analysis_results['c2_communication'] = c2_analysis
        return c2_analysis

# Example reverse engineering workflow
def perform_reverse_engineering(sample_path):
    """Perform comprehensive reverse engineering analysis"""

    analyzer = ReverseEngineeringAnalysis(sample_path)

    # Analyze entry point
    entry_analysis = analyzer.analyze_entry_point()
    print(f"Entry point analysis completed")

    # Analyze string obfuscation
    string_analysis = analyzer.analyze_string_obfuscation()
    print(f"String obfuscation analysis completed")

    # Analyze API resolution
    api_analysis = analyzer.analyze_api_resolution()
    print(f"API resolution analysis completed")

    # Analyze persistence
    persistence_analysis = analyzer.analyze_persistence_mechanism()
    print(f"Persistence analysis completed")

    # Analyze C2 communication
    c2_analysis = analyzer.analyze_c2_communication()
    print(f"C2 communication analysis completed")

    return analyzer.analysis_results
```

## 🔍 IOC Extraction and Validation

### **Automated IOC Extraction**

```python
# Comprehensive IOC extraction framework
class IOCExtractor:
    def __init__(self, analysis_results):
        self.analysis_results = analysis_results
        self.iocs = []

    def extract_file_iocs(self, sample_path):
        """Extract file-based IOCs"""
        import hashlib

        # Calculate file hashes
        with open(sample_path, 'rb') as f:
            file_data = f.read()

        file_iocs = [
            {
                'type': 'md5',
                'value': hashlib.md5(file_data).hexdigest(),
                'description': 'MD5 hash of malware sample',
                'confidence': 'high'
            },
            {
                'type': 'sha1',
                'value': hashlib.sha1(file_data).hexdigest(),
                'description': 'SHA1 hash of malware sample',
                'confidence': 'high'
            },
            {
                'type': 'sha256',
                'value': hashlib.sha256(file_data).hexdigest(),
                'description': 'SHA256 hash of malware sample',
                'confidence': 'high'
            }
        ]

        # Extract file size
        file_iocs.append({
            'type': 'file_size',
            'value': str(len(file_data)),
            'description': 'File size in bytes',
            'confidence': 'medium'
        })

        self.iocs.extend(file_iocs)
        return file_iocs

    def extract_network_iocs(self):
        """Extract network-based IOCs from analysis"""
        network_iocs = []

        # Extract from static analysis strings
        if 'static_analysis' in self.analysis_results:
            strings = self.analysis_results['static_analysis'].get('strings', [])
            for string in strings:
                # IP addresses
                if self.is_ip_address(string):
                    network_iocs.append({
                        'type': 'ip',
                        'value': string,
                        'description': 'IP address found in malware strings',
                        'confidence': 'medium'
                    })

                # Domains
                if self.is_domain(string):
                    network_iocs.append({
                        'type': 'domain',
                        'value': string,
                        'description': 'Domain found in malware strings',
                        'confidence': 'medium'
                    })

                # URLs
                if self.is_url(string):
                    network_iocs.append({
                        'type': 'url',
                        'value': string,
                        'description': 'URL found in malware strings',
                        'confidence': 'high'
                    })

        # Extract from network analysis
        if 'network_analysis' in self.analysis_results:
            dns_requests = self.analysis_results['network_analysis'].get('dns', {}).get('suspicious_domains', [])
            for request in dns_requests:
                network_iocs.append({
                    'type': 'domain',
                    'value': request['domain'],
                    'description': 'Suspicious domain from DNS requests',
                    'confidence': 'high'
                })

        self.iocs.extend(network_iocs)
        return network_iocs

    def extract_registry_iocs(self):
        """Extract registry-based IOCs"""
        registry_iocs = []

        # Extract from reverse engineering analysis
        if 'reverse_engineering' in self.analysis_results:
            persistence = self.analysis_results['reverse_engineering'].get('persistence', {})
            for technique in persistence.get('techniques', []):
                if technique['method'] == 'Registry Run Key':
                    registry_iocs.append({
                        'type': 'registry_key',
                        'value': f"{technique['location']}\\{technique['value_name']}",
                        'description': 'Registry persistence mechanism',
                        'confidence': 'high'
                    })

        self.iocs.extend(registry_iocs)
        return registry_iocs

    def extract_behavioral_iocs(self):
        """Extract behavioral IOCs"""
        behavioral_iocs = []

        # Extract from dynamic analysis
        if 'dynamic_analysis' in self.analysis_results:
            # Process creation patterns
            new_processes = self.analysis_results['dynamic_analysis'].get('new_processes', [])
            for process in new_processes:
                if self.is_suspicious_process_name(process['name']):
                    behavioral_iocs.append({
                        'type': 'process_name',
                        'value': process['name'],
                        'description': 'Suspicious process created by malware',
                        'confidence': 'medium'
                    })

            # File creation patterns
            created_files = self.analysis_results['dynamic_analysis'].get('created_files', [])
            for file_path in created_files:
                if self.is_suspicious_file_location(file_path):
                    behavioral_iocs.append({
                        'type': 'file_path',
                        'value': file_path,
                        'description': 'Suspicious file created by malware',
                        'confidence': 'medium'
                    })

        self.iocs.extend(behavioral_iocs)
        return behavioral_iocs

    def validate_iocs(self):
        """Validate extracted IOCs"""
        validated_iocs = []

        for ioc in self.iocs:
            validation_result = self.validate_single_ioc(ioc)
            if validation_result['valid']:
                ioc['validation'] = validation_result
                validated_iocs.append(ioc)

        return validated_iocs

    def validate_single_ioc(self, ioc):
        """Validate a single IOC"""
        validation = {
            'valid': True,
            'checks': [],
            'confidence_adjustment': 0
        }

        # Format validation
        if ioc['type'] == 'ip':
            if not self.is_valid_ip(ioc['value']):
                validation['valid'] = False
                validation['checks'].append('Invalid IP format')

        elif ioc['type'] == 'domain':
            if not self.is_valid_domain(ioc['value']):
                validation['valid'] = False
                validation['checks'].append('Invalid domain format')

        # Reputation checks (simulated)
        if ioc['type'] in ['ip', 'domain', 'url']:
            reputation = self.check_reputation(ioc['value'])
            if reputation == 'malicious':
                validation['confidence_adjustment'] = 20
                validation['checks'].append('Known malicious indicator')
            elif reputation == 'suspicious':
                validation['confidence_adjustment'] = 10
                validation['checks'].append('Suspicious reputation')

        return validation

    def generate_yara_rule(self, sample_name):
        """Generate YARA rule from extracted IOCs"""
        yara_rule = f"""
rule {sample_name.replace('.', '_').replace('-', '_')} {{
    meta:
        description = "Detects {sample_name} malware family"
        author = "GOAD-Blue Malware Analysis"
        date = "{datetime.now().strftime('%Y-%m-%d')}"
        hash = "{next((ioc['value'] for ioc in self.iocs if ioc['type'] == 'sha256'), 'N/A')}"

    strings:
"""

        # Add string-based IOCs
        string_counter = 1
        for ioc in self.iocs:
            if ioc['type'] in ['domain', 'url']:
                yara_rule += f'        $s{string_counter} = "{ioc["value"]}" ascii\n'
                string_counter += 1

        yara_rule += """
    condition:
        any of them
}
"""

        return yara_rule

    def export_iocs(self, format='json'):
        """Export IOCs in specified format"""
        if format == 'json':
            return json.dumps(self.iocs, indent=2)
        elif format == 'csv':
            import csv
            import io

            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=['type', 'value', 'description', 'confidence'])
            writer.writeheader()
            writer.writerows(self.iocs)
            return output.getvalue()
        elif format == 'stix':
            return self.export_stix()

    def is_ip_address(self, string):
        """Check if string is an IP address"""
        import re
        ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        return re.match(ip_pattern, string) is not None

    def is_domain(self, string):
        """Check if string is a domain"""
        import re
        domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        return re.match(domain_pattern, string) is not None and '.' in string

    def is_url(self, string):
        """Check if string is a URL"""
        return string.startswith(('http://', 'https://', 'ftp://'))

## 📊 Analysis Questions and Challenges

### **Technical Analysis Questions**

1. **Static Analysis Challenges**
   - What packing or obfuscation techniques are used?
   - How are strings encrypted/encoded in the sample?
   - What anti-analysis techniques are implemented?
   - What APIs are imported and what do they suggest about functionality?

2. **Dynamic Analysis Challenges**
   - What persistence mechanisms does the malware establish?
   - How does the malware communicate with C2 servers?
   - What data does the malware collect or exfiltrate?
   - How does the malware evade detection during execution?

3. **Reverse Engineering Challenges**
   - What is the malware's main functionality?
   - How is the configuration data stored and encrypted?
   - What command and control protocol is implemented?
   - How can the malware be effectively detected and prevented?

### **Investigation Scenarios**

```yaml
investigation_scenarios:
  scenario_1:
    title: "Banking Trojan Investigation"
    description: "Analyze a sophisticated banking trojan with web injection capabilities"
    challenges:
      - Decrypt configuration data
      - Identify targeted banks
      - Analyze web injection mechanism
      - Extract C2 communication protocol

    expected_findings:
      - List of targeted financial institutions
      - Web injection scripts and targets
      - C2 server infrastructure
      - Credential theft mechanisms

  scenario_2:
    title: "Ransomware Family Analysis"
    description: "Reverse engineer a new ransomware variant"
    challenges:
      - Identify encryption algorithm
      - Analyze file targeting logic
      - Extract ransom note templates
      - Understand payment mechanism

    expected_findings:
      - Encryption implementation details
      - File extension targeting
      - Ransom payment infrastructure
      - Potential decryption weaknesses

  scenario_3:
    title: "APT Backdoor Analysis"
    description: "Analyze a sophisticated backdoor used by APT groups"
    challenges:
      - Identify stealth techniques
      - Analyze command and control
      - Extract intelligence gathering capabilities
      - Understand persistence mechanisms

    expected_findings:
      - Advanced evasion techniques
      - Custom C2 protocol
      - Data collection and exfiltration
      - Attribution indicators
```

## 🛡️ Detection Rule Development

### **YARA Rule Creation Workshop**

```yaml
yara_rule_workshop:
  objective: "Create comprehensive YARA rules for malware detection"

  rule_categories:
    family_detection:
      description: "Rules to identify specific malware families"
      techniques:
        - String-based identification
        - Structural pattern matching
        - Behavioral pattern detection
        - Metadata correlation

    technique_detection:
      description: "Rules to detect specific attack techniques"
      techniques:
        - API usage patterns
        - Code injection techniques
        - Persistence mechanisms
        - Anti-analysis methods

    configuration_extraction:
      description: "Rules to extract malware configuration"
      techniques:
        - Encrypted string patterns
        - Configuration structure matching
        - Key derivation patterns
        - C2 server extraction

  example_rules:
    banking_trojan: |
      rule BankingTrojan_ConfigExtraction {
          meta:
              description = "Extracts banking trojan configuration"
              author = "GOAD-Blue Team"

          strings:
              $config_marker = { 43 4F 4E 46 49 47 }  // "CONFIG"
              $bank_pattern = /https?:\/\/[a-z0-9.-]+\.(?:bank|credit|financial)/
              $inject_script = /<script[^>]*>.*inject.*<\/script>/

          condition:
              $config_marker and ($bank_pattern or $inject_script)
      }

    ransomware_detection: |
      rule Ransomware_EncryptionLoop {
          meta:
              description = "Detects ransomware encryption patterns"

          strings:
              $crypto_api1 = "CryptGenRandom"
              $crypto_api2 = "CryptEncrypt"
              $file_enum = "FindFirstFileW"
              $ransom_ext = /\.[a-z]{3,8}$/ // Common ransom extensions

          condition:
              all of ($crypto_api*) and $file_enum and $ransom_ext
      }
```

### **Snort Rule Development**

```bash
# Network detection rules for malware C2 communication

# Banking trojan C2 detection
alert tcp $HOME_NET any -> $EXTERNAL_NET $HTTP_PORTS (
    msg:"Banking Trojan C2 Communication";
    flow:established,to_server;
    content:"POST";
    http_method;
    content:"/api/bot/";
    http_uri;
    content:"User-Agent|3A| BankBot/";
    http_header;
    classtype:trojan-activity;
    sid:1000001;
    rev:1;
)

# Ransomware C2 beacon detection
alert tcp $HOME_NET any -> $EXTERNAL_NET $HTTP_PORTS (
    msg:"Ransomware C2 Beacon";
    flow:established,to_server;
    content:"GET";
    http_method;
    content:"/gate.php";
    http_uri;
    content:"id=";
    http_uri;
    pcre:"/id=[a-f0-9]{32}/i";
    classtype:trojan-activity;
    sid:1000002;
    rev:1;
)

# DNS tunneling detection
alert udp $HOME_NET any -> $EXTERNAL_NET 53 (
    msg:"Possible DNS Tunneling";
    content:"|01 00 00 01 00 00 00 00 00 00|";
    offset:2;
    depth:10;
    byte_test:1,>,50,12;
    classtype:policy-violation;
    sid:1000003;
    rev:1;
)
```

## 📋 Scenario Assessment

### **Deliverables and Scoring**

```yaml
assessment_framework:
  deliverables:
    technical_report:
      weight: 35
      components:
        - Static analysis findings (10 points)
        - Dynamic analysis results (10 points)
        - Reverse engineering insights (10 points)
        - IOC extraction and validation (5 points)

      quality_criteria:
        - Accuracy of technical findings
        - Depth of analysis
        - Clarity of explanations
        - Evidence documentation

    ioc_package:
      weight: 25
      components:
        - File-based IOCs (5 points)
        - Network IOCs (10 points)
        - Behavioral IOCs (5 points)
        - IOC validation (5 points)

      quality_criteria:
        - IOC accuracy and relevance
        - Confidence scoring
        - Format compliance
        - Actionability

    detection_rules:
      weight: 25
      components:
        - YARA rules (10 points)
        - Snort rules (10 points)
        - Rule testing (5 points)

      quality_criteria:
        - Detection effectiveness
        - False positive rate
        - Performance impact
        - Rule documentation

    presentation:
      weight: 15
      components:
        - Executive summary (5 points)
        - Technical briefing (5 points)
        - Q&A handling (5 points)

      quality_criteria:
        - Communication clarity
        - Audience appropriateness
        - Technical accuracy
        - Professional delivery

  assessment_rubric:
    excellent: "90-100%"
    criteria:
      - Comprehensive analysis with deep insights
      - High-quality IOCs with validation
      - Effective detection rules with low false positives
      - Clear and professional presentation

    good: "80-89%"
    criteria:
      - Thorough analysis with good insights
      - Quality IOCs with some validation
      - Good detection rules with acceptable false positives
      - Clear presentation with minor issues

    satisfactory: "70-79%"
    criteria:
      - Basic analysis with some insights
      - Adequate IOCs with limited validation
      - Basic detection rules with some false positives
      - Adequate presentation with some clarity issues

    needs_improvement: "<70%"
    criteria:
      - Incomplete or inaccurate analysis
      - Poor quality or invalid IOCs
      - Ineffective detection rules
      - Unclear or unprofessional presentation
```

### **Practical Assessment Tasks**

1. **Timed Analysis Challenge** (2 hours)
   - Analyze unknown malware sample
   - Extract key IOCs
   - Create basic detection rule
   - Present findings to panel

2. **Collaborative Investigation** (3 hours)
   - Team-based malware family analysis
   - Divide analysis tasks among team members
   - Correlate findings and create comprehensive report
   - Present unified analysis to stakeholders

3. **Real-world Simulation** (4 hours)
   - Analyze malware from simulated incident
   - Coordinate with incident response team
   - Provide actionable intelligence
   - Support containment and remediation efforts

## 🎓 Learning Outcomes and Certification

### **Skill Validation**

```yaml
skill_validation:
  technical_competencies:
    static_analysis:
      - File format analysis proficiency
      - String and metadata extraction
      - Packer and obfuscation detection
      - Disassembly and code analysis

    dynamic_analysis:
      - Behavioral monitoring setup
      - Network traffic analysis
      - System change detection
      - Sandbox analysis interpretation

    reverse_engineering:
      - Disassembler tool proficiency
      - Algorithm understanding
      - Anti-analysis technique detection
      - Configuration extraction

    ioc_development:
      - IOC extraction techniques
      - Validation methodologies
      - Format standardization
      - Quality assessment

  professional_skills:
    analytical_thinking:
      - Problem decomposition
      - Pattern recognition
      - Hypothesis formation
      - Evidence correlation

    communication:
      - Technical report writing
      - Executive briefings
      - Peer collaboration
      - Knowledge transfer

    tool_proficiency:
      - FLARE-VM environment
      - Analysis tool mastery
      - Automation scripting
      - Custom tool development

certification_levels:
  associate_analyst:
    requirements:
      - Complete basic malware analysis training
      - Successfully analyze 5 samples
      - Pass practical examination
      - Demonstrate tool proficiency

    competencies:
      - Basic static and dynamic analysis
      - IOC extraction and validation
      - Report writing
      - Tool usage

  professional_analyst:
    requirements:
      - Hold Associate certification
      - Complete advanced training
      - Lead complex investigations
      - Mentor junior analysts

    competencies:
      - Advanced reverse engineering
      - Custom tool development
      - Threat intelligence integration
      - Training delivery

  expert_researcher:
    requirements:
      - Hold Professional certification
      - Contribute to research
      - Develop new techniques
      - Industry recognition

    competencies:
      - Original research
      - Tool and technique development
      - Community leadership
      - Knowledge advancement
```

---

!!! tip "Malware Analysis Best Practices"
    - Always work in isolated environments with proper snapshots
    - Document every step of the analysis process thoroughly
    - Validate findings with multiple analysis techniques
    - Share intelligence promptly with relevant teams
    - Continuously update tools and techniques

!!! warning "Safety and Legal Considerations"
    - Never analyze malware on production systems
    - Ensure proper network isolation during dynamic analysis
    - Follow organizational policies for malware handling
    - Be aware of legal implications of malware possession
    - Maintain proper chain of custody for evidence

!!! info "Advanced Learning Resources"
    - Practical Malware Analysis textbook
    - SANS FOR610 Reverse Engineering Malware course
    - Malware analysis blogs and research papers
    - Open source malware analysis tools
    - Community challenges and competitions
```
```