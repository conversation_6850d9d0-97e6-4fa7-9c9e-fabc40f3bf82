# Velociraptor

Velociraptor is an advanced digital forensics and incident response platform that provides comprehensive endpoint visibility and rapid response capabilities. In GOAD-Blue, Velociraptor serves as the primary endpoint detection and response (EDR) solution.

## 🎯 Overview

Velociraptor provides real-time endpoint monitoring, digital forensics capabilities, and automated incident response across the GOAD environment, enabling security teams to detect, investigate, and respond to threats effectively.

```mermaid
graph TB
    subgraph "🦖 Velociraptor Architecture"
        SERVER[🖥️ Velociraptor Server<br/>Central Management<br/>Web Interface]
        FRONTEND[🌐 Frontend<br/>API Gateway<br/>Load Balancer]
        DATASTORE[💾 Datastore<br/>Artifact Storage<br/>Query Results]
        NOTEBOOK[📓 Notebook Server<br/>Analysis Environment<br/>Collaborative Investigation]
    end
    
    subgraph "🤖 Client Agents"
        WIN_AGENTS[🪟 Windows Agents<br/>GOAD Domain Systems<br/>Real-time Monitoring]
        LINUX_AGENTS[🐧 Linux Agents<br/>Security Tools<br/>Management Systems]
        SENSORS[📡 Network Sensors<br/>Passive Collection<br/>Traffic Analysis]
    end
    
    subgraph "📊 Artifacts & Collections"
        SYSTEM_ARTIFACTS[🔧 System Artifacts<br/>Process Lists<br/>Network Connections<br/>File Operations]
        FORENSIC_ARTIFACTS[🕵️ Forensic Artifacts<br/>Memory Dumps<br/>Disk Images<br/>Timeline Analysis]
        CUSTOM_ARTIFACTS[⚙️ Custom Artifacts<br/>GOAD-specific<br/>Threat Hunting<br/>IOC Matching]
    end
    
    subgraph "🚨 Response Capabilities"
        HUNTING[🔍 Threat Hunting<br/>Proactive Detection<br/>IOC Searches<br/>Behavioral Analysis]
        COLLECTION[📦 Evidence Collection<br/>Automated Acquisition<br/>Chain of Custody<br/>Forensic Imaging]
        REMEDIATION[🛠️ Automated Remediation<br/>Threat Containment<br/>System Isolation<br/>Malware Removal]
    end
    
    subgraph "🎮 GOAD Environment"
        GOAD_DC[👑 Domain Controllers<br/>Authentication Logs<br/>Replication Events]
        GOAD_SERVERS[⚔️ Member Servers<br/>File Access<br/>Service Events]
        GOAD_WORKSTATIONS[🖥️ Workstations<br/>User Activity<br/>Process Execution]
    end
    
    GOAD_DC --> WIN_AGENTS
    GOAD_SERVERS --> WIN_AGENTS
    GOAD_WORKSTATIONS --> WIN_AGENTS
    
    WIN_AGENTS --> SERVER
    LINUX_AGENTS --> SERVER
    SENSORS --> SERVER
    
    SERVER --> FRONTEND
    SERVER --> DATASTORE
    SERVER --> NOTEBOOK
    
    SERVER --> SYSTEM_ARTIFACTS
    SERVER --> FORENSIC_ARTIFACTS
    SERVER --> CUSTOM_ARTIFACTS
    
    SYSTEM_ARTIFACTS --> HUNTING
    FORENSIC_ARTIFACTS --> COLLECTION
    CUSTOM_ARTIFACTS --> REMEDIATION
    
    classDef velociraptor fill:#2e7d32,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef agents fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef artifacts fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef response fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef goad fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class SERVER,FRONTEND,DATASTORE,NOTEBOOK velociraptor
    class WIN_AGENTS,LINUX_AGENTS,SENSORS agents
    class SYSTEM_ARTIFACTS,FORENSIC_ARTIFACTS,CUSTOM_ARTIFACTS artifacts
    class HUNTING,COLLECTION,REMEDIATION response
    class GOAD_DC,GOAD_SERVERS,GOAD_WORKSTATIONS goad
```

## 🚀 Installation and Setup

### **Automated Installation**

```bash
# Install Velociraptor using GOAD-Blue automation
python3 goad-blue.py install --component velociraptor --deployment-type server

# Configure Velociraptor for GOAD integration
python3 goad-blue.py configure --component velociraptor --enable-goad-monitoring

# Deploy agents to GOAD systems
python3 goad-blue.py deploy-agents --component velociraptor --target-network 192.168.56.0/24
```

### **Manual Installation**

```bash
# Download Velociraptor
wget https://github.com/Velocidex/velociraptor/releases/latest/download/velociraptor-linux-amd64
chmod +x velociraptor-linux-amd64
sudo mv velociraptor-linux-amd64 /usr/local/bin/velociraptor

# Generate server configuration
velociraptor config generate > server.config.yaml

# Initialize server
velociraptor --config server.config.yaml frontend --datastore_location /opt/velociraptor/

# Create admin user
velociraptor --config server.config.yaml user add admin --role administrator
```

### **Configuration Files**

```yaml
# server.config.yaml - Velociraptor server configuration
version: *******

Client:
  server_urls:
    - https://velociraptor.goad-blue.local:8000/
  ca_certificate: |
    -----BEGIN CERTIFICATE-----
    [CA Certificate Content]
    -----END CERTIFICATE-----
  
  nonce: "GOAD-Blue-Deployment"
  
  # Performance settings
  max_poll: 600
  max_poll_std: 30
  
Frontend:
  hostname: "velociraptor.goad-blue.local"
  bind_address: 0.0.0.0
  bind_port: 8000
  
  # SSL configuration
  certificate: |
    -----BEGIN CERTIFICATE-----
    [Server Certificate Content]
    -----END CERTIFICATE-----
  private_key: |
    -----BEGIN PRIVATE KEY-----
    [Private Key Content]
    -----END PRIVATE KEY-----
    
GUI:
  bind_address: 0.0.0.0
  bind_port: 8889
  
  # Authentication
  authenticator:
    type: Basic
    
Datastore:
  implementation: FileBaseDataStore
  location: /opt/velociraptor/datastore
  filestore_directory: /opt/velociraptor/filestore
  
# GOAD-Blue specific settings
defaults:
  hunt_expiry_hours: 168  # 7 days
  notebook_cell_timeout_min: 10
  
logging:
  output_directory: /var/log/velociraptor/
  separate_logs_per_component: true
```

## 📊 GOAD-Blue Artifacts

### **Windows Domain Monitoring**

```yaml
# Windows.GOAD.DomainActivity
name: Windows.GOAD.DomainActivity
description: Monitor domain-specific activities in GOAD environment

parameters:
  - name: DomainName
    default: "sevenkingdoms.local"
    
  - name: MonitoringPeriod
    default: "24h"

sources:
  - precondition: SELECT OS From info() where OS = 'windows'
    
    query: |
      -- Monitor domain authentication events
      SELECT 
        timestamp(epoch=EventTime) AS EventTime,
        Computer,
        EventData.TargetUserName as Username,
        EventData.TargetDomainName as Domain,
        EventData.IpAddress as SourceIP,
        EventData.LogonType as LogonType,
        EventData.AuthenticationPackageName as AuthPackage
      FROM watch_etw(guid="{*************-4994-A5BA-3E3B0328C30D}")
      WHERE EventID = 4624
        AND EventData.TargetDomainName = DomainName
        AND EventTime > now() - parse_duration(string=MonitoringPeriod)
      
      UNION ALL
      
      -- Monitor Kerberos ticket requests
      SELECT 
        timestamp(epoch=EventTime) AS EventTime,
        Computer,
        EventData.TargetUserName as Username,
        EventData.ServiceName as ServiceName,
        EventData.IpAddress as SourceIP,
        "Kerberos" as LogonType,
        EventData.TicketOptions as AuthPackage
      FROM watch_etw(guid="{*************-4994-A5BA-3E3B0328C30D}")
      WHERE EventID = 4769
        AND EventData.TargetDomainName = DomainName
```

### **Lateral Movement Detection**

```yaml
# Windows.GOAD.LateralMovement
name: Windows.GOAD.LateralMovement
description: Detect lateral movement activities in GOAD environment

parameters:
  - name: TimeWindow
    default: "1h"
    
  - name: ConnectionThreshold
    default: 3

sources:
  - query: |
      -- Detect multiple system connections from single source
      LET connections = SELECT 
        EventData.IpAddress as SourceIP,
        Computer as TargetHost,
        EventData.TargetUserName as Username,
        count() as ConnectionCount
      FROM watch_etw(guid="{*************-4994-A5BA-3E3B0328C30D}")
      WHERE EventID = 4624
        AND EventData.LogonType = 3  -- Network logon
        AND EventTime > now() - parse_duration(string=TimeWindow)
      GROUP BY SourceIP, Username
      
      SELECT * FROM connections
      WHERE ConnectionCount >= ConnectionThreshold
      
  - query: |
      -- Detect SMB admin share access
      SELECT 
        timestamp(epoch=EventTime) AS EventTime,
        Computer,
        EventData.SubjectUserName as Username,
        EventData.ObjectName as SharePath,
        EventData.AccessMask as AccessType
      FROM watch_etw(guid="{*************-4994-A5BA-3E3B0328C30D}")
      WHERE EventID = 5140
        AND (EventData.ObjectName =~ ".*ADMIN\\$.*"
          OR EventData.ObjectName =~ ".*C\\$.*")
```

### **Credential Harvesting Detection**

```yaml
# Windows.GOAD.CredentialHarvesting
name: Windows.GOAD.CredentialHarvesting
description: Detect credential harvesting attempts

sources:
  - query: |
      -- Monitor LSASS access
      SELECT 
        timestamp(epoch=EventTime) AS EventTime,
        Computer,
        EventData.Image as ProcessPath,
        EventData.TargetImage as TargetProcess,
        EventData.SourceProcessId as SourcePID,
        EventData.TargetProcessId as TargetPID
      FROM watch_etw(guid="{5770385F-C22A-43E0-BF4C-06F5698FFBD9}")
      WHERE EventID = 10  -- Process access
        AND EventData.TargetImage =~ ".*lsass.exe"
        AND EventData.GrantedAccess = "0x1010"
        
  - query: |
      -- Monitor suspicious process creation
      SELECT 
        timestamp(epoch=EventTime) AS EventTime,
        Computer,
        EventData.Image as ProcessPath,
        EventData.CommandLine as CommandLine,
        EventData.ParentImage as ParentProcess,
        EventData.User as User
      FROM watch_etw(guid="{5770385F-C22A-43E0-BF4C-06F5698FFBD9}")
      WHERE EventID = 1  -- Process creation
        AND (EventData.CommandLine =~ ".*sekurlsa.*"
          OR EventData.CommandLine =~ ".*mimikatz.*"
          OR EventData.CommandLine =~ ".*procdump.*lsass.*"
          OR EventData.CommandLine =~ ".*comsvcs.*MiniDump.*")
```

## 🔍 Threat Hunting Workflows

### **Interactive Threat Hunting**

```sql
-- VQL query for hunting suspicious processes
SELECT 
  Pid,
  Name,
  Exe,
  CommandLine,
  Username,
  CreateTime
FROM pslist()
WHERE (Name =~ "(?i)(powershell|cmd|wmic|rundll32)"
   AND CommandLine =~ "(?i)(bypass|hidden|encoded|download)")
   OR (Exe =~ "(?i)temp|public|programdata"
   AND NOT Exe =~ "(?i)microsoft|windows")
ORDER BY CreateTime DESC;

-- Hunt for persistence mechanisms
SELECT 
  Key,
  Name,
  Data.value as Value,
  Data.type as Type
FROM registry(
  globs="HKEY_LOCAL_MACHINE/SOFTWARE/Microsoft/Windows/CurrentVersion/Run/*",
  accessor="registry"
)
WHERE Value =~ "(?i)(temp|public|programdata|users)"
   OR Value =~ "(?i)(powershell|cmd|wscript|cscript)";

-- Network connection analysis
SELECT 
  Pid,
  Name,
  Laddr.IP as LocalIP,
  Laddr.Port as LocalPort,
  Raddr.IP as RemoteIP,
  Raddr.Port as RemotePort,
  Status
FROM netstat()
WHERE Status = "ESTABLISHED"
  AND NOT Raddr.IP =~ "^(10\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.|192\\.168\\.)"
  AND NOT Raddr.IP =~ "^(127\\.|169\\.254\\.)"
ORDER BY RemoteIP;
```

### **Automated Hunt Deployment**

```python
# Python script for automated hunt deployment
import velociraptor_api
import json
from datetime import datetime, timedelta

class GOADBlueHunter:
    def __init__(self, server_url, api_key):
        self.client = velociraptor_api.Client(server_url, api_key)
        
    def deploy_lateral_movement_hunt(self):
        """Deploy hunt for lateral movement detection"""
        
        hunt_config = {
            "artifacts": ["Windows.GOAD.LateralMovement"],
            "condition": 'label(client_id) =~ "GOAD"',
            "description": "Hunt for lateral movement in GOAD environment",
            "expires": int((datetime.now() + timedelta(days=7)).timestamp()),
            "parameters": {
                "TimeWindow": "24h",
                "ConnectionThreshold": 3
            }
        }
        
        hunt_id = self.client.create_hunt(**hunt_config)
        return hunt_id
        
    def deploy_credential_hunting(self):
        """Deploy hunt for credential harvesting"""
        
        hunt_config = {
            "artifacts": ["Windows.GOAD.CredentialHarvesting"],
            "condition": 'label(client_id) =~ "GOAD"',
            "description": "Hunt for credential harvesting attempts",
            "expires": int((datetime.now() + timedelta(days=7)).timestamp())
        }
        
        hunt_id = self.client.create_hunt(**hunt_config)
        return hunt_id
        
    def monitor_hunt_progress(self, hunt_id):
        """Monitor hunt execution progress"""
        
        while True:
            status = self.client.get_hunt_status(hunt_id)
            
            print(f"Hunt {hunt_id} Status:")
            print(f"  State: {status['state']}")
            print(f"  Total Clients: {status['total_clients_scheduled']}")
            print(f"  Completed: {status['total_clients_with_results']}")
            
            if status['state'] in ['STOPPED', 'COMPLETED']:
                break
                
            time.sleep(30)
            
    def analyze_hunt_results(self, hunt_id):
        """Analyze hunt results and generate report"""
        
        results = self.client.get_hunt_results(hunt_id)
        
        analysis = {
            "hunt_id": hunt_id,
            "total_results": len(results),
            "findings": [],
            "recommendations": []
        }
        
        # Process results based on artifact type
        for result in results:
            if result.get('artifact') == 'Windows.GOAD.LateralMovement':
                analysis['findings'].append({
                    "type": "lateral_movement",
                    "client": result['client_id'],
                    "details": result['data']
                })
                
        return analysis
```

## 🚨 Incident Response Capabilities

### **Automated Evidence Collection**

```yaml
# Server.Utils.IncidentResponse
name: Server.Utils.IncidentResponse
description: Automated incident response artifact

parameters:
  - name: ClientId
    description: Target client for evidence collection
    
  - name: IncidentType
    default: "malware"
    type: choices
    choices:
      - malware
      - lateral_movement
      - data_exfiltration
      - credential_theft

sources:
  - query: |
      -- Collect evidence based on incident type
      LET evidence_artifacts = SELECT * FROM switch(
        a={
          SELECT "Windows.Forensics.ProcessMemory" as Artifact
          FROM scope()
          WHERE IncidentType = "malware"
        },
        b={
          SELECT "Windows.EventLogs.RDPAuth" as Artifact
          FROM scope()
          WHERE IncidentType = "lateral_movement"
        },
        c={
          SELECT "Windows.Network.PacketCapture" as Artifact
          FROM scope()
          WHERE IncidentType = "data_exfiltration"
        },
        d={
          SELECT "Windows.Forensics.SAM" as Artifact
          FROM scope()
          WHERE IncidentType = "credential_theft"
        }
      )
      
      -- Execute collection
      SELECT collect_client(
        client_id=ClientId,
        artifacts=evidence_artifacts.Artifact
      ) as CollectionId
      FROM evidence_artifacts
```

### **System Isolation and Remediation**

```yaml
# Windows.Remediation.Quarantine
name: Windows.Remediation.Quarantine
description: Isolate compromised system from network

parameters:
  - name: IsolationLevel
    default: "network"
    type: choices
    choices:
      - network
      - process
      - full

sources:
  - precondition: SELECT OS From info() where OS = 'windows'
    
    query: |
      -- Network isolation
      LET network_isolation = SELECT * FROM execve(
        argv=["netsh", "advfirewall", "set", "allprofiles", "firewallpolicy", "blockinbound,blockoutbound"]
      ) WHERE IsolationLevel =~ "network|full"
      
      -- Process termination
      LET process_termination = SELECT * FROM foreach(
        row={
          SELECT Pid FROM pslist()
          WHERE Name =~ "(?i)(powershell|cmd|wmic|rundll32)"
            AND CommandLine =~ "(?i)(bypass|hidden|encoded)"
        },
        query={
          SELECT * FROM execve(argv=["taskkill", "/F", "/PID", str(str=Pid)])
        }
      ) WHERE IsolationLevel =~ "process|full"
      
      -- Create isolation log
      SELECT 
        timestamp(epoch=now()) as IsolationTime,
        IsolationLevel,
        network_isolation,
        process_termination
      FROM scope()
```

## 📊 Forensic Analysis

### **Memory Analysis**

```yaml
# Windows.Forensics.ProcessMemory
name: Windows.Forensics.ProcessMemory
description: Acquire process memory for analysis

parameters:
  - name: ProcessRegex
    default: ".*"
    
  - name: DumpPath
    default: "C:\\temp\\memory_dumps\\"

sources:
  - query: |
      -- Create dump directory
      LET _ = SELECT * FROM execve(
        argv=["mkdir", DumpPath]
      )
      
      -- Dump process memory
      SELECT 
        Pid,
        Name,
        Exe,
        DumpPath + Name + "_" + str(str=Pid) + ".dmp" as DumpFile,
        upload(
          file=process_dump(pid=Pid),
          name=Name + "_" + str(str=Pid) + ".dmp"
        ) as Upload
      FROM pslist()
      WHERE Name =~ ProcessRegex
        AND Pid > 0
```

### **Timeline Analysis**

```yaml
# Windows.Forensics.Timeline
name: Windows.Forensics.Timeline
description: Create forensic timeline of system activities

parameters:
  - name: StartTime
    type: timestamp
    
  - name: EndTime
    type: timestamp

sources:
  - query: |
      -- File system timeline
      LET file_events = SELECT 
        "file_creation" as EventType,
        Mtime as Timestamp,
        FullPath as Details,
        "filesystem" as Source
      FROM glob(globs="C:\\**", accessor="file")
      WHERE Mtime > StartTime AND Mtime < EndTime
      
      -- Process creation timeline
      LET process_events = SELECT 
        "process_creation" as EventType,
        timestamp(epoch=EventTime) as Timestamp,
        EventData.Image + " " + EventData.CommandLine as Details,
        "sysmon" as Source
      FROM watch_etw(guid="{5770385F-C22A-43E0-BF4C-06F5698FFBD9}")
      WHERE EventID = 1
        AND EventTime > timestamp(time=StartTime)
        AND EventTime < timestamp(time=EndTime)
      
      -- Network connection timeline
      LET network_events = SELECT 
        "network_connection" as EventType,
        timestamp(epoch=EventTime) as Timestamp,
        EventData.Image + " -> " + EventData.DestinationIp + ":" + str(str=EventData.DestinationPort) as Details,
        "sysmon" as Source
      FROM watch_etw(guid="{5770385F-C22A-43E0-BF4C-06F5698FFBD9}")
      WHERE EventID = 3
        AND EventTime > timestamp(time=StartTime)
        AND EventTime < timestamp(time=EndTime)
      
      -- Combine all events
      SELECT * FROM chain(
        a=file_events,
        b=process_events,
        c=network_events
      )
      ORDER BY Timestamp
```

## 🔗 Integration and Automation

### **SIEM Integration**

```python
# Velociraptor to SIEM integration
import requests
import json
from velociraptor_api import Client

class VelociraptorSIEMIntegration:
    def __init__(self, velo_client, siem_config):
        self.velo_client = velo_client
        self.siem_config = siem_config
        
    def forward_hunt_results(self, hunt_id):
        """Forward hunt results to SIEM"""
        
        results = self.velo_client.get_hunt_results(hunt_id)
        
        for result in results:
            siem_event = {
                "timestamp": result.get("timestamp"),
                "source": "velociraptor",
                "hunt_id": hunt_id,
                "client_id": result.get("client_id"),
                "artifact": result.get("artifact"),
                "data": result.get("data"),
                "severity": self.calculate_severity(result)
            }
            
            self.send_to_siem(siem_event)
            
    def send_to_siem(self, event):
        """Send event to SIEM platform"""
        
        if self.siem_config["type"] == "splunk":
            self.send_to_splunk(event)
        elif self.siem_config["type"] == "elastic":
            self.send_to_elastic(event)
            
    def send_to_splunk(self, event):
        """Send event to Splunk HEC"""
        
        hec_url = f"{self.siem_config['url']}/services/collector/event"
        headers = {
            "Authorization": f"Splunk {self.siem_config['token']}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "event": event,
            "index": "goad_blue_endpoint",
            "sourcetype": "velociraptor"
        }
        
        response = requests.post(hec_url, headers=headers, json=payload)
        return response.status_code == 200
        
    def calculate_severity(self, result):
        """Calculate event severity based on artifact and data"""
        
        high_severity_artifacts = [
            "Windows.GOAD.CredentialHarvesting",
            "Windows.GOAD.LateralMovement",
            "Windows.Forensics.ProcessMemory"
        ]
        
        if result.get("artifact") in high_severity_artifacts:
            return "high"
        else:
            return "medium"
```

## 🎓 Training Scenarios

### **Digital Forensics Training**

1. **Memory Analysis**
   - Process memory acquisition
   - Malware analysis in memory
   - Credential extraction techniques

2. **Disk Forensics**
   - File system analysis
   - Deleted file recovery
   - Timeline reconstruction

3. **Network Forensics**
   - Packet capture analysis
   - Network connection tracking
   - Data exfiltration detection

### **Incident Response Training**

1. **Evidence Collection**
   - Automated collection workflows
   - Chain of custody procedures
   - Forensic imaging techniques

2. **Threat Containment**
   - System isolation procedures
   - Malware removal techniques
   - Network segmentation

3. **Investigation Techniques**
   - Artifact analysis
   - IOC development
   - Attribution analysis

---

!!! tip "Velociraptor Best Practices"
    - Deploy agents across all critical systems
    - Regularly update artifacts and hunting rules
    - Implement proper access controls and authentication
    - Monitor agent performance and connectivity
    - Practice incident response procedures regularly

!!! warning "Performance Considerations"
    Velociraptor can be resource-intensive during large-scale collections. Monitor system performance and implement appropriate throttling.

!!! info "Learning Resources"
    - Velociraptor documentation and training materials
    - GOAD-Blue specific artifacts and use cases
    - Digital forensics and incident response best practices
    - Community-contributed artifacts and hunting rules
