---
# Configure Windows for FLARE-VM malware analysis

- name: Disable Windows Defender Real-time Protection
  win_shell: |
    Set-MpPreference -DisableRealtimeMonitoring $true
    Set-MpPreference -DisableBehaviorMonitoring $true
    Set-MpPreference -DisableBlockAtFirstSeen $true
    Set-MpPreference -DisableIOAVProtection $true
    Set-MpPreference -DisablePrivacyMode $true
    Set-MpPreference -DisableScriptScanning $true
    Set-MpPreference -DisableArchiveScanning $true
    Set-MpPreference -DisableIntrusionPreventionSystem $true
    Set-MpPreference -DisableRemovableDriveScanning $true
    Set-MpPreference -DisableEmailScanning $true
  ignore_errors: yes

- name: Disable Windows Defender via Registry
  win_regedit:
    path: "{{ item.path }}"
    name: "{{ item.name }}"
    data: "{{ item.data }}"
    type: "{{ item.type }}"
  loop:
    - path: HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender
      name: DisableAntiSpyware
      data: 1
      type: dword
    - path: HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection
      name: DisableRealtimeMonitoring
      data: 1
      type: dword
    - path: HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection
      name: DisableBehaviorMonitoring
      data: 1
      type: dword
  ignore_errors: yes

- name: Disable Windows Update
  win_regedit:
    path: HKLM:\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU
    name: "{{ item.name }}"
    data: "{{ item.data }}"
    type: dword
  loop:
    - name: NoAutoUpdate
      data: 1
    - name: AUOptions
      data: 1

- name: Disable Windows Firewall
  win_shell: |
    netsh advfirewall set allprofiles state off
  ignore_errors: yes

- name: Configure Windows for malware analysis
  win_regedit:
    path: "{{ item.path }}"
    name: "{{ item.name }}"
    data: "{{ item.data }}"
    type: "{{ item.type }}"
  loop:
    # Disable UAC
    - path: HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System
      name: EnableLUA
      data: 0
      type: dword
    # Disable Windows Error Reporting
    - path: HKLM:\SOFTWARE\Microsoft\Windows\Windows Error Reporting
      name: Disabled
      data: 1
      type: dword
    # Disable automatic sample submission
    - path: HKLM:\SOFTWARE\Microsoft\Windows\Windows Error Reporting
      name: DontSendAdditionalData
      data: 1
      type: dword
    # Show hidden files and extensions
    - path: HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced
      name: Hidden
      data: 1
      type: dword
    - path: HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced
      name: HideFileExt
      data: 0
      type: dword
    - path: HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced
      name: ShowSuperHidden
      data: 1
      type: dword

- name: Disable Windows SmartScreen
  win_regedit:
    path: "{{ item.path }}"
    name: "{{ item.name }}"
    data: "{{ item.data }}"
    type: "{{ item.type }}"
  loop:
    - path: HKLM:\SOFTWARE\Policies\Microsoft\Windows\System
      name: EnableSmartScreen
      data: 0
      type: dword
    - path: HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer
      name: SmartScreenEnabled
      data: "Off"
      type: string

- name: Configure Internet Explorer for malware analysis
  win_regedit:
    path: "{{ item.path }}"
    name: "{{ item.name }}"
    data: "{{ item.data }}"
    type: dword
  loop:
    # Disable protected mode
    - path: HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings\Zones\3
      name: 2500
      data: 3
    # Disable download blocking
    - path: HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings\Zones\3
      name: 1803
      data: 0
    # Allow active content
    - path: HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings\Zones\3
      name: 1200
      data: 0

- name: Create analysis user account
  win_user:
    name: "{{ flare_vm.analysis_user }}"
    password: "{{ flare_vm.analysis_password }}"
    groups:
      - Administrators
      - Users
    state: present

- name: Configure automatic logon for analysis user
  win_regedit:
    path: HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon
    name: "{{ item.name }}"
    data: "{{ item.data }}"
    type: string
  loop:
    - name: AutoAdminLogon
      data: "1"
    - name: DefaultUserName
      data: "{{ flare_vm.analysis_user }}"
    - name: DefaultPassword
      data: "{{ flare_vm.analysis_password }}"

- name: Disable Windows services for analysis
  win_service:
    name: "{{ item }}"
    state: stopped
    start_mode: disabled
  loop:
    - WinDefend
    - wscsvc
    - WerSvc
    - Spooler
    - BITS
    - wuauserv
  ignore_errors: yes

- name: Configure network isolation
  win_shell: |
    # Create fake DNS entries to prevent real network communication
    Add-Content -Path "C:\Windows\System32\drivers\etc\hosts" -Value "127.0.0.1 google.com"
    Add-Content -Path "C:\Windows\System32\drivers\etc\hosts" -Value "127.0.0.1 microsoft.com"
    Add-Content -Path "C:\Windows\System32\drivers\etc\hosts" -Value "127.0.0.1 update.microsoft.com"
    Add-Content -Path "C:\Windows\System32\drivers\etc\hosts" -Value "127.0.0.1 windowsupdate.microsoft.com"
  when: flare_vm.network_isolation | default(true)

- name: Configure Windows time to prevent time-based evasion
  win_shell: |
    # Set time to a fixed date for analysis
    Set-Date -Date "2024-01-15 12:00:00"
    # Disable time synchronization
    w32tm /config /manualpeerlist:" " /syncfromflags:manual
    Stop-Service w32time
    Set-Service w32time -StartupType Disabled
  when: flare_vm.fix_time | default(true)

- name: Create desktop shortcuts for analysis tools
  win_shortcut:
    src: "{{ item.src }}"
    dest: "C:\\Users\\<USER>\\Desktop\\{{ item.name }}.lnk"
    icon: "{{ item.icon | default(item.src) }}"
  loop:
    - name: "Command Prompt"
      src: "C:\\Windows\\System32\\cmd.exe"
    - name: "PowerShell"
      src: "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe"
    - name: "Registry Editor"
      src: "C:\\Windows\\regedit.exe"
    - name: "Event Viewer"
      src: "C:\\Windows\\System32\\eventvwr.exe"

- name: Configure Windows performance for analysis
  win_regedit:
    path: "{{ item.path }}"
    name: "{{ item.name }}"
    data: "{{ item.data }}"
    type: dword
  loop:
    # Disable visual effects
    - path: HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects
      name: VisualFXSetting
      data: 2
    # Disable animations
    - path: HKCU:\Control Panel\Desktop\WindowMetrics
      name: MinAnimate
      data: 0
    # Disable menu delays
    - path: HKCU:\Control Panel\Desktop
      name: MenuShowDelay
      data: 0

- name: Restart Windows to apply configuration changes
  win_reboot:
    reboot_timeout: 600
    msg: "Restarting to apply FLARE-VM configuration changes"
