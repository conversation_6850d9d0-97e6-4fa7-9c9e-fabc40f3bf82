# VMware vSphere outputs for GOAD-Blue

output "resource_pool_id" {
  description = "ID of the GOAD-Blue resource pool"
  value       = vsphere_resource_pool.goad_blue_pool.id
}

output "folder_path" {
  description = "Path of the GOAD-Blue VM folder"
  value       = vsphere_folder.goad_blue_folder.path
}

output "vm_info" {
  description = "Information about deployed VMs"
  value = {
    siem = var.siem_enabled ? {
      name = vsphere_virtual_machine.siem[0].name
      ip   = cidrhost(var.siem_subnet, 10)
      type = var.siem_type
    } : null
    
    security_onion = var.security_onion_enabled ? {
      name = vsphere_virtual_machine.security_onion[0].name
      ip   = cidrhost(var.monitoring_subnet, 10)
    } : null
    
    malcolm = var.malcolm_enabled ? {
      name = vsphere_virtual_machine.malcolm[0].name
      ip   = cidrhost(var.monitoring_subnet, 11)
    } : null
    
    velociraptor = var.velociraptor_enabled ? {
      name = vsphere_virtual_machine.velociraptor[0].name
      ip   = cidrhost(var.monitoring_subnet, 20)
    } : null
    
    misp = var.misp_enabled ? {
      name = vsphere_virtual_machine.misp[0].name
      ip   = cidrhost(var.analysis_subnet, 30)
    } : null
    
    flare_vm = var.flare_vm_enabled ? {
      name = vsphere_virtual_machine.flare_vm[0].name
      ip   = cidrhost(var.analysis_subnet, 40)
    } : null
  }
}

output "access_urls" {
  description = "Access URLs for GOAD-Blue components"
  value = {
    siem = var.siem_enabled ? (
      var.siem_type == "splunk" ? 
        "https://${cidrhost(var.siem_subnet, 10)}:8000" : 
        "https://${cidrhost(var.siem_subnet, 10)}:5601"
    ) : null
    
    security_onion = var.security_onion_enabled ? 
      "https://${cidrhost(var.monitoring_subnet, 10)}" : null
    
    malcolm = var.malcolm_enabled ? 
      "https://${cidrhost(var.monitoring_subnet, 11)}" : null
    
    velociraptor = var.velociraptor_enabled ? 
      "https://${cidrhost(var.monitoring_subnet, 20)}:8889" : null
    
    misp = var.misp_enabled ? 
      "https://${cidrhost(var.analysis_subnet, 30)}" : null
    
    flare_vm = var.flare_vm_enabled ? 
      "rdp://${cidrhost(var.analysis_subnet, 40)}:3389" : null
  }
}

output "network_info" {
  description = "Network configuration information"
  value = {
    base_cidr = var.base_cidr
    subnets = {
      siem        = var.siem_subnet
      monitoring  = var.monitoring_subnet
      red_team    = var.red_team_subnet
      analysis    = var.analysis_subnet
    }
  }
}
