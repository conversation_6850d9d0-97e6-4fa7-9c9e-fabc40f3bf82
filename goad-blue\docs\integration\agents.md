# Agent Deployment

This guide covers the deployment of monitoring agents to GOAD systems for comprehensive visibility and detection capabilities.

## 🎯 Agent Architecture

```mermaid
graph TB
    subgraph "🎮 GOAD Systems"
        DC[🏰 Domain Controller<br/>Windows Server 2019]
        SRV[⚔️ Member Server<br/>Windows Server 2019]
        WS[🌹 Workstation<br/>Windows 10]
    end
    
    subgraph "🤖 Monitoring Agents"
        SYSMON[👁️ Sysmon<br/>Process & Network Monitoring]
        SPLUNK_UF[📦 Splunk Universal Forwarder<br/>Log Collection & Forwarding]
        VELO_AGENT[🦖 Velociraptor Agent<br/>Endpoint Detection & Response]
        WINLOGBEAT[🥁 Winlogbeat<br/>Windows Event Log Shipping]
    end
    
    subgraph "🛡️ GOAD-Blue Platform"
        SPLUNK[📊 Splunk Enterprise<br/>**************]
        ELASTIC[🔍 Elasticsearch<br/>**************]
        VELO_SRV[🦖 Velociraptor Server<br/>**************]
        SO[🧅 Security Onion<br/>**************]
    end
    
    %% Agent Installation
    DC --> SYSMON
    DC --> SPLUNK_UF
    DC --> VELO_AGENT
    DC --> WINLOGBEAT
    
    SRV --> SYSMON
    SRV --> SPLUNK_UF
    SRV --> VELO_AGENT
    SRV --> WINLOGBEAT
    
    WS --> SYSMON
    WS --> SPLUNK_UF
    WS --> VELO_AGENT
    WS --> WINLOGBEAT
    
    %% Data Flow
    SYSMON --> SPLUNK_UF
    SPLUNK_UF --> SPLUNK
    WINLOGBEAT --> ELASTIC
    VELO_AGENT --> VELO_SRV
    
    %% Network Monitoring
    DC -.->|Network Traffic| SO
    SRV -.->|Network Traffic| SO
    WS -.->|Network Traffic| SO
    
    classDef goad fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef agents fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef platform fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    
    class DC,SRV,WS goad
    class SYSMON,SPLUNK_UF,VELO_AGENT,WINLOGBEAT agents
    class SPLUNK,ELASTIC,VELO_SRV,SO platform
```

## 📦 Agent Components

### **Sysmon (System Monitor)**

**Purpose:** Advanced system activity monitoring for Windows
**Data Collected:**
- Process creation and termination
- Network connections
- File creation and modification
- Registry changes
- Image/DLL loads
- Process access events

**Installation:**
```powershell
# Download Sysmon
Invoke-WebRequest -Uri "https://download.sysinternals.com/files/Sysmon.zip" -OutFile "C:\temp\Sysmon.zip"
Expand-Archive -Path "C:\temp\Sysmon.zip" -DestinationPath "C:\temp\Sysmon"

# Install with GOAD-Blue configuration
C:\temp\Sysmon\Sysmon64.exe -accepteula -i C:\goad-blue\configs\sysmon-config.xml

# Verify installation
Get-Service Sysmon64
Get-WinEvent -LogName "Microsoft-Windows-Sysmon/Operational" -MaxEvents 5
```

### **Splunk Universal Forwarder**

**Purpose:** Log collection and forwarding to Splunk Enterprise
**Data Collected:**
- Windows Event Logs (Security, System, Application)
- Sysmon events
- Custom application logs
- Performance counters

**Installation:**
```powershell
# Download Universal Forwarder
$splunkUrl = "https://download.splunk.com/products/universalforwarder/releases/9.1.2/windows/splunkforwarder-9.1.2-b6b9c8185839-x64-release.msi"
Invoke-WebRequest -Uri $splunkUrl -OutFile "C:\temp\splunkforwarder.msi"

# Silent installation with configuration
msiexec /i C:\temp\splunkforwarder.msi `
    RECEIVING_INDEXER="**************:9997" `
    WINEVENTLOG_APP_ENABLE=1 `
    WINEVENTLOG_SEC_ENABLE=1 `
    WINEVENTLOG_SYS_ENABLE=1 `
    AGREETOLICENSE=Yes `
    /quiet

# Configure additional inputs
$inputsConf = @"
[WinEventLog://Application]
disabled = false
index = goad_blue_windows
sourcetype = WinEventLog:Application

[WinEventLog://Security]
disabled = false
index = goad_blue_windows
sourcetype = WinEventLog:Security

[WinEventLog://System]
disabled = false
index = goad_blue_windows
sourcetype = WinEventLog:System

[WinEventLog://Microsoft-Windows-Sysmon/Operational]
disabled = false
index = goad_blue_windows
sourcetype = WinEventLog:Sysmon
renderXml = true

[WinEventLog://Microsoft-Windows-PowerShell/Operational]
disabled = false
index = goad_blue_windows
sourcetype = WinEventLog:PowerShell

[WinEventLog://Windows PowerShell]
disabled = false
index = goad_blue_windows
sourcetype = WinEventLog:PowerShell
"@

$inputsConf | Out-File -FilePath "C:\Program Files\SplunkUniversalForwarder\etc\system\local\inputs.conf" -Encoding UTF8

# Start and enable service
Start-Service SplunkForwarder
Set-Service SplunkForwarder -StartupType Automatic
```

### **Velociraptor Agent**

**Purpose:** Endpoint detection, response, and forensic artifact collection
**Capabilities:**
- Live response and investigation
- Artifact collection (files, registry, memory)
- Hunt execution across endpoints
- Forensic timeline analysis

**Installation:**
```powershell
# Download Velociraptor client
$veloUrl = "https://github.com/Velocidex/velociraptor/releases/download/v0.7.0/velociraptor-v0.7.0-windows-amd64.exe"
Invoke-WebRequest -Uri $veloUrl -OutFile "C:\temp\velociraptor.exe"

# Create client configuration
$clientConfig = @"
Client:
  server_urls:
    - https://**************:8000/
  ca_certificate: |
    -----BEGIN CERTIFICATE-----
    [CA Certificate Content]
    -----END CERTIFICATE-----
  nonce: [Client Nonce]
  writeback_darwin: /opt/velociraptor/velociraptor.writeback.yaml
  writeback_linux: /opt/velociraptor/velociraptor.writeback.yaml
  writeback_windows: C:\Program Files\Velociraptor\velociraptor.writeback.yaml
  max_poll: 60
  max_poll_std: 5
"@

$clientConfig | Out-File -FilePath "C:\temp\velociraptor-client.yaml" -Encoding UTF8

# Install as Windows service
C:\temp\velociraptor.exe --config C:\temp\velociraptor-client.yaml service install

# Start service
Start-Service Velociraptor
Set-Service Velociraptor -StartupType Automatic
```

### **Winlogbeat (Elastic Stack)**

**Purpose:** Windows Event Log shipping to Elasticsearch
**Data Collected:**
- Windows Event Logs with ECS field mapping
- Custom event log channels
- Event filtering and enrichment

**Installation:**
```powershell
# Download Winlogbeat
$beatUrl = "https://artifacts.elastic.co/downloads/beats/winlogbeat/winlogbeat-8.10.0-windows-x86_64.zip"
Invoke-WebRequest -Uri $beatUrl -OutFile "C:\temp\winlogbeat.zip"
Expand-Archive -Path "C:\temp\winlogbeat.zip" -DestinationPath "C:\Program Files\"

# Create configuration
$winlogbeatConfig = @"
winlogbeat.event_logs:
  - name: Application
    index: goad-blue-windows
  - name: Security
    index: goad-blue-windows
  - name: System
    index: goad-blue-windows
  - name: Microsoft-Windows-Sysmon/Operational
    index: goad-blue-windows
  - name: Microsoft-Windows-PowerShell/Operational
    index: goad-blue-windows

output.elasticsearch:
  hosts: ["**************:9200"]
  index: "goad-blue-windows-%{+yyyy.MM.dd}"

setup.template.name: "goad-blue-windows"
setup.template.pattern: "goad-blue-windows-*"

processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
  - add_docker_metadata: ~
  - add_kubernetes_metadata: ~

logging.level: info
logging.to_files: true
logging.files:
  path: C:\ProgramData\winlogbeat\Logs
  name: winlogbeat
  keepfiles: 7
  permissions: 0644
"@

$winlogbeatConfig | Out-File -FilePath "C:\Program Files\winlogbeat-8.10.0-windows-x86_64\winlogbeat.yml" -Encoding UTF8

# Install as service
cd "C:\Program Files\winlogbeat-8.10.0-windows-x86_64"
.\winlogbeat.exe --environment=windows_service --path.home="C:\Program Files\winlogbeat-8.10.0-windows-x86_64" --path.config="C:\Program Files\winlogbeat-8.10.0-windows-x86_64" --path.data="C:\ProgramData\winlogbeat" --path.logs="C:\ProgramData\winlogbeat\logs" --service.name=winlogbeat install

# Start service
Start-Service winlogbeat
Set-Service winlogbeat -StartupType Automatic
```

## 🚀 Automated Deployment

### **PowerShell Deployment Script**

```powershell
# deploy-goad-agents.ps1
param(
    [Parameter(Mandatory=$true)]
    [string[]]$TargetHosts,
    
    [Parameter(Mandatory=$true)]
    [string]$DomainAdmin,
    
    [Parameter(Mandatory=$true)]
    [string]$DomainPassword,
    
    [string]$SplunkIndexer = "**************",
    [string]$ElasticsearchHost = "**************",
    [string]$VelociraptorServer = "**************"
)

function Deploy-Sysmon {
    param([string]$ComputerName)
    
    Write-Host "Deploying Sysmon to $ComputerName..."
    
    $session = New-PSSession -ComputerName $ComputerName -Credential $credential
    
    Invoke-Command -Session $session -ScriptBlock {
        # Download Sysmon
        if (!(Test-Path "C:\temp")) { New-Item -Path "C:\temp" -ItemType Directory }
        
        Invoke-WebRequest -Uri "https://download.sysinternals.com/files/Sysmon.zip" -OutFile "C:\temp\Sysmon.zip"
        Expand-Archive -Path "C:\temp\Sysmon.zip" -DestinationPath "C:\temp\Sysmon" -Force
        
        # Install Sysmon
        & "C:\temp\Sysmon\Sysmon64.exe" -accepteula -i
        
        Write-Host "Sysmon installed successfully on $env:COMPUTERNAME"
    }
    
    Remove-PSSession $session
}

function Deploy-SplunkUF {
    param([string]$ComputerName, [string]$IndexerHost)
    
    Write-Host "Deploying Splunk Universal Forwarder to $ComputerName..."
    
    $session = New-PSSession -ComputerName $ComputerName -Credential $credential
    
    Invoke-Command -Session $session -ArgumentList $IndexerHost -ScriptBlock {
        param($IndexerHost)
        
        # Download Splunk UF
        $splunkUrl = "https://download.splunk.com/products/universalforwarder/releases/9.1.2/windows/splunkforwarder-9.1.2-b6b9c8185839-x64-release.msi"
        Invoke-WebRequest -Uri $splunkUrl -OutFile "C:\temp\splunkforwarder.msi"
        
        # Install Splunk UF
        $arguments = @(
            "/i", "C:\temp\splunkforwarder.msi",
            "RECEIVING_INDEXER=$IndexerHost:9997",
            "WINEVENTLOG_APP_ENABLE=1",
            "WINEVENTLOG_SEC_ENABLE=1",
            "WINEVENTLOG_SYS_ENABLE=1",
            "AGREETOLICENSE=Yes",
            "/quiet"
        )
        
        Start-Process msiexec -ArgumentList $arguments -Wait
        
        # Configure inputs
        $inputsConf = @"
[WinEventLog://Application]
disabled = false
index = goad_blue_windows

[WinEventLog://Security]
disabled = false
index = goad_blue_windows

[WinEventLog://System]
disabled = false
index = goad_blue_windows

[WinEventLog://Microsoft-Windows-Sysmon/Operational]
disabled = false
index = goad_blue_windows
renderXml = true
"@
        
        $inputsConf | Out-File -FilePath "C:\Program Files\SplunkUniversalForwarder\etc\system\local\inputs.conf" -Encoding UTF8
        
        # Start service
        Start-Service SplunkForwarder
        Set-Service SplunkForwarder -StartupType Automatic
        
        Write-Host "Splunk Universal Forwarder installed successfully on $env:COMPUTERNAME"
    }
    
    Remove-PSSession $session
}

function Deploy-VelociraptorAgent {
    param([string]$ComputerName, [string]$ServerHost)
    
    Write-Host "Deploying Velociraptor Agent to $ComputerName..."
    
    $session = New-PSSession -ComputerName $ComputerName -Credential $credential
    
    Invoke-Command -Session $session -ArgumentList $ServerHost -ScriptBlock {
        param($ServerHost)
        
        # Download Velociraptor
        $veloUrl = "https://github.com/Velocidex/velociraptor/releases/download/v0.7.0/velociraptor-v0.7.0-windows-amd64.exe"
        Invoke-WebRequest -Uri $veloUrl -OutFile "C:\temp\velociraptor.exe"
        
        # Create basic client config (would need actual server cert in production)
        $clientConfig = @"
Client:
  server_urls:
    - https://$ServerHost:8000/
  max_poll: 60
"@
        
        $clientConfig | Out-File -FilePath "C:\temp\velociraptor-client.yaml" -Encoding UTF8
        
        # Install as service
        & "C:\temp\velociraptor.exe" --config "C:\temp\velociraptor-client.yaml" service install
        
        # Start service
        Start-Service Velociraptor
        Set-Service Velociraptor -StartupType Automatic
        
        Write-Host "Velociraptor Agent installed successfully on $env:COMPUTERNAME"
    }
    
    Remove-PSSession $session
}

# Main deployment logic
$securePassword = ConvertTo-SecureString $DomainPassword -AsPlainText -Force
$credential = New-Object System.Management.Automation.PSCredential($DomainAdmin, $securePassword)

foreach ($host in $TargetHosts) {
    Write-Host "Starting deployment to $host..."
    
    try {
        # Test connectivity
        if (Test-Connection -ComputerName $host -Count 2 -Quiet) {
            Deploy-Sysmon -ComputerName $host
            Deploy-SplunkUF -ComputerName $host -IndexerHost $SplunkIndexer
            Deploy-VelociraptorAgent -ComputerName $host -ServerHost $VelociraptorServer
            
            Write-Host "Deployment completed successfully for $host" -ForegroundColor Green
        } else {
            Write-Warning "Cannot reach $host - skipping deployment"
        }
    } catch {
        Write-Error "Deployment failed for $host`: $($_.Exception.Message)"
    }
}

Write-Host "Agent deployment completed for all hosts."
```

### **Ansible Deployment Playbook**

```yaml
# ansible/playbooks/deploy-goad-agents.yml
---
- name: Deploy GOAD-Blue Monitoring Agents
  hosts: goad_windows
  gather_facts: true
  vars:
    splunk_indexer: "**************"
    elasticsearch_host: "**************"
    velociraptor_server: "**************"
    
  tasks:
    - name: Create temp directory
      win_file:
        path: C:\temp
        state: directory
        
    - name: Download Sysmon
      win_get_url:
        url: https://download.sysinternals.com/files/Sysmon.zip
        dest: C:\temp\Sysmon.zip
        
    - name: Extract Sysmon
      win_unzip:
        src: C:\temp\Sysmon.zip
        dest: C:\temp\Sysmon
        
    - name: Install Sysmon
      win_command: C:\temp\Sysmon\Sysmon64.exe -accepteula -i
      
    - name: Download Splunk Universal Forwarder
      win_get_url:
        url: https://download.splunk.com/products/universalforwarder/releases/9.1.2/windows/splunkforwarder-9.1.2-b6b9c8185839-x64-release.msi
        dest: C:\temp\splunkforwarder.msi
        
    - name: Install Splunk Universal Forwarder
      win_package:
        path: C:\temp\splunkforwarder.msi
        arguments:
          - RECEIVING_INDEXER={{ splunk_indexer }}:9997
          - WINEVENTLOG_APP_ENABLE=1
          - WINEVENTLOG_SEC_ENABLE=1
          - WINEVENTLOG_SYS_ENABLE=1
          - AGREETOLICENSE=Yes
        state: present
        
    - name: Configure Splunk inputs
      win_copy:
        content: |
          [WinEventLog://Application]
          disabled = false
          index = goad_blue_windows
          
          [WinEventLog://Security]
          disabled = false
          index = goad_blue_windows
          
          [WinEventLog://System]
          disabled = false
          index = goad_blue_windows
          
          [WinEventLog://Microsoft-Windows-Sysmon/Operational]
          disabled = false
          index = goad_blue_windows
          renderXml = true
        dest: C:\Program Files\SplunkUniversalForwarder\etc\system\local\inputs.conf
        
    - name: Start Splunk Universal Forwarder
      win_service:
        name: SplunkForwarder
        state: started
        start_mode: auto
        
    - name: Download Velociraptor
      win_get_url:
        url: https://github.com/Velocidex/velociraptor/releases/download/v0.7.0/velociraptor-v0.7.0-windows-amd64.exe
        dest: C:\temp\velociraptor.exe
        
    - name: Create Velociraptor client config
      win_copy:
        content: |
          Client:
            server_urls:
              - https://{{ velociraptor_server }}:8000/
            max_poll: 60
        dest: C:\temp\velociraptor-client.yaml
        
    - name: Install Velociraptor as service
      win_command: C:\temp\velociraptor.exe --config C:\temp\velociraptor-client.yaml service install
      
    - name: Start Velociraptor service
      win_service:
        name: Velociraptor
        state: started
        start_mode: auto
```

### **Bash Deployment Script (Linux)**

```bash
#!/bin/bash
# deploy-goad-agents.sh

GOAD_HOSTS=(
    "*************:kingslanding"
    "*************:winterfell"
    "*************:meereen"
    "*************:castelblack"
    "*************:braavos"
    "*************:tyrell"
)

DOMAIN_ADMIN="sevenkingdoms\\administrator"
DOMAIN_PASSWORD="Password123!"
SPLUNK_INDEXER="**************"
VELOCIRAPTOR_SERVER="**************"

deploy_to_host() {
    local host_ip=$1
    local hostname=$2
    
    echo "Deploying agents to $hostname ($host_ip)..."
    
    # Test connectivity
    if ! ping -c 2 "$host_ip" > /dev/null 2>&1; then
        echo "ERROR: Cannot reach $host_ip"
        return 1
    fi
    
    # Deploy via WinRM
    winrs -r:"$host_ip" -u:"$DOMAIN_ADMIN" -p:"$DOMAIN_PASSWORD" "
        powershell.exe -Command \"
            # Create temp directory
            if (!(Test-Path 'C:\\temp')) { New-Item -Path 'C:\\temp' -ItemType Directory }
            
            # Download and install Sysmon
            Invoke-WebRequest -Uri 'https://download.sysinternals.com/files/Sysmon.zip' -OutFile 'C:\\temp\\Sysmon.zip'
            Expand-Archive -Path 'C:\\temp\\Sysmon.zip' -DestinationPath 'C:\\temp\\Sysmon' -Force
            C:\\temp\\Sysmon\\Sysmon64.exe -accepteula -i
            
            # Download and install Splunk UF
            Invoke-WebRequest -Uri 'https://download.splunk.com/products/universalforwarder/releases/9.1.2/windows/splunkforwarder-9.1.2-b6b9c8185839-x64-release.msi' -OutFile 'C:\\temp\\splunkforwarder.msi'
            msiexec /i C:\\temp\\splunkforwarder.msi RECEIVING_INDEXER=$SPLUNK_INDEXER:9997 WINEVENTLOG_APP_ENABLE=1 WINEVENTLOG_SEC_ENABLE=1 WINEVENTLOG_SYS_ENABLE=1 AGREETOLICENSE=Yes /quiet
            
            # Configure Splunk inputs
            \\\$inputsConf = @\\\"
[WinEventLog://Application]
disabled = false
index = goad_blue_windows

[WinEventLog://Security]
disabled = false
index = goad_blue_windows

[WinEventLog://System]
disabled = false
index = goad_blue_windows

[WinEventLog://Microsoft-Windows-Sysmon/Operational]
disabled = false
index = goad_blue_windows
renderXml = true
\\\"@
            
            \\\$inputsConf | Out-File -FilePath 'C:\\Program Files\\SplunkUniversalForwarder\\etc\\system\\local\\inputs.conf' -Encoding UTF8
            
            # Start Splunk service
            Start-Service SplunkForwarder
            Set-Service SplunkForwarder -StartupType Automatic
            
            Write-Host 'Agent deployment completed on $hostname'
        \"
    "
    
    echo "Deployment completed for $hostname"
}

# Main deployment loop
for host_entry in "${GOAD_HOSTS[@]}"; do
    IFS=':' read -r host_ip hostname <<< "$host_entry"
    deploy_to_host "$host_ip" "$hostname"
done

echo "All agent deployments completed."
```

## 🔍 Verification and Testing

### **Agent Status Check**

```powershell
# Check agent status on GOAD systems
function Test-AgentStatus {
    param([string[]]$ComputerNames)
    
    foreach ($computer in $ComputerNames) {
        Write-Host "Checking agent status on $computer..." -ForegroundColor Yellow
        
        $session = New-PSSession -ComputerName $computer
        
        Invoke-Command -Session $session -ScriptBlock {
            # Check Sysmon
            $sysmon = Get-Service -Name "Sysmon64" -ErrorAction SilentlyContinue
            if ($sysmon) {
                Write-Host "  ✓ Sysmon: $($sysmon.Status)" -ForegroundColor Green
            } else {
                Write-Host "  ✗ Sysmon: Not installed" -ForegroundColor Red
            }
            
            # Check Splunk UF
            $splunk = Get-Service -Name "SplunkForwarder" -ErrorAction SilentlyContinue
            if ($splunk) {
                Write-Host "  ✓ Splunk UF: $($splunk.Status)" -ForegroundColor Green
            } else {
                Write-Host "  ✗ Splunk UF: Not installed" -ForegroundColor Red
            }
            
            # Check Velociraptor
            $velo = Get-Service -Name "Velociraptor" -ErrorAction SilentlyContinue
            if ($velo) {
                Write-Host "  ✓ Velociraptor: $($velo.Status)" -ForegroundColor Green
            } else {
                Write-Host "  ✗ Velociraptor: Not installed" -ForegroundColor Red
            }
            
            # Check event log generation
            $recentEvents = Get-WinEvent -LogName "Microsoft-Windows-Sysmon/Operational" -MaxEvents 5 -ErrorAction SilentlyContinue
            if ($recentEvents) {
                Write-Host "  ✓ Sysmon events: $($recentEvents.Count) recent events" -ForegroundColor Green
            } else {
                Write-Host "  ⚠ Sysmon events: No recent events" -ForegroundColor Yellow
            }
        }
        
        Remove-PSSession $session
    }
}

# Test all GOAD systems
$goadHosts = @("kingslanding", "winterfell", "meereen", "castelblack", "braavos", "tyrell")
Test-AgentStatus -ComputerNames $goadHosts
```

### **Data Flow Validation**

```bash
# Validate data flow to GOAD-Blue platform
python3 goad-blue.py validate_data_flow --source goad --timeout 300

# Check specific data sources
python3 goad-blue.py check_splunk_data --index goad_blue_windows --timeframe "last 1 hour"
python3 goad-blue.py check_velociraptor_agents --status online
python3 goad-blue.py check_sysmon_events --host kingslanding --count 100
```

---

!!! success "Agent Deployment Complete"
    Once agents are deployed and verified, your GOAD environment will have comprehensive monitoring coverage for blue team operations.

!!! tip "Performance Monitoring"
    Monitor the performance impact of agents on GOAD systems, especially during intensive red team exercises. Adjust collection intervals if needed.

!!! warning "Security Considerations"
    - Use dedicated service accounts for agent deployment
    - Implement proper certificate validation for Velociraptor
    - Monitor agent communications for any anomalies
    - Regularly update agent versions for security patches
