---
# GOAD-Blue Security Onion Deployment Playbook
# Deploys and configures Security Onion for network security monitoring

- name: Deploy Security Onion
  hosts: security_onion_servers
  become: yes
  vars_files:
    - vars/goad-blue-config.yml
    - vars/security-onion-config.yml
  
  pre_tasks:
    - name: Validate Security Onion requirements
      assert:
        that:
          - ansible_memtotal_mb >= 16384
          - ansible_processor_vcpus >= 4
        fail_msg: "Security Onion requires minimum 16GB RAM and 4 CPU cores"

    - name: Check network interfaces
      assert:
        that:
          - ansible_interfaces | length >= 2
        fail_msg: "Security Onion requires at least 2 network interfaces"

  tasks:
    - name: Download Security Onion ISO
      get_url:
        url: "{{ security_onion.download_url }}"
        dest: "/tmp/securityonion-{{ security_onion.version }}.iso"
        mode: '0644'
      when: security_onion.install_method == 'iso'

    - name: Install Security Onion via script
      include_tasks: tasks/security_onion/install_so.yml
      when: security_onion.install_method == 'script'

    - name: Configure Security Onion interfaces
      include_tasks: tasks/security_onion/configure_interfaces.yml

    - name: Run Security Onion setup
      include_tasks: tasks/security_onion/run_setup.yml

    - name: Configure Suricata rules
      include_tasks: tasks/security_onion/configure_suricata.yml

    - name: Configure Zeek scripts
      include_tasks: tasks/security_onion/configure_zeek.yml

    - name: Configure Elasticsearch
      include_tasks: tasks/security_onion/configure_elasticsearch.yml

    - name: Configure Kibana dashboards
      include_tasks: tasks/security_onion/configure_kibana.yml

    - name: Configure GOAD-Blue integration
      include_tasks: tasks/security_onion/configure_goad_integration.yml

    - name: Start Security Onion services
      include_tasks: tasks/security_onion/start_services.yml

    - name: Configure monitoring rules for GOAD
      include_tasks: tasks/security_onion/configure_goad_rules.yml

    - name: Validate Security Onion deployment
      include_tasks: tasks/security_onion/validate_deployment.yml

  post_tasks:
    - name: Generate Security Onion access information
      template:
        src: templates/security_onion_access_info.j2
        dest: "{{ playbook_dir }}/output/security_onion_access.yml"
      delegate_to: localhost

    - name: Display Security Onion deployment summary
      debug:
        msg: |
          Security Onion Deployment Complete:
          Version: {{ security_onion.version }}
          Management URL: https://{{ ansible_default_ipv4.address }}
          Kibana URL: https://{{ ansible_default_ipv4.address }}/kibana
          Admin User: {{ security_onion.admin_user }}
          Monitoring Interfaces: {{ security_onion.monitor_interfaces | join(', ') }}

- name: Configure Security Onion Sensors
  hosts: security_onion_sensors
  become: yes
  vars_files:
    - vars/goad-blue-config.yml
    - vars/security-onion-config.yml
  
  tasks:
    - name: Install Security Onion sensor
      include_tasks: tasks/security_onion/install_sensor.yml

    - name: Configure sensor interfaces
      include_tasks: tasks/security_onion/configure_sensor_interfaces.yml

    - name: Connect sensor to manager
      include_tasks: tasks/security_onion/connect_sensor.yml

    - name: Configure GOAD network monitoring
      include_tasks: tasks/security_onion/configure_goad_monitoring.yml

    - name: Start sensor services
      include_tasks: tasks/security_onion/start_sensor_services.yml

    - name: Validate sensor deployment
      include_tasks: tasks/security_onion/validate_sensor.yml
