---
# GOAD-Blue Velociraptor Deployment Playbook
# Deploys and configures Velociraptor for endpoint detection and response

- name: Deploy Velociraptor Server
  hosts: velociraptor_servers
  become: yes
  vars_files:
    - vars/goad-blue-config.yml
    - vars/velociraptor-config.yml
  
  pre_tasks:
    - name: Validate Velociraptor requirements
      assert:
        that:
          - ansible_memtotal_mb >= 4096
          - ansible_processor_vcpus >= 2
        fail_msg: "Velociraptor requires minimum 4GB RAM and 2 CPU cores"

  tasks:
    - name: Download Velociraptor binary
      get_url:
        url: "{{ velociraptor.download_url }}"
        dest: "/usr/local/bin/velociraptor"
        mode: '0755'
        owner: root
        group: root

    - name: Create Velociraptor user
      user:
        name: velociraptor
        system: yes
        shell: /bin/false
        home: /opt/velociraptor
        create_home: yes

    - name: Create Velociraptor directories
      file:
        path: "{{ item }}"
        state: directory
        owner: velociraptor
        group: velociraptor
        mode: '0755'
      loop:
        - /etc/velociraptor
        - /var/log/velociraptor
        - /opt/velociraptor/datastore
        - /opt/velociraptor/filestore

    - name: Generate Velociraptor server configuration
      include_tasks: tasks/velociraptor/generate_server_config.yml

    - name: Configure Velociraptor SSL certificates
      include_tasks: tasks/velociraptor/configure_ssl.yml

    - name: Create Velociraptor systemd service
      include_tasks: tasks/velociraptor/create_service.yml

    - name: Configure Velociraptor for GOAD integration
      include_tasks: tasks/velociraptor/configure_goad_integration.yml

    - name: Start Velociraptor server
      systemd:
        name: velociraptor
        state: started
        enabled: yes

    - name: Configure Velociraptor users
      include_tasks: tasks/velociraptor/configure_users.yml

    - name: Import GOAD-Blue artifacts
      include_tasks: tasks/velociraptor/import_artifacts.yml

    - name: Configure hunt templates
      include_tasks: tasks/velociraptor/configure_hunt_templates.yml

    - name: Configure SIEM integration
      include_tasks: tasks/velociraptor/configure_siem_integration.yml

    - name: Validate Velociraptor server
      include_tasks: tasks/velociraptor/validate_server.yml

  post_tasks:
    - name: Generate Velociraptor access information
      template:
        src: templates/velociraptor_access_info.j2
        dest: "{{ playbook_dir }}/output/velociraptor_access.yml"
      delegate_to: localhost

    - name: Display Velociraptor deployment summary
      debug:
        msg: |
          Velociraptor Server Deployment Complete:
          Version: {{ velociraptor.version }}
          Web Interface: https://{{ ansible_default_ipv4.address }}:8889
          Admin User: {{ velociraptor.admin_user }}
          Client Download: https://{{ ansible_default_ipv4.address }}:8000

- name: Deploy Velociraptor Clients to GOAD
  hosts: goad_windows
  vars_files:
    - vars/goad-blue-config.yml
    - vars/velociraptor-config.yml
  
  tasks:
    - name: Download Velociraptor client
      win_get_url:
        url: "{{ velociraptor_server_url }}/downloads/velociraptor.exe"
        dest: "C:\\temp\\velociraptor.exe"

    - name: Download client configuration
      win_get_url:
        url: "{{ velociraptor_server_url }}/downloads/client.config.yaml"
        dest: "C:\\temp\\client.config.yaml"

    - name: Install Velociraptor client service
      win_shell: |
        C:\temp\velociraptor.exe service install --config C:\temp\client.config.yaml
      register: install_result

    - name: Start Velociraptor client service
      win_service:
        name: Velociraptor
        state: started
        start_mode: auto

    - name: Validate client connectivity
      include_tasks: tasks/velociraptor/validate_client_windows.yml

- name: Deploy Velociraptor Clients to Linux GOAD
  hosts: goad_linux
  become: yes
  vars_files:
    - vars/goad-blue-config.yml
    - vars/velociraptor-config.yml
  
  tasks:
    - name: Download Velociraptor client
      get_url:
        url: "{{ velociraptor_server_url }}/downloads/velociraptor-linux"
        dest: "/tmp/velociraptor"
        mode: '0755'

    - name: Download client configuration
      get_url:
        url: "{{ velociraptor_server_url }}/downloads/client.config.yaml"
        dest: "/tmp/client.config.yaml"

    - name: Install Velociraptor client service
      shell: |
        /tmp/velociraptor service install --config /tmp/client.config.yaml
      register: install_result

    - name: Start Velociraptor client service
      systemd:
        name: velociraptor_client
        state: started
        enabled: yes

    - name: Validate client connectivity
      include_tasks: tasks/velociraptor/validate_client_linux.yml
