[default]
; Note: ansible_host *MUST* be an IPv4 address or setting things like DNS
; servers will break.
; ------------------------------------------------
; ninja.local
; ------------------------------------------------
dc01 ansible_host={{ip_range}}.30 dns_domain=dc01 dns_domain=dc02 dict_key=dc01
dc02 ansible_host={{ip_range}}.31 dns_domain=dc02 dict_key=dc02
srv01 ansible_host={{ip_range}}.32 dns_domain=dc02 dict_key=srv01
srv02 ansible_host={{ip_range}}.33 dns_domain=dc02 dict_key=srv02
srv03 ansible_host={{ip_range}}.34 dns_domain=dc02 dict_key=srv03


[all:vars]
force_dns_server=no
dns_server={{ip_range}}.254

dns_server_forwarder={{ip_range}}.254

ansible_user=localuser
ansible_password=password