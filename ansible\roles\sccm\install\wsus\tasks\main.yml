# Step 11 – Install WSUS Role
# --------------------------------------------------------------

- name: install WSUS
  win_feature:
    name:
      - UpdateServices-Services
      - UpdateServices-DB
    include_management_tools: yes
  register: wsus_install_result

- name: "Reboot and wait for the AD system to restart"
  win_reboot:
    reboot_timeout: 900
    post_reboot_delay: 100
  when: wsus_install_result.changed

- name: create directory to store updates
  ansible.windows.win_file:
    path: C:\sources\WSUS
    state: directory

# %COMPUTERNAME% won't work. Neither $env:computername.
# So, I put a hardcoded server name for now.
- name: WSUS Post-installation (setup the link with the SQL Server database and a directory to store updates)
  win_shell: .\wsusutil.exe PostInstall SQL_INSTANCE_NAME={{sccm_mssql_server}} CONTENT_DIR=C:\sources\WSUS
  args:
    chdir: C:\Program Files\Update Services\Tools
  vars:
    ansible_become: yes
    ansible_become_method: runas
    domain_name: "{{domain}}"
    ansible_become_user: "{{domain_username}}"
    ansible_become_password: "{{domain_password}}"