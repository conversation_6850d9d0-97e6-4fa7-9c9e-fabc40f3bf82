# Proxmox Module Outputs

# Splunk Enterprise Outputs
output "splunk_vm_id" {
  description = "Splunk Enterprise VM ID"
  value       = var.deploy_splunk ? proxmox_vm_qemu.splunk_enterprise[0].vmid : null
}

output "splunk_vm_name" {
  description = "Splunk Enterprise VM name"
  value       = var.deploy_splunk ? proxmox_vm_qemu.splunk_enterprise[0].name : null
}

output "splunk_ip_address" {
  description = "Splunk Enterprise IP address"
  value       = var.deploy_splunk ? var.network_config.splunk.ip : null
}

output "splunk_web_url" {
  description = "Splunk Web interface URL"
  value       = var.deploy_splunk ? "https://${var.network_config.splunk.ip}:8000" : null
}

# Security Onion Outputs
output "security_onion_manager_vm_id" {
  description = "Security Onion Manager VM ID"
  value       = var.deploy_security_onion ? proxmox_vm_qemu.security_onion_manager[0].vmid : null
}

output "security_onion_manager_vm_name" {
  description = "Security Onion Manager VM name"
  value       = var.deploy_security_onion ? proxmox_vm_qemu.security_onion_manager[0].name : null
}

output "security_onion_manager_ip" {
  description = "Security Onion Manager IP address"
  value       = var.deploy_security_onion ? var.network_config.security_onion_manager.ip : null
}

output "security_onion_web_url" {
  description = "Security Onion web interface URL"
  value       = var.deploy_security_onion ? "https://${var.network_config.security_onion_manager.ip}" : null
}

output "security_onion_sensor_vm_ids" {
  description = "Security Onion Sensor VM IDs"
  value       = var.deploy_security_onion ? proxmox_vm_qemu.security_onion_sensor[*].vmid : []
}

output "security_onion_sensor_vm_names" {
  description = "Security Onion Sensor VM names"
  value       = var.deploy_security_onion ? proxmox_vm_qemu.security_onion_sensor[*].name : []
}

output "security_onion_sensor_ips" {
  description = "Security Onion Sensor IP addresses"
  value = var.deploy_security_onion ? [
    for i in range(var.security_onion_sensor_count) :
    cidrhost(var.network_config.goad_blue_cidr, 71 + i)
  ] : []
}

# Velociraptor Outputs
output "velociraptor_vm_id" {
  description = "Velociraptor Server VM ID"
  value       = var.deploy_velociraptor ? proxmox_vm_qemu.velociraptor_server[0].vmid : null
}

output "velociraptor_vm_name" {
  description = "Velociraptor Server VM name"
  value       = var.deploy_velociraptor ? proxmox_vm_qemu.velociraptor_server[0].name : null
}

output "velociraptor_ip_address" {
  description = "Velociraptor Server IP address"
  value       = var.deploy_velociraptor ? var.network_config.velociraptor.ip : null
}

output "velociraptor_web_url" {
  description = "Velociraptor web interface URL"
  value       = var.deploy_velociraptor ? "https://${var.network_config.velociraptor.ip}:8889" : null
}

# MISP Outputs
output "misp_vm_id" {
  description = "MISP Server VM ID"
  value       = var.deploy_misp ? proxmox_vm_qemu.misp_server[0].vmid : null
}

output "misp_vm_name" {
  description = "MISP Server VM name"
  value       = var.deploy_misp ? proxmox_vm_qemu.misp_server[0].name : null
}

output "misp_ip_address" {
  description = "MISP Server IP address"
  value       = var.deploy_misp ? var.network_config.misp.ip : null
}

output "misp_web_url" {
  description = "MISP web interface URL"
  value       = var.deploy_misp ? "https://${var.network_config.misp.ip}" : null
}

# Network Configuration Outputs
output "goad_blue_network" {
  description = "GOAD-Blue network configuration"
  value = {
    cidr    = var.network_config.goad_blue_cidr
    bridge  = var.network_config.goad_blue_bridge
    vlan    = var.network_config.goad_blue_vlan
    gateway = var.network_config.gateway
  }
}

output "goad_network" {
  description = "GOAD network configuration"
  value = {
    cidr   = var.network_config.goad_cidr
    bridge = var.network_config.goad_bridge
    vlan   = var.network_config.goad_vlan
  }
}

# VM Summary
output "vm_summary" {
  description = "Summary of all deployed VMs"
  value = {
    splunk = var.deploy_splunk ? {
      vm_id      = proxmox_vm_qemu.splunk_enterprise[0].vmid
      name       = proxmox_vm_qemu.splunk_enterprise[0].name
      ip_address = var.network_config.splunk.ip
      web_url    = "https://${var.network_config.splunk.ip}:8000"
      cores      = var.vm_configs.splunk.cores
      memory     = var.vm_configs.splunk.memory
    } : null
    
    security_onion_manager = var.deploy_security_onion ? {
      vm_id      = proxmox_vm_qemu.security_onion_manager[0].vmid
      name       = proxmox_vm_qemu.security_onion_manager[0].name
      ip_address = var.network_config.security_onion_manager.ip
      web_url    = "https://${var.network_config.security_onion_manager.ip}"
      cores      = var.vm_configs.security_onion_manager.cores
      memory     = var.vm_configs.security_onion_manager.memory
    } : null
    
    velociraptor = var.deploy_velociraptor ? {
      vm_id      = proxmox_vm_qemu.velociraptor_server[0].vmid
      name       = proxmox_vm_qemu.velociraptor_server[0].name
      ip_address = var.network_config.velociraptor.ip
      web_url    = "https://${var.network_config.velociraptor.ip}:8889"
      cores      = var.vm_configs.velociraptor.cores
      memory     = var.vm_configs.velociraptor.memory
    } : null
    
    misp = var.deploy_misp ? {
      vm_id      = proxmox_vm_qemu.misp_server[0].vmid
      name       = proxmox_vm_qemu.misp_server[0].name
      ip_address = var.network_config.misp.ip
      web_url    = "https://${var.network_config.misp.ip}"
      cores      = var.vm_configs.misp.cores
      memory     = var.vm_configs.misp.memory
    } : null
  }
}

# Resource Summary
output "resource_summary" {
  description = "Summary of allocated resources"
  value = {
    total_vms = (
      (var.deploy_splunk ? 1 : 0) +
      (var.deploy_security_onion ? 1 + var.security_onion_sensor_count : 0) +
      (var.deploy_velociraptor ? 1 : 0) +
      (var.deploy_misp ? 1 : 0)
    )
    
    total_cores = (
      (var.deploy_splunk ? var.vm_configs.splunk.cores : 0) +
      (var.deploy_security_onion ? var.vm_configs.security_onion_manager.cores + (var.vm_configs.security_onion_sensor.cores * var.security_onion_sensor_count) : 0) +
      (var.deploy_velociraptor ? var.vm_configs.velociraptor.cores : 0) +
      (var.deploy_misp ? var.vm_configs.misp.cores : 0)
    )
    
    total_memory_mb = (
      (var.deploy_splunk ? var.vm_configs.splunk.memory : 0) +
      (var.deploy_security_onion ? var.vm_configs.security_onion_manager.memory + (var.vm_configs.security_onion_sensor.memory * var.security_onion_sensor_count) : 0) +
      (var.deploy_velociraptor ? var.vm_configs.velociraptor.memory : 0) +
      (var.deploy_misp ? var.vm_configs.misp.memory : 0)
    )
    
    total_memory_gb = (
      (var.deploy_splunk ? var.vm_configs.splunk.memory : 0) +
      (var.deploy_security_onion ? var.vm_configs.security_onion_manager.memory + (var.vm_configs.security_onion_sensor.memory * var.security_onion_sensor_count) : 0) +
      (var.deploy_velociraptor ? var.vm_configs.velociraptor.memory : 0) +
      (var.deploy_misp ? var.vm_configs.misp.memory : 0)
    ) / 1024
  }
}

# Access Information
output "access_information" {
  description = "Access information for all services"
  value = {
    ssh_access = {
      user        = var.vm_user
      private_key = "Use your SSH private key corresponding to the provided public key"
    }
    
    web_interfaces = {
      splunk = var.deploy_splunk ? {
        url      = "https://${var.network_config.splunk.ip}:8000"
        username = "admin"
        password = "changeme (default)"
      } : null
      
      security_onion = var.deploy_security_onion ? {
        url      = "https://${var.network_config.security_onion_manager.ip}"
        username = "admin"
        password = "Check Security Onion documentation"
      } : null
      
      velociraptor = var.deploy_velociraptor ? {
        url      = "https://${var.network_config.velociraptor.ip}:8889"
        username = "admin"
        password = "Check Velociraptor configuration"
      } : null
      
      misp = var.deploy_misp ? {
        url      = "https://${var.network_config.misp.ip}"
        username = "<EMAIL>"
        password = "admin (default)"
      } : null
    }
  }
}

# Deployment Status
output "deployment_status" {
  description = "Deployment status of all components"
  value = {
    splunk_deployed         = var.deploy_splunk
    security_onion_deployed = var.deploy_security_onion
    velociraptor_deployed   = var.deploy_velociraptor
    misp_deployed          = var.deploy_misp
    sensor_count           = var.deploy_security_onion ? var.security_onion_sensor_count : 0
    target_node            = var.target_node
    deployment_time        = timestamp()
  }
}
