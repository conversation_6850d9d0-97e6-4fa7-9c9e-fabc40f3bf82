# Proxmox VE Deployment Guide

This guide covers deploying GOAD-Blue on Proxmox Virtual Environment (VE), a powerful open-source virtualization platform that combines KVM hypervisor and LXC containers with a web-based management interface.

## 🏗️ Proxmox Architecture

```mermaid
graph TB
    subgraph "🏢 Proxmox Cluster"
        subgraph "🖥️ Proxmox Nodes"
            PVE1[🖥️ pve-node-1<br/>************<br/>Dell PowerEdge R730]
            PVE2[🖥️ pve-node-2<br/>***********1<br/>Dell PowerEdge R730]
            PVE3[🖥️ pve-node-3<br/>***********2<br/>Dell PowerEdge R730]
        end
        
        subgraph "🎛️ Management"
            WEB_UI[🌐 Proxmox Web UI<br/>https://************:8006<br/>Cluster Management]
            API[🔌 Proxmox API<br/>REST API Interface]
        end
        
        subgraph "💾 Storage"
            LOCAL[💾 Local Storage<br/>Each Node Local Disk]
            CEPH[🐙 Ceph Cluster<br/>Distributed Storage<br/>3x Replication]
            NFS[🗄️ NFS Storage<br/>Shared Network Storage]
        end
    end
    
    subgraph "🌐 Network Infrastructure"
        subgraph "🌉 Linux Bridges"
            VMBR0[🌉 vmbr0<br/>Management Bridge<br/>***********/24]
            VMBR1[🌉 vmbr1<br/>GOAD Bridge<br/>************/24]
            VMBR2[🌉 vmbr2<br/>GOAD-Blue Bridge<br/>*************/24]
            VMBR3[🌉 vmbr3<br/>Analysis Bridge<br/>192.168.200.0/24]
        end
        
        subgraph "🔒 VLANs"
            VLAN10[🏷️ VLAN 10<br/>Management]
            VLAN56[🏷️ VLAN 56<br/>GOAD Network]
            VLAN100[🏷️ VLAN 100<br/>GOAD-Blue Network]
            VLAN200[🏷️ VLAN 200<br/>Analysis Network]
        end
    end
    
    subgraph "🛡️ GOAD-Blue VMs"
        SPLUNK[📊 Splunk Enterprise<br/>VM ID: 200<br/>8 vCPU, 16GB RAM<br/>500GB Storage]
        
        SO_MGR[🧅 Security Onion Manager<br/>VM ID: 270<br/>16 vCPU, 32GB RAM<br/>1TB Storage]
        
        SO_SENSOR[📡 Security Onion Sensor<br/>VM ID: 271<br/>8 vCPU, 16GB RAM<br/>500GB Storage]
        
        VELO[🦖 Velociraptor Server<br/>VM ID: 285<br/>4 vCPU, 8GB RAM<br/>200GB Storage]
        
        MISP[🧠 MISP Server<br/>VM ID: 230<br/>4 vCPU, 8GB RAM<br/>200GB Storage]
        
        FLARE[🔥 FLARE-VM<br/>VM ID: 210<br/>8 vCPU, 16GB RAM<br/>500GB Storage]
    end
    
    subgraph "🎮 GOAD Integration"
        GOAD_DC[🏰 GOAD Domain Controllers<br/>Existing GOAD VMs]
        GOAD_SRV[⚔️ GOAD Servers<br/>Member Systems]
        GOAD_WS[🖥️ GOAD Workstations<br/>User Systems]
    end
    
    %% Cluster connections
    PVE1 --> CEPH
    PVE2 --> CEPH
    PVE3 --> CEPH
    
    WEB_UI --> PVE1
    WEB_UI --> PVE2
    WEB_UI --> PVE3
    
    %% Network connections
    VMBR0 --> VLAN10
    VMBR1 --> VLAN56
    VMBR2 --> VLAN100
    VMBR3 --> VLAN200
    
    %% VM connections
    VMBR2 --> SPLUNK
    VMBR2 --> SO_MGR
    VMBR2 --> SO_SENSOR
    VMBR2 --> VELO
    VMBR2 --> MISP
    VMBR3 --> FLARE
    
    VMBR1 --> GOAD_DC
    VMBR1 --> GOAD_SRV
    VMBR1 --> GOAD_WS
    
    classDef nodes fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef management fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef storage fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef network fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef goadblue fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef goad fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class PVE1,PVE2,PVE3 nodes
    class WEB_UI,API management
    class LOCAL,CEPH,NFS storage
    class VMBR0,VMBR1,VMBR2,VMBR3,VLAN10,VLAN56,VLAN100,VLAN200 network
    class SPLUNK,SO_MGR,SO_SENSOR,VELO,MISP,FLARE goadblue
    class GOAD_DC,GOAD_SRV,GOAD_WS goad
```

## 📋 Prerequisites

### **Proxmox VE Requirements**

#### **Hardware Requirements**
- **CPU**: Intel VT-x or AMD-V support
- **RAM**: 32GB minimum (64GB+ recommended)
- **Storage**: 1TB+ (SSD recommended)
- **Network**: Gigabit Ethernet (10GbE recommended)

#### **Software Requirements**
- **Proxmox VE**: 7.0+ or 8.0+
- **Debian**: Based on Debian 11 (Bullseye) or 12 (Bookworm)
- **KVM**: Kernel-based Virtual Machine support
- **ZFS**: Optional but recommended for storage

### **Proxmox Installation**

#### **Download and Install Proxmox VE**

```bash
# Download Proxmox VE ISO
wget https://www.proxmox.com/en/downloads/item/proxmox-ve-8-0-iso-installer

# Create bootable USB (Linux)
sudo dd if=proxmox-ve_8.0-2.iso of=/dev/sdX bs=1M status=progress

# Boot from USB and follow installation wizard
# Configure:
# - Hostname: pve-node-1.example.com
# - IP Address: ************/24
# - Gateway: ***********
# - DNS: ***********
```

#### **Post-Installation Configuration**

```bash
# Update system
apt update && apt upgrade -y

# Configure repositories (remove enterprise repo if no subscription)
echo "deb http://download.proxmox.com/debian/pve bookworm pve-no-subscription" > /etc/apt/sources.list.d/pve-no-subscription.list
rm /etc/apt/sources.list.d/pve-enterprise.list

# Update package lists
apt update

# Install additional tools
apt install -y curl wget git vim htop iotop
```

### **Network Configuration**

#### **Configure Network Bridges**

```bash
# Edit network configuration
nano /etc/network/interfaces

# Network configuration example:
auto lo
iface lo inet loopback

# Management interface
iface eno1 inet manual

# Management bridge
auto vmbr0
iface vmbr0 inet static
    address ************/24
    gateway ***********
    bridge-ports eno1
    bridge-stp off
    bridge-fd 0

# GOAD network bridge
auto vmbr1
iface vmbr1 inet static
    address ************/24
    bridge-ports none
    bridge-stp off
    bridge-fd 0
    post-up echo 1 > /proc/sys/net/ipv4/ip_forward
    post-up iptables -t nat -A POSTROUTING -s '************/24' -o vmbr0 -j MASQUERADE
    post-down iptables -t nat -D POSTROUTING -s '************/24' -o vmbr0 -j MASQUERADE

# GOAD-Blue network bridge
auto vmbr2
iface vmbr2 inet static
    address *************/24
    bridge-ports none
    bridge-stp off
    bridge-fd 0
    post-up echo 1 > /proc/sys/net/ipv4/ip_forward
    post-up iptables -t nat -A POSTROUTING -s '*************/24' -o vmbr0 -j MASQUERADE
    post-up iptables -A FORWARD -s ************/24 -d *************/24 -j ACCEPT
    post-up iptables -A FORWARD -s *************/24 -d ************/24 -j ACCEPT
    post-down iptables -t nat -D POSTROUTING -s '*************/24' -o vmbr0 -j MASQUERADE
    post-down iptables -D FORWARD -s ************/24 -d *************/24 -j ACCEPT
    post-down iptables -D FORWARD -s *************/24 -d ************/24 -j ACCEPT

# Analysis network bridge (isolated)
auto vmbr3
iface vmbr3 inet static
    address *************/24
    bridge-ports none
    bridge-stp off
    bridge-fd 0
    # No internet access for analysis network

# Apply network configuration
systemctl restart networking
```

## 🚀 Deployment Process

### **1. Terraform Deployment**

#### **Terraform Configuration for Proxmox**

```hcl
# terraform/providers/proxmox/main.tf
terraform {
  required_version = ">= 1.0"
  required_providers {
    proxmox = {
      source  = "telmate/proxmox"
      version = "~> 2.9"
    }
  }
}

provider "proxmox" {
  pm_api_url      = var.proxmox_api_url
  pm_user         = var.proxmox_user
  pm_password     = var.proxmox_password
  pm_tls_insecure = var.proxmox_tls_insecure
}

# Splunk Enterprise VM
resource "proxmox_vm_qemu" "splunk_enterprise" {
  name        = "goad-blue-splunk"
  target_node = var.target_node
  vmid        = 200
  
  # VM Configuration
  cores    = 8
  memory   = 16384
  sockets  = 1
  cpu      = "host"
  numa     = true
  hotplug  = "network,disk,usb"
  
  # Boot configuration
  boot     = "order=scsi0"
  agent    = 1
  os_type  = "cloud-init"
  
  # Cloud-init configuration
  ciuser     = var.vm_user
  cipassword = var.vm_password
  sshkeys    = var.ssh_public_key
  ipconfig0  = "ip=*************0/24,gw=*************"
  nameserver = "*************"
  
  # Disk configuration
  disk {
    slot     = 0
    type     = "scsi"
    storage  = var.storage_name
    size     = "500G"
    format   = "qcow2"
    ssd      = 1
    discard  = "on"
  }
  
  # Network configuration
  network {
    model  = "virtio"
    bridge = "vmbr2"
    tag    = 100
  }
  
  # Additional network for internet access
  network {
    model  = "virtio"
    bridge = "vmbr0"
  }
  
  # Lifecycle management
  lifecycle {
    ignore_changes = [
      network,
    ]
  }
  
  # Tags
  tags = "goad-blue,splunk"
}

# Security Onion Manager VM
resource "proxmox_vm_qemu" "security_onion_manager" {
  name        = "goad-blue-so-manager"
  target_node = var.target_node
  vmid        = 270
  
  # VM Configuration
  cores    = 16
  memory   = 32768
  sockets  = 1
  cpu      = "host"
  numa     = true
  hotplug  = "network,disk,usb"
  
  # Boot configuration
  boot     = "order=scsi0"
  agent    = 1
  os_type  = "cloud-init"
  
  # Cloud-init configuration
  ciuser     = var.vm_user
  cipassword = var.vm_password
  sshkeys    = var.ssh_public_key
  ipconfig0  = "ip=**************/24,gw=*************"
  ipconfig1  = "ip=*************/24"  # Monitoring interface
  nameserver = "*************"
  
  # Disk configuration
  disk {
    slot     = 0
    type     = "scsi"
    storage  = var.storage_name
    size     = "1000G"
    format   = "qcow2"
    ssd      = 1
    discard  = "on"
  }
  
  # Network configuration
  network {
    model  = "virtio"
    bridge = "vmbr2"
    tag    = 100
  }
  
  # Monitoring network interface
  network {
    model  = "virtio"
    bridge = "vmbr1"
    tag    = 56
  }
  
  # Internet access
  network {
    model  = "virtio"
    bridge = "vmbr0"
  }
  
  # Tags
  tags = "goad-blue,security-onion"
}

# Velociraptor Server VM
resource "proxmox_vm_qemu" "velociraptor_server" {
  name        = "goad-blue-velociraptor"
  target_node = var.target_node
  vmid        = 285
  
  # VM Configuration
  cores    = 4
  memory   = 8192
  sockets  = 1
  cpu      = "host"
  numa     = true
  
  # Boot configuration
  boot     = "order=scsi0"
  agent    = 1
  os_type  = "cloud-init"
  
  # Cloud-init configuration
  ciuser     = var.vm_user
  cipassword = var.vm_password
  sshkeys    = var.ssh_public_key
  ipconfig0  = "ip=**************/24,gw=*************"
  nameserver = "*************"
  
  # Disk configuration
  disk {
    slot     = 0
    type     = "scsi"
    storage  = var.storage_name
    size     = "200G"
    format   = "qcow2"
    ssd      = 1
    discard  = "on"
  }
  
  # Network configuration
  network {
    model  = "virtio"
    bridge = "vmbr2"
    tag    = 100
  }
  
  network {
    model  = "virtio"
    bridge = "vmbr0"
  }
  
  # Tags
  tags = "goad-blue,velociraptor"
}

# MISP Server VM
resource "proxmox_vm_qemu" "misp_server" {
  name        = "goad-blue-misp"
  target_node = var.target_node
  vmid        = 230
  
  # VM Configuration
  cores    = 4
  memory   = 8192
  sockets  = 1
  cpu      = "host"
  numa     = true
  
  # Boot configuration
  boot     = "order=scsi0"
  agent    = 1
  os_type  = "cloud-init"
  
  # Cloud-init configuration
  ciuser     = var.vm_user
  cipassword = var.vm_password
  sshkeys    = var.ssh_public_key
  ipconfig0  = "ip=**************/24,gw=*************"
  nameserver = "*************"
  
  # Disk configuration
  disk {
    slot     = 0
    type     = "scsi"
    storage  = var.storage_name
    size     = "200G"
    format   = "qcow2"
    ssd      = 1
    discard  = "on"
  }
  
  # Network configuration
  network {
    model  = "virtio"
    bridge = "vmbr2"
    tag    = 100
  }
  
  network {
    model  = "virtio"
    bridge = "vmbr0"
  }
  
  # Tags
  tags = "goad-blue,misp"
}
```

#### **Variables Configuration**

```hcl
# terraform/providers/proxmox/variables.tf
variable "proxmox_api_url" {
  description = "Proxmox API URL"
  type        = string
  default     = "https://************:8006/api2/json"
}

variable "proxmox_user" {
  description = "Proxmox username"
  type        = string
  default     = "root@pam"
}

variable "proxmox_password" {
  description = "Proxmox password"
  type        = string
  sensitive   = true
}

variable "proxmox_tls_insecure" {
  description = "Allow insecure TLS connections"
  type        = bool
  default     = true
}

variable "target_node" {
  description = "Proxmox target node"
  type        = string
  default     = "pve-node-1"
}

variable "storage_name" {
  description = "Proxmox storage name"
  type        = string
  default     = "local-lvm"
}

variable "vm_user" {
  description = "VM default user"
  type        = string
  default     = "ubuntu"
}

variable "vm_password" {
  description = "VM default password"
  type        = string
  sensitive   = true
  default     = "goadblue123!"
}

variable "ssh_public_key" {
  description = "SSH public key for VM access"
  type        = string
}
```

#### **Deployment Commands**

```bash
# Navigate to Proxmox provider directory
cd terraform/providers/proxmox

# Initialize Terraform
terraform init

# Create terraform.tfvars file
cat > terraform.tfvars << EOF
proxmox_api_url = "https://************:8006/api2/json"
proxmox_user = "root@pam"
proxmox_password = "your-proxmox-password"
target_node = "pve-node-1"
storage_name = "local-lvm"
vm_user = "ubuntu"
vm_password = "goadblue123!"
ssh_public_key = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."
EOF

# Plan deployment
terraform plan -var-file="terraform.tfvars"

# Apply deployment
terraform apply -var-file="terraform.tfvars"
```

### **2. Manual VM Creation via CLI**

#### **Create VMs using qm command**

```bash
# Create Splunk Enterprise VM
qm create 200 \
  --name goad-blue-splunk \
  --memory 16384 \
  --cores 8 \
  --net0 virtio,bridge=vmbr2,tag=100 \
  --net1 virtio,bridge=vmbr0 \
  --scsi0 local-lvm:500 \
  --ide2 local:iso/ubuntu-22.04.3-live-server-amd64.iso,media=cdrom \
  --boot order=scsi0 \
  --ostype l26 \
  --agent enabled=1

# Create Security Onion Manager VM
qm create 270 \
  --name goad-blue-so-manager \
  --memory 32768 \
  --cores 16 \
  --net0 virtio,bridge=vmbr2,tag=100 \
  --net1 virtio,bridge=vmbr1,tag=56 \
  --net2 virtio,bridge=vmbr0 \
  --scsi0 local-lvm:1000 \
  --ide2 local:iso/ubuntu-22.04.3-live-server-amd64.iso,media=cdrom \
  --boot order=scsi0 \
  --ostype l26 \
  --agent enabled=1

# Start VMs
qm start 200
qm start 270

# Check VM status
qm status 200
qm list
```

### **3. Template Creation**

#### **Create VM Template**

```bash
# Create base VM for template
qm create 9000 \
  --name ubuntu-22.04-template \
  --memory 4096 \
  --cores 2 \
  --net0 virtio,bridge=vmbr0 \
  --scsi0 local-lvm:50 \
  --ide2 local:iso/ubuntu-22.04.3-live-server-amd64.iso,media=cdrom \
  --boot order=scsi0 \
  --ostype l26 \
  --agent enabled=1

# Install Ubuntu and configure cloud-init
# ... (manual installation process)

# Prepare template
qm set 9000 --cloud-init-storage local-lvm
qm set 9000 --serial0 socket --vga serial0

# Convert to template
qm template 9000

# Clone from template
qm clone 9000 200 --name goad-blue-splunk --full
qm set 200 --memory 16384 --cores 8
qm set 200 --net0 virtio,bridge=vmbr2,tag=100
qm set 200 --ipconfig0 ip=*************0/24,gw=*************
```

## 🔧 Proxmox-Specific Features

### **Ceph Storage Configuration**

```bash
# Install Ceph on all nodes
pveceph install

# Create Ceph cluster
pveceph init --network ***********/24

# Create Ceph monitors
pveceph mon create

# Create Ceph OSDs
pveceph osd create /dev/sdb
pveceph osd create /dev/sdc
pveceph osd create /dev/sdd

# Create Ceph pool
pveceph pool create goad-blue-pool --size 3 --min_size 2

# Add Ceph storage to Proxmox
pvesm add cephfs goad-blue-ceph --monhost ************,***********1,***********2 --pool goad-blue-pool
```

### **Backup Configuration**

```bash
# Create backup schedule
vzdump --mode snapshot --compress lzo --storage local --node pve-node-1 200 270 285 230

# Configure automatic backups
cat > /etc/cron.d/goad-blue-backup << 'EOF'
# GOAD-Blue VM backups
0 2 * * * root vzdump --mode snapshot --compress lzo --storage backup-storage --node pve-node-1 200 270 285 230
EOF

# Backup to external storage
vzdump --mode snapshot --compress lzo --storage nfs-backup 200
```

### **High Availability Setup**

```bash
# Create HA group
ha-manager add goad-blue-group --nodes pve-node-1:1,pve-node-2:2,pve-node-3:3 --nofailback 0

# Add VMs to HA
ha-manager add vm:200 --group goad-blue-group --max_restart 2 --max_relocate 2
ha-manager add vm:270 --group goad-blue-group --max_restart 2 --max_relocate 2

# Check HA status
ha-manager status
```

### **Live Migration**

```bash
# Migrate VM to another node
qm migrate 200 pve-node-2 --online

# Migrate with storage
qm migrate 200 pve-node-2 --online --targetstorage local-lvm

# Check migration status
qm status 200
```

### **Performance Monitoring**

```bash
# Monitor VM performance
qm monitor 200
info cpus
info memory
info block

# Check cluster status
pvecm status
pvecm nodes

# Monitor storage
pvesm status
ceph status
```

---

!!! success "Proxmox Deployment Complete"
    Your GOAD-Blue environment is now deployed on Proxmox VE with enterprise-grade features including high availability, live migration, and distributed storage.

!!! tip "Proxmox Best Practices"
    - Use Ceph for distributed storage across nodes
    - Configure HA for critical VMs
    - Implement regular backup schedules
    - Monitor cluster health regularly
    - Use VLANs for network segmentation

!!! warning "Performance Considerations"
    - Ensure adequate CPU and RAM for all VMs
    - Use SSD storage for better I/O performance
    - Configure proper network bandwidth
    - Monitor Ceph cluster health
    - Plan for storage growth and expansion

!!! info "Proxmox Advantages"
    - Open-source and free
    - Built-in clustering and HA
    - Web-based management interface
    - Support for both VMs and containers
    - Integrated backup and restore
    - Live migration capabilities
