- name: Create SQL Linked server and enable RPC
  win_shell: |
    SqlCmd -E -Q "EXEC master.dbo.sp_addlinkedserver @server = N'{{item.key}}', @srvproduct=N'', @provider=N'SQLOLEDB', @datasrc=N'{{item.value.data_src}}'"
    SqlCmd -E -Q "EXEC master.dbo.sp_serveroption @server=N'{{item.key}}', @optname=N'rpc', @optvalue=N'true'"
    SqlCmd -E -Q "EXEC master.dbo.sp_serveroption @server=N'{{item.key}}', @optname=N'rpc out', @optvalue=N'true'"
  become: yes
  become_method: runas
  become_user: "{{SQLSVCACCOUNT}}"
  vars:
    ansible_become_pass: "{{SQLSVCPASSWORD}}"
  with_dict: "{{linked_servers}}"

#- name: Create logins mapping to specific users
#  win_shell: |
#    SqlCmd -E -Q "EXEC master.dbo.sp_addlinkedsrvlogin @rmtsrvname = N'{{item.key}}', @locallogin = N'{{item.value.local_login}}', @useself = N'False', @rmtuser = N'{{item.value.remote_login}}', @rmtpassword = N'{{item.value.remote_password}}'"
#  become: yes
#  become_method: runas
#  become_user: "{{SQLSVCACCOUNT}}"
#  vars:
#    ansible_become_pass: "{{SQLSVCPASSWORD}}"
#  with_dict: "{{linked_servers.users_mapping}}"

- name: "create logins"
  include_tasks: logins.yml  
  vars:
    linked_server: "{{item.key}}"
    users_mapping: "{{item.value.users_mapping}}"
  with_dict: "{{linked_servers}}"

- name: default login impersonation
  win_shell: |
    SqlCmd -E -Q "EXEC master.dbo.sp_addlinkedsrvlogin @rmtsrvname = N'{{item.key}}', @locallogin = NULL , @useself = N'True'"
  become: yes
  become_method: runas
  become_user: "{{SQLSVCACCOUNT}}"
  vars:
    ansible_become_pass: "{{SQLSVCPASSWORD}}"
  with_dict: "{{linked_servers}}"
