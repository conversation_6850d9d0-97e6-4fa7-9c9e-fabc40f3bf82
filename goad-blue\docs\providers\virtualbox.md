# VirtualBox Deployment Guide

This guide covers deploying GOAD-Blue on Oracle VirtualBox for development, testing, and educational purposes. VirtualBox provides a cost-effective way to run GOAD-Blue on personal computers and laptops.

## 🏗️ VirtualBox Architecture

```mermaid
graph TB
    subgraph "💻 Host System"
        HOST[🖥️ Host Machine<br/>Windows/Linux/macOS<br/>64GB RAM, 1TB Storage]
        
        subgraph "📦 VirtualBox Hypervisor"
            VBOX[📦 VirtualBox 7.0+<br/>Type-2 Hypervisor]
            
            subgraph "🌐 Virtual Networks"
                HOSTONLY1[🔗 Host-Only Network 1<br/>vboxnet0: ************/24<br/>GOAD Network]
                HOSTONLY2[🔗 Host-Only Network 2<br/>vboxnet1: *************/24<br/>GOAD-Blue Network]
                HOSTONLY3[🔗 Host-Only Network 3<br/>vboxnet2: *************/24<br/>Analysis Network]
                NAT[🌐 NAT Network<br/>Internet Access]
            end
        end
    end
    
    subgraph "🛡️ GOAD-Blue VMs"
        SPLUNK[📊 Splunk Enterprise<br/>4 vCPU, 8GB RAM<br/>100GB Storage<br/>**************]
        
        SO_MGR[🧅 Security Onion Manager<br/>4 vCPU, 8GB RAM<br/>200GB Storage<br/>**************]
        
        SO_SENSOR[📡 Security Onion Sensor<br/>2 vCPU, 4GB RAM<br/>100GB Storage<br/>**************]
        
        VELO[🦖 Velociraptor Server<br/>2 vCPU, 4GB RAM<br/>50GB Storage<br/>**************]
        
        MISP[🧠 MISP Server<br/>2 vCPU, 4GB RAM<br/>50GB Storage<br/>**************]
        
        FLARE[🔥 FLARE-VM<br/>4 vCPU, 8GB RAM<br/>100GB Storage<br/>**************]
    end
    
    subgraph "🎮 GOAD VMs (Existing)"
        GOAD_DC[🏰 GOAD Domain Controllers<br/>Existing GOAD Installation]
        GOAD_SRV[⚔️ GOAD Servers<br/>Member Systems]
        GOAD_WS[🖥️ GOAD Workstations<br/>User Systems]
    end
    
    %% Host connections
    HOST --> VBOX
    VBOX --> HOSTONLY1
    VBOX --> HOSTONLY2
    VBOX --> HOSTONLY3
    VBOX --> NAT
    
    %% Network connections
    HOSTONLY2 --> SPLUNK
    HOSTONLY2 --> SO_MGR
    HOSTONLY2 --> SO_SENSOR
    HOSTONLY2 --> VELO
    HOSTONLY2 --> MISP
    
    HOSTONLY3 --> FLARE
    
    HOSTONLY1 --> GOAD_DC
    HOSTONLY1 --> GOAD_SRV
    HOSTONLY1 --> GOAD_WS
    
    NAT --> SPLUNK
    NAT --> SO_MGR
    NAT --> VELO
    NAT --> MISP
    
    classDef host fill:#f9f9f9,stroke:#333,stroke-width:2px
    classDef hypervisor fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef network fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef goadblue fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef goad fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class HOST host
    class VBOX hypervisor
    class HOSTONLY1,HOSTONLY2,HOSTONLY3,NAT network
    class SPLUNK,SO_MGR,SO_SENSOR,VELO,MISP,FLARE goadblue
    class GOAD_DC,GOAD_SRV,GOAD_WS goad
```

## 📋 Prerequisites

### **Host System Requirements**

#### **Minimum Requirements**
- **CPU**: 8 cores (Intel VT-x or AMD-V)
- **RAM**: 32GB (64GB recommended)
- **Storage**: 500GB free space (1TB recommended)
- **OS**: Windows 10+, Ubuntu 20.04+, macOS 10.15+

#### **Recommended Requirements**
- **CPU**: 16+ cores with virtualization support
- **RAM**: 64GB+ for full deployment
- **Storage**: 1TB+ SSD for better performance
- **Network**: Gigabit Ethernet for large data transfers

### **VirtualBox Installation**

#### **Windows Installation**
```powershell
# Download VirtualBox from official website
Invoke-WebRequest -Uri "https://download.virtualbox.org/virtualbox/7.0.12/VirtualBox-7.0.12-159484-Win.exe" -OutFile "VirtualBox-installer.exe"

# Install VirtualBox
Start-Process -FilePath "VirtualBox-installer.exe" -ArgumentList "/S" -Wait

# Install Extension Pack
Invoke-WebRequest -Uri "https://download.virtualbox.org/virtualbox/7.0.12/Oracle_VM_VirtualBox_Extension_Pack-7.0.12.vbox-extpack" -OutFile "VirtualBox-ExtPack.vbox-extpack"
VBoxManage extpack install VirtualBox-ExtPack.vbox-extpack
```

#### **Ubuntu/Debian Installation**
```bash
# Add VirtualBox repository
wget -q https://www.virtualbox.org/download/oracle_vbox_2016.asc -O- | sudo apt-key add -
echo "deb [arch=amd64] https://download.virtualbox.org/virtualbox/debian $(lsb_release -cs) contrib" | sudo tee /etc/apt/sources.list.d/virtualbox.list

# Install VirtualBox
sudo apt update
sudo apt install -y virtualbox-7.0

# Install Extension Pack
wget https://download.virtualbox.org/virtualbox/7.0.12/Oracle_VM_VirtualBox_Extension_Pack-7.0.12.vbox-extpack
sudo VBoxManage extpack install Oracle_VM_VirtualBox_Extension_Pack-7.0.12.vbox-extpack

# Add user to vboxusers group
sudo usermod -aG vboxusers $USER
```

#### **macOS Installation**
```bash
# Install using Homebrew
brew install --cask virtualbox
brew install --cask virtualbox-extension-pack

# Or download from official website
# https://www.virtualbox.org/wiki/Downloads
```

### **Network Configuration**

#### **Create Host-Only Networks**
```bash
# Create host-only networks for GOAD-Blue
VBoxManage hostonlyif create  # vboxnet0 (usually exists)
VBoxManage hostonlyif create  # vboxnet1
VBoxManage hostonlyif create  # vboxnet2

# Configure GOAD network (vboxnet0)
VBoxManage hostonlyif ipconfig vboxnet0 --ip ************ --netmask *************

# Configure GOAD-Blue network (vboxnet1)
VBoxManage hostonlyif ipconfig vboxnet1 --ip ************* --netmask *************

# Configure Analysis network (vboxnet2)
VBoxManage hostonlyif ipconfig vboxnet2 --ip ************* --netmask *************

# Enable DHCP for networks
VBoxManage dhcpserver add --netname HostInterfaceNetworking-vboxnet0 \
  --ip ************ --netmask ************* \
  --lowerip ************00 --upperip ************** --enable

VBoxManage dhcpserver add --netname HostInterfaceNetworking-vboxnet1 \
  --ip ************* --netmask ************* \
  --lowerip **************0 --upperip *************** --enable

VBoxManage dhcpserver add --netname HostInterfaceNetworking-vboxnet2 \
  --ip ************* --netmask ************* \
  --lowerip **************0 --upperip *************** --enable
```

## 🚀 Deployment Process

### **1. Automated Deployment with Vagrant**

#### **Vagrantfile Configuration**

```ruby
# Vagrantfile
Vagrant.configure("2") do |config|
  # Global configuration
  config.vm.box_check_update = false
  config.vm.synced_folder ".", "/vagrant", disabled: true
  
  # VirtualBox provider settings
  config.vm.provider "virtualbox" do |vb|
    vb.gui = false
    vb.linked_clone = true
    vb.customize ["modifyvm", :id, "--groups", "/GOAD-Blue"]
    vb.customize ["modifyvm", :id, "--vram", "128"]
    vb.customize ["modifyvm", :id, "--accelerate3d", "on"]
  end
  
  # Splunk Enterprise VM
  config.vm.define "splunk" do |splunk|
    splunk.vm.box = "ubuntu/jammy64"
    splunk.vm.hostname = "splunk"
    
    # Network configuration
    splunk.vm.network "private_network", 
      ip: "**************", 
      virtualbox__hostonly: "vboxnet1"
    splunk.vm.network "private_network", type: "dhcp", virtualbox__natnet: "********/24"
    
    # Resource allocation
    splunk.vm.provider "virtualbox" do |vb|
      vb.name = "GOAD-Blue-Splunk"
      vb.cpus = 4
      vb.memory = 8192
      
      # Create additional disk for Splunk data
      unless File.exist?("splunk-data.vdi")
        vb.customize ["createhd", "--filename", "splunk-data.vdi", "--size", 100 * 1024]
      end
      vb.customize ["storageattach", :id, "--storagectl", "SCSI", "--port", 2, "--device", 0, "--type", "hdd", "--medium", "splunk-data.vdi"]
    end
    
    # Provisioning
    splunk.vm.provision "shell", path: "scripts/virtualbox/install-splunk.sh"
    splunk.vm.provision "ansible" do |ansible|
      ansible.playbook = "ansible/playbooks/virtualbox/splunk.yml"
      ansible.inventory_path = "ansible/inventory/virtualbox"
    end
  end
  
  # Security Onion Manager VM
  config.vm.define "so-manager" do |so|
    so.vm.box = "ubuntu/jammy64"
    so.vm.hostname = "so-manager"
    
    # Network configuration
    so.vm.network "private_network", 
      ip: "**************", 
      virtualbox__hostonly: "vboxnet1"
    so.vm.network "private_network", 
      ip: "*************", 
      virtualbox__hostonly: "vboxnet0"  # Monitoring interface
    so.vm.network "private_network", type: "dhcp", virtualbox__natnet: "********/24"
    
    # Resource allocation
    so.vm.provider "virtualbox" do |vb|
      vb.name = "GOAD-Blue-SecurityOnion-Manager"
      vb.cpus = 4
      vb.memory = 8192
      
      # Enable promiscuous mode for monitoring
      vb.customize ["modifyvm", :id, "--nicpromisc2", "allow-all"]
      
      # Create additional disk for Security Onion data
      unless File.exist?("so-data.vdi")
        vb.customize ["createhd", "--filename", "so-data.vdi", "--size", 200 * 1024]
      end
      vb.customize ["storageattach", :id, "--storagectl", "SCSI", "--port", 2, "--device", 0, "--type", "hdd", "--medium", "so-data.vdi"]
    end
    
    # Provisioning
    so.vm.provision "shell", path: "scripts/virtualbox/install-security-onion.sh"
    so.vm.provision "ansible" do |ansible|
      ansible.playbook = "ansible/playbooks/virtualbox/security-onion.yml"
      ansible.inventory_path = "ansible/inventory/virtualbox"
    end
  end
  
  # Velociraptor Server VM
  config.vm.define "velociraptor" do |velo|
    velo.vm.box = "ubuntu/jammy64"
    velo.vm.hostname = "velociraptor"
    
    # Network configuration
    velo.vm.network "private_network", 
      ip: "**************", 
      virtualbox__hostonly: "vboxnet1"
    velo.vm.network "private_network", type: "dhcp", virtualbox__natnet: "********/24"
    
    # Resource allocation
    velo.vm.provider "virtualbox" do |vb|
      vb.name = "GOAD-Blue-Velociraptor"
      vb.cpus = 2
      vb.memory = 4096
    end
    
    # Provisioning
    velo.vm.provision "shell", path: "scripts/virtualbox/install-velociraptor.sh"
    velo.vm.provision "ansible" do |ansible|
      ansible.playbook = "ansible/playbooks/virtualbox/velociraptor.yml"
      ansible.inventory_path = "ansible/inventory/virtualbox"
    end
  end
  
  # MISP Server VM
  config.vm.define "misp" do |misp|
    misp.vm.box = "ubuntu/jammy64"
    misp.vm.hostname = "misp"
    
    # Network configuration
    misp.vm.network "private_network", 
      ip: "**************", 
      virtualbox__hostonly: "vboxnet1"
    misp.vm.network "private_network", type: "dhcp", virtualbox__natnet: "********/24"
    
    # Resource allocation
    misp.vm.provider "virtualbox" do |vb|
      vb.name = "GOAD-Blue-MISP"
      vb.cpus = 2
      vb.memory = 4096
    end
    
    # Provisioning
    misp.vm.provision "shell", path: "scripts/virtualbox/install-misp.sh"
    misp.vm.provision "ansible" do |ansible|
      ansible.playbook = "ansible/playbooks/virtualbox/misp.yml"
      ansible.inventory_path = "ansible/inventory/virtualbox"
    end
  end
  
  # FLARE-VM (Windows Analysis VM)
  config.vm.define "flare-vm" do |flare|
    flare.vm.box = "gusztavvargadr/windows-10"
    flare.vm.hostname = "flare-vm"
    
    # Network configuration (isolated)
    flare.vm.network "private_network", 
      ip: "**************", 
      virtualbox__hostonly: "vboxnet2"
    
    # Resource allocation
    flare.vm.provider "virtualbox" do |vb|
      vb.name = "GOAD-Blue-FLARE-VM"
      vb.cpus = 4
      vb.memory = 8192
      vb.gui = true  # Enable GUI for Windows
    end
    
    # Provisioning
    flare.vm.provision "shell", path: "scripts/virtualbox/install-flare-vm.ps1"
  end
end
```

#### **Deployment Commands**

```bash
# Navigate to project directory
cd goad-blue

# Install required Vagrant plugins
vagrant plugin install vagrant-vbguest
vagrant plugin install vagrant-reload

# Deploy all VMs
vagrant up

# Deploy specific VMs
vagrant up splunk
vagrant up so-manager
vagrant up velociraptor
vagrant up misp

# Check VM status
vagrant status

# SSH into VMs
vagrant ssh splunk
vagrant ssh so-manager
```

### **2. Manual VM Creation**

#### **Create Splunk Enterprise VM**

```bash
# Create VM
VBoxManage createvm --name "GOAD-Blue-Splunk" --ostype "Ubuntu_64" --register

# Configure VM settings
VBoxManage modifyvm "GOAD-Blue-Splunk" \
  --memory 8192 \
  --cpus 4 \
  --vram 128 \
  --boot1 dvd \
  --boot2 disk \
  --boot3 none \
  --boot4 none \
  --acpi on \
  --ioapic on \
  --rtcuseutc on \
  --accelerate3d on

# Create and attach storage
VBoxManage createhd --filename "GOAD-Blue-Splunk.vdi" --size 102400 --format VDI
VBoxManage storagectl "GOAD-Blue-Splunk" --name "SATA Controller" --add sata --controller IntelAhci
VBoxManage storageattach "GOAD-Blue-Splunk" --storagectl "SATA Controller" --port 0 --device 0 --type hdd --medium "GOAD-Blue-Splunk.vdi"

# Configure network adapters
VBoxManage modifyvm "GOAD-Blue-Splunk" --nic1 hostonly --hostonlyadapter1 vboxnet1
VBoxManage modifyvm "GOAD-Blue-Splunk" --nic2 nat

# Attach Ubuntu ISO
VBoxManage storageattach "GOAD-Blue-Splunk" --storagectl "SATA Controller" --port 1 --device 0 --type dvddrive --medium "ubuntu-22.04.3-live-server-amd64.iso"

# Start VM
VBoxManage startvm "GOAD-Blue-Splunk" --type gui
```

### **3. Template Creation with Packer**

#### **VirtualBox Packer Template**

```json
{
  "variables": {
    "vm_name": "goad-blue-ubuntu-template",
    "disk_size": "50000",
    "memory": "4096",
    "cpus": "2",
    "iso_url": "https://releases.ubuntu.com/22.04/ubuntu-22.04.3-live-server-amd64.iso",
    "iso_checksum": "sha256:a4acfda10b18da50e2ec50ccaf860d7f20b389df8765611142305c0e911d16fd"
  },
  "builders": [
    {
      "type": "virtualbox-iso",
      "vm_name": "{{user `vm_name`}}",
      "guest_os_type": "Ubuntu_64",
      
      "iso_url": "{{user `iso_url`}}",
      "iso_checksum": "{{user `iso_checksum`}}",
      
      "disk_size": "{{user `disk_size`}}",
      "hard_drive_interface": "sata",
      
      "memory": "{{user `memory`}}",
      "cpus": "{{user `cpus`}}",
      "vram": "128",
      
      "headless": false,
      "vboxmanage": [
        ["modifyvm", "{{.Name}}", "--nat-localhostreachable1", "on"],
        ["modifyvm", "{{.Name}}", "--memory", "{{user `memory`}}"],
        ["modifyvm", "{{.Name}}", "--cpus", "{{user `cpus`}}"]
      ],
      
      "ssh_username": "ubuntu",
      "ssh_password": "ubuntu",
      "ssh_timeout": "20m",
      
      "boot_wait": "5s",
      "boot_command": [
        "<enter><wait><f6><wait><esc><wait>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs>",
        "/install/vmlinuz",
        " initrd=/install/initrd.gz",
        " priority=critical",
        " locale=en_US",
        " file=/media/preseed.cfg",
        "<enter>"
      ],
      
      "shutdown_command": "echo 'ubuntu' | sudo -S shutdown -P now"
    }
  ],
  "provisioners": [
    {
      "type": "shell",
      "inline": [
        "sudo apt-get update",
        "sudo apt-get upgrade -y",
        "sudo apt-get install -y virtualbox-guest-additions-iso",
        "sudo apt-get install -y curl wget unzip git"
      ]
    },
    {
      "type": "shell",
      "script": "scripts/virtualbox/prepare-template.sh"
    }
  ],
  "post-processors": [
    {
      "type": "vagrant",
      "output": "boxes/goad-blue-ubuntu-{{timestamp}}.box"
    }
  ]
}
```

#### **Build Template**

```bash
# Build VirtualBox template
cd packer/templates/virtualbox
packer build goad-blue-ubuntu-template.json

# Add box to Vagrant
vagrant box add goad-blue/ubuntu boxes/goad-blue-ubuntu-*.box
```

## 🔧 VirtualBox-Specific Configuration

### **Performance Optimization**

```bash
# Enable hardware acceleration
VBoxManage modifyvm "GOAD-Blue-Splunk" --hwvirtex on --nestedpaging on --largepages on

# Configure CPU settings
VBoxManage modifyvm "GOAD-Blue-Splunk" --cpuexecutioncap 100 --pae on

# Optimize disk performance
VBoxManage modifyvm "GOAD-Blue-Splunk" --hda-type writethrough

# Enable 3D acceleration
VBoxManage modifyvm "GOAD-Blue-Splunk" --accelerate3d on --vram 128
```

### **Snapshot Management**

```bash
# Create snapshots before configuration
VBoxManage snapshot "GOAD-Blue-Splunk" take "Fresh Install" --description "Clean Ubuntu installation"

# List snapshots
VBoxManage snapshot "GOAD-Blue-Splunk" list

# Restore snapshot
VBoxManage snapshot "GOAD-Blue-Splunk" restore "Fresh Install"

# Delete snapshot
VBoxManage snapshot "GOAD-Blue-Splunk" delete "Fresh Install"
```

### **Resource Monitoring**

```bash
# Monitor VM resource usage
VBoxManage metrics query "GOAD-Blue-Splunk" CPU/Load/User,CPU/Load/Kernel,RAM/Usage/Used

# Get VM information
VBoxManage showvminfo "GOAD-Blue-Splunk"

# List running VMs
VBoxManage list runningvms
```

### **Network Troubleshooting**

```bash
# Test network connectivity
ping **************  # Splunk
ping **************  # Security Onion

# Check host-only network configuration
VBoxManage list hostonlyifs

# Restart VirtualBox networking
sudo systemctl restart vboxdrv
sudo systemctl restart vboxnetflt
```

## 💡 Development Workflow

### **Rapid Development Cycle**

```bash
# Quick deployment for development
vagrant up splunk --provision

# Test configuration changes
vagrant provision splunk

# Reset to clean state
vagrant destroy splunk
vagrant up splunk

# Export VM for sharing
VBoxManage export "GOAD-Blue-Splunk" --output "goad-blue-splunk.ova"
```

### **Integration Testing**

```bash
# Test GOAD integration
vagrant up
python3 goad-blue.py test_integration --platform virtualbox

# Validate data flow
python3 goad-blue.py test_data_flow --timeout 300

# Performance testing
python3 goad-blue.py performance_test --duration 3600
```

---

!!! success "VirtualBox Deployment Complete"
    Your GOAD-Blue environment is now running on VirtualBox, providing a cost-effective platform for development, testing, and learning.

!!! tip "VirtualBox Optimization"
    - Allocate sufficient RAM to avoid swapping
    - Use SSD storage for better I/O performance
    - Enable hardware acceleration features
    - Create snapshots before major changes
    - Monitor resource usage regularly

!!! warning "VirtualBox Limitations"
    - Lower performance compared to Type-1 hypervisors
    - Limited networking features
    - No live migration capabilities
    - Host system dependency for resources
    - Not suitable for production environments

!!! info "Development Benefits"
    - Free and open-source
    - Cross-platform compatibility
    - Easy snapshot and cloning
    - Good for learning and testing
    - Vagrant integration for automation
