[default]
dc01 ansible_host={{ip_range}}.30 dns_domain=dc01 dict_key=dc01 ansible_user=<EMAIL> ansible_password=8dCT-6546541qsdDJjgScp
dc02 ansible_host={{ip_range}}.31 dns_domain=dc02 dict_key=dc02 ansible_user=<EMAIL> ansible_password=Ufe-qsdaz789bVXSx9rk
srv01 ansible_host={{ip_range}}.32 dns_domain=dc02 dict_key=srv01 ansible_user=<EMAIL> ansible_password=Ufe-qsdaz789bVXSx9rk
srv02 ansible_host={{ip_range}}.33 dns_domain=dc02 dict_key=srv02 ansible_user=<EMAIL> ansible_password=Ufe-qsdaz789bVXSx9rk
srv03 ansible_host={{ip_range}}.34 dns_domain=dc02 dict_key=srv03 ansible_user=<EMAIL> ansible_password=Ufe-qsdaz789bVXSx9rk

[all:vars]
; domain_name : folder inside ad/
domain_name=NHA

; winrm connection (windows)
ansible_winrm_transport=ntlm
ansible_user=notused
ansible_password=notused
ansible_connection=winrm
ansible_winrm_server_cert_validation=ignore
ansible_winrm_operation_timeout_sec=400
ansible_winrm_read_timeout_sec=500

[domain]
dc01
dc02
srv01
srv02
srv03
