# -*- mode: ruby -*-
# vi: set ft=ruby :

# GOAD-Blue Vagrant Configuration
# Multi-VM setup for cybersecurity training environment

require 'yaml'

# Load configuration
config_file = File.join(File.dirname(__FILE__), 'config.yml')
if File.exist?(config_file)
  vm_config = YAML.load_file(config_file)
else
  puts "Configuration file not found: #{config_file}"
  exit 1
end

Vagrant.configure("2") do |config|
  # Global VM settings
  config.vm.box_check_update = false
  config.vm.boot_timeout = 600
  
  # Plugin requirements
  required_plugins = %w[vagrant-reload vagrant-vbguest]
  required_plugins.each do |plugin|
    unless Vagrant.has_plugin?(plugin)
      puts "Installing plugin: #{plugin}"
      system("vagrant plugin install #{plugin}")
    end
  end

  # Define VMs
  vm_config['vms'].each do |vm_name, vm_settings|
    config.vm.define vm_name do |vm|
      # Base box configuration
      vm.vm.box = vm_settings['box']
      vm.vm.box_version = vm_settings['box_version'] if vm_settings['box_version']
      vm.vm.hostname = vm_settings['hostname']
      
      # Network configuration
      if vm_settings['networks']
        vm_settings['networks'].each do |network|
          case network['type']
          when 'private_network'
            vm.vm.network "private_network", 
              ip: network['ip'],
              netmask: network['netmask'] || "*************",
              virtualbox__intnet: network['intnet'] || false
          when 'public_network'
            vm.vm.network "public_network", 
              bridge: network['bridge'] || nil
          when 'forwarded_port'
            vm.vm.network "forwarded_port",
              guest: network['guest'],
              host: network['host'],
              auto_correct: true
          end
        end
      end
      
      # Provider-specific configuration
      vm.vm.provider "virtualbox" do |vb|
        vb.name = vm_settings['hostname']
        vb.memory = vm_settings['memory'] || 2048
        vb.cpus = vm_settings['cpus'] || 2
        vb.gui = vm_settings['gui'] || false
        
        # Additional VirtualBox settings
        if vm_settings['vbox_settings']
          vm_settings['vbox_settings'].each do |setting, value|
            vb.customize ["modifyvm", :id, "--#{setting}", value]
          end
        end
        
        # Add additional disks if specified
        if vm_settings['additional_disks']
          vm_settings['additional_disks'].each_with_index do |disk, index|
            disk_path = File.join(File.dirname(__FILE__), ".vagrant", "#{vm_name}_disk_#{index}.vdi")
            unless File.exist?(disk_path)
              vb.customize ['createhd', '--filename', disk_path, '--size', disk['size']]
            end
            vb.customize ['storageattach', :id, '--storagectl', 'SATA Controller', '--port', index + 1, '--device', 0, '--type', 'hdd', '--medium', disk_path]
          end
        end
      end
      
      vm.vm.provider "vmware_desktop" do |vmware|
        vmware.vmx["displayName"] = vm_settings['hostname']
        vmware.vmx["memsize"] = vm_settings['memory'] || 2048
        vmware.vmx["numvcpus"] = vm_settings['cpus'] || 2
        vmware.gui = vm_settings['gui'] || false
        
        # VMware-specific settings
        if vm_settings['vmware_settings']
          vm_settings['vmware_settings'].each do |setting, value|
            vmware.vmx[setting] = value
          end
        end
      end
      
      # Shared folders
      if vm_settings['shared_folders']
        vm_settings['shared_folders'].each do |folder|
          vm.vm.synced_folder folder['host'], folder['guest'],
            type: folder['type'] || 'virtualbox',
            disabled: folder['disabled'] || false
        end
      end
      
      # SSH configuration
      if vm_settings['ssh']
        vm.vm.ssh.username = vm_settings['ssh']['username'] if vm_settings['ssh']['username']
        vm.vm.ssh.password = vm_settings['ssh']['password'] if vm_settings['ssh']['password']
        vm.vm.ssh.insert_key = vm_settings['ssh']['insert_key'] if vm_settings['ssh']['insert_key']
        vm.vm.ssh.forward_agent = vm_settings['ssh']['forward_agent'] if vm_settings['ssh']['forward_agent']
      end
      
      # WinRM configuration for Windows VMs
      if vm_settings['winrm']
        vm.vm.communicator = "winrm"
        vm.vm.winrm.username = vm_settings['winrm']['username']
        vm.vm.winrm.password = vm_settings['winrm']['password']
        vm.vm.winrm.timeout = vm_settings['winrm']['timeout'] || 1800
      end
      
      # Provisioning scripts
      if vm_settings['provisioners']
        vm_settings['provisioners'].each do |provisioner|
          case provisioner['type']
          when 'shell'
            if provisioner['inline']
              vm.vm.provision "shell", inline: provisioner['inline'], privileged: provisioner['privileged'] || true
            elsif provisioner['path']
              vm.vm.provision "shell", path: provisioner['path'], privileged: provisioner['privileged'] || true, args: provisioner['args'] || []
            end
          when 'powershell'
            if provisioner['inline']
              vm.vm.provision "shell", inline: provisioner['inline'], powershell_elevated_interactive: true
            elsif provisioner['path']
              vm.vm.provision "shell", path: provisioner['path'], powershell_elevated_interactive: true, args: provisioner['args'] || []
            end
          when 'ansible'
            vm.vm.provision "ansible" do |ansible|
              ansible.playbook = provisioner['playbook']
              ansible.inventory_path = provisioner['inventory'] if provisioner['inventory']
              ansible.limit = provisioner['limit'] if provisioner['limit']
              ansible.extra_vars = provisioner['extra_vars'] if provisioner['extra_vars']
              ansible.verbose = provisioner['verbose'] if provisioner['verbose']
            end
          when 'file'
            vm.vm.provision "file", source: provisioner['source'], destination: provisioner['destination']
          when 'reload'
            vm.vm.provision :reload
          end
        end
      end
      
      # Post-provisioning tasks
      if vm_settings['post_provision']
        vm_settings['post_provision'].each do |task|
          case task['type']
          when 'shell'
            vm.vm.provision "shell", inline: task['command'], run: "always"
          when 'message'
            vm.vm.provision "shell", inline: "echo '#{task['message']}'"
          end
        end
      end
    end
  end
  
  # Global provisioning (runs on all VMs)
  config.vm.provision "shell", inline: <<-SHELL
    echo "GOAD-Blue VM provisioning started at $(date)"
    echo "Hostname: $(hostname)"
    echo "IP Address: $(hostname -I | awk '{print $1}')"
  SHELL
end
