# Performance Issues

This guide covers diagnosing and resolving performance problems in GOAD-Blue environments, including resource optimization and scaling strategies.

## 🔍 Performance Monitoring

### **System Resource Monitoring**

**Real-time Monitoring Commands:**
```bash
# CPU and Memory monitoring
top -o %CPU
htop
vmstat 1 10

# Disk I/O monitoring
iotop
iostat -x 1 10

# Network monitoring
iftop
nethogs
ss -tuln

# Process monitoring
ps aux --sort=-%cpu | head -20
ps aux --sort=-%mem | head -20
```

**Automated Performance Collection:**
```bash
#!/bin/bash
# Performance data collection script

LOG_FILE="/var/log/goad-blue-performance.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

echo "=== Performance Report - $DATE ===" >> $LOG_FILE

# System load
echo "Load Average:" >> $LOG_FILE
uptime >> $LOG_FILE

# Memory usage
echo "Memory Usage:" >> $LOG_FILE
free -h >> $LOG_FILE

# Disk usage
echo "Disk Usage:" >> $LOG_FILE
df -h >> $LOG_FILE

# Top processes by CPU
echo "Top CPU Processes:" >> $LOG_FILE
ps aux --sort=-%cpu | head -10 >> $LOG_FILE

# Top processes by Memory
echo "Top Memory Processes:" >> $LOG_FILE
ps aux --sort=-%mem | head -10 >> $LOG_FILE

echo "========================" >> $LOG_FILE
```

### **Component-Specific Monitoring**

**Splunk Performance:**
```bash
# Splunk performance metrics
curl -k -u admin:password "https://localhost:8000/services/server/introspection/indexer?output_mode=json"

# Check indexing performance
curl -k -u admin:password "https://localhost:8000/services/data/indexes?output_mode=json"

# Monitor search performance
tail -f /opt/splunk/var/log/splunk/metrics.log | grep search
```

**Elasticsearch Performance:**
```bash
# Cluster stats
curl -X GET "localhost:9200/_cluster/stats?pretty"

# Node stats
curl -X GET "localhost:9200/_nodes/stats?pretty"

# Index performance
curl -X GET "localhost:9200/_cat/indices?v&s=store.size:desc"

# Query performance
curl -X GET "localhost:9200/_cat/thread_pool?v&h=name,active,rejected,completed"
```

## ⚡ Common Performance Issues

### **High CPU Usage**

**Problem:** System experiencing high CPU utilization.

**Diagnostic Steps:**
```bash
# Identify CPU-intensive processes
top -o %CPU
ps aux --sort=-%cpu | head -10

# Check CPU frequency scaling
cat /proc/cpuinfo | grep MHz
cpufreq-info

# Monitor CPU usage over time
sar -u 1 60 > cpu_usage.log
```

**Solutions:**

**Process Optimization:**
```bash
# Adjust process priorities
sudo renice -10 $(pgrep splunk)
sudo renice 5 $(pgrep suricata)

# Limit CPU usage with systemd
sudo systemctl edit splunk
# Add:
[Service]
CPUQuota=50%
```

**Splunk CPU Optimization:**
```conf
# server.conf
[general]
parallelIngestionPipelines = 2
maxKBps = 1024

[indexing]
maxMemMB = 500
maxDataSize = auto_high_volume
```

**Suricata CPU Optimization:**
```yaml
# suricata.yaml
threading:
  set-cpu-affinity: yes
  cpu-affinity:
    - management-cpu-set:
        cpu: [ 0 ]
    - receive-cpu-set:
        cpu: [ 1, 2 ]
    - worker-cpu-set:
        cpu: [ 3, 4, 5, 6 ]
```

### **Memory Issues**

**Problem:** High memory usage or out-of-memory errors.

**Memory Analysis:**
```bash
# Check memory usage
free -h
cat /proc/meminfo

# Identify memory-intensive processes
ps aux --sort=-%mem | head -10

# Check for memory leaks
valgrind --tool=memcheck --leak-check=full program

# Monitor memory over time
sar -r 1 60 > memory_usage.log
```

**Memory Optimization:**

**System-level:**
```bash
# Adjust swappiness
echo 'vm.swappiness=10' >> /etc/sysctl.conf

# Configure huge pages
echo 'vm.nr_hugepages=1024' >> /etc/sysctl.conf

# Apply changes
sysctl -p
```

**Elasticsearch Memory:**
```bash
# Set heap size (50% of available RAM, max 32GB)
echo "-Xms8g" >> /etc/elasticsearch/jvm.options
echo "-Xmx8g" >> /etc/elasticsearch/jvm.options

# Disable swap for Elasticsearch
echo "bootstrap.memory_lock: true" >> /etc/elasticsearch/elasticsearch.yml
```

**Splunk Memory:**
```conf
# limits.conf
[indexing]
maxMemMB = 2048

[search]
max_mem_usage_mb = 4096
max_searches_per_cpu = 1
```

### **Disk I/O Performance**

**Problem:** Slow disk performance affecting system responsiveness.

**Disk Analysis:**
```bash
# Check disk I/O
iostat -x 1 10
iotop -o

# Test disk performance
dd if=/dev/zero of=/tmp/testfile bs=1G count=1 oflag=direct
hdparm -tT /dev/sda

# Check disk usage
df -h
du -sh /opt/splunk/var/lib/splunk/
```

**Disk Optimization:**

**File System Tuning:**
```bash
# Mount options for performance
mount -o noatime,nodiratime /dev/sdb1 /opt/splunk

# Add to /etc/fstab
/dev/sdb1 /opt/splunk ext4 noatime,nodiratime 0 2

# SSD optimization
echo deadline > /sys/block/sda/queue/scheduler
```

**Splunk Disk Optimization:**
```conf
# indexes.conf
[goad_blue_main]
homePath = /fast_disk/splunk/goad_blue_main/db
coldPath = /slow_disk/splunk/goad_blue_main/colddb
thawedPath = /slow_disk/splunk/goad_blue_main/thaweddb
maxDataSize = 1024MB
maxHotBuckets = 3
```

**Log Rotation:**
```bash
# Configure logrotate for GOAD-Blue logs
cat > /etc/logrotate.d/goad-blue << EOF
/var/log/goad-blue/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 goad-blue goad-blue
}
EOF
```

### **Network Performance Issues**

**Problem:** Network latency or bandwidth limitations.

**Network Analysis:**
```bash
# Test network performance
iperf3 -s  # On server
iperf3 -c server_ip  # On client

# Check network utilization
iftop -i eth0
nethogs

# Monitor network statistics
ss -i
netstat -i
```

**Network Optimization:**

**Network Interface Tuning:**
```bash
# Increase network buffers
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.wmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_rmem = 4096 87380 134217728' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_wmem = 4096 65536 134217728' >> /etc/sysctl.conf

# Apply changes
sysctl -p
```

**Splunk Network Optimization:**
```conf
# outputs.conf
[tcpout]
defaultGroup = default-autolb-group
compressed = true
useACK = true

[tcpout:default-autolb-group]
server = **************:9997
sendCookedData = true
```

## 🚀 Performance Optimization Strategies

### **Resource Allocation Optimization**

**VM Resource Tuning:**
```bash
# Check current VM resources
virsh dominfo goad-blue-siem
virsh dominfo goad-blue-monitoring

# Adjust VM resources
virsh setmem goad-blue-siem 16G --config
virsh setvcpus goad-blue-siem 8 --config

# For VMware
vmrun writeVariable /path/to/vm.vmx guestVar memsize 16384
vmrun writeVariable /path/to/vm.vmx guestVar numvcpus 8
```

**Container Resource Limits:**
```yaml
# docker-compose.yml
version: '3.8'
services:
  elasticsearch:
    image: elasticsearch:7.15.0
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4.0'
        reservations:
          memory: 4G
          cpus: '2.0'
```

### **Database Optimization**

**Elasticsearch Optimization:**
```yaml
# elasticsearch.yml
cluster.name: goad-blue-cluster
node.name: goad-blue-node-1

# Memory settings
bootstrap.memory_lock: true

# Index settings
index.number_of_shards: 1
index.number_of_replicas: 0
index.refresh_interval: 30s

# Performance settings
indices.memory.index_buffer_size: 20%
indices.fielddata.cache.size: 40%
```

**Index Lifecycle Management:**
```json
{
  "policy": {
    "phases": {
      "hot": {
        "actions": {
          "rollover": {
            "max_size": "10GB",
            "max_age": "7d"
          }
        }
      },
      "warm": {
        "min_age": "7d",
        "actions": {
          "allocate": {
            "number_of_replicas": 0
          }
        }
      },
      "cold": {
        "min_age": "30d",
        "actions": {
          "allocate": {
            "number_of_replicas": 0
          }
        }
      },
      "delete": {
        "min_age": "90d"
      }
    }
  }
}
```

### **Monitoring and Alerting Optimization**

**Suricata Performance Tuning:**
```yaml
# suricata.yaml
max-pending-packets: 1024
runmode: workers

threading:
  set-cpu-affinity: yes
  detect-thread-ratio: 1.5

af-packet:
  - interface: eth1
    threads: 4
    cluster-id: 99
    cluster-type: cluster_flow
    defrag: yes
    use-mmap: yes
    ring-size: 200000
```

**Velociraptor Performance:**
```yaml
# server.config.yaml
Client:
  max_poll: 60
  max_poll_std: 30

Frontend:
  concurrency: 20
  max_upload_size: 52428800

Datastore:
  implementation: FileBaseDataStore
  location: /opt/velociraptor/datastore
  filestore_directory: /opt/velociraptor/filestore
```

## 📊 Performance Benchmarking

### **Benchmarking Scripts**

```bash
#!/bin/bash
# GOAD-Blue performance benchmark

echo "=== GOAD-Blue Performance Benchmark ==="

# System information
echo "System Information:"
echo "CPU: $(nproc) cores"
echo "Memory: $(free -h | grep Mem | awk '{print $2}')"
echo "Disk: $(df -h / | tail -1 | awk '{print $2}')"

# CPU benchmark
echo "CPU Benchmark:"
time dd if=/dev/zero bs=1M count=1024 | md5sum

# Memory benchmark
echo "Memory Benchmark:"
time dd if=/dev/zero of=/dev/null bs=1M count=1024

# Disk benchmark
echo "Disk Benchmark:"
time dd if=/dev/zero of=/tmp/benchmark bs=1M count=1024 oflag=direct
rm /tmp/benchmark

# Network benchmark (if iperf3 available)
if command -v iperf3 &> /dev/null; then
    echo "Network Benchmark:"
    iperf3 -c localhost -t 10
fi

# SIEM performance test
echo "SIEM Performance Test:"
time python3 goad-blue.py test_siem_performance

echo "Benchmark complete"
```

### **Performance Monitoring Dashboard**

```python
#!/usr/bin/env python3
# Performance monitoring dashboard

import psutil
import time
import json
from datetime import datetime

def collect_metrics():
    """Collect system performance metrics"""
    return {
        'timestamp': datetime.now().isoformat(),
        'cpu': {
            'percent': psutil.cpu_percent(interval=1),
            'count': psutil.cpu_count(),
            'freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
        },
        'memory': {
            'total': psutil.virtual_memory().total,
            'available': psutil.virtual_memory().available,
            'percent': psutil.virtual_memory().percent,
            'used': psutil.virtual_memory().used
        },
        'disk': {
            'total': psutil.disk_usage('/').total,
            'used': psutil.disk_usage('/').used,
            'free': psutil.disk_usage('/').free,
            'percent': psutil.disk_usage('/').percent
        },
        'network': {
            'bytes_sent': psutil.net_io_counters().bytes_sent,
            'bytes_recv': psutil.net_io_counters().bytes_recv,
            'packets_sent': psutil.net_io_counters().packets_sent,
            'packets_recv': psutil.net_io_counters().packets_recv
        }
    }

def main():
    """Main monitoring loop"""
    while True:
        metrics = collect_metrics()
        
        # Save to file
        with open('/var/log/goad-blue-metrics.json', 'a') as f:
            f.write(json.dumps(metrics) + '\n')
        
        # Print summary
        print(f"CPU: {metrics['cpu']['percent']:.1f}% | "
              f"Memory: {metrics['memory']['percent']:.1f}% | "
              f"Disk: {metrics['disk']['percent']:.1f}%")
        
        time.sleep(60)  # Collect every minute

if __name__ == "__main__":
    main()
```

---

!!! tip "Performance Optimization Tips"
    - Monitor resource usage continuously
    - Implement proper log rotation and retention
    - Use SSD storage for high I/O components
    - Tune component configurations for your workload
    - Scale horizontally when vertical scaling isn't sufficient

!!! warning "Performance Pitfalls"
    - Don't over-allocate resources to VMs
    - Avoid running too many services on single hosts
    - Monitor for resource contention between components
    - Be careful with aggressive log retention policies
    - Test performance changes in non-production environments

!!! info "Scaling Strategies"
    - Implement load balancing for high-availability
    - Use distributed architectures for large deployments
    - Consider cloud-based scaling for variable workloads
    - Monitor performance trends to predict scaling needs
    - Document performance baselines and optimization procedures
