# GOAD-Blue Proxmox Deployment Quick Start

This guide provides a quick start for deploying GOAD-Blue on Proxmox VE.

## 🚀 Quick Installation

### **Prerequisites**
- Proxmox VE 7.4+ or 8.0+
- 64GB RAM minimum (128GB recommended)
- 1TB storage minimum
- Network bridges configured
- SSH key pair generated

### **One-Line Installation**
```bash
curl -sSL https://raw.githubusercontent.com/your-org/goad-blue/main/scripts/install-proxmox.sh | bash -s -- --host YOUR_PROXMOX_IP
```

### **Manual Installation**
```bash
# Clone repository
git clone https://github.com/your-org/goad-blue.git
cd goad-blue

# Run Proxmox installer
./scripts/install-proxmox.sh --host ************* --password your-password
```

## 📋 Pre-Installation Checklist

### **1. Proxmox VE Setup**
- [ ] Proxmox VE installed and accessible
- [ ] Web interface available at https://your-proxmox:8006
- [ ] Root or admin user credentials available
- [ ] Network bridges configured (vmbr1, vmbr2)
- [ ] Sufficient storage allocated

### **2. Network Configuration**
- [ ] Management network: ***********/24 (or your existing network)
- [ ] GOAD-Blue network: *************/24
- [ ] Analysis network: *************/24 (isolated)
- [ ] Internet connectivity for downloads

### **3. Templates Preparation**
```bash
# Create VM templates (run on Proxmox host)
./scripts/create-proxmox-templates.sh

# Or create manually:
# 1. Download Ubuntu 22.04 ISO
# 2. Create VM with cloud-init support
# 3. Install Ubuntu with minimal packages
# 4. Convert to template
```

### **4. SSH Key Setup**
```bash
# Generate SSH key if not exists
ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa

# Verify public key exists
ls -la ~/.ssh/id_rsa.pub
```

## ⚙️ Configuration Options

### **Minimal Deployment (Development)**
```bash
./scripts/install-proxmox.sh \
  --host ************* \
  --password your-password \
  --minimal
```

**Components:**
- Splunk Enterprise (4 cores, 8GB RAM)
- Security Onion Manager (4 cores, 8GB RAM)
- Velociraptor (2 cores, 4GB RAM)

**Total Resources:** 10 cores, 20GB RAM, 250GB storage

### **Standard Deployment (Training)**
```bash
./scripts/install-proxmox.sh \
  --host ************* \
  --user root@pam \
  --password your-password \
  --node pve
```

**Components:**
- Splunk Enterprise (4 cores, 8GB RAM)
- Security Onion Manager (8 cores, 16GB RAM)
- Security Onion Sensors x2 (4 cores, 8GB RAM each)
- Velociraptor (2 cores, 4GB RAM)
- MISP (2 cores, 4GB RAM)

**Total Resources:** 24 cores, 48GB RAM, 600GB storage

### **Enterprise Deployment (Production)**
```bash
# Edit terraform.tfvars for production settings
cp terraform/environments/proxmox/terraform.tfvars.example \
   terraform/environments/proxmox/terraform.tfvars

# Customize for production
nano terraform/environments/proxmox/terraform.tfvars

# Deploy with custom configuration
./scripts/install-proxmox.sh \
  --host ************* \
  --config terraform/environments/proxmox/terraform.tfvars
```

## 🔧 Manual Deployment Steps

### **1. Infrastructure Deployment**
```bash
cd terraform/environments/proxmox

# Initialize Terraform
terraform init

# Plan deployment
terraform plan -var-file="terraform.tfvars"

# Apply configuration
terraform apply -var-file="terraform.tfvars"
```

### **2. System Configuration**
```bash
cd ansible

# Generate inventory
python3 inventory/proxmox_inventory.py > inventory/proxmox_hosts.yml

# Configure systems
ansible-playbook -i inventory/proxmox_hosts.yml playbooks/site.yml
```

### **3. GOAD Integration**
```bash
# Deploy agents to GOAD VMs
ansible-playbook -i inventory/ playbooks/goad-integration.yml

# Verify integration
python3 goad-blue.py test_goad_integration
```

## 📊 Post-Deployment Verification

### **1. Check VM Status**
```bash
# On Proxmox host
qm list | grep goad-blue

# Check VM resources
pvesh get /nodes/pve/qemu --output-format=table
```

### **2. Verify Network Connectivity**
```bash
# Test connectivity to all components
ping **************  # Splunk
ping **************  # Security Onion
ping **************  # Velociraptor
ping **************  # MISP
```

### **3. Access Web Interfaces**
- **Splunk**: https://**************:8000
- **Security Onion**: https://**************
- **Velociraptor**: https://**************:8889
- **MISP**: https://**************

### **4. Verify Data Flow**
```bash
# Check SIEM data ingestion
python3 goad-blue.py test_data_flow

# Generate test events
python3 goad-blue.py generate_test_events

# Check component health
python3 goad-blue.py --health-check
```

## 🛠️ Troubleshooting

### **Common Issues**

#### **Template Not Found**
```bash
# Error: template 'ubuntu-22.04-template' not found
# Solution: Create templates first
./scripts/create-proxmox-templates.sh ubuntu
```

#### **Insufficient Resources**
```bash
# Error: not enough memory/CPU
# Solution: Adjust configuration
nano terraform/environments/proxmox/terraform.tfvars

# Reduce resource allocation
components = {
  splunk = {
    cores = 2
    memory = 4096
  }
}
```

#### **Network Connectivity Issues**
```bash
# Check bridge configuration
ip addr show vmbr1

# Verify routing
ip route show

# Test Proxmox API
curl -k https://*************:8006/api2/json
```

#### **Authentication Failures**
```bash
# Verify credentials
pvesh get /access/users

# Test API authentication
curl -k -d "username=root@pam&password=yourpass" \
  https://*************:8006/api2/json/access/ticket
```

### **Log Locations**
- **Terraform logs**: `terraform/environments/proxmox/terraform.log`
- **Ansible logs**: `ansible/logs/ansible.log`
- **VM console**: Proxmox web interface → VM → Console
- **System logs**: `/var/log/` on each VM

## 📚 Additional Resources

### **Documentation**
- [Complete Proxmox Guide](proxmox.md)
- [Network Configuration](../configuration/network.md)
- [Troubleshooting Guide](../troubleshooting/)

### **Proxmox Resources**
- [Proxmox VE Documentation](https://pve.proxmox.com/pve-docs/)
- [Terraform Proxmox Provider](https://registry.terraform.io/providers/Telmate/proxmox/latest/docs)
- [Proxmox Community Forum](https://forum.proxmox.com/)

### **Support**
- **GitHub Issues**: https://github.com/your-org/goad-blue/issues
- **Discord**: #goad-blue-support
- **Documentation**: https://goad-blue.readthedocs.io

---

!!! tip "Performance Optimization"
    For best performance on Proxmox:
    
    - Use ZFS storage with SSD
    - Enable CPU host passthrough
    - Use virtio drivers for network and storage
    - Allocate sufficient RAM to avoid swapping

!!! warning "Security Notice"
    This deployment creates a lab environment. Ensure proper network isolation and change all default passwords before production use.

!!! info "GOAD Integration"
    If you have an existing GOAD installation, the installer will automatically detect and integrate with it. Ensure GOAD VMs are accessible from the GOAD-Blue network.
