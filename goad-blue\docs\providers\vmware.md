# VMware Deployment Guide

This guide covers deploying GOAD-Blue on VMware platforms including vSphere/ESXi, Workstation, and Player using Infrastructure as Code and automation tools.

## 🏗️ VMware Architecture

```mermaid
graph TB
    subgraph "🏢 VMware Infrastructure"
        subgraph "🖥️ ESXi Hosts"
            ESXi1[🖥️ ESXi Host 1<br/>************<br/>Dell PowerEdge R740]
            ESXi2[🖥️ ESXi Host 2<br/>************<br/>Dell PowerEdge R740]
            ESXi3[🖥️ ESXi Host 3<br/>************<br/>Dell PowerEdge R740]
        end
        
        subgraph "🎛️ Management Layer"
            VCENTER[🎛️ vCenter Server<br/>************0<br/>Cluster Management]
            NSX[🌐 NSX Manager<br/>************1<br/>Network Virtualization]
        end
        
        subgraph "💾 Storage"
            VSAN[💾 vSAN Datastore<br/>All-Flash Configuration<br/>10TB Capacity]
            NFS[🗄️ NFS Datastore<br/>Synology NAS<br/>5TB Capacity]
        end
    end
    
    subgraph "🌐 Network Infrastructure"
        subgraph "🔀 Distributed Switches"
            VDS1[🔀 Management-DSwitch<br/>Management Traffic]
            VDS2[🔀 GOAD-Blue-DSwitch<br/>Lab Traffic]
            VDS3[🔀 Storage-DSwitch<br/>Storage Traffic]
        end
        
        subgraph "📡 Port Groups"
            PG_MGMT[📡 Management-PG<br/>***********/24]
            PG_GOAD[📡 GOAD-Network-PG<br/>************/24]
            PG_BLUE[📡 GOAD-Blue-PG<br/>*************/24]
            PG_ANALYSIS[📡 Analysis-PG<br/>*************/24]
            PG_STORAGE[📡 Storage-PG<br/>192.168.10.0/24]
        end
    end
    
    subgraph "🛡️ GOAD-Blue VMs"
        SPLUNK[📊 Splunk Enterprise<br/>8 vCPU, 16GB RAM<br/>500GB Storage]
        SO_MGR[🧅 Security Onion Manager<br/>16 vCPU, 32GB RAM<br/>1TB Storage]
        SO_SENSOR[📡 Security Onion Sensor<br/>8 vCPU, 16GB RAM<br/>500GB Storage]
        VELO[🦖 Velociraptor Server<br/>4 vCPU, 8GB RAM<br/>200GB Storage]
        MISP[🧠 MISP Server<br/>4 vCPU, 8GB RAM<br/>200GB Storage]
        FLARE[🔥 FLARE-VM<br/>8 vCPU, 16GB RAM<br/>500GB Storage]
    end
    
    subgraph "🎮 GOAD Integration"
        GOAD_DC[🏰 GOAD Domain Controllers<br/>Existing GOAD VMs]
        GOAD_SRV[⚔️ GOAD Servers<br/>Member Systems]
        GOAD_WS[🖥️ GOAD Workstations<br/>User Systems]
    end
    
    %% Infrastructure connections
    VCENTER --> ESXi1
    VCENTER --> ESXi2
    VCENTER --> ESXi3
    
    NSX --> VDS2
    
    ESXi1 --> VSAN
    ESXi2 --> VSAN
    ESXi3 --> VSAN
    
    %% Network connections
    VDS1 --> PG_MGMT
    VDS2 --> PG_GOAD
    VDS2 --> PG_BLUE
    VDS2 --> PG_ANALYSIS
    VDS3 --> PG_STORAGE
    
    %% VM connections
    PG_BLUE --> SPLUNK
    PG_BLUE --> SO_MGR
    PG_BLUE --> SO_SENSOR
    PG_BLUE --> VELO
    PG_BLUE --> MISP
    PG_ANALYSIS --> FLARE
    
    PG_GOAD --> GOAD_DC
    PG_GOAD --> GOAD_SRV
    PG_GOAD --> GOAD_WS
    
    classDef infrastructure fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef management fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef storage fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef network fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef goadblue fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef goad fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class ESXi1,ESXi2,ESXi3 infrastructure
    class VCENTER,NSX management
    class VSAN,NFS storage
    class VDS1,VDS2,VDS3,PG_MGMT,PG_GOAD,PG_BLUE,PG_ANALYSIS,PG_STORAGE network
    class SPLUNK,SO_MGR,SO_SENSOR,VELO,MISP,FLARE goadblue
    class GOAD_DC,GOAD_SRV,GOAD_WS goad
```

## 📋 Prerequisites

### **VMware Platform Requirements**

#### **vSphere/ESXi Environment**
- **vCenter Server**: 7.0+ or 8.0+
- **ESXi Hosts**: 7.0+ with sufficient resources
- **vSphere Client**: Web client or PowerCLI
- **Networking**: Distributed switches configured
- **Storage**: Shared storage (vSAN, NFS, or iSCSI)

#### **VMware Workstation/Player**
- **VMware Workstation**: Pro 16+ or Player 16+
- **Host System**: 64GB RAM minimum, 1TB storage
- **Network Configuration**: Multiple virtual networks
- **Virtualization**: Intel VT-x or AMD-V enabled

### **Required Tools**

```bash
# Install Terraform with VMware provider
terraform --version

# Install PowerCLI (Windows/PowerShell)
Install-Module -Name VMware.PowerCLI -Force

# Install govc (Linux/macOS)
curl -L -o - "https://github.com/vmware/govmomi/releases/latest/download/govc_$(uname -s)_$(uname -m).tar.gz" | tar -C /usr/local/bin -xvzf - govc

# Install Ansible with VMware collection
ansible-galaxy collection install community.vmware
```

### **Authentication Setup**

#### **vSphere Authentication**

```bash
# Set environment variables for vSphere
export VSPHERE_SERVER="vcenter.example.com"
export VSPHERE_USER="<EMAIL>"
export VSPHERE_PASSWORD="your-password"
export VSPHERE_ALLOW_UNVERIFIED_SSL="true"

# Test connectivity with govc
govc about

# Test with PowerCLI
Connect-VIServer -Server $env:VSPHERE_SERVER -User $env:VSPHERE_USER -Password $env:VSPHERE_PASSWORD
```

#### **Workstation Authentication**

```bash
# VMware Workstation uses local authentication
# Ensure VMware services are running
sudo systemctl start vmware
sudo systemctl enable vmware

# Verify installation
vmrun list
```

## 🚀 Deployment Process

### **1. vSphere Deployment**

#### **Terraform Configuration for vSphere**

```hcl
# terraform/providers/vmware/vsphere/main.tf
terraform {
  required_version = ">= 1.0"
  required_providers {
    vsphere = {
      source  = "hashicorp/vsphere"
      version = "~> 2.0"
    }
  }
}

provider "vsphere" {
  user                 = var.vsphere_user
  password             = var.vsphere_password
  vsphere_server       = var.vsphere_server
  allow_unverified_ssl = var.allow_unverified_ssl
}

# Data sources
data "vsphere_datacenter" "datacenter" {
  name = var.datacenter_name
}

data "vsphere_compute_cluster" "cluster" {
  name          = var.cluster_name
  datacenter_id = data.vsphere_datacenter.datacenter.id
}

data "vsphere_datastore" "datastore" {
  name          = var.datastore_name
  datacenter_id = data.vsphere_datacenter.datacenter.id
}

data "vsphere_network" "goad_blue_network" {
  name          = var.goad_blue_network_name
  datacenter_id = data.vsphere_datacenter.datacenter.id
}

data "vsphere_network" "goad_network" {
  name          = var.goad_network_name
  datacenter_id = data.vsphere_datacenter.datacenter.id
}

data "vsphere_virtual_machine" "ubuntu_template" {
  name          = var.ubuntu_template_name
  datacenter_id = data.vsphere_datacenter.datacenter.id
}

# Resource pool for GOAD-Blue
resource "vsphere_resource_pool" "goad_blue_pool" {
  name                    = "GOAD-Blue-Pool"
  parent_resource_pool_id = data.vsphere_compute_cluster.cluster.resource_pool_id
  
  cpu_share_level    = "normal"
  cpu_reservation    = 8000
  cpu_expandable     = true
  cpu_limit          = -1
  
  memory_share_level = "normal"
  memory_reservation = 32768
  memory_expandable  = true
  memory_limit       = -1
}

# Splunk Enterprise VM
resource "vsphere_virtual_machine" "splunk_enterprise" {
  name             = "${var.name_prefix}-splunk"
  resource_pool_id = vsphere_resource_pool.goad_blue_pool.id
  datastore_id     = data.vsphere_datastore.datastore.id
  folder           = var.vm_folder
  
  num_cpus  = 8
  memory    = 16384
  guest_id  = data.vsphere_virtual_machine.ubuntu_template.guest_id
  scsi_type = data.vsphere_virtual_machine.ubuntu_template.scsi_type
  
  network_interface {
    network_id   = data.vsphere_network.goad_blue_network.id
    adapter_type = data.vsphere_virtual_machine.ubuntu_template.network_interface_types[0]
  }
  
  disk {
    label            = "disk0"
    size             = 500
    thin_provisioned = true
  }
  
  clone {
    template_uuid = data.vsphere_virtual_machine.ubuntu_template.id
    
    customize {
      linux_options {
        host_name = "splunk"
        domain    = "goad-blue.local"
      }
      
      network_interface {
        ipv4_address = "*************0"
        ipv4_netmask = 24
      }
      
      ipv4_gateway = "*************"
      dns_server_list = ["*************", "*******"]
    }
  }
  
  tags = [vsphere_tag.goad_blue.id, vsphere_tag.splunk.id]
}

# Security Onion Manager VM
resource "vsphere_virtual_machine" "security_onion_manager" {
  name             = "${var.name_prefix}-so-manager"
  resource_pool_id = vsphere_resource_pool.goad_blue_pool.id
  datastore_id     = data.vsphere_datastore.datastore.id
  folder           = var.vm_folder
  
  num_cpus  = 16
  memory    = 32768
  guest_id  = data.vsphere_virtual_machine.ubuntu_template.guest_id
  scsi_type = data.vsphere_virtual_machine.ubuntu_template.scsi_type
  
  network_interface {
    network_id   = data.vsphere_network.goad_blue_network.id
    adapter_type = data.vsphere_virtual_machine.ubuntu_template.network_interface_types[0]
  }
  
  # Additional network interface for monitoring
  network_interface {
    network_id   = data.vsphere_network.goad_network.id
    adapter_type = "vmxnet3"
  }
  
  disk {
    label            = "disk0"
    size             = 1000
    thin_provisioned = true
  }
  
  clone {
    template_uuid = data.vsphere_virtual_machine.ubuntu_template.id
    
    customize {
      linux_options {
        host_name = "so-manager"
        domain    = "goad-blue.local"
      }
      
      network_interface {
        ipv4_address = "**************"
        ipv4_netmask = 24
      }
      
      network_interface {}  # DHCP for monitoring interface
      
      ipv4_gateway = "*************"
      dns_server_list = ["*************", "*******"]
    }
  }
  
  tags = [vsphere_tag.goad_blue.id, vsphere_tag.security_onion.id]
}

# Tags for organization
resource "vsphere_tag_category" "goad_blue_category" {
  name               = "GOAD-Blue"
  description        = "GOAD-Blue component tags"
  cardinality        = "MULTIPLE"
  associable_types   = ["VirtualMachine"]
}

resource "vsphere_tag" "goad_blue" {
  name        = "goad-blue"
  category_id = vsphere_tag_category.goad_blue_category.id
  description = "GOAD-Blue infrastructure"
}

resource "vsphere_tag" "splunk" {
  name        = "splunk"
  category_id = vsphere_tag_category.goad_blue_category.id
  description = "Splunk Enterprise"
}

resource "vsphere_tag" "security_onion" {
  name        = "security-onion"
  category_id = vsphere_tag_category.goad_blue_category.id
  description = "Security Onion"
}
```

#### **Variables for vSphere**

```hcl
# terraform/providers/vmware/vsphere/variables.tf
variable "vsphere_server" {
  description = "vSphere server FQDN or IP"
  type        = string
}

variable "vsphere_user" {
  description = "vSphere username"
  type        = string
}

variable "vsphere_password" {
  description = "vSphere password"
  type        = string
  sensitive   = true
}

variable "allow_unverified_ssl" {
  description = "Allow unverified SSL certificates"
  type        = bool
  default     = true
}

variable "datacenter_name" {
  description = "vSphere datacenter name"
  type        = string
  default     = "Datacenter"
}

variable "cluster_name" {
  description = "vSphere cluster name"
  type        = string
  default     = "Cluster"
}

variable "datastore_name" {
  description = "vSphere datastore name"
  type        = string
  default     = "datastore1"
}

variable "goad_blue_network_name" {
  description = "GOAD-Blue network name"
  type        = string
  default     = "GOAD-Blue-Network"
}

variable "goad_network_name" {
  description = "GOAD network name"
  type        = string
  default     = "GOAD-Network"
}

variable "ubuntu_template_name" {
  description = "Ubuntu template name"
  type        = string
  default     = "ubuntu-22.04-template"
}

variable "name_prefix" {
  description = "Prefix for VM names"
  type        = string
  default     = "goad-blue"
}

variable "vm_folder" {
  description = "VM folder in vCenter"
  type        = string
  default     = "GOAD-Blue"
}
```

### **2. VMware Workstation Deployment**

#### **Terraform Configuration for Workstation**

```hcl
# terraform/providers/vmware/workstation/main.tf
terraform {
  required_providers {
    vmware = {
      source  = "elsudano/vmware"
      version = "~> 1.0"
    }
  }
}

provider "vmware" {
  # VMware Workstation provider configuration
}

# Create virtual networks
resource "vmware_vnet" "goad_blue_network" {
  name     = "vmnet2"
  type     = "hostonly"
  dhcp     = true
  subnet   = "*************"
  mask     = "*************"
  nat      = false
}

resource "vmware_vnet" "analysis_network" {
  name     = "vmnet3"
  type     = "hostonly"
  dhcp     = true
  subnet   = "*************"
  mask     = "*************"
  nat      = false
}

# Splunk Enterprise VM
resource "vmware_vm" "splunk_enterprise" {
  sourceid     = var.ubuntu_template_path
  name         = "goad-blue-splunk"
  path         = "${var.vm_path}/goad-blue-splunk"
  
  processors   = 8
  memory       = 16384
  
  network_adapter {
    type           = "custom"
    network        = vmware_vnet.goad_blue_network.name
    network_type   = "hostonly"
    adapter_type   = "e1000"
    start_connected = true
  }
  
  disk {
    name = "disk0.vmdk"
    size = 500000  # 500GB in MB
    type = "thin"
  }
  
  cdrom {
    type = "client"
  }
  
  boot_delay    = 10
  power_on      = true
  skip_shutdown = true
}

# Security Onion Manager VM
resource "vmware_vm" "security_onion_manager" {
  sourceid     = var.ubuntu_template_path
  name         = "goad-blue-so-manager"
  path         = "${var.vm_path}/goad-blue-so-manager"
  
  processors   = 16
  memory       = 32768
  
  network_adapter {
    type           = "custom"
    network        = vmware_vnet.goad_blue_network.name
    network_type   = "hostonly"
    adapter_type   = "e1000"
    start_connected = true
  }
  
  # Additional network adapter for monitoring
  network_adapter {
    type           = "custom"
    network        = "vmnet8"  # Existing GOAD network
    network_type   = "nat"
    adapter_type   = "e1000"
    start_connected = true
  }
  
  disk {
    name = "disk0.vmdk"
    size = 1000000  # 1TB in MB
    type = "thin"
  }
  
  power_on = true
}
```

### **3. Network Configuration**

#### **vSphere Distributed Switch Setup**

```powershell
# PowerCLI script for network configuration
Connect-VIServer -Server $env:VSPHERE_SERVER

# Create distributed switch
$vds = New-VDSwitch -Name "GOAD-Blue-DSwitch" -Location (Get-Datacenter)

# Add hosts to distributed switch
$vmhosts = Get-VMHost
Add-VDSwitchVMHost -VDSwitch $vds -VMHost $vmhosts

# Create port groups
New-VDPortgroup -VDSwitch $vds -Name "GOAD-Blue-Network" -VlanId 100
New-VDPortgroup -VDSwitch $vds -Name "GOAD-Network" -VlanId 56
New-VDPortgroup -VDSwitch $vds -Name "Analysis-Network" -VlanId 200

# Configure SPAN port for monitoring
$spanPG = New-VDPortgroup -VDSwitch $vds -Name "Monitoring-SPAN" -VlanId 999

# Enable promiscuous mode for monitoring
$securityPolicy = New-Object VMware.Vim.DVSSecurityPolicy
$securityPolicy.AllowPromiscuous = $true
$securityPolicy.ForgedTransmits = $true
$securityPolicy.MacChanges = $true

$spanPG | Set-VDPortgroup -Policy $securityPolicy
```

#### **Workstation Network Configuration**

```bash
# Configure VMware Workstation networks
sudo vmware-netcfg

# Edit network configuration files
sudo nano /etc/vmware/networking

# Add custom networks
echo "answer VNET_2_DHCP yes" | sudo tee -a /etc/vmware/networking
echo "answer VNET_2_HOSTONLY_NETMASK *************" | sudo tee -a /etc/vmware/networking
echo "answer VNET_2_HOSTONLY_SUBNET *************" | sudo tee -a /etc/vmware/networking

echo "answer VNET_3_DHCP yes" | sudo tee -a /etc/vmware/networking
echo "answer VNET_3_HOSTONLY_NETMASK *************" | sudo tee -a /etc/vmware/networking
echo "answer VNET_3_HOSTONLY_SUBNET *************" | sudo tee -a /etc/vmware/networking

# Restart VMware networking
sudo systemctl restart vmware-networks
```

### **4. Template Creation**

#### **Create VM Templates with Packer**

```json
{
  "variables": {
    "vsphere_server": "{{env `VSPHERE_SERVER`}}",
    "vsphere_username": "{{env `VSPHERE_USER`}}",
    "vsphere_password": "{{env `VSPHERE_PASSWORD`}}",
    "datacenter": "Datacenter",
    "cluster": "Cluster",
    "datastore": "datastore1",
    "network": "VM Network"
  },
  "builders": [
    {
      "type": "vsphere-iso",
      "vcenter_server": "{{user `vsphere_server`}}",
      "username": "{{user `vsphere_username`}}",
      "password": "{{user `vsphere_password`}}",
      "insecure_connection": true,
      
      "datacenter": "{{user `datacenter`}}",
      "cluster": "{{user `cluster`}}",
      "datastore": "{{user `datastore`}}",
      "network": "{{user `network`}}",
      
      "vm_name": "goad-blue-ubuntu-template",
      "guest_os_type": "ubuntu64Guest",
      
      "CPUs": 2,
      "RAM": 4096,
      "disk_controller_type": "pvscsi",
      "disk_size": 50000,
      "disk_thin_provisioned": true,
      
      "iso_paths": [
        "[datastore1] ISO/ubuntu-22.04.3-live-server-amd64.iso"
      ],
      
      "ssh_username": "ubuntu",
      "ssh_password": "ubuntu",
      "ssh_timeout": "20m",
      
      "boot_command": [
        "<enter><wait><f6><wait><esc><wait>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs>",
        "/install/vmlinuz",
        " initrd=/install/initrd.gz",
        " priority=critical",
        " locale=en_US",
        " file=/media/preseed.cfg",
        "<enter>"
      ],
      
      "convert_to_template": true
    }
  ],
  "provisioners": [
    {
      "type": "shell",
      "inline": [
        "sudo apt-get update",
        "sudo apt-get upgrade -y",
        "sudo apt-get install -y open-vm-tools"
      ]
    },
    {
      "type": "shell",
      "script": "../../scripts/vmware/prepare-template.sh"
    }
  ]
}
```

### **5. Ansible Configuration**

#### **VMware Dynamic Inventory**

```yaml
# ansible/inventory/vmware.yml
plugin: community.vmware.vmware_vm_inventory
strict: false
keyed_groups:
  - key: tags
    prefix: tag
  - key: guest_os
    prefix: os
  - key: power_state
    prefix: power
hostnames:
  - config.name
compose:
  ansible_host: guest.ipAddress
  ansible_user: ubuntu
```

#### **VMware-Specific Playbook**

```yaml
# ansible/playbooks/vmware/site.yml
---
- name: Configure VMware GOAD-Blue Infrastructure
  hosts: localhost
  gather_facts: false
  tasks:
    - name: Display deployment information
      debug:
        msg: |
          Configuring GOAD-Blue on VMware
          vCenter: {{ vcenter_hostname }}
          Datacenter: {{ datacenter_name }}

- name: Configure VMware tools and settings
  hosts: all
  become: true
  roles:
    - common
    - vmware-tools
    - vmware-optimization
  tags:
    - common
    - vmware

- name: Configure Splunk Enterprise
  hosts: tag_splunk
  become: true
  roles:
    - splunk-enterprise
    - vmware-splunk-integration
  tags:
    - splunk

- name: Configure Security Onion
  hosts: tag_security_onion
  become: true
  roles:
    - security-onion
    - vmware-security-onion-integration
  tags:
    - security-onion
```

## 🔧 VMware-Specific Features

### **vMotion and High Availability**

```powershell
# Enable vMotion on hosts
Get-VMHost | Get-VMHostNetworkAdapter -VMKernel | Set-VMHostNetworkAdapter -VMotionEnabled $true

# Configure HA cluster
$cluster = Get-Cluster "GOAD-Blue-Cluster"
$cluster | Set-Cluster -HAEnabled:$true -HAAdmissionControlEnabled:$true

# Configure DRS
$cluster | Set-Cluster -DrsEnabled:$true -DrsAutomationLevel "FullyAutomated"
```

### **Storage vMotion and Snapshots**

```powershell
# Create VM snapshots before major changes
Get-VM "goad-blue-*" | New-Snapshot -Name "Pre-Configuration" -Description "Before GOAD-Blue configuration"

# Storage vMotion for load balancing
Get-VM "goad-blue-splunk" | Move-VM -Datastore (Get-Datastore "datastore2")
```

### **Performance Monitoring**

```bash
# Install vSphere Performance Monitoring
# Configure esxtop for performance monitoring
esxtop -b -d 60 -n 1440 > /tmp/performance.csv

# Monitor VM performance
govc metric.sample vm/goad-blue-splunk cpu.usage.average
govc metric.sample vm/goad-blue-splunk mem.usage.average
```

---

!!! success "VMware Deployment Complete"
    Your GOAD-Blue environment is now deployed on VMware with enterprise-grade virtualization features including HA, DRS, and vMotion capabilities.

!!! tip "VMware Best Practices"
    - Use distributed switches for better network management
    - Enable DRS for automatic load balancing
    - Configure HA for high availability
    - Use vSAN for software-defined storage
    - Implement proper resource pools for isolation

!!! warning "Performance Considerations"
    - Monitor CPU and memory overcommitment ratios
    - Use appropriate VM hardware versions
    - Configure memory reservations for critical VMs
    - Enable CPU hot-add/hot-plug for flexibility
    - Use paravirtualized drivers for better performance
