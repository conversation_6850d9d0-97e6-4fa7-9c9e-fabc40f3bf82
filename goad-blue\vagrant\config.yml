# GOAD-Blue Vagrant VM Configuration

vms:
  # Splunk SIEM Server
  splunk-server:
    box: "ubuntu/jammy64"
    box_version: "20231215.0.0"
    hostname: "splunk-server.goad-blue.local"
    memory: 8192
    cpus: 4
    gui: false
    networks:
      - type: "private_network"
        ip: "**************"
        netmask: "*************"
        intnet: "goad-blue-siem"
      - type: "forwarded_port"
        guest: 8000
        host: 8000
      - type: "forwarded_port"
        guest: 8089
        host: 8089
    additional_disks:
      - size: 102400  # 100GB for Splunk data
    vbox_settings:
      natdnshostresolver1: "on"
      natdnsproxy1: "on"
    shared_folders:
      - host: "./shared"
        guest: "/vagrant_shared"
        type: "virtualbox"
    provisioners:
      - type: "shell"
        path: "scripts/common/update-system.sh"
      - type: "shell"
        path: "scripts/splunk/install-splunk.sh"
      - type: "ansible"
        playbook: "../ansible/goad-blue-splunk-server.yml"
        extra_vars:
          splunk_admin_password: "ChangeMeNow!"
    post_provision:
      - type: "message"
        message: "Splunk Server ready at https://**************:8000"

  # Security Onion Server
  security-onion:
    box: "ubuntu/jammy64"
    hostname: "security-onion.goad-blue.local"
    memory: 16384
    cpus: 4
    gui: false
    networks:
      - type: "private_network"
        ip: "**************"
        netmask: "*************"
        intnet: "goad-blue-monitor"
      - type: "private_network"
        ip: "*************"
        netmask: "*************"
        intnet: "goad-network"
        promiscuous: "allow-all"
    additional_disks:
      - size: 204800  # 200GB for logs and PCAP
    vbox_settings:
      natdnshostresolver1: "on"
      natdnsproxy1: "on"
      nicpromisc2: "allow-all"
      nicpromisc3: "allow-all"
    provisioners:
      - type: "shell"
        path: "scripts/common/update-system.sh"
      - type: "shell"
        path: "scripts/security-onion/install-security-onion.sh"
      - type: "ansible"
        playbook: "../ansible/goad-blue-security-onion.yml"
    post_provision:
      - type: "message"
        message: "Security Onion ready at https://**************"

  # Malcolm Network Analysis
  malcolm:
    box: "ubuntu/jammy64"
    hostname: "malcolm.goad-blue.local"
    memory: 12288
    cpus: 4
    gui: false
    networks:
      - type: "private_network"
        ip: "**************"
        netmask: "*************"
        intnet: "goad-blue-monitor"
      - type: "forwarded_port"
        guest: 443
        host: 8443
    additional_disks:
      - size: 102400  # 100GB for PCAP storage
    provisioners:
      - type: "shell"
        path: "scripts/common/update-system.sh"
      - type: "shell"
        path: "scripts/common/install-docker.sh"
      - type: "shell"
        path: "scripts/malcolm/install-malcolm.sh"
      - type: "ansible"
        playbook: "../ansible/goad-blue-malcolm.yml"
    post_provision:
      - type: "message"
        message: "Malcolm ready at https://**************"

  # Velociraptor Server
  velociraptor:
    box: "ubuntu/jammy64"
    hostname: "velociraptor.goad-blue.local"
    memory: 4096
    cpus: 2
    gui: false
    networks:
      - type: "private_network"
        ip: "**************"
        netmask: "*************"
        intnet: "goad-blue-siem"
      - type: "forwarded_port"
        guest: 8889
        host: 8889
      - type: "forwarded_port"
        guest: 8000
        host: 8001
    provisioners:
      - type: "shell"
        path: "scripts/common/update-system.sh"
      - type: "shell"
        path: "scripts/velociraptor/install-velociraptor.sh"
      - type: "ansible"
        playbook: "../ansible/goad-blue-velociraptor.yml"
    post_provision:
      - type: "message"
        message: "Velociraptor ready at https://**************:8889"

  # MISP Threat Intelligence
  misp:
    box: "ubuntu/jammy64"
    hostname: "misp.goad-blue.local"
    memory: 6144
    cpus: 2
    gui: false
    networks:
      - type: "private_network"
        ip: "**************"
        netmask: "*************"
        intnet: "goad-blue-siem"
      - type: "forwarded_port"
        guest: 443
        host: 8444
    provisioners:
      - type: "shell"
        path: "scripts/common/update-system.sh"
      - type: "shell"
        path: "scripts/misp/install-misp.sh"
      - type: "ansible"
        playbook: "../ansible/goad-blue-misp.yml"
    post_provision:
      - type: "message"
        message: "MISP ready at https://**************"

  # FLARE-VM Malware Analysis
  flare-vm:
    box: "gusztavvargadr/windows-10"
    hostname: "flare-vm"
    memory: 8192
    cpus: 4
    gui: true
    networks:
      - type: "private_network"
        ip: "**************"
        netmask: "*************"
        intnet: "goad-blue-analysis"
      - type: "forwarded_port"
        guest: 3389
        host: 3389
    winrm:
      username: "vagrant"
      password: "vagrant"
      timeout: 1800
    vbox_settings:
      natdnshostresolver1: "on"
      natdnsproxy1: "on"
      clipboard: "bidirectional"
      draganddrop: "bidirectional"
    provisioners:
      - type: "powershell"
        path: "scripts/common/disable-windows-defender.ps1"
      - type: "powershell"
        path: "scripts/flare-vm/configure-windows.ps1"
      - type: "powershell"
        path: "scripts/flare-vm/install-chocolatey.ps1"
      - type: "reload"
      - type: "powershell"
        path: "scripts/flare-vm/install-flare-vm.ps1"
      - type: "ansible"
        playbook: "../ansible/goad-blue-flare-vm.yml"
    post_provision:
      - type: "message"
        message: "FLARE-VM ready at ************** (RDP)"

  # Elasticsearch Server (Alternative to Splunk)
  elasticsearch:
    box: "ubuntu/jammy64"
    hostname: "elasticsearch.goad-blue.local"
    memory: 8192
    cpus: 4
    gui: false
    networks:
      - type: "private_network"
        ip: "**************"
        netmask: "*************"
        intnet: "goad-blue-siem"
      - type: "forwarded_port"
        guest: 9200
        host: 9200
      - type: "forwarded_port"
        guest: 5601
        host: 5601
    additional_disks:
      - size: 102400  # 100GB for Elasticsearch data
    provisioners:
      - type: "shell"
        path: "scripts/common/update-system.sh"
      - type: "shell"
        path: "scripts/elastic/install-elasticsearch.sh"
      - type: "shell"
        path: "scripts/elastic/install-kibana.sh"
      - type: "ansible"
        playbook: "../ansible/goad-blue-siem.yml"
        extra_vars:
          siem_type: "elastic"
    post_provision:
      - type: "message"
        message: "Elasticsearch ready at http://**************:9200, Kibana at http://**************:5601"

  # pfSense Firewall (FreeBSD)
  pfsense:
    box: "eugenmayer/pfsense-ce"
    hostname: "pfsense.goad-blue.local"
    memory: 2048
    cpus: 2
    gui: false
    networks:
      - type: "private_network"
        ip: "***********"
        netmask: "*************"
        intnet: "goad-blue-mgmt"
      - type: "private_network"
        ip: "*************"
        netmask: "*************"
        intnet: "goad-blue-siem"
      - type: "private_network"
        ip: "************"
        netmask: "*************"
        intnet: "goad-network"
      - type: "forwarded_port"
        guest: 443
        host: 8445
    provisioners:
      - type: "shell"
        path: "scripts/pfsense/configure-pfsense.sh"
    post_provision:
      - type: "message"
        message: "pfSense ready at https://***********"

  # Management/Jump Box
  management:
    box: "ubuntu/jammy64"
    hostname: "management.goad-blue.local"
    memory: 4096
    cpus: 2
    gui: false
    networks:
      - type: "private_network"
        ip: "************"
        netmask: "*************"
        intnet: "goad-blue-mgmt"
    shared_folders:
      - host: "../"
        guest: "/opt/goad-blue"
        type: "virtualbox"
    provisioners:
      - type: "shell"
        path: "scripts/common/update-system.sh"
      - type: "shell"
        path: "scripts/management/install-tools.sh"
      - type: "shell"
        path: "scripts/management/configure-ansible.sh"
    post_provision:
      - type: "message"
        message: "Management server ready at ************"
