# 🇮 instances

When you create a lab, goad will create an instance folder.
All the instances are stored in the workspace/ folder inside goad.

```
workspace/
    .
    ├── 6caf1a-goad-light-azure              # Instance ID
    │   ├── exchange_inventory               # extension inventory
    │   ├── instance.json                    # instance json file (name, status, etc..)
    │   ├── inventory                        # provider inventory
    │   ├── provider                         # provider folder
    │   │   ├── jumpbox.tf
    │   │   ├── linux.tf
    │   │   ├── main.tf
    │   │   ├── network.tf
    │   │   ├── outputs.tf
    │   │   ├── terraform.tfstate
    │   │   ├── terraform.tfstate.backup
    │   │   ├── variables.tf
    │   │   └── windows.tf
    │   └── ssh_keys                         # the keys generated by this instance
    │       └── ubuntu-jumpbox.pem
    ├── 7b12f1-goad-light-vmware             # another instance
    │   ├── instance.json
    │   ├── inventory
    │   ├── inventory_disable_vagrant
    │   ├── provider
    │   │   └── Vagrantfile
    │   └── ssh_keys
```

## instance creation

- On instance folder creation (when you run `install` or `create_empty`), the provider files inside the template/<provider> folder are copied into the instance.
- These files are merged with the datas inside `ad/<lab>/providers/<provider>/` folder and the datas inside `extensions/<extension>/providers/<provider>/`
- The merged result is present in the `workspace/<instance_id>/provider/` folder and contain all the recipes to create the infrastructure

- inventories files are also copied from `ad/<lab>/provider/<provider>/inventory` and `extensions/<extension>/inventory` (see [provisioning](provisioning.md) for more information in provisioning)