# Elastic Stack

The Elastic Stack (ELK Stack) provides a powerful, open-source alternative for log aggregation, search, and visualization. In GOAD-Blue, the Elastic Stack serves as a scalable SIEM platform with advanced analytics capabilities.

## 🎯 Overview

The Elastic Stack in GOAD-Blue consists of Elasticsearch, Logstash, Kibana, and Beats, providing comprehensive data ingestion, storage, search, and visualization capabilities for security operations.

```mermaid
graph TB
    subgraph "📊 Elastic Stack Architecture"
        KIBANA[📈 Kibana<br/>Visualization<br/>Dashboards & Analytics]
        ELASTICSEARCH[🔍 Elasticsearch<br/>Search Engine<br/>Data Storage]
        LOGSTASH[⚙️ Logstash<br/>Data Processing<br/>ETL Pipeline]
        BEATS[📡 Beats<br/>Data Shippers<br/>Lightweight Collectors]
    end
    
    subgraph "📥 Data Sources"
        GOAD[🎮 GOAD Environment<br/>Windows Event Logs<br/>Sysmon Data]
        NETWORK[🌐 Network Data<br/>Security Onion<br/>Firewall Logs]
        ENDPOINT[🖥️ Endpoint Data<br/>Velociraptor<br/>System Metrics]
    end
    
    subgraph "🔧 Processing Pipeline"
        WINLOGBEAT[🪟 Winlogbeat<br/>Windows Events<br/>Sysmon Logs]
        FILEBEAT[📄 Filebeat<br/>Log Files<br/>Network Logs]
        METRICBEAT[📊 Metricbeat<br/>System Metrics<br/>Performance Data]
    end
    
    subgraph "📊 Analytics & Visualization"
        DASHBOARDS[📊 Security Dashboards<br/>Real-time Monitoring<br/>Executive Views]
        ALERTS[🚨 Watcher Alerts<br/>Automated Detection<br/>Notifications]
        ML[🤖 Machine Learning<br/>Anomaly Detection<br/>Behavioral Analysis]
    end
    
    GOAD --> WINLOGBEAT
    NETWORK --> FILEBEAT
    ENDPOINT --> METRICBEAT
    
    WINLOGBEAT --> LOGSTASH
    FILEBEAT --> LOGSTASH
    METRICBEAT --> LOGSTASH
    
    LOGSTASH --> ELASTICSEARCH
    BEATS --> ELASTICSEARCH
    
    ELASTICSEARCH --> KIBANA
    KIBANA --> DASHBOARDS
    KIBANA --> ALERTS
    KIBANA --> ML
    
    classDef elastic fill:#005571,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef data fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef processing fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef analytics fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class KIBANA,ELASTICSEARCH,LOGSTASH,BEATS elastic
    class GOAD,NETWORK,ENDPOINT data
    class WINLOGBEAT,FILEBEAT,METRICBEAT processing
    class DASHBOARDS,ALERTS,ML analytics
```

## 🚀 Installation and Setup

### **Automated Installation**

```bash
# Install Elastic Stack using GOAD-Blue automation
python3 goad-blue.py install --component elastic --version 8.10.0

# Configure Elastic Stack for GOAD integration
python3 goad-blue.py configure --component elastic --enable-goad-integration

# Start Elastic Stack services
python3 goad-blue.py start --component elastic
```

### **Manual Installation**

```bash
# Install Elasticsearch
wget -qO - https://artifacts.elastic.co/GPG-KEY-elasticsearch | sudo apt-key add -
echo "deb https://artifacts.elastic.co/packages/8.x/apt stable main" | sudo tee /etc/apt/sources.list.d/elastic-8.x.list
sudo apt-get update && sudo apt-get install elasticsearch

# Install Kibana
sudo apt-get install kibana

# Install Logstash
sudo apt-get install logstash

# Start services
sudo systemctl enable elasticsearch kibana logstash
sudo systemctl start elasticsearch kibana logstash
```

### **Configuration Files**

```yaml
# elasticsearch.yml
cluster.name: goad-blue-cluster
node.name: goad-blue-es-01
path.data: /var/lib/elasticsearch
path.logs: /var/log/elasticsearch
network.host: 0.0.0.0
http.port: 9200
discovery.type: single-node

# Security settings
xpack.security.enabled: true
xpack.security.enrollment.enabled: true
xpack.security.http.ssl:
  enabled: true
  keystore.path: certs/http.p12
xpack.security.transport.ssl:
  enabled: true
  verification_mode: certificate
  keystore.path: certs/transport.p12
  truststore.path: certs/transport.p12

# Index lifecycle management
xpack.ilm.enabled: true
```

```yaml
# kibana.yml
server.port: 5601
server.host: "0.0.0.0"
elasticsearch.hosts: ["https://localhost:9200"]
elasticsearch.username: "kibana_system"
elasticsearch.password: "your_password"

# Security settings
elasticsearch.ssl.certificateAuthorities: ["/etc/kibana/certs/ca.crt"]
server.ssl.enabled: true
server.ssl.certificate: "/etc/kibana/certs/kibana.crt"
server.ssl.key: "/etc/kibana/certs/kibana.key"

# GOAD-Blue specific settings
server.name: "goad-blue-kibana"
logging.appenders.file.fileName: /var/log/kibana/kibana.log
```

## 📊 Index Templates and Mappings

### **Windows Event Log Template**

```json
{
  "index_patterns": ["goad-blue-windows-*"],
  "template": {
    "settings": {
      "number_of_shards": 1,
      "number_of_replicas": 1,
      "index.lifecycle.name": "goad-blue-windows-policy",
      "index.lifecycle.rollover_alias": "goad-blue-windows"
    },
    "mappings": {
      "properties": {
        "@timestamp": {"type": "date"},
        "event": {
          "properties": {
            "code": {"type": "keyword"},
            "provider": {"type": "keyword"},
            "action": {"type": "keyword"}
          }
        },
        "winlog": {
          "properties": {
            "computer_name": {"type": "keyword"},
            "event_id": {"type": "keyword"},
            "channel": {"type": "keyword"},
            "provider_name": {"type": "keyword"}
          }
        },
        "user": {
          "properties": {
            "name": {"type": "keyword"},
            "domain": {"type": "keyword"},
            "id": {"type": "keyword"}
          }
        },
        "source": {
          "properties": {
            "ip": {"type": "ip"},
            "port": {"type": "integer"}
          }
        },
        "destination": {
          "properties": {
            "ip": {"type": "ip"},
            "port": {"type": "integer"}
          }
        }
      }
    }
  }
}
```

### **Network Security Template**

```json
{
  "index_patterns": ["goad-blue-network-*"],
  "template": {
    "settings": {
      "number_of_shards": 2,
      "number_of_replicas": 1,
      "index.lifecycle.name": "goad-blue-network-policy"
    },
    "mappings": {
      "properties": {
        "@timestamp": {"type": "date"},
        "suricata": {
          "properties": {
            "eve": {
              "properties": {
                "alert": {
                  "properties": {
                    "signature": {"type": "text"},
                    "signature_id": {"type": "keyword"},
                    "severity": {"type": "integer"},
                    "category": {"type": "keyword"}
                  }
                }
              }
            }
          }
        },
        "zeek": {
          "properties": {
            "conn": {
              "properties": {
                "id": {
                  "properties": {
                    "orig_h": {"type": "ip"},
                    "orig_p": {"type": "integer"},
                    "resp_h": {"type": "ip"},
                    "resp_p": {"type": "integer"}
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
```

## 🔄 Logstash Configuration

### **Windows Event Log Processing**

```ruby
# logstash/conf.d/windows-events.conf
input {
  beats {
    port => 5044
    type => "winlogbeat"
  }
}

filter {
  if [agent][type] == "winlogbeat" {
    # Parse Windows Event Log fields
    if [winlog][event_id] {
      mutate {
        add_field => { "event_id" => "%{[winlog][event_id]}" }
      }
    }
    
    # Enrich authentication events
    if [winlog][event_id] == "4624" or [winlog][event_id] == "4625" {
      mutate {
        add_field => { "event_category" => "authentication" }
      }
      
      if [winlog][event_id] == "4624" {
        mutate { add_field => { "auth_result" => "success" } }
      } else {
        mutate { add_field => { "auth_result" => "failure" } }
      }
    }
    
    # Enrich process creation events (Sysmon Event ID 1)
    if [winlog][event_id] == "1" {
      mutate {
        add_field => { "event_category" => "process_creation" }
      }
      
      # Extract command line arguments
      if [winlog][event_data][CommandLine] {
        mutate {
          add_field => { "process_command_line" => "%{[winlog][event_data][CommandLine]}" }
        }
      }
    }
    
    # GeoIP enrichment for source IPs
    if [winlog][event_data][IpAddress] {
      geoip {
        source => "[winlog][event_data][IpAddress]"
        target => "geoip"
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["localhost:9200"]
    index => "goad-blue-windows-%{+YYYY.MM.dd}"
    template_name => "goad-blue-windows"
    template_pattern => "goad-blue-windows-*"
  }
}
```

### **Network Data Processing**

```ruby
# logstash/conf.d/network-security.conf
input {
  file {
    path => "/nsm/sensor_data/*/logs/suricata/eve.json"
    start_position => "end"
    codec => "json"
    type => "suricata"
  }
  
  file {
    path => "/nsm/sensor_data/*/logs/zeek/*.log"
    start_position => "end"
    type => "zeek"
  }
}

filter {
  if [type] == "suricata" {
    # Parse Suricata EVE JSON
    if [event_type] == "alert" {
      mutate {
        add_field => { "event_category" => "network_alert" }
        add_field => { "alert_signature" => "%{[alert][signature]}" }
        add_field => { "alert_severity" => "%{[alert][severity]}" }
      }
      
      # Threat intelligence enrichment
      if [alert][signature_id] {
        elasticsearch {
          hosts => ["localhost:9200"]
          index => "threat-intel"
          query => "signature_id:%{[alert][signature_id]}"
          fields => { "threat_type" => "threat_type" }
        }
      }
    }
  }
  
  if [type] == "zeek" {
    # Parse Zeek logs based on log type
    if [path] =~ /conn\.log$/ {
      mutate { add_field => { "zeek_log_type" => "connection" } }
    } else if [path] =~ /dns\.log$/ {
      mutate { add_field => { "zeek_log_type" => "dns" } }
    } else if [path] =~ /http\.log$/ {
      mutate { add_field => { "zeek_log_type" => "http" } }
    }
  }
}

output {
  if [type] == "suricata" {
    elasticsearch {
      hosts => ["localhost:9200"]
      index => "goad-blue-network-%{+YYYY.MM.dd}"
    }
  }
  
  if [type] == "zeek" {
    elasticsearch {
      hosts => ["localhost:9200"]
      index => "goad-blue-zeek-%{+YYYY.MM.dd}"
    }
  }
}
```

## 📊 Kibana Dashboards

### **Security Operations Dashboard**

```json
{
  "version": "8.10.0",
  "objects": [
    {
      "id": "goad-blue-soc-dashboard",
      "type": "dashboard",
      "attributes": {
        "title": "GOAD-Blue SOC Overview",
        "panelsJSON": "[{\"version\":\"8.10.0\",\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15},\"panelIndex\":\"1\",\"embeddableConfig\":{},\"panelRefName\":\"panel_1\"}]",
        "timeRestore": true,
        "timeTo": "now",
        "timeFrom": "now-24h"
      }
    }
  ]
}
```

### **Authentication Monitoring**

```json
{
  "aggs": {
    "auth_timeline": {
      "date_histogram": {
        "field": "@timestamp",
        "calendar_interval": "1h"
      },
      "aggs": {
        "auth_types": {
          "terms": {
            "field": "auth_result"
          }
        }
      }
    },
    "failed_logins": {
      "filter": {
        "term": {
          "auth_result": "failure"
        }
      },
      "aggs": {
        "top_sources": {
          "terms": {
            "field": "source.ip",
            "size": 10
          }
        }
      }
    }
  }
}
```

## 🤖 Machine Learning Jobs

### **Authentication Anomaly Detection**

```json
{
  "job_id": "goad-blue-auth-anomaly",
  "description": "Detect unusual authentication patterns",
  "analysis_config": {
    "bucket_span": "15m",
    "detectors": [
      {
        "function": "count",
        "by_field_name": "user.name",
        "detector_description": "Unusual login frequency by user"
      },
      {
        "function": "distinct_count",
        "field_name": "source.ip",
        "by_field_name": "user.name",
        "detector_description": "Unusual number of source IPs for user"
      }
    ],
    "influencers": ["user.name", "source.ip"]
  },
  "data_description": {
    "time_field": "@timestamp"
  },
  "datafeed_config": {
    "indices": ["goad-blue-windows-*"],
    "query": {
      "bool": {
        "must": [
          {"term": {"event_category": "authentication"}}
        ]
      }
    }
  }
}
```

### **Network Traffic Anomaly Detection**

```json
{
  "job_id": "goad-blue-network-anomaly",
  "description": "Detect unusual network traffic patterns",
  "analysis_config": {
    "bucket_span": "10m",
    "detectors": [
      {
        "function": "high_count",
        "by_field_name": "destination.port",
        "detector_description": "Unusual traffic volume to specific ports"
      },
      {
        "function": "rare",
        "by_field_name": "destination.ip",
        "detector_description": "Rare destination IPs"
      }
    ],
    "influencers": ["source.ip", "destination.ip", "destination.port"]
  },
  "data_description": {
    "time_field": "@timestamp"
  },
  "datafeed_config": {
    "indices": ["goad-blue-network-*"],
    "query": {
      "match_all": {}
    }
  }
}
```

## 🚨 Watcher Alerts

### **Failed Authentication Alert**

```json
{
  "trigger": {
    "schedule": {
      "interval": "5m"
    }
  },
  "input": {
    "search": {
      "request": {
        "search_type": "query_then_fetch",
        "indices": ["goad-blue-windows-*"],
        "body": {
          "query": {
            "bool": {
              "must": [
                {"term": {"auth_result": "failure"}},
                {"range": {"@timestamp": {"gte": "now-5m"}}}
              ]
            }
          },
          "aggs": {
            "failed_by_ip": {
              "terms": {
                "field": "source.ip",
                "size": 10
              }
            }
          }
        }
      }
    }
  },
  "condition": {
    "compare": {
      "ctx.payload.hits.total": {
        "gt": 50
      }
    }
  },
  "actions": {
    "send_email": {
      "email": {
        "to": ["<EMAIL>"],
        "subject": "GOAD-Blue: High Volume of Failed Authentications",
        "body": "Detected {{ctx.payload.hits.total}} failed authentication attempts in the last 5 minutes."
      }
    },
    "webhook_notification": {
      "webhook": {
        "scheme": "https",
        "host": "hooks.slack.com",
        "port": 443,
        "method": "post",
        "path": "/services/YOUR/SLACK/WEBHOOK",
        "params": {},
        "headers": {
          "Content-Type": "application/json"
        },
        "body": "{\"text\":\"🚨 GOAD-Blue Alert: {{ctx.payload.hits.total}} failed authentication attempts detected\"}"
      }
    }
  }
}
```

## 🔍 Essential Queries

### **KQL Queries for Threat Hunting**

```kql
# Lateral movement detection
event_category:"authentication" AND auth_result:"success" AND winlog.event_id:"4624" AND winlog.event_data.LogonType:"3"
| stats cardinality(winlog.computer_name) by source.ip, user.name
| where cardinality > 3

# Privilege escalation
winlog.event_id:"4672" AND NOT (user.name:*$ OR user.name:"SYSTEM" OR user.name:"LOCAL SERVICE")

# Suspicious process execution
winlog.event_id:"1" AND (process_command_line:*powershell* OR process_command_line:*cmd*) AND (process_command_line:*ExecutionPolicy* OR process_command_line:*EncodedCommand*)

# DNS tunneling
zeek_log_type:"dns" AND dns.query.length > 50

# Rare network connections
NOT (destination.ip:192.168.* OR destination.ip:10.* OR destination.ip:172.16.*)
| rare destination.ip by source.ip
```

### **Aggregation Queries**

```json
{
  "aggs": {
    "authentication_summary": {
      "date_histogram": {
        "field": "@timestamp",
        "calendar_interval": "1h"
      },
      "aggs": {
        "success_count": {
          "filter": {
            "term": {"auth_result": "success"}
          }
        },
        "failure_count": {
          "filter": {
            "term": {"auth_result": "failure"}
          }
        },
        "unique_users": {
          "cardinality": {
            "field": "user.name"
          }
        }
      }
    }
  }
}
```

## 🔗 Integration Points

### **Beats Configuration for GOAD**

```yaml
# winlogbeat.yml for GOAD systems
winlogbeat.event_logs:
  - name: Security
    index: goad-blue-windows
  - name: Microsoft-Windows-Sysmon/Operational
    index: goad-blue-windows

output.logstash:
  hosts: ["goad-blue-logstash:5044"]

processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
  - add_docker_metadata: ~
  - add_kubernetes_metadata: ~
```

```yaml
# filebeat.yml for Security Onion integration
filebeat.inputs:
- type: log
  paths:
    - /nsm/sensor_data/*/logs/suricata/eve.json
  json.keys_under_root: true
  json.add_error_key: true
  index: goad-blue-network

output.elasticsearch:
  hosts: ["goad-blue-elasticsearch:9200"]
  username: "elastic"
  password: "your_password"
```

## 📚 Training Scenarios

### **Elastic Stack Fundamentals**

1. **Index Management**: Learn to create and manage indices
2. **Query Development**: Master KQL and aggregation queries
3. **Dashboard Creation**: Build effective security dashboards
4. **Alert Configuration**: Set up Watcher alerts for threats

### **Advanced Analytics**

1. **Machine Learning**: Implement anomaly detection jobs
2. **Custom Processors**: Develop Logstash filters
3. **API Integration**: Use Elasticsearch APIs for automation
4. **Performance Tuning**: Optimize cluster performance

---

!!! tip "Elastic Stack Best Practices"
    - Use index lifecycle management for data retention
    - Implement proper field mappings for performance
    - Monitor cluster health and resource usage
    - Use machine learning for advanced threat detection

!!! warning "Security Considerations"
    - Enable security features (authentication, encryption)
    - Regularly update to latest versions
    - Monitor access logs and user activities
    - Implement proper backup and recovery procedures

!!! info "Learning Resources"
    - Elastic Certified Engineer training
    - GOAD-Blue specific use cases and scenarios
    - Community dashboards and detection rules
