# Installation Guide

This comprehensive guide walks you through installing GOAD-Blue using various methods, from quick automated installation to detailed manual setup.

## 🚀 Quick Installation (Recommended)

### **Method 1: Interactive Installer**

The fastest way to get GOAD-Blue running:

```bash
# Clone GOAD-Blue
git clone https://github.com/your-org/goad-blue.git
cd goad-blue

# Run interactive installer
chmod +x scripts/goad-blue-installer.sh
./scripts/goad-blue-installer.sh
```

The installer will:
1. ✅ Check all prerequisites
2. 🔍 Detect existing GOAD installation
3. ⚙️ Guide you through component selection
4. 🏗️ Build VM images with Packer
5. 🌍 Deploy infrastructure with Terraform
6. ⚙️ Configure systems with Ansible
7. 🔗 Integrate with GOAD environment

### **Method 2: Python CLI**

For more control over the installation process:

```bash
# Interactive configuration
python3 goad-blue.py --interactive

# Quick deployment with specific components
python3 goad-blue.py -t install -s splunk -c security_onion,velociraptor,misp

# Custom configuration file
python3 goad-blue.py --config custom-config.yml -t install
```

## 📋 Step-by-Step Installation

### **Step 1: Download and Prepare**

```bash
# Option A: Clone from Git
git clone https://github.com/your-org/goad-blue.git
cd goad-blue

# Option B: Download release
wget https://github.com/your-org/goad-blue/releases/latest/download/goad-blue.tar.gz
tar -xzf goad-blue.tar.gz
cd goad-blue

# Install Python dependencies
pip3 install -r requirements.txt
```

### **Step 2: Configuration**

```bash
# Copy example configuration
cp goad-blue-config.yml.example goad-blue-config.yml

# Edit configuration
nano goad-blue-config.yml
```

**Basic Configuration Example:**
```yaml
goad_blue:
  name: "my-goad-blue-lab"
  provider: "vmware"  # vmware, virtualbox, aws, azure

siem:
  type: "splunk"      # splunk or elastic
  enabled: true

components:
  security_onion:
    enabled: true
  velociraptor:
    enabled: true
  misp:
    enabled: true
  flare_vm:
    enabled: false    # Disable for minimal setup

network:
  base_cidr: "*************/24"
  siem_subnet: "*************/26"
  monitoring_subnet: "**************/26"
  red_team_subnet: "***************/26"
  analysis_subnet: "***************/26"

integration:
  goad_enabled: true
  auto_discover: true
  goad_path: "../"    # Path to existing GOAD installation
```

### **Step 3: Build VM Images**

```bash
# Build all required images
cd packer

# Build SIEM image
packer build goad-blue-splunk-server.json

# Build Security Onion image
packer build goad-blue-security-onion.json

# Build other component images as needed
packer build goad-blue-velociraptor.json
packer build goad-blue-misp.json
packer build goad-blue-flare-vm.json
```

### **Step 4: Deploy Infrastructure**

```bash
# Initialize Terraform
cd terraform
terraform init

# Plan deployment
terraform plan -var-file="../goad-blue-config.yml"

# Apply configuration
terraform apply -var-file="../goad-blue-config.yml"
```

### **Step 5: Configure Systems**

```bash
# Deploy all components
cd ansible
ansible-playbook -i inventory/ goad-blue-site.yml

# Or deploy specific components
ansible-playbook -i inventory/ goad-blue-splunk-server.yml
ansible-playbook -i inventory/ goad-blue-security-onion.yml
```

### **Step 6: GOAD Integration**

```bash
# Integrate with existing GOAD
ansible-playbook -i inventory/ goad-blue-integration.yml

# Or use CLI
python3 goad-blue.py --integrate-goad
```

## 🌐 Platform-Specific Installation

### **VMware Workstation**

```bash
# No additional configuration needed
export VMWARE_WORKSTATION=true
./scripts/goad-blue-installer.sh
```

**Requirements:**
- VMware Workstation Pro 16.2+
- Sufficient host resources
- Virtualization enabled in BIOS

### **VMware ESXi**

```bash
# Set ESXi environment variables
export VSPHERE_USER="<EMAIL>"
export VSPHERE_PASSWORD="your_password"
export VSPHERE_SERVER="your_esxi_host"
export VSPHERE_DATACENTER="Datacenter"
export VSPHERE_CLUSTER="Cluster"
export VSPHERE_DATASTORE="datastore1"

# Run installation
python3 goad-blue.py -t install -p vmware
```

### **AWS Deployment**

```bash
# Configure AWS credentials
aws configure
# Enter: Access Key ID, Secret Access Key, Region

# Set AWS-specific variables
export AWS_DEFAULT_REGION="us-east-1"
export AWS_VPC_ID="vpc-********"  # Optional: use existing VPC

# Deploy to AWS
python3 goad-blue.py -t install -p aws
```

**AWS Resources Created:**
- EC2 instances for each component
- VPC and subnets (if not existing)
- Security groups
- EBS volumes for storage
- Elastic IPs for static addressing

### **Azure Deployment**

```bash
# Login to Azure
az login

# Set subscription
az account set --subscription "Your Subscription"

# Set Azure-specific variables
export AZURE_LOCATION="East US"
export AZURE_RESOURCE_GROUP="goad-blue-rg"

# Deploy to Azure
python3 goad-blue.py -t install -p azure
```

**Azure Resources Created:**
- Virtual machines for each component
- Virtual network and subnets
- Network security groups
- Storage accounts
- Public IP addresses

### **Proxmox VE**

```bash
# Set Proxmox environment variables
export PROXMOX_API_URL="https://*************:8006/api2/json"
export PROXMOX_USER="root@pam"
export PROXMOX_PASSWORD="your_password"
export PROXMOX_NODE="pve"

# Deploy to Proxmox
python3 goad-blue.py -t install -p proxmox
```

**Proxmox Resources Created:**
- Virtual machines for each component
- Virtual networks and bridges
- Storage allocation on ZFS/LVM
- Cloud-init configuration
- Firewall rules and security groups

### **VirtualBox**

```bash
# Ensure VirtualBox is installed and Extension Pack added
VBoxManage --version

# Run installation
python3 goad-blue.py -t install -p virtualbox
```

**VirtualBox Limitations:**
- Performance may be lower than VMware
- Some advanced features may not be available
- Recommended for development/testing only

## 🔧 Advanced Installation Options

### **Custom Component Selection**

```bash
# Minimal deployment (SIEM + basic monitoring)
python3 goad-blue.py -t install \
  --components siem,security_onion \
  --siem-type splunk

# Full deployment (all components)
python3 goad-blue.py -t install \
  --components siem,security_onion,malcolm,velociraptor,misp,flare_vm \
  --siem-type elastic

# Research deployment (network focus)
python3 goad-blue.py -t install \
  --components siem,security_onion,malcolm \
  --enable-pcap-storage \
  --storage-size 2TB
```

### **High Availability Deployment**

```yaml
# goad-blue-config.yml
high_availability:
  enabled: true
  siem:
    indexers: 3
    search_heads: 2
    cluster_master: true
  monitoring:
    sensors: 2
    manager_ha: true
  load_balancer:
    enabled: true
    type: "nginx"
```

### **Distributed Deployment**

```yaml
# Multi-site deployment
sites:
  primary:
    location: "datacenter1"
    components: ["siem", "monitoring", "intelligence"]
  secondary:
    location: "datacenter2"
    components: ["monitoring", "analysis"]
  remote:
    location: "branch_office"
    components: ["monitoring"]
```

## 🔍 Installation Verification

### **Component Status Check**

```bash
# Check all components
python3 goad-blue.py -t status

# Detailed health check
python3 goad-blue.py --health-check

# Test specific components
python3 goad-blue.py test_component --name splunk
python3 goad-blue.py test_component --name security_onion
```

### **Network Connectivity Test**

```bash
# Test internal connectivity
python3 goad-blue.py test_connectivity

# Test GOAD integration
python3 goad-blue.py test_goad_integration

# Test external connectivity
python3 goad-blue.py test_external_connectivity
```

### **Data Flow Verification**

```bash
# Generate test events
python3 goad-blue.py generate_test_events

# Check SIEM data ingestion
# Splunk: Search for index=goad_blue_*
# Elastic: Check goad-blue-* indices

# Verify alert generation
python3 goad-blue.py test_alerting
```

## 🛠️ Post-Installation Configuration

### **Initial Security Setup**

```bash
# Change default passwords
python3 goad-blue.py change_passwords

# Generate SSL certificates
python3 goad-blue.py generate_certificates

# Configure firewall rules
python3 goad-blue.py configure_firewall
```

### **Performance Tuning**

```bash
# Optimize for your environment
python3 goad-blue.py optimize --profile production

# Adjust resource allocation
python3 goad-blue.py tune_resources --auto

# Configure log retention
python3 goad-blue.py configure_retention --days 90
```

### **Backup Configuration**

```bash
# Setup automated backups
python3 goad-blue.py setup_backup \
  --schedule daily \
  --retention 30 \
  --destination /backup/goad-blue

# Create initial backup
python3 goad-blue.py backup --full
```

## 🚨 Troubleshooting Installation

### **Common Installation Issues**

**Packer Build Failures:**
```bash
# Enable debug logging
export PACKER_LOG=1
packer build -debug template.json

# Check available disk space
df -h

# Verify ISO checksums
sha256sum ubuntu-22.04.3-live-server-amd64.iso
```

**Terraform Deployment Errors:**
```bash
# Enable debug logging
export TF_LOG=DEBUG
terraform apply

# Check provider credentials
terraform providers

# Validate configuration
terraform validate
terraform plan
```

**Ansible Configuration Failures:**
```bash
# Test connectivity
ansible all -i inventory/ -m ping

# Run with verbose output
ansible-playbook -vvv playbook.yml

# Check SSH keys and permissions
ssh-add -l
chmod 600 ~/.ssh/id_rsa
```

### **Recovery Procedures**

```bash
# Clean failed installation
python3 goad-blue.py cleanup --force

# Reset to clean state
python3 goad-blue.py reset --confirm

# Partial reinstall
python3 goad-blue.py reinstall --component splunk
```

---

!!! success "Installation Complete"
    Once installation is complete, proceed to [First Steps](first-steps.md) to begin using your GOAD-Blue environment.

!!! tip "Save Your Configuration"
    Keep a backup of your `goad-blue-config.yml` file for future deployments or disaster recovery.

!!! warning "Security Notice"
    Change all default passwords and configure SSL certificates before using in production environments.
