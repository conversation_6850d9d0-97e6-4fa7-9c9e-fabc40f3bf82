# GOAD-Blue Component Interaction Diagrams

## Complete Component Ecosystem

```mermaid
graph TB
    subgraph "🎯 GOAD Attack Environment"
        DC1[🏰 Kingslanding DC<br/>sevenkingdoms.local]
        DC2[❄️ Winterfell DC<br/>north.sevenkingdoms.local]
        DC3[🐉 Meereen DC<br/>essos.local]
        SRV1[⚔️ Castelblack<br/>File Server]
        SRV2[🏛️ Braavos<br/>Web Server]
        WS1[🌹 Tyrell WS<br/>User Workstation]
    end
    
    subgraph "📊 SIEM Platform"
        SPLUNK[📈 Splunk Enterprise<br/>• Universal Forwarders<br/>• Search Head<br/>• Indexers]
        ELASTIC[🔍 Elastic Stack<br/>• Elasticsearch<br/>• Logstash<br/>• Kibana]
    end
    
    subgraph "🔍 Network Monitoring"
        SO_MGR[🧅 Security Onion Manager<br/>• Central Management<br/>• Hunt Platform<br/>• Case Management]
        SO_SENSOR[📡 Security Onion Sensors<br/>• Suricata IDS<br/>• Zeek NSM<br/>• Wazuh HIDS]
        MALCOLM[🕵️ Malcolm<br/>• Network Forensics<br/>• PCAP Analysis<br/>• Arkime Integration]
    end
    
    subgraph "💻 Endpoint Visibility"
        VELO_SRV[🦖 Velociraptor Server<br/>• Artifact Collection<br/>• Hunt Management<br/>• Forensic Analysis]
        VELO_AGENTS[🤖 Velociraptor Agents<br/>• Process Monitoring<br/>• File Collection<br/>• Memory Analysis]
        SYSMON[👁️ Sysmon<br/>• Process Creation<br/>• Network Connections<br/>• File Modifications]
    end
    
    subgraph "🧠 Threat Intelligence"
        MISP_SRV[🎯 MISP Server<br/>• IOC Management<br/>• Event Correlation<br/>• Threat Sharing]
        THREAT_FEEDS[📡 Threat Feeds<br/>• External IOCs<br/>• Reputation Data<br/>• Attribution Intel]
    end
    
    subgraph "🔬 Malware Analysis"
        FLARE_VM[🔥 FLARE-VM<br/>• Static Analysis<br/>• Dynamic Analysis<br/>• Reverse Engineering]
        SANDBOX[📦 Analysis Sandbox<br/>• Automated Analysis<br/>• Behavioral Monitoring<br/>• Report Generation]
    end
    
    subgraph "🎛️ Management & Orchestration"
        BLUE_MGR[🎯 Blue Team Manager<br/>• Component Orchestration<br/>• Configuration Management<br/>• Integration Engine]
        CLI[🖥️ GOAD-Blue CLI<br/>• Interactive Management<br/>• Deployment Control<br/>• Status Monitoring]
    end
    
    %% GOAD to Monitoring Data Flow
    DC1 -->|Windows Events| SPLUNK
    DC2 -->|Windows Events| SPLUNK
    DC3 -->|Windows Events| SPLUNK
    SRV1 -->|Application Logs| SPLUNK
    SRV2 -->|Web Logs| SPLUNK
    WS1 -->|User Activity| SPLUNK
    
    DC1 -->|Network Traffic| SO_SENSOR
    DC2 -->|Network Traffic| SO_SENSOR
    SRV1 -->|Network Traffic| SO_SENSOR
    SRV2 -->|Network Traffic| SO_SENSOR
    
    %% Endpoint Monitoring
    VELO_AGENTS -->|Endpoint Data| VELO_SRV
    SYSMON -->|Process Events| SPLUNK
    VELO_SRV -->|Hunt Results| SPLUNK
    
    %% Network Monitoring Integration
    SO_SENSOR -->|IDS Alerts| SO_MGR
    SO_MGR -->|Alerts & Logs| SPLUNK
    MALCOLM -->|Network Analysis| SPLUNK
    SO_SENSOR -->|PCAP Data| MALCOLM
    
    %% Threat Intelligence Flow
    THREAT_FEEDS -->|IOCs| MISP_SRV
    MISP_SRV -->|Threat Intel| SPLUNK
    MISP_SRV -->|IOCs| SO_MGR
    MISP_SRV -->|Indicators| VELO_SRV
    
    %% Analysis Workflow
    SPLUNK -->|Suspicious Files| FLARE_VM
    VELO_SRV -->|Collected Samples| FLARE_VM
    FLARE_VM -->|Analysis Results| MISP_SRV
    SANDBOX -->|Automated Reports| SPLUNK
    
    %% Management & Control
    CLI -->|Commands| BLUE_MGR
    BLUE_MGR -->|Configuration| SPLUNK
    BLUE_MGR -->|Configuration| SO_MGR
    BLUE_MGR -->|Configuration| VELO_SRV
    BLUE_MGR -->|Configuration| MISP_SRV
    
    %% Agent Deployment
    BLUE_MGR -.->|Deploy Agents| DC1
    BLUE_MGR -.->|Deploy Agents| DC2
    BLUE_MGR -.->|Deploy Agents| DC3
    BLUE_MGR -.->|Deploy Agents| SRV1
    BLUE_MGR -.->|Deploy Agents| SRV2
    BLUE_MGR -.->|Deploy Agents| WS1
    
    %% Styling
    classDef goad fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    classDef siem fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef monitoring fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    classDef endpoint fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    classDef intelligence fill:#fce4ec,stroke:#c2185b,stroke-width:3px
    classDef analysis fill:#ffebee,stroke:#d32f2f,stroke-width:3px
    classDef management fill:#e0f2f1,stroke:#00695c,stroke-width:3px
    
    class DC1,DC2,DC3,SRV1,SRV2,WS1 goad
    class SPLUNK,ELASTIC siem
    class SO_MGR,SO_SENSOR,MALCOLM monitoring
    class VELO_SRV,VELO_AGENTS,SYSMON endpoint
    class MISP_SRV,THREAT_FEEDS intelligence
    class FLARE_VM,SANDBOX analysis
    class BLUE_MGR,CLI management
```

## Attack Detection Workflow

```mermaid
sequenceDiagram
    participant Attacker as 👨‍💻 Red Team
    participant GOAD as 🎯 GOAD Environment
    participant Network as 🌐 Network Monitor
    participant Endpoint as 💻 Endpoint Agent
    participant SIEM as 📊 SIEM Platform
    participant Intel as 🧠 Threat Intel
    participant Analyst as 👤 Blue Team Analyst
    participant Response as 🛡️ Response System
    
    Note over Attacker,Response: Kerberoasting Attack Scenario
    
    Attacker->>GOAD: Execute PowerView
    GOAD->>GOAD: Load PowerShell Module
    
    par Network Detection
        GOAD->>Network: Generate Network Traffic
        Network->>Network: Analyze Protocol Anomalies
        Network->>SIEM: Send Network Alerts
    and Endpoint Detection
        GOAD->>Endpoint: Process Creation Event
        Endpoint->>Endpoint: Monitor PowerShell Activity
        Endpoint->>SIEM: Send Process Events
    and Log Generation
        GOAD->>SIEM: Windows Event Logs
        SIEM->>SIEM: Parse Event ID 4769
    end
    
    Attacker->>GOAD: Request Service Tickets
    GOAD->>GOAD: Generate Kerberos TGS Requests
    
    par Detection Correlation
        SIEM->>SIEM: Correlate Multiple Events
        SIEM->>Intel: Query IOC Database
        Intel-->>SIEM: Return Threat Context
    end
    
    SIEM->>SIEM: Apply Detection Rules
    SIEM->>SIEM: Generate High-Severity Alert
    
    SIEM->>Analyst: Alert Notification
    Analyst->>SIEM: Investigate Timeline
    Analyst->>Endpoint: Request Additional Data
    Endpoint-->>Analyst: Provide Process Tree
    
    Analyst->>Intel: Update IOCs
    Analyst->>Response: Initiate Containment
    Response->>GOAD: Isolate Compromised Host
    
    Note over Analyst,Response: Incident Response Complete
```

## Data Flow Architecture

```mermaid
flowchart LR
    subgraph "📥 Data Sources"
        WIN_EVENTS[🪟 Windows Events<br/>• Security Log<br/>• System Log<br/>• Application Log]
        NET_TRAFFIC[🌐 Network Traffic<br/>• Raw Packets<br/>• Flow Records<br/>• Protocol Logs]
        ENDPOINT_DATA[💻 Endpoint Data<br/>• Process Events<br/>• File Changes<br/>• Registry Mods]
        THREAT_DATA[🎯 Threat Data<br/>• IOCs<br/>• TTPs<br/>• Attribution]
    end
    
    subgraph "🚚 Collection Layer"
        UF[📦 Universal Forwarders<br/>Splunk Agents]
        BEATS[🥁 Elastic Beats<br/>Log Shippers]
        VELO_COLLECT[🦖 Velociraptor<br/>Artifact Collector]
        MISP_API[🧠 MISP API<br/>Intel Feeds]
    end
    
    subgraph "⚙️ Processing Pipeline"
        PARSE[📋 Parsing<br/>• Log Format Recognition<br/>• Field Extraction<br/>• Data Validation]
        
        NORMALIZE[🔄 Normalization<br/>• CIM/ECS Mapping<br/>• Field Standardization<br/>• Data Cleansing]
        
        ENRICH[✨ Enrichment<br/>• GeoIP Lookup<br/>• DNS Resolution<br/>• Asset Correlation]
        
        CORRELATE[🔗 Correlation<br/>• Event Linking<br/>• Pattern Matching<br/>• Behavioral Analysis]
    end
    
    subgraph "🧠 Analysis Engine"
        RULES[📏 Detection Rules<br/>• Signature-based<br/>• Behavioral<br/>• Statistical]
        
        ML[🤖 Machine Learning<br/>• Anomaly Detection<br/>• Classification<br/>• Clustering]
        
        HUNT[🎯 Threat Hunting<br/>• Hypothesis Testing<br/>• Proactive Search<br/>• IOC Hunting]
    end
    
    subgraph "📊 Output & Response"
        ALERTS[🚨 Alerts<br/>• Real-time Notifications<br/>• Severity Scoring<br/>• Context Addition]
        
        DASHBOARDS[📈 Dashboards<br/>• Executive Views<br/>• Operational Metrics<br/>• Trend Analysis]
        
        REPORTS[📄 Reports<br/>• Scheduled Reports<br/>• Compliance Reports<br/>• Investigation Reports]
        
        RESPONSE[🛡️ Response Actions<br/>• Automated Response<br/>• Playbook Execution<br/>• Containment]
    end
    
    %% Data Flow
    WIN_EVENTS --> UF
    NET_TRAFFIC --> BEATS
    ENDPOINT_DATA --> VELO_COLLECT
    THREAT_DATA --> MISP_API
    
    UF --> PARSE
    BEATS --> PARSE
    VELO_COLLECT --> PARSE
    MISP_API --> PARSE
    
    PARSE --> NORMALIZE
    NORMALIZE --> ENRICH
    ENRICH --> CORRELATE
    
    CORRELATE --> RULES
    CORRELATE --> ML
    CORRELATE --> HUNT
    
    RULES --> ALERTS
    ML --> ALERTS
    HUNT --> ALERTS
    
    ALERTS --> DASHBOARDS
    ALERTS --> REPORTS
    ALERTS --> RESPONSE
    
    %% Feedback Loops
    RESPONSE -.-> RULES
    HUNT -.-> THREAT_DATA
    REPORTS -.-> HUNT
    
    %% Styling
    classDef sources fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef collection fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef processing fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef analysis fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef output fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    
    class WIN_EVENTS,NET_TRAFFIC,ENDPOINT_DATA,THREAT_DATA sources
    class UF,BEATS,VELO_COLLECT,MISP_API collection
    class PARSE,NORMALIZE,ENRICH,CORRELATE processing
    class RULES,ML,HUNT analysis
    class ALERTS,DASHBOARDS,REPORTS,RESPONSE output
```

## Component Health Monitoring

```mermaid
graph TB
    subgraph "📊 Health Monitoring Dashboard"
        HEALTH_DASH[🏥 Health Dashboard<br/>Real-time Status]
    end
    
    subgraph "🔍 Component Monitoring"
        SIEM_HEALTH[📈 SIEM Health<br/>• Indexing Rate<br/>• Search Performance<br/>• License Usage<br/>• Disk Space]
        
        NETWORK_HEALTH[🌐 Network Monitor Health<br/>• Packet Loss<br/>• Processing Lag<br/>• Rule Updates<br/>• Storage Usage]
        
        ENDPOINT_HEALTH[💻 Endpoint Health<br/>• Agent Connectivity<br/>• Collection Rate<br/>• Response Time<br/>• Coverage %]
        
        INTEL_HEALTH[🧠 Intel Platform Health<br/>• Feed Updates<br/>• API Response<br/>• Database Size<br/>• Correlation Rate]
    end
    
    subgraph "⚠️ Alerting System"
        THRESHOLD_ALERTS[📏 Threshold Alerts<br/>• Performance Degradation<br/>• Resource Exhaustion<br/>• Service Failures]
        
        PREDICTIVE_ALERTS[🔮 Predictive Alerts<br/>• Capacity Planning<br/>• Trend Analysis<br/>• Proactive Warnings]
        
        INTEGRATION_ALERTS[🔗 Integration Alerts<br/>• GOAD Connectivity<br/>• Agent Status<br/>• Data Flow Issues]
    end
    
    subgraph "🔧 Automated Response"
        AUTO_RESTART[🔄 Auto Restart<br/>• Service Recovery<br/>• Process Monitoring<br/>• Dependency Checks]
        
        SCALE_ACTIONS[📈 Scaling Actions<br/>• Resource Allocation<br/>• Load Balancing<br/>• Performance Tuning]
        
        NOTIFICATION[📢 Notifications<br/>• Admin Alerts<br/>• Status Updates<br/>• Escalation Procedures]
    end
    
    %% Monitoring Flow
    SIEM_HEALTH --> HEALTH_DASH
    NETWORK_HEALTH --> HEALTH_DASH
    ENDPOINT_HEALTH --> HEALTH_DASH
    INTEL_HEALTH --> HEALTH_DASH
    
    %% Alert Generation
    SIEM_HEALTH --> THRESHOLD_ALERTS
    NETWORK_HEALTH --> PREDICTIVE_ALERTS
    ENDPOINT_HEALTH --> INTEGRATION_ALERTS
    INTEL_HEALTH --> THRESHOLD_ALERTS
    
    %% Response Actions
    THRESHOLD_ALERTS --> AUTO_RESTART
    PREDICTIVE_ALERTS --> SCALE_ACTIONS
    INTEGRATION_ALERTS --> NOTIFICATION
    
    %% Feedback Loop
    AUTO_RESTART -.-> SIEM_HEALTH
    SCALE_ACTIONS -.-> NETWORK_HEALTH
    NOTIFICATION -.-> ENDPOINT_HEALTH
    
    %% Styling
    classDef dashboard fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef monitoring fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef alerting fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef response fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    
    class HEALTH_DASH dashboard
    class SIEM_HEALTH,NETWORK_HEALTH,ENDPOINT_HEALTH,INTEL_HEALTH monitoring
    class THRESHOLD_ALERTS,PREDICTIVE_ALERTS,INTEGRATION_ALERTS alerting
    class AUTO_RESTART,SCALE_ACTIONS,NOTIFICATION response
```
