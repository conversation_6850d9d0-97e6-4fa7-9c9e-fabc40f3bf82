# Data Flow Configuration

This guide covers the configuration of data flows from GOAD systems to GOAD-Blue monitoring platforms, ensuring comprehensive visibility and real-time analysis capabilities.

## 📊 Data Flow Architecture

```mermaid
graph TB
    subgraph "🎮 GOAD Data Sources"
        DC_LOGS[🏰 Domain Controller Logs<br/>Security, System, Application]
        SRV_LOGS[⚔️ Server Logs<br/>File Access, Services]
        WS_LOGS[🌹 Workstation Logs<br/>User Activity, Processes]
        SYSMON[👁️ Sysmon Events<br/>Process, Network, File]
        NETWORK[🌐 Network Traffic<br/>DNS, SMB, Kerberos]
    end
    
    subgraph "🚚 Data Collection Layer"
        SPLUNK_UF[📦 Splunk Universal Forwarders<br/>Log Collection & Parsing]
        WINLOGBEAT[🥁 Winlogbeat<br/>Event Log Shipping]
        VELO_AGENTS[🦖 Velociraptor Agents<br/>Artifact Collection]
        NETWORK_TAPS[📡 Network Monitoring<br/>Packet Capture & Analysis]
    end
    
    subgraph "⚙️ Data Processing Layer"
        SPLUNK_IDX[📊 Splunk Indexers<br/>Data Indexing & Search]
        ELASTIC_NODES[🔍 Elasticsearch Nodes<br/>Document Storage & Search]
        LOGSTASH[🔄 Logstash<br/>Data Transformation]
        SO_PROCESSING[🧅 Security Onion<br/>Network Analysis]
    end
    
    subgraph "🎯 Analysis & Visualization"
        SPLUNK_SH[📈 Splunk Search Heads<br/>Dashboards & Alerts]
        KIBANA[📊 Kibana<br/>Visualization & Discovery]
        SO_DASHBOARDS[🧅 Security Onion UI<br/>Network Monitoring]
        VELO_SERVER[🦖 Velociraptor Server<br/>Hunt Management]
        MISP[🧠 MISP<br/>Threat Intelligence]
    end
    
    %% Data Source to Collection
    DC_LOGS --> SPLUNK_UF
    DC_LOGS --> WINLOGBEAT
    SRV_LOGS --> SPLUNK_UF
    SRV_LOGS --> WINLOGBEAT
    WS_LOGS --> SPLUNK_UF
    WS_LOGS --> WINLOGBEAT
    SYSMON --> SPLUNK_UF
    SYSMON --> WINLOGBEAT
    NETWORK --> NETWORK_TAPS
    
    %% Collection to Processing
    SPLUNK_UF --> SPLUNK_IDX
    WINLOGBEAT --> LOGSTASH
    LOGSTASH --> ELASTIC_NODES
    VELO_AGENTS --> VELO_SERVER
    NETWORK_TAPS --> SO_PROCESSING
    
    %% Processing to Analysis
    SPLUNK_IDX --> SPLUNK_SH
    ELASTIC_NODES --> KIBANA
    SO_PROCESSING --> SO_DASHBOARDS
    VELO_SERVER --> VELO_SERVER
    SPLUNK_SH --> MISP
    KIBANA --> MISP
    
    classDef sources fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef collection fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef processing fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef analysis fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class DC_LOGS,SRV_LOGS,WS_LOGS,SYSMON,NETWORK sources
    class SPLUNK_UF,WINLOGBEAT,VELO_AGENTS,NETWORK_TAPS collection
    class SPLUNK_IDX,ELASTIC_NODES,LOGSTASH,SO_PROCESSING processing
    class SPLUNK_SH,KIBANA,SO_DASHBOARDS,VELO_SERVER,MISP analysis
```

## 🔧 Splunk Data Flow Configuration

### **Universal Forwarder Setup**

#### **inputs.conf Configuration**

```ini
# /opt/splunkforwarder/etc/system/local/inputs.conf

[default]
host = $decideOnStartup

# Windows Event Logs
[WinEventLog://Application]
disabled = false
start_from = oldest
current_only = false
checkpointInterval = 5
index = goad_blue_windows
sourcetype = WinEventLog:Application

[WinEventLog://Security]
disabled = false
start_from = oldest
current_only = false
checkpointInterval = 5
index = goad_blue_windows
sourcetype = WinEventLog:Security

[WinEventLog://System]
disabled = false
start_from = oldest
current_only = false
checkpointInterval = 5
index = goad_blue_windows
sourcetype = WinEventLog:System

# Sysmon Events
[WinEventLog://Microsoft-Windows-Sysmon/Operational]
disabled = false
start_from = oldest
current_only = false
checkpointInterval = 5
index = goad_blue_windows
sourcetype = WinEventLog:Sysmon
renderXml = true

# PowerShell Logs
[WinEventLog://Microsoft-Windows-PowerShell/Operational]
disabled = false
start_from = oldest
current_only = false
checkpointInterval = 5
index = goad_blue_windows
sourcetype = WinEventLog:PowerShell

[WinEventLog://Windows PowerShell]
disabled = false
start_from = oldest
current_only = false
checkpointInterval = 5
index = goad_blue_windows
sourcetype = WinEventLog:PowerShell

# DNS Server Logs (Domain Controllers)
[WinEventLog://DNS Server]
disabled = false
start_from = oldest
current_only = false
checkpointInterval = 5
index = goad_blue_windows
sourcetype = WinEventLog:DNS

# Active Directory Logs
[WinEventLog://Directory Service]
disabled = false
start_from = oldest
current_only = false
checkpointInterval = 5
index = goad_blue_windows
sourcetype = WinEventLog:DirectoryService

# File Replication Service
[WinEventLog://File Replication Service]
disabled = false
start_from = oldest
current_only = false
checkpointInterval = 5
index = goad_blue_windows
sourcetype = WinEventLog:FRS

# Custom Application Logs
[monitor://C:\inetpub\logs\LogFiles\W3SVC1\*.log]
disabled = false
index = goad_blue_windows
sourcetype = iis
crcSalt = <SOURCE>

# Performance Counters
[perfmon://CPU]
object = Processor
counters = % Processor Time
instances = *
interval = 30
index = goad_blue_performance
disabled = false

[perfmon://Memory]
object = Memory
counters = Available MBytes; Pages/sec
interval = 30
index = goad_blue_performance
disabled = false

[perfmon://Network]
object = Network Interface
counters = Bytes Total/sec; Packets/sec
instances = *
interval = 30
index = goad_blue_performance
disabled = false
```

#### **outputs.conf Configuration**

```ini
# /opt/splunkforwarder/etc/system/local/outputs.conf

[tcpout]
defaultGroup = goad_blue_indexers

[tcpout:goad_blue_indexers]
server = **************:9997
compressed = true
useACK = true

[tcpout-server://**************:9997]
```

#### **props.conf for Data Parsing**

```ini
# /opt/splunk/etc/apps/goad_blue/local/props.conf

[WinEventLog:Security]
SHOULD_LINEMERGE = false
TRUNCATE = 0
KV_MODE = xml
category = Windows Event Logs
description = Windows Security Event Log
pulldown_type = 1

# Extract key fields for authentication events
EXTRACT-user = <Data Name='TargetUserName'>(?<user>[^<]+)
EXTRACT-src_ip = <Data Name='IpAddress'>(?<src_ip>[^<]+)
EXTRACT-logon_type = <Data Name='LogonType'>(?<logon_type>[^<]+)
EXTRACT-process = <Data Name='ProcessName'>(?<process>[^<]+)

[WinEventLog:Sysmon]
SHOULD_LINEMERGE = false
TRUNCATE = 0
KV_MODE = xml
category = Sysmon
description = Sysmon Event Log
pulldown_type = 1

# Extract Sysmon fields
EXTRACT-process_name = <Data Name='Image'>(?<process_name>[^<]+)
EXTRACT-command_line = <Data Name='CommandLine'>(?<command_line>[^<]+)
EXTRACT-parent_process = <Data Name='ParentImage'>(?<parent_process>[^<]+)
EXTRACT-network_connection = <Data Name='DestinationIp'>(?<dest_ip>[^<]+).*<Data Name='DestinationPort'>(?<dest_port>[^<]+)

[WinEventLog:PowerShell]
SHOULD_LINEMERGE = false
TRUNCATE = 0
KV_MODE = xml
category = PowerShell
description = PowerShell Event Log
pulldown_type = 1

# Extract PowerShell command details
EXTRACT-script_block = <Data Name='ScriptBlockText'>(?<script_block>[^<]+)
EXTRACT-script_id = <Data Name='ScriptBlockId'>(?<script_id>[^<]+)
```

### **Index Configuration**

```ini
# /opt/splunk/etc/apps/goad_blue/local/indexes.conf

[goad_blue_windows]
homePath = $SPLUNK_DB/goad_blue_windows/db
coldPath = $SPLUNK_DB/goad_blue_windows/colddb
thawedPath = $SPLUNK_DB/goad_blue_windows/thaweddb
maxDataSize = 500MB
maxHotBuckets = 3
maxWarmDBCount = 20
maxTotalDataSizeMB = 50000
frozenTimePeriodInSecs = 7776000

[goad_blue_network]
homePath = $SPLUNK_DB/goad_blue_network/db
coldPath = $SPLUNK_DB/goad_blue_network/colddb
thawedPath = $SPLUNK_DB/goad_blue_network/thaweddb
maxDataSize = 1GB
maxHotBuckets = 5
maxWarmDBCount = 30
maxTotalDataSizeMB = 100000
frozenTimePeriodInSecs = 2592000

[goad_blue_performance]
homePath = $SPLUNK_DB/goad_blue_performance/db
coldPath = $SPLUNK_DB/goad_blue_performance/colddb
thawedPath = $SPLUNK_DB/goad_blue_performance/thaweddb
maxDataSize = 100MB
maxHotBuckets = 2
maxWarmDBCount = 10
maxTotalDataSizeMB = 10000
frozenTimePeriodInSecs = 2592000
```

## 🔍 Elastic Stack Data Flow

### **Winlogbeat Configuration**

```yaml
# /etc/winlogbeat/winlogbeat.yml

winlogbeat.event_logs:
  - name: Application
    level: critical, error, warning, information
    event_id: '-4624, -4625, -4648'
    
  - name: Security
    level: critical, error, warning, information
    processors:
      - script:
          lang: javascript
          id: security_enrichment
          source: >
            function process(event) {
              var winlog = event.Get("winlog");
              if (winlog && winlog.event_id) {
                switch(winlog.event_id) {
                  case 4624:
                    event.Put("event.action", "logon");
                    event.Put("event.category", "authentication");
                    break;
                  case 4625:
                    event.Put("event.action", "logon_failed");
                    event.Put("event.category", "authentication");
                    break;
                  case 4648:
                    event.Put("event.action", "explicit_logon");
                    event.Put("event.category", "authentication");
                    break;
                }
              }
            }
    
  - name: System
    level: critical, error, warning
    
  - name: Microsoft-Windows-Sysmon/Operational
    processors:
      - script:
          lang: javascript
          id: sysmon_enrichment
          source: >
            function process(event) {
              var winlog = event.Get("winlog");
              if (winlog && winlog.event_id) {
                switch(winlog.event_id) {
                  case 1:
                    event.Put("event.action", "process_creation");
                    event.Put("event.category", "process");
                    break;
                  case 3:
                    event.Put("event.action", "network_connection");
                    event.Put("event.category", "network");
                    break;
                  case 11:
                    event.Put("event.action", "file_creation");
                    event.Put("event.category", "file");
                    break;
                }
              }
            }

output.logstash:
  hosts: ["**************:5044"]

processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
  - add_docker_metadata: ~
  - add_kubernetes_metadata: ~

logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/winlogbeat
  name: winlogbeat
  keepfiles: 7
  permissions: 0644
```

### **Logstash Pipeline Configuration**

```ruby
# /etc/logstash/conf.d/goad-blue-pipeline.conf

input {
  beats {
    port => 5044
    type => "winlogbeat"
  }
  
  tcp {
    port => 514
    type => "syslog"
  }
  
  udp {
    port => 514
    type => "syslog"
  }
}

filter {
  # Windows Event Log processing
  if [agent][type] == "winlogbeat" {
    # Add GOAD-Blue specific fields
    mutate {
      add_field => { "lab_environment" => "goad" }
      add_field => { "monitoring_platform" => "goad-blue" }
    }
    
    # Parse Windows Event Log XML
    if [winlog][event_data] {
      # Extract common fields
      if [winlog][event_data][TargetUserName] {
        mutate {
          add_field => { "user_name" => "%{[winlog][event_data][TargetUserName]}" }
        }
      }
      
      if [winlog][event_data][IpAddress] {
        mutate {
          add_field => { "source_ip" => "%{[winlog][event_data][IpAddress]}" }
        }
      }
      
      if [winlog][event_data][ProcessName] {
        mutate {
          add_field => { "process_name" => "%{[winlog][event_data][ProcessName]}" }
        }
      }
    }
    
    # Sysmon specific processing
    if [winlog][channel] == "Microsoft-Windows-Sysmon/Operational" {
      if [winlog][event_id] == 1 {
        # Process creation
        mutate {
          add_field => { "event_type" => "process_creation" }
        }
        
        if [winlog][event_data][CommandLine] {
          # Detect suspicious commands
          if [winlog][event_data][CommandLine] =~ /powershell.*-enc/ {
            mutate {
              add_field => { "suspicious_activity" => "encoded_powershell" }
              add_tag => [ "suspicious", "powershell", "encoded" ]
            }
          }
          
          if [winlog][event_data][CommandLine] =~ /mimikatz|sekurlsa|kerberoast/ {
            mutate {
              add_field => { "suspicious_activity" => "credential_access" }
              add_tag => [ "suspicious", "credential_access", "attack_tool" ]
            }
          }
        }
      }
      
      if [winlog][event_id] == 3 {
        # Network connection
        mutate {
          add_field => { "event_type" => "network_connection" }
        }
        
        if [winlog][event_data][DestinationIp] and [winlog][event_data][DestinationPort] {
          mutate {
            add_field => { "destination" => "%{[winlog][event_data][DestinationIp]}:%{[winlog][event_data][DestinationPort]}" }
          }
        }
      }
    }
    
    # Authentication event processing
    if [winlog][event_id] == 4624 {
      mutate {
        add_field => { "event_type" => "successful_logon" }
      }
      
      # Detect unusual logon types
      if [winlog][event_data][LogonType] == "10" {
        mutate {
          add_tag => [ "rdp_logon" ]
        }
      }
    }
    
    if [winlog][event_id] == 4625 {
      mutate {
        add_field => { "event_type" => "failed_logon" }
        add_tag => [ "authentication_failure" ]
      }
    }
    
    # Kerberos events
    if [winlog][event_id] == 4769 {
      mutate {
        add_field => { "event_type" => "kerberos_service_ticket" }
      }
      
      # Detect potential Kerberoasting
      if [winlog][event_data][TicketEncryptionType] == "0x17" {
        mutate {
          add_field => { "suspicious_activity" => "potential_kerberoasting" }
          add_tag => [ "suspicious", "kerberoasting", "credential_access" ]
        }
      }
    }
  }
  
  # Network log processing
  if [type] == "syslog" {
    grok {
      match => { "message" => "%{SYSLOGTIMESTAMP:timestamp} %{IPORHOST:host} %{DATA:program}: %{GREEDYDATA:log_message}" }
    }
    
    date {
      match => [ "timestamp", "MMM  d HH:mm:ss", "MMM dd HH:mm:ss" ]
    }
  }
  
  # GeoIP enrichment for external IPs
  if [source_ip] and [source_ip] !~ /^(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.)/ {
    geoip {
      source => "source_ip"
      target => "geoip"
    }
  }
  
  # Add timestamp
  date {
    match => [ "[winlog][time_created]", "ISO8601" ]
  }
}

output {
  # Send to Elasticsearch
  elasticsearch {
    hosts => ["**************:9200"]
    index => "goad-blue-windows-%{+YYYY.MM.dd}"
    template_name => "goad-blue-windows"
    template => "/etc/logstash/templates/goad-blue-template.json"
    template_overwrite => true
  }
  
  # Debug output (remove in production)
  if "suspicious" in [tags] {
    file {
      path => "/var/log/logstash/suspicious-activity.log"
      codec => json_lines
    }
  }
}
```

### **Elasticsearch Index Template**

```json
{
  "index_patterns": ["goad-blue-*"],
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "refresh_interval": "30s",
    "index.mapping.total_fields.limit": 2000
  },
  "mappings": {
    "properties": {
      "@timestamp": {
        "type": "date"
      },
      "event": {
        "properties": {
          "action": {"type": "keyword"},
          "category": {"type": "keyword"},
          "type": {"type": "keyword"}
        }
      },
      "host": {
        "properties": {
          "name": {"type": "keyword"},
          "ip": {"type": "ip"}
        }
      },
      "user_name": {"type": "keyword"},
      "source_ip": {"type": "ip"},
      "destination_ip": {"type": "ip"},
      "process_name": {"type": "keyword"},
      "command_line": {"type": "text"},
      "suspicious_activity": {"type": "keyword"},
      "lab_environment": {"type": "keyword"},
      "winlog": {
        "properties": {
          "event_id": {"type": "long"},
          "channel": {"type": "keyword"},
          "computer_name": {"type": "keyword"},
          "event_data": {"type": "object"}
        }
      }
    }
  }
}
```

## 🦖 Velociraptor Data Collection

### **Artifact Collection Configuration**

```yaml
# velociraptor-server.yaml
Client:
  server_urls:
    - https://**************:8000/
  
  # Artifact collection schedule
  events:
    - artifact: Windows.Events.EventLogs
      parameters:
        eventChannels: >
          Security,System,Application,
          Microsoft-Windows-Sysmon/Operational,
          Microsoft-Windows-PowerShell/Operational
    
    - artifact: Windows.System.Pslist
      parameters:
        period: 300  # Every 5 minutes
    
    - artifact: Windows.Network.Netstat
      parameters:
        period: 300
    
    - artifact: Custom.GOAD.ProcessMonitoring
      parameters:
        period: 60
        
    - artifact: Custom.GOAD.NetworkConnections
      parameters:
        period: 120

# Custom GOAD monitoring artifacts
artifacts:
  - name: Custom.GOAD.ProcessMonitoring
    description: Monitor processes for GOAD-specific activities
    sources:
      - query: |
          SELECT timestamp() as Timestamp,
                 Pid, Ppid, Name, CommandLine, Username
          FROM pslist()
          WHERE CommandLine =~ "mimikatz|sekurlsa|kerberoast|bloodhound|sharphound"
             OR Name =~ "powershell|cmd|wmic|net"
             
  - name: Custom.GOAD.NetworkConnections
    description: Monitor network connections for lateral movement
    sources:
      - query: |
          SELECT timestamp() as Timestamp,
                 Pid, Name, LocalAddr, RemoteAddr, State
          FROM netstat()
          WHERE RemoteAddr.IP =~ "192\.168\.56\."
             AND State = "ESTABLISHED"
             AND RemoteAddr.Port in (445, 3389, 5985, 5986)
```

## 📊 Data Flow Monitoring and Validation

### **Data Flow Health Check Script**

```python
#!/usr/bin/env python3
# check_data_flow.py

import requests
import json
import time
from datetime import datetime, timedelta

class DataFlowChecker:
    def __init__(self):
        self.splunk_host = "**************"
        self.elastic_host = "**************"
        self.velociraptor_host = "**************"
        
    def check_splunk_data_flow(self):
        """Check if data is flowing into Splunk"""
        print("Checking Splunk data flow...")
        
        # Check recent events in last 5 minutes
        search_query = 'search index=goad_blue_windows earliest=-5m | stats count'
        
        try:
            # This would require proper Splunk SDK implementation
            # For demo purposes, showing the concept
            response = requests.get(
                f"https://{self.splunk_host}:8089/services/search/jobs",
                auth=('admin', 'changeme'),
                verify=False
            )
            
            if response.status_code == 200:
                print("✓ Splunk is accessible")
                # Additional checks for data volume, recent events, etc.
                return True
            else:
                print("✗ Splunk connection failed")
                return False
                
        except Exception as e:
            print(f"✗ Splunk check failed: {e}")
            return False
    
    def check_elasticsearch_data_flow(self):
        """Check if data is flowing into Elasticsearch"""
        print("Checking Elasticsearch data flow...")
        
        try:
            # Check cluster health
            response = requests.get(f"http://{self.elastic_host}:9200/_cluster/health")
            
            if response.status_code == 200:
                health = response.json()
                print(f"✓ Elasticsearch cluster status: {health['status']}")
                
                # Check recent documents
                five_min_ago = (datetime.now() - timedelta(minutes=5)).isoformat()
                query = {
                    "query": {
                        "range": {
                            "@timestamp": {
                                "gte": five_min_ago
                            }
                        }
                    }
                }
                
                response = requests.post(
                    f"http://{self.elastic_host}:9200/goad-blue-*/_count",
                    json=query
                )
                
                if response.status_code == 200:
                    count = response.json()['count']
                    print(f"✓ Recent documents in last 5 minutes: {count}")
                    return count > 0
                    
            return False
            
        except Exception as e:
            print(f"✗ Elasticsearch check failed: {e}")
            return False
    
    def check_velociraptor_agents(self):
        """Check Velociraptor agent connectivity"""
        print("Checking Velociraptor agent status...")
        
        try:
            # This would require proper Velociraptor API implementation
            response = requests.get(
                f"https://{self.velociraptor_host}:8889/api/v1/GetClients",
                verify=False
            )
            
            if response.status_code == 200:
                clients = response.json()
                online_clients = [c for c in clients if c.get('last_seen_at', 0) > time.time() - 300]
                print(f"✓ Velociraptor agents online: {len(online_clients)}")
                return len(online_clients) > 0
            
            return False
            
        except Exception as e:
            print(f"✗ Velociraptor check failed: {e}")
            return False
    
    def run_health_check(self):
        """Run complete data flow health check"""
        print("=== GOAD-Blue Data Flow Health Check ===")
        print(f"Timestamp: {datetime.now().isoformat()}")
        print()
        
        results = {
            'splunk': self.check_splunk_data_flow(),
            'elasticsearch': self.check_elasticsearch_data_flow(),
            'velociraptor': self.check_velociraptor_agents()
        }
        
        print()
        print("=== Summary ===")
        for platform, status in results.items():
            status_icon = "✓" if status else "✗"
            print(f"{status_icon} {platform.capitalize()}: {'OK' if status else 'FAILED'}")
        
        overall_health = all(results.values())
        print(f"\nOverall Data Flow Health: {'HEALTHY' if overall_health else 'DEGRADED'}")
        
        return overall_health

if __name__ == "__main__":
    checker = DataFlowChecker()
    checker.run_health_check()
```

### **Automated Data Flow Testing**

```bash
#!/bin/bash
# test_data_flow.sh

echo "=== GOAD-Blue Data Flow Test ==="

# Generate test events on GOAD systems
echo "Generating test events..."

# Test authentication events
winrs -r:192.168.56.10 -u:"sevenkingdoms\administrator" -p:"Password123!" "
    # Generate failed logon
    runas /user:testuser cmd.exe
    
    # Generate successful logon
    net use \\\\castelblack\\c$ /user:sevenkingdoms\\administrator Password123!
    
    # Generate Kerberos events
    klist purge
    klist get krbtgt
"

# Test process creation events
winrs -r:************* -u:"sevenkingdoms\margaery.tyrell" -p:"Password123!" "
    # Generate suspicious PowerShell
    powershell.exe -EncodedCommand SQBuAHYAbwBrAGUALQBXAGUAYgBSAGUAcQB1AGUAcwB0AA==
    
    # Generate process creation
    whoami /all
    net user
    ipconfig /all
"

echo "Test events generated. Waiting for data flow..."
sleep 30

# Check if events appear in SIEM
echo "Checking data flow to SIEM platforms..."

# Check Splunk
curl -k -u admin:changeme "https://**************:8089/services/search/jobs" \
  -d "search=search index=goad_blue_windows earliest=-5m | stats count" \
  -d "output_mode=json"

# Check Elasticsearch
curl -X GET "**************:9200/goad-blue-*/_count" \
  -H "Content-Type: application/json" \
  -d '{
    "query": {
      "range": {
        "@timestamp": {
          "gte": "now-5m"
        }
      }
    }
  }'

echo "Data flow test completed."
```

---

!!! success "Data Flow Configuration Complete"
    Once data flows are configured and validated, GOAD systems will provide comprehensive visibility into all security-relevant activities for blue team analysis.

!!! tip "Performance Optimization"
    Monitor data ingestion rates and adjust collection intervals based on your analysis requirements and system performance.

!!! warning "Data Retention"
    Configure appropriate data retention policies based on your storage capacity and compliance requirements. Consider implementing data archival for long-term storage.
