#!/bin/bash
# GOAD-Blue Master Monitoring Script
# Orchestrates monitoring of all GOAD-Blue components

set -e

# Configuration
GOAD_BLUE_HOME="/opt/goad-blue"
MONITOR_DIR="$GOAD_BLUE_HOME/scripts/monitoring"
LOG_DIR="/var/log/goad-blue"
REPORT_DIR="$GOAD_BLUE_HOME/reports"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Create directories if they don't exist
mkdir -p "$LOG_DIR" "$REPORT_DIR"

# Print colored output
print_header() {
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}========================================${NC}"
}

print_status() {
    local status=$1
    local message=$2
    case $status in
        "OK")
            echo -e "${GREEN}[OK]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
    esac
}

# Check if component is installed
is_component_installed() {
    local component=$1
    case $component in
        "splunk")
            [ -d "/opt/splunk" ] && systemctl list-unit-files | grep -q "Splunkd"
            ;;
        "velociraptor")
            [ -f "/usr/local/bin/velociraptor" ] && systemctl list-unit-files | grep -q "velociraptor"
            ;;
        "malcolm")
            [ -d "/opt/malcolm" ] && [ -f "/opt/malcolm/docker-compose.yml" ]
            ;;
        "misp")
            [ -d "/var/www/MISP" ] && systemctl list-unit-files | grep -q "apache2\|nginx"
            ;;
        "security-onion")
            [ -d "/opt/so" ] || [ -d "/nsm" ]
            ;;
        "elasticsearch")
            systemctl list-unit-files | grep -q "elasticsearch"
            ;;
        "flare-vm")
            # This would be checked on Windows systems
            false
            ;;
        *)
            false
            ;;
    esac
}

# Run component monitoring
monitor_component() {
    local component=$1
    local monitor_script="$MONITOR_DIR/${component}-monitor.sh"
    
    print_status "INFO" "Monitoring $component..."
    
    if [ -f "$monitor_script" ]; then
        if bash "$monitor_script" --quiet; then
            print_status "OK" "$component monitoring completed"
            return 0
        else
            print_status "ERROR" "$component monitoring failed"
            return 1
        fi
    else
        print_status "WARNING" "Monitor script not found: $monitor_script"
        return 1
    fi
}

# System overview
system_overview() {
    print_header "GOAD-Blue System Overview"
    
    echo "Timestamp: $(date)"
    echo "Hostname: $(hostname)"
    echo "Uptime: $(uptime -p)"
    echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"
    echo ""
    
    # System resources
    echo "=== System Resources ==="
    echo "CPU Cores: $(nproc)"
    echo "Memory: $(free -h | grep Mem | awk '{print $3 "/" $2 " (" int($3/$2*100) "%)"}')"
    echo "Disk Usage: $(df -h / | tail -1 | awk '{print $3 "/" $2 " (" $5 ")"}')"
    echo ""
    
    # Network interfaces
    echo "=== Network Interfaces ==="
    ip addr show | grep -E '^[0-9]+:|inet ' | sed 's/^[[:space:]]*//' | head -10
    echo ""
}

# Component discovery
discover_components() {
    print_header "GOAD-Blue Component Discovery"
    
    local components=("splunk" "velociraptor" "malcolm" "misp" "security-onion" "elasticsearch")
    local installed_components=()
    
    for component in "${components[@]}"; do
        if is_component_installed "$component"; then
            print_status "OK" "$component is installed"
            installed_components+=("$component")
        else
            print_status "INFO" "$component is not installed"
        fi
    done
    
    echo ""
    echo "${installed_components[@]}"
}

# Monitor all components
monitor_all_components() {
    print_header "GOAD-Blue Component Monitoring"
    
    local components
    components=$(discover_components | tail -1)
    
    local success_count=0
    local total_count=0
    
    for component in $components; do
        total_count=$((total_count + 1))
        if monitor_component "$component"; then
            success_count=$((success_count + 1))
        fi
        echo ""
    done
    
    print_status "INFO" "Monitoring completed: $success_count/$total_count components successful"
    
    return $((total_count - success_count))
}

# Network connectivity check
check_network_connectivity() {
    print_header "GOAD-Blue Network Connectivity"
    
    local endpoints=(
        "splunk:https://localhost:8000"
        "velociraptor:https://localhost:8889"
        "malcolm:https://localhost"
        "elasticsearch:http://localhost:9200"
        "kibana:http://localhost:5601"
    )
    
    for endpoint in "${endpoints[@]}"; do
        local name=$(echo "$endpoint" | cut -d: -f1)
        local url=$(echo "$endpoint" | cut -d: -f2-)
        
        if curl -k -s --connect-timeout 5 "$url" >/dev/null 2>&1; then
            print_status "OK" "$name is accessible ($url)"
        else
            print_status "WARNING" "$name is not accessible ($url)"
        fi
    done
    
    echo ""
}

# Service status check
check_service_status() {
    print_header "GOAD-Blue Service Status"
    
    local services=("Splunkd" "velociraptor" "elasticsearch" "kibana" "apache2" "nginx" "docker")
    
    for service in "${services[@]}"; do
        if systemctl list-unit-files | grep -q "$service"; then
            if systemctl is-active --quiet "$service"; then
                print_status "OK" "$service is running"
            else
                print_status "WARNING" "$service is not running"
            fi
        fi
    done
    
    echo ""
}

# Generate health report
generate_health_report() {
    local report_file="$REPORT_DIR/goad-blue-health-$TIMESTAMP.txt"
    
    print_header "Generating Health Report"
    
    {
        echo "GOAD-Blue Health Report"
        echo "Generated: $(date)"
        echo "========================================"
        echo ""
        
        # System information
        echo "=== System Information ==="
        echo "Hostname: $(hostname)"
        echo "OS: $(lsb_release -d 2>/dev/null | cut -f2 || uname -a)"
        echo "Kernel: $(uname -r)"
        echo "Uptime: $(uptime -p)"
        echo ""
        
        # Resource usage
        echo "=== Resource Usage ==="
        echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)% used"
        echo "Memory: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')"
        echo "Disk: $(df -h / | tail -1 | awk '{print $5}')"
        echo ""
        
        # Component status
        echo "=== Component Status ==="
        local components
        components=$(discover_components | tail -1)
        for component in $components; do
            if is_component_installed "$component"; then
                echo "$component: Installed"
            fi
        done
        echo ""
        
        # Service status
        echo "=== Service Status ==="
        local services=("Splunkd" "velociraptor" "elasticsearch" "docker")
        for service in "${services[@]}"; do
            if systemctl list-unit-files | grep -q "$service"; then
                local status=$(systemctl is-active "$service" 2>/dev/null || echo "inactive")
                echo "$service: $status"
            fi
        done
        echo ""
        
        # Recent logs
        echo "=== Recent Issues ==="
        if [ -d "$LOG_DIR" ]; then
            find "$LOG_DIR" -name "*.log" -mtime -1 -exec grep -l "ERROR\|FATAL\|CRITICAL" {} \; 2>/dev/null | head -5 | while read logfile; do
                echo "Issues found in: $logfile"
                grep "ERROR\|FATAL\|CRITICAL" "$logfile" | tail -3
                echo ""
            done
        fi
        
    } > "$report_file"
    
    print_status "OK" "Health report generated: $report_file"
    echo ""
}

# Show usage
show_usage() {
    echo "GOAD-Blue Master Monitor"
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --overview, -o         Show system overview only"
    echo "  --discover, -d         Discover installed components"
    echo "  --monitor, -m          Monitor all components"
    echo "  --network, -n          Check network connectivity"
    echo "  --services, -s         Check service status"
    echo "  --report, -r           Generate health report"
    echo "  --all, -a              Run all checks (default)"
    echo "  --component <name>     Monitor specific component"
    echo "  --quiet, -q            Quiet mode"
    echo "  --help, -h             Show this help"
    echo ""
    echo "Components: splunk, velociraptor, malcolm, misp, security-onion, elasticsearch"
    echo ""
}

# Main function
main() {
    local mode="all"
    local component=""
    local quiet=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --overview|-o)
                mode="overview"
                shift
                ;;
            --discover|-d)
                mode="discover"
                shift
                ;;
            --monitor|-m)
                mode="monitor"
                shift
                ;;
            --network|-n)
                mode="network"
                shift
                ;;
            --services|-s)
                mode="services"
                shift
                ;;
            --report|-r)
                mode="report"
                shift
                ;;
            --all|-a)
                mode="all"
                shift
                ;;
            --component)
                mode="component"
                component="$2"
                shift 2
                ;;
            --quiet|-q)
                quiet=true
                shift
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Redirect output if quiet mode
    if [ "$quiet" = true ]; then
        exec > "$LOG_DIR/goad-blue-monitor-$TIMESTAMP.log" 2>&1
    fi
    
    # Execute based on mode
    case $mode in
        "overview")
            system_overview
            ;;
        "discover")
            discover_components
            ;;
        "monitor")
            monitor_all_components
            ;;
        "network")
            check_network_connectivity
            ;;
        "services")
            check_service_status
            ;;
        "report")
            generate_health_report
            ;;
        "component")
            if [ -n "$component" ]; then
                monitor_component "$component"
            else
                echo "Component name required"
                exit 1
            fi
            ;;
        "all")
            system_overview
            discover_components
            check_service_status
            check_network_connectivity
            monitor_all_components
            generate_health_report
            ;;
    esac
    
    if [ "$quiet" = true ]; then
        echo "Monitoring completed. Check $LOG_DIR/goad-blue-monitor-$TIMESTAMP.log for details."
    fi
}

# Run main function with all arguments
main "$@"
