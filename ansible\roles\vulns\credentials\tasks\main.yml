- name: Store a password in Credential Manager
  community.windows.win_credential:
    name: "{{item.key}}"
    type: domain_password
    username: "{{item.value.username}}"
    secret: "{{item.value.secret}}"
    comment: "Credential for {{item.value.username}}"
    persistence: local
    state: present
  ignore_errors: true
  vars:
    ansible_become: yes
    ansible_become_method: runas
    ansible_become_user: "{{item.value.runas | default(domain_username) }}"
    ansible_become_password: "{{item.value.runas_password | default(domain_password) }}"
  with_dict: "{{ vulns_vars }}"