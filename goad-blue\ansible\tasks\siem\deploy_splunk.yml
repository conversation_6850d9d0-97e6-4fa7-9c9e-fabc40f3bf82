---
# Deploy Splunk Enterprise for GOAD-Blue

- name: Create splunk user
  user:
    name: splunk
    system: yes
    shell: /bin/bash
    home: /opt/splunk
    create_home: no

- name: Download Splunk Enterprise
  get_url:
    url: "{{ splunk.download_url }}"
    dest: "/tmp/splunk-{{ splunk.version }}-linux-2.6-x86_64.tgz"
    mode: '0644'
  register: splunk_download

- name: Extract Splunk
  unarchive:
    src: "/tmp/splunk-{{ splunk.version }}-linux-2.6-x86_64.tgz"
    dest: /opt
    remote_src: yes
    owner: splunk
    group: splunk
  when: splunk_download.changed

- name: Set Splunk ownership
  file:
    path: /opt/splunk
    owner: splunk
    group: splunk
    recurse: yes

- name: Create Splunk systemd service
  template:
    src: splunk.service.j2
    dest: /etc/systemd/system/splunk.service
    mode: '0644'
  notify: reload systemd

- name: Start Splunk for first time setup
  become_user: splunk
  shell: |
    /opt/splunk/bin/splunk start --accept-license --answer-yes --no-prompt --seed-passwd {{ splunk.admin_password }}
  args:
    creates: /opt/splunk/etc/passwd

- name: Enable Splunk boot start
  become_user: splunk
  shell: |
    /opt/splunk/bin/splunk enable boot-start -systemd-managed 1 --accept-license --answer-yes --no-prompt
  args:
    creates: /etc/systemd/system/Splunkd.service

- name: Configure Splunk web SSL
  template:
    src: web.conf.j2
    dest: /opt/splunk/etc/system/local/web.conf
    owner: splunk
    group: splunk
    mode: '0644'
  notify: restart splunk

- name: Configure Splunk server settings
  template:
    src: server.conf.j2
    dest: /opt/splunk/etc/system/local/server.conf
    owner: splunk
    group: splunk
    mode: '0644'
  notify: restart splunk

- name: Configure Splunk inputs
  template:
    src: inputs.conf.j2
    dest: /opt/splunk/etc/system/local/inputs.conf
    owner: splunk
    group: splunk
    mode: '0644'
  notify: restart splunk

- name: Configure Splunk outputs (if forwarder)
  template:
    src: outputs.conf.j2
    dest: /opt/splunk/etc/system/local/outputs.conf
    owner: splunk
    group: splunk
    mode: '0644'
  when: splunk.role == 'forwarder'
  notify: restart splunk

- name: Start and enable Splunk service
  systemd:
    name: splunk
    state: started
    enabled: yes
    daemon_reload: yes

- name: Wait for Splunk to be ready
  uri:
    url: "https://{{ ansible_default_ipv4.address }}:8000"
    method: GET
    validate_certs: no
    status_code: 200
  register: splunk_ready
  until: splunk_ready.status == 200
  retries: 30
  delay: 10

- name: Set Splunk admin password
  uri:
    url: "https://{{ ansible_default_ipv4.address }}:8000/services/authentication/users/admin"
    method: POST
    user: admin
    password: changeme
    validate_certs: no
    body_format: form-urlencoded
    body:
      password: "{{ splunk.admin_password }}"
      roles: admin
  ignore_errors: yes  # May fail if password already changed
