# MISP

MISP (Malware Information Sharing Platform) is an open-source threat intelligence platform designed for sharing, storing, and correlating indicators of compromise (IOCs) and threat intelligence. In GOAD-Blue, MISP serves as the central threat intelligence hub.

## 🎯 Overview

MISP provides comprehensive threat intelligence management capabilities, enabling security teams to collect, analyze, and share threat information to enhance detection and response capabilities across the GOAD environment.

```mermaid
graph TB
    subgraph "🧠 MISP Architecture"
        CORE[🏛️ MISP Core<br/>Central Platform<br/>Web Interface]
        DATABASE[🗄️ Database<br/>MySQL/MariaDB<br/>Threat Data Storage]
        REDIS[⚡ Redis<br/>Caching Layer<br/>Session Management]
        WORKERS[👷 Background Workers<br/>Data Processing<br/>Feed Synchronization]
    end
    
    subgraph "📥 Data Sources"
        FEEDS[📡 Threat Feeds<br/>External Sources<br/>Commercial & Open]
        MANUAL[✋ Manual Input<br/>Analyst Contributions<br/>Incident Reports]
        API[🔌 API Integrations<br/>Automated Ingestion<br/>Tool Integration]
        SHARING[🤝 Sharing Groups<br/>Community Intelligence<br/>Peer Organizations]
    end
    
    subgraph "🔍 Intelligence Processing"
        CORRELATION[🔗 Correlation Engine<br/>IOC Relationships<br/>Pattern Matching]
        ENRICHMENT[📈 Data Enrichment<br/>Context Addition<br/>Attribution Analysis]
        VALIDATION[✅ Data Validation<br/>Quality Control<br/>False Positive Reduction]
        TAXONOMY[📚 Taxonomies<br/>Classification Systems<br/>Standardized Tags]
    end
    
    subgraph "📤 Intelligence Distribution"
        SIEM_INTEGRATION[📊 SIEM Integration<br/>Splunk/Elastic<br/>Real-time Feeds]
        EDR_INTEGRATION[🛡️ EDR Integration<br/>Velociraptor<br/>IOC Deployment]
        NETWORK_INTEGRATION[🌐 Network Integration<br/>Security Onion<br/>Signature Updates]
        REPORTS[📋 Intelligence Reports<br/>Executive Summaries<br/>Technical Analysis]
    end
    
    subgraph "🎮 GOAD Environment"
        GOAD_INCIDENTS[🚨 GOAD Incidents<br/>Attack Artifacts<br/>IOC Generation]
        GOAD_ANALYSIS[🔬 Malware Analysis<br/>FLARE-VM Results<br/>Behavioral IOCs]
    end
    
    FEEDS --> CORE
    MANUAL --> CORE
    API --> CORE
    SHARING --> CORE
    
    CORE --> DATABASE
    CORE --> REDIS
    CORE --> WORKERS
    
    CORE --> CORRELATION
    CORE --> ENRICHMENT
    CORE --> VALIDATION
    CORE --> TAXONOMY
    
    CORRELATION --> SIEM_INTEGRATION
    ENRICHMENT --> EDR_INTEGRATION
    VALIDATION --> NETWORK_INTEGRATION
    TAXONOMY --> REPORTS
    
    GOAD_INCIDENTS --> MANUAL
    GOAD_ANALYSIS --> API
    
    classDef misp fill:#d32f2f,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef sources fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef processing fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef distribution fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef goad fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class CORE,DATABASE,REDIS,WORKERS misp
    class FEEDS,MANUAL,API,SHARING sources
    class CORRELATION,ENRICHMENT,VALIDATION,TAXONOMY processing
    class SIEM_INTEGRATION,EDR_INTEGRATION,NETWORK_INTEGRATION,REPORTS distribution
    class GOAD_INCIDENTS,GOAD_ANALYSIS goad
```

## 🚀 Installation and Setup

### **Automated Installation**

```bash
# Install MISP using GOAD-Blue automation
python3 goad-blue.py install --component misp --deployment-type standalone

# Configure MISP for GOAD integration
python3 goad-blue.py configure --component misp --enable-goad-feeds

# Initialize threat intelligence feeds
python3 goad-blue.py setup-feeds --component misp --feed-type all
```

### **Manual Installation**

```bash
# Install dependencies
sudo apt-get update
sudo apt-get install -y apache2 mariadb-server php php-mysql php-xml php-mbstring php-zip php-gd php-curl php-redis redis-server git

# Clone MISP repository
cd /var/www/html
sudo git clone https://github.com/MISP/MISP.git
cd MISP
sudo git submodule update --init --recursive

# Install PHP dependencies
sudo -u www-data php composer.phar install --no-dev

# Set up database
sudo mysql -u root -p << EOF
CREATE DATABASE misp;
CREATE USER 'misp'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON misp.* TO 'misp'@'localhost';
FLUSH PRIVILEGES;
EOF

# Import database schema
sudo -u www-data mysql -u misp -p misp < INSTALL/MYSQL.sql

# Configure MISP
sudo cp app/Config/bootstrap.default.php app/Config/bootstrap.php
sudo cp app/Config/database.default.php app/Config/database.php
sudo cp app/Config/core.default.php app/Config/core.php
sudo cp app/Config/config.default.php app/Config/config.php

# Set permissions
sudo chown -R www-data:www-data /var/www/html/MISP
sudo chmod -R 755 /var/www/html/MISP
```

### **Configuration Files**

```php
<?php
// app/Config/config.php - GOAD-Blue MISP Configuration

$config = array(
    // Database configuration
    'Database' => array(
        'default' => array(
            'datasource' => 'Database/Mysql',
            'persistent' => false,
            'host' => 'localhost',
            'login' => 'misp',
            'password' => 'secure_password',
            'database' => 'misp',
            'prefix' => '',
            'encoding' => 'utf8',
        )
    ),
    
    // Redis configuration
    'Redis' => array(
        'host' => '127.0.0.1',
        'port' => 6379,
        'database' => 13,
        'password' => '',
    ),
    
    // GOAD-Blue specific settings
    'MISP' => array(
        'baseurl' => 'https://misp.goad-blue.local',
        'org' => 'GOAD-Blue',
        'email' => '<EMAIL>',
        'contact' => '<EMAIL>',
        
        // Security settings
        'security_level' => 'high',
        'password_policy_length' => 12,
        'password_policy_complexity' => '/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{12,}$/',
        
        // API settings
        'rest_client_enable_arbitrary_urls' => true,
        'rest_client_baseurl' => 'https://misp.goad-blue.local',
        
        // Background jobs
        'background_jobs' => true,
        'cached_attachments' => true,
        
        // GOAD-Blue integrations
        'goad_blue_integration' => true,
        'splunk_integration' => array(
            'enabled' => true,
            'url' => 'https://splunk.goad-blue.local:8089',
            'token' => 'YOUR_SPLUNK_HEC_TOKEN'
        ),
        'velociraptor_integration' => array(
            'enabled' => true,
            'url' => 'https://velociraptor.goad-blue.local',
            'api_key' => 'YOUR_VELOCIRAPTOR_API_KEY'
        )
    )
);
?>
```

## 📊 GOAD-Blue Threat Intelligence

### **Custom Taxonomies for GOAD**

```json
{
  "namespace": "goad-blue",
  "description": "GOAD-Blue specific threat intelligence taxonomy",
  "version": 1,
  "predicates": [
    {
      "value": "attack-technique",
      "expanded": "GOAD Attack Technique",
      "description": "Techniques observed in GOAD environment"
    },
    {
      "value": "environment-component",
      "expanded": "GOAD Environment Component", 
      "description": "Specific GOAD infrastructure components"
    },
    {
      "value": "training-scenario",
      "expanded": "Training Scenario",
      "description": "Educational attack scenarios"
    }
  ],
  "values": [
    {
      "predicate": "attack-technique",
      "entry": [
        {
          "value": "credential-dumping",
          "expanded": "Credential Dumping",
          "description": "Techniques for extracting credentials from GOAD systems"
        },
        {
          "value": "lateral-movement",
          "expanded": "Lateral Movement", 
          "description": "Movement between GOAD domain systems"
        },
        {
          "value": "persistence",
          "expanded": "Persistence",
          "description": "Maintaining access to GOAD environment"
        },
        {
          "value": "privilege-escalation",
          "expanded": "Privilege Escalation",
          "description": "Escalating privileges within GOAD domain"
        }
      ]
    },
    {
      "predicate": "environment-component",
      "entry": [
        {
          "value": "domain-controller",
          "expanded": "Domain Controller",
          "description": "GOAD domain controller systems"
        },
        {
          "value": "member-server",
          "expanded": "Member Server",
          "description": "GOAD domain member servers"
        },
        {
          "value": "workstation",
          "expanded": "Workstation",
          "description": "GOAD user workstations"
        }
      ]
    }
  ]
}
```

### **GOAD-Specific Event Templates**

```json
{
  "Event": {
    "info": "GOAD-Blue Incident: Credential Dumping Attack",
    "threat_level_id": "2",
    "analysis": "1",
    "distribution": "1",
    "orgc_id": "1",
    "org_id": "1",
    "date": "2024-01-15",
    "extends_uuid": "",
    "published": false,
    "attribute_count": "0",
    "analysis": "1",
    "timestamp": "1705334400",
    "sharing_group_id": "0",
    "proposal_email_lock": false,
    "locked": false,
    "disable_correlation": false,
    "event_creator_email": "<EMAIL>",
    "Tag": [
      {
        "name": "goad-blue:attack-technique=\"credential-dumping\"",
        "colour": "#ff0000",
        "exportable": true
      },
      {
        "name": "tlp:amber",
        "colour": "#ffc000",
        "exportable": true
      }
    ],
    "Attribute": [
      {
        "type": "filename",
        "category": "Payload delivery",
        "value": "mimikatz.exe",
        "comment": "Credential dumping tool observed in GOAD environment",
        "to_ids": true,
        "uuid": "uuid-here",
        "timestamp": "1705334400",
        "distribution": "5",
        "sharing_group_id": "0"
      },
      {
        "type": "sha256",
        "category": "Payload delivery", 
        "value": "b0e914d1bbe19433cc9df64ea1ca07fe77f7b150b511b786e40c0e4b89b3b7f5",
        "comment": "SHA256 hash of mimikatz.exe",
        "to_ids": true,
        "uuid": "uuid-here",
        "timestamp": "1705334400",
        "distribution": "5"
      },
      {
        "type": "ip-dst",
        "category": "Network activity",
        "value": "*************",
        "comment": "GOAD domain controller targeted",
        "to_ids": true,
        "uuid": "uuid-here",
        "timestamp": "1705334400",
        "distribution": "5"
      }
    ]
  }
}
```

## 🔄 Automated Intelligence Processing

### **Feed Management**

```python
# Python script for MISP feed management
import requests
import json
from pymisp import PyMISP
from datetime import datetime, timedelta

class GOADMISPManager:
    def __init__(self, misp_url, misp_key):
        self.misp = PyMISP(misp_url, misp_key)
        self.goad_feeds = [
            {
                'name': 'GOAD-Blue Internal Feed',
                'url': 'https://feeds.goad-blue.local/internal',
                'format': 'misp',
                'enabled': True
            },
            {
                'name': 'Emerging Threats',
                'url': 'https://rules.emergingthreats.net/blockrules/compromised-ips.txt',
                'format': 'csv',
                'enabled': True
            },
            {
                'name': 'Abuse.ch URLhaus',
                'url': 'https://urlhaus.abuse.ch/downloads/misp/',
                'format': 'misp',
                'enabled': True
            }
        ]
    
    def setup_goad_feeds(self):
        """Set up threat intelligence feeds for GOAD-Blue"""
        for feed_config in self.goad_feeds:
            try:
                feed = self.misp.add_feed(
                    name=feed_config['name'],
                    url=feed_config['url'],
                    distribution=1,  # This community only
                    default=True,
                    enabled=feed_config['enabled'],
                    caching_enabled=True,
                    lookup_visible=True
                )
                print(f"Added feed: {feed_config['name']}")
            except Exception as e:
                print(f"Error adding feed {feed_config['name']}: {e}")
    
    def sync_feeds(self):
        """Synchronize all enabled feeds"""
        feeds = self.misp.feeds()
        
        for feed in feeds:
            if feed.get('Feed', {}).get('enabled'):
                try:
                    result = self.misp.fetch_feed(feed['Feed']['id'])
                    print(f"Synced feed: {feed['Feed']['name']}")
                except Exception as e:
                    print(f"Error syncing feed {feed['Feed']['name']}: {e}")
    
    def create_goad_incident_event(self, incident_data):
        """Create MISP event from GOAD incident"""
        event = {
            'info': f"GOAD-Blue Incident: {incident_data['title']}",
            'threat_level_id': self.map_severity_to_threat_level(incident_data['severity']),
            'analysis': '1',  # Ongoing
            'distribution': '1',  # This community only
            'published': False,
            'Tag': [
                {'name': 'goad-blue:training-scenario'},
                {'name': f"tlp:{incident_data.get('tlp', 'amber')}"}
            ]
        }
        
        # Add attributes from incident data
        attributes = []
        
        # Add IOCs
        for ioc in incident_data.get('iocs', []):
            attributes.append({
                'type': ioc['type'],
                'value': ioc['value'],
                'category': self.map_ioc_category(ioc['type']),
                'comment': ioc.get('comment', ''),
                'to_ids': True
            })
        
        # Add network indicators
        for network_indicator in incident_data.get('network_indicators', []):
            attributes.append({
                'type': 'ip-dst',
                'value': network_indicator['ip'],
                'category': 'Network activity',
                'comment': f"Observed in GOAD environment: {network_indicator['description']}",
                'to_ids': True
            })
        
        event['Attribute'] = attributes
        
        # Create event in MISP
        misp_event = self.misp.add_event(event)
        return misp_event
    
    def map_severity_to_threat_level(self, severity):
        """Map incident severity to MISP threat level"""
        mapping = {
            'critical': '1',  # High
            'high': '2',      # Medium
            'medium': '3',    # Low
            'low': '4'        # Undefined
        }
        return mapping.get(severity.lower(), '4')
    
    def map_ioc_category(self, ioc_type):
        """Map IOC type to MISP category"""
        mapping = {
            'md5': 'Payload delivery',
            'sha1': 'Payload delivery',
            'sha256': 'Payload delivery',
            'filename': 'Payload delivery',
            'ip-src': 'Network activity',
            'ip-dst': 'Network activity',
            'domain': 'Network activity',
            'url': 'Network activity',
            'email': 'Payload delivery',
            'registry-key': 'Persistence mechanism'
        }
        return mapping.get(ioc_type, 'Other')
    
    def export_iocs_for_tools(self, tool_type='splunk'):
        """Export IOCs in format suitable for security tools"""
        # Get recent events
        events = self.misp.search(
            published=True,
            timestamp='7d',
            to_ids=True
        )
        
        if tool_type == 'splunk':
            return self.export_splunk_format(events)
        elif tool_type == 'suricata':
            return self.export_suricata_format(events)
        elif tool_type == 'velociraptor':
            return self.export_velociraptor_format(events)
    
    def export_splunk_format(self, events):
        """Export IOCs in Splunk lookup format"""
        iocs = []
        
        for event in events:
            for attribute in event.get('Attribute', []):
                if attribute.get('to_ids'):
                    iocs.append({
                        'indicator': attribute['value'],
                        'type': attribute['type'],
                        'category': attribute['category'],
                        'threat_level': event.get('threat_level_id'),
                        'first_seen': event.get('date'),
                        'source': 'MISP',
                        'description': attribute.get('comment', '')
                    })
        
        return iocs
    
    def export_suricata_format(self, events):
        """Export IOCs as Suricata rules"""
        rules = []
        rule_id = 1000000
        
        for event in events:
            for attribute in event.get('Attribute', []):
                if attribute.get('to_ids'):
                    if attribute['type'] == 'ip-dst':
                        rule = f'alert tcp any any -> {attribute["value"]} any (msg:"MISP IOC: {attribute.get("comment", "Malicious IP")}"; sid:{rule_id}; rev:1;)'
                        rules.append(rule)
                        rule_id += 1
                    elif attribute['type'] == 'domain':
                        rule = f'alert dns any any -> any any (msg:"MISP IOC: {attribute.get("comment", "Malicious Domain")}"; dns_query; content:"{attribute["value"]}"; sid:{rule_id}; rev:1;)'
                        rules.append(rule)
                        rule_id += 1
        
        return rules
    
    def export_velociraptor_format(self, events):
        """Export IOCs for Velociraptor hunting"""
        hunt_artifacts = {
            'file_hashes': [],
            'ip_addresses': [],
            'domains': [],
            'filenames': []
        }
        
        for event in events:
            for attribute in event.get('Attribute', []):
                if attribute.get('to_ids'):
                    if attribute['type'] in ['md5', 'sha1', 'sha256']:
                        hunt_artifacts['file_hashes'].append({
                            'hash': attribute['value'],
                            'type': attribute['type'],
                            'description': attribute.get('comment', '')
                        })
                    elif attribute['type'] in ['ip-src', 'ip-dst']:
                        hunt_artifacts['ip_addresses'].append({
                            'ip': attribute['value'],
                            'description': attribute.get('comment', '')
                        })
                    elif attribute['type'] == 'domain':
                        hunt_artifacts['domains'].append({
                            'domain': attribute['value'],
                            'description': attribute.get('comment', '')
                        })
                    elif attribute['type'] == 'filename':
                        hunt_artifacts['filenames'].append({
                            'filename': attribute['value'],
                            'description': attribute.get('comment', '')
                        })
        
        return hunt_artifacts

## 🔗 Integration with GOAD-Blue Components

### **Splunk Integration**

```python
# Splunk HEC integration for MISP IOCs
import requests
import json
from datetime import datetime

class MISPSplunkIntegration:
    def __init__(self, splunk_url, hec_token):
        self.splunk_url = splunk_url
        self.hec_token = hec_token
        self.headers = {
            'Authorization': f'Splunk {hec_token}',
            'Content-Type': 'application/json'
        }

    def send_iocs_to_splunk(self, iocs):
        """Send MISP IOCs to Splunk as lookup table"""

        # Create lookup table entries
        lookup_entries = []
        for ioc in iocs:
            lookup_entries.append({
                'indicator': ioc['indicator'],
                'type': ioc['type'],
                'threat_level': ioc['threat_level'],
                'first_seen': ioc['first_seen'],
                'source': 'MISP',
                'description': ioc['description']
            })

        # Send to Splunk HEC
        event = {
            'time': int(datetime.now().timestamp()),
            'event': {
                'sourcetype': 'misp:ioc',
                'index': 'threat_intel',
                'data': lookup_entries
            }
        }

        response = requests.post(
            f'{self.splunk_url}/services/collector/event',
            headers=self.headers,
            json=event
        )

        return response.status_code == 200

    def create_splunk_searches(self):
        """Create Splunk saved searches for MISP IOC matching"""

        searches = [
            {
                'name': 'MISP IOC Match - File Hashes',
                'search': '''
                index=goad_blue_*
                | eval file_hash=coalesce(md5, sha1, sha256)
                | lookup misp_iocs.csv indicator as file_hash OUTPUT type, threat_level, description
                | where isnotnull(type)
                | table _time, host, file_hash, type, threat_level, description
                ''',
                'cron_schedule': '*/15 * * * *'
            },
            {
                'name': 'MISP IOC Match - IP Addresses',
                'search': '''
                index=goad_blue_*
                | eval ip_addr=coalesce(src_ip, dest_ip, clientip)
                | lookup misp_iocs.csv indicator as ip_addr OUTPUT type, threat_level, description
                | where isnotnull(type)
                | table _time, host, ip_addr, type, threat_level, description
                ''',
                'cron_schedule': '*/15 * * * *'
            }
        ]

        return searches
```

### **Velociraptor Integration**

```yaml
# Velociraptor artifact for MISP IOC hunting
name: Windows.MISP.IOCHunt
description: Hunt for MISP IOCs across GOAD environment

parameters:
  - name: MISPServer
    default: "https://misp.goad-blue.local"

  - name: APIKey
    description: MISP API key for IOC retrieval

sources:
  - query: |
      -- Retrieve IOCs from MISP
      LET misp_iocs = SELECT * FROM http_client(
        url=MISPServer + "/attributes/restSearch",
        headers=dict(`Authorization`="Bearer " + APIKey,
                    `Content-Type`="application/json"),
        data=serialize(format="json",
                      item=dict(`returnFormat`="json",
                               `to_ids`=1,
                               `published`=1,
                               `timestamp`="7d"))
      )

      -- Extract file hashes
      LET file_hashes = SELECT value as hash, type
      FROM foreach(row=misp_iocs.Content.response.Attribute)
      WHERE type IN ("md5", "sha1", "sha256")

      -- Extract IP addresses
      LET ip_addresses = SELECT value as ip, type
      FROM foreach(row=misp_iocs.Content.response.Attribute)
      WHERE type IN ("ip-src", "ip-dst")

      -- Hunt for file hashes
      LET hash_matches = SELECT
        FullPath,
        Size,
        Mtime,
        hash.MD5 as MD5,
        hash.SHA1 as SHA1,
        hash.SHA256 as SHA256,
        "file_hash_match" as match_type
      FROM glob(globs="C:\\**", accessor="file")
      WHERE hash.MD5 IN file_hashes.hash
         OR hash.SHA1 IN file_hashes.hash
         OR hash.SHA256 IN file_hashes.hash

      -- Hunt for network connections
      LET network_matches = SELECT
        Pid,
        Name,
        Laddr,
        Raddr,
        Status,
        "network_connection_match" as match_type
      FROM netstat()
      WHERE Raddr.IP IN ip_addresses.ip

      -- Combine results
      SELECT * FROM chain(
        a=hash_matches,
        b=network_matches
      )
```

### **Security Onion Integration**

```bash
#!/bin/bash
# Script to integrate MISP IOCs with Security Onion

MISP_URL="https://misp.goad-blue.local"
MISP_API_KEY="YOUR_API_KEY"
SO_INTEL_DIR="/opt/so/conf/intel"

# Function to fetch IOCs from MISP
fetch_misp_iocs() {
    echo "Fetching IOCs from MISP..."

    # Fetch IP addresses
    curl -s -H "Authorization: Bearer $MISP_API_KEY" \
         -H "Content-Type: application/json" \
         -d '{"returnFormat":"json","to_ids":1,"published":1,"type":["ip-src","ip-dst"],"timestamp":"7d"}' \
         "$MISP_URL/attributes/restSearch" | \
    jq -r '.response.Attribute[] | .value' > "$SO_INTEL_DIR/misp-ips.txt"

    # Fetch domains
    curl -s -H "Authorization: Bearer $MISP_API_KEY" \
         -H "Content-Type: application/json" \
         -d '{"returnFormat":"json","to_ids":1,"published":1,"type":"domain","timestamp":"7d"}' \
         "$MISP_URL/attributes/restSearch" | \
    jq -r '.response.Attribute[] | .value' > "$SO_INTEL_DIR/misp-domains.txt"

    # Fetch file hashes
    curl -s -H "Authorization: Bearer $MISP_API_KEY" \
         -H "Content-Type: application/json" \
         -d '{"returnFormat":"json","to_ids":1,"published":1,"type":["md5","sha1","sha256"],"timestamp":"7d"}' \
         "$MISP_URL/attributes/restSearch" | \
    jq -r '.response.Attribute[] | .value' > "$SO_INTEL_DIR/misp-hashes.txt"
}

# Function to create Zeek intelligence files
create_zeek_intel() {
    echo "Creating Zeek intelligence files..."

    # Create IP intelligence file
    echo "#fields	indicator	indicator_type	meta.source	meta.desc" > "$SO_INTEL_DIR/misp-intel.txt"

    # Add IP addresses
    while read -r ip; do
        echo -e "$ip\tIntel::ADDR\tMISP\tMalicious IP from MISP" >> "$SO_INTEL_DIR/misp-intel.txt"
    done < "$SO_INTEL_DIR/misp-ips.txt"

    # Add domains
    while read -r domain; do
        echo -e "$domain\tIntel::DOMAIN\tMISP\tMalicious domain from MISP" >> "$SO_INTEL_DIR/misp-intel.txt"
    done < "$SO_INTEL_DIR/misp-domains.txt"

    # Restart Zeek to load new intelligence
    sudo so-zeek-restart
}

# Function to create Suricata rules
create_suricata_rules() {
    echo "Creating Suricata rules from MISP IOCs..."

    RULE_FILE="$SO_INTEL_DIR/misp-rules.rules"
    SID=1000000

    echo "# MISP IOC Rules - Generated $(date)" > "$RULE_FILE"

    # Create IP-based rules
    while read -r ip; do
        echo "alert tcp any any -> $ip any (msg:\"MISP IOC: Malicious IP $ip\"; sid:$SID; rev:1;)" >> "$RULE_FILE"
        ((SID++))
    done < "$SO_INTEL_DIR/misp-ips.txt"

    # Create domain-based rules
    while read -r domain; do
        echo "alert dns any any -> any any (msg:\"MISP IOC: Malicious domain $domain\"; dns_query; content:\"$domain\"; sid:$SID; rev:1;)" >> "$RULE_FILE"
        ((SID++))
    done < "$SO_INTEL_DIR/misp-domains.txt"

    # Restart Suricata to load new rules
    sudo so-suricata-restart
}

# Main execution
main() {
    fetch_misp_iocs
    create_zeek_intel
    create_suricata_rules

    echo "MISP IOC integration completed successfully"
}

# Run if executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
```

## 📊 Intelligence Analysis and Reporting

### **Automated Threat Reports**

```python
# Automated threat intelligence reporting
from pymisp import PyMISP
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import pandas as pd
from jinja2 import Template

class MISPReportGenerator:
    def __init__(self, misp_url, misp_key):
        self.misp = PyMISP(misp_url, misp_key)

    def generate_weekly_report(self):
        """Generate weekly threat intelligence report"""

        # Get events from last week
        start_date = datetime.now() - timedelta(days=7)
        events = self.misp.search(
            timestamp=start_date.strftime('%Y-%m-%d'),
            published=True
        )

        # Analyze events
        analysis = self.analyze_events(events)

        # Generate visualizations
        charts = self.create_visualizations(analysis)

        # Generate report
        report = self.create_report(analysis, charts)

        return report

    def analyze_events(self, events):
        """Analyze MISP events for reporting"""
        analysis = {
            'total_events': len(events),
            'threat_levels': {},
            'attack_techniques': {},
            'affected_components': {},
            'ioc_types': {},
            'timeline': []
        }

        for event in events:
            # Threat level analysis
            threat_level = event.get('threat_level_id', '4')
            analysis['threat_levels'][threat_level] = analysis['threat_levels'].get(threat_level, 0) + 1

            # Timeline data
            analysis['timeline'].append({
                'date': event.get('date'),
                'info': event.get('info'),
                'threat_level': threat_level
            })

            # Analyze tags for attack techniques
            for tag in event.get('Tag', []):
                tag_name = tag.get('name', '')
                if 'goad-blue:attack-technique' in tag_name:
                    technique = tag_name.split('=')[1].strip('"')
                    analysis['attack_techniques'][technique] = analysis['attack_techniques'].get(technique, 0) + 1
                elif 'goad-blue:environment-component' in tag_name:
                    component = tag_name.split('=')[1].strip('"')
                    analysis['affected_components'][component] = analysis['affected_components'].get(component, 0) + 1

            # Analyze IOC types
            for attribute in event.get('Attribute', []):
                ioc_type = attribute.get('type')
                if ioc_type:
                    analysis['ioc_types'][ioc_type] = analysis['ioc_types'].get(ioc_type, 0) + 1

        return analysis

    def create_visualizations(self, analysis):
        """Create charts for the report"""
        charts = {}

        # Threat level distribution
        if analysis['threat_levels']:
            plt.figure(figsize=(8, 6))
            threat_labels = {'1': 'High', '2': 'Medium', '3': 'Low', '4': 'Undefined'}
            labels = [threat_labels.get(k, k) for k in analysis['threat_levels'].keys()]
            plt.pie(analysis['threat_levels'].values(), labels=labels, autopct='%1.1f%%')
            plt.title('Threat Level Distribution')
            plt.savefig('threat_levels.png')
            charts['threat_levels'] = 'threat_levels.png'

        # Attack techniques
        if analysis['attack_techniques']:
            plt.figure(figsize=(10, 6))
            techniques = list(analysis['attack_techniques'].keys())
            counts = list(analysis['attack_techniques'].values())
            plt.bar(techniques, counts)
            plt.title('Attack Techniques Observed')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig('attack_techniques.png')
            charts['attack_techniques'] = 'attack_techniques.png'

        return charts

    def create_report(self, analysis, charts):
        """Create HTML report"""

        template_str = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>GOAD-Blue Weekly Threat Intelligence Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .header { background-color: #d32f2f; color: white; padding: 20px; text-align: center; }
                .section { margin: 20px 0; }
                .metric { display: inline-block; margin: 10px; padding: 15px; background-color: #f5f5f5; border-radius: 5px; }
                .chart { text-align: center; margin: 20px 0; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>GOAD-Blue Weekly Threat Intelligence Report</h1>
                <p>Report Period: {{ report_period }}</p>
            </div>

            <div class="section">
                <h2>Executive Summary</h2>
                <div class="metric">
                    <h3>{{ total_events }}</h3>
                    <p>Total Events</p>
                </div>
                <div class="metric">
                    <h3>{{ high_threat_events }}</h3>
                    <p>High Threat Events</p>
                </div>
                <div class="metric">
                    <h3>{{ unique_techniques }}</h3>
                    <p>Unique Attack Techniques</p>
                </div>
            </div>

            <div class="section">
                <h2>Threat Analysis</h2>
                {% if charts.threat_levels %}
                <div class="chart">
                    <img src="{{ charts.threat_levels }}" alt="Threat Level Distribution">
                </div>
                {% endif %}

                {% if charts.attack_techniques %}
                <div class="chart">
                    <img src="{{ charts.attack_techniques }}" alt="Attack Techniques">
                </div>
                {% endif %}
            </div>

            <div class="section">
                <h2>Recent Events</h2>
                <table>
                    <tr>
                        <th>Date</th>
                        <th>Event</th>
                        <th>Threat Level</th>
                    </tr>
                    {% for event in recent_events %}
                    <tr>
                        <td>{{ event.date }}</td>
                        <td>{{ event.info }}</td>
                        <td>{{ event.threat_level }}</td>
                    </tr>
                    {% endfor %}
                </table>
            </div>

            <div class="section">
                <h2>Recommendations</h2>
                <ul>
                    {% for recommendation in recommendations %}
                    <li>{{ recommendation }}</li>
                    {% endfor %}
                </ul>
            </div>
        </body>
        </html>
        """

        template = Template(template_str)

        # Prepare template data
        template_data = {
            'report_period': f"{(datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')} to {datetime.now().strftime('%Y-%m-%d')}",
            'total_events': analysis['total_events'],
            'high_threat_events': analysis['threat_levels'].get('1', 0),
            'unique_techniques': len(analysis['attack_techniques']),
            'charts': charts,
            'recent_events': analysis['timeline'][-10:],  # Last 10 events
            'recommendations': self.generate_recommendations(analysis)
        }

        return template.render(**template_data)

    def generate_recommendations(self, analysis):
        """Generate security recommendations based on analysis"""
        recommendations = []

        # High threat level events
        if analysis['threat_levels'].get('1', 0) > 0:
            recommendations.append("Investigate high-severity threats immediately and implement additional monitoring")

        # Common attack techniques
        top_technique = max(analysis['attack_techniques'].items(), key=lambda x: x[1]) if analysis['attack_techniques'] else None
        if top_technique:
            recommendations.append(f"Focus defensive measures on {top_technique[0]} attacks, which were most common this week")

        # IOC deployment
        if analysis['ioc_types']:
            recommendations.append("Deploy identified IOCs to detection systems and update hunting rules")

        # Training recommendations
        if 'credential-dumping' in analysis['attack_techniques']:
            recommendations.append("Conduct additional training on credential protection and detection")

        return recommendations

## 🎓 Training and Education

### **MISP Training Scenarios**

1. **Threat Intelligence Fundamentals**
   - IOC identification and classification
   - Event creation and attribute management
   - Taxonomy and tagging best practices
   - Data quality and validation

2. **Advanced Intelligence Analysis**
   - Correlation and relationship mapping
   - Attribution analysis techniques
   - Threat actor profiling
   - Campaign tracking and analysis

3. **Integration and Automation**
   - API usage and automation
   - Feed management and synchronization
   - Custom tool integration
   - Workflow optimization

### **GOAD-Blue Specific Use Cases**

```python
# Training scenario: Creating MISP event from GOAD incident
def training_create_misp_event():
    """Training exercise: Create MISP event from GOAD attack scenario"""

    # Scenario: Mimikatz credential dumping attack
    scenario_data = {
        'attack_type': 'credential_dumping',
        'tools_used': ['mimikatz.exe', 'procdump.exe'],
        'target_systems': ['GOAD-DC01', 'GOAD-SRV01'],
        'iocs': [
            {'type': 'filename', 'value': 'mimikatz.exe'},
            {'type': 'sha256', 'value': 'b0e914d1bbe19433cc9df64ea1ca07fe77f7b150b511b786e40c0e4b89b3b7f5'},
            {'type': 'ip-dst', 'value': '*************'},
            {'type': 'registry-key', 'value': 'HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run\\SecurityUpdate'}
        ],
        'ttps': ['T1003.001', 'T1055', 'T1547.001']  # MITRE ATT&CK techniques
    }

    # Exercise: Students create MISP event with proper:
    # - Event classification and threat level
    # - Attribute creation with correct types
    # - Tag application (taxonomies)
    # - Relationship mapping
    # - Sharing group assignment

    return scenario_data

def training_ioc_enrichment():
    """Training exercise: Enrich IOCs with external intelligence"""

    # Exercise steps:
    # 1. Take basic IOC (IP address, hash, domain)
    # 2. Research using external sources (VirusTotal, etc.)
    # 3. Add context and attribution information
    # 4. Create relationships with other IOCs
    # 5. Apply appropriate tags and classifications

    pass

def training_feed_management():
    """Training exercise: Set up and manage threat intelligence feeds"""

    # Exercise components:
    # 1. Configure external threat feeds
    # 2. Set up feed synchronization schedules
    # 3. Implement feed validation and filtering
    # 4. Monitor feed quality and performance
    # 5. Troubleshoot feed issues

    pass
```

## 🔧 Administration and Maintenance

### **System Maintenance**

```bash
#!/bin/bash
# MISP maintenance script for GOAD-Blue

MISP_DIR="/var/www/html/MISP"
BACKUP_DIR="/backup/misp"
LOG_FILE="/var/log/misp-maintenance.log"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Database backup
backup_database() {
    log_message "Starting database backup..."

    mkdir -p "$BACKUP_DIR/$(date +%Y%m%d)"

    mysqldump -u misp -p misp > "$BACKUP_DIR/$(date +%Y%m%d)/misp_db_$(date +%H%M%S).sql"

    if [ $? -eq 0 ]; then
        log_message "Database backup completed successfully"
    else
        log_message "Database backup failed"
        exit 1
    fi
}

# Update MISP
update_misp() {
    log_message "Starting MISP update..."

    cd "$MISP_DIR"

    # Backup current version
    cp -r app/Config "$BACKUP_DIR/$(date +%Y%m%d)/config_backup"

    # Pull latest changes
    git pull origin 2.4
    git submodule update --init --recursive

    # Update dependencies
    sudo -u www-data php composer.phar update

    # Run database updates
    sudo -u www-data cake Admin updateDatabase

    # Clear cache
    sudo -u www-data cake Admin clearCache

    log_message "MISP update completed"
}

# Clean old data
cleanup_old_data() {
    log_message "Starting data cleanup..."

    # Remove old logs
    find /var/log/misp/ -name "*.log" -mtime +30 -delete

    # Remove old backups
    find "$BACKUP_DIR" -type d -mtime +90 -exec rm -rf {} +

    # Clean temporary files
    find "$MISP_DIR/app/tmp" -type f -mtime +7 -delete

    log_message "Data cleanup completed"
}

# Performance optimization
optimize_performance() {
    log_message "Starting performance optimization..."

    # Optimize MySQL tables
    mysql -u misp -p misp -e "OPTIMIZE TABLE attributes, events, objects;"

    # Update statistics
    sudo -u www-data cake Admin updateDatabase

    # Rebuild correlation tables
    sudo -u www-data cake Admin updateCorrelations

    log_message "Performance optimization completed"
}

# Health check
health_check() {
    log_message "Starting health check..."

    # Check MISP status
    if ! curl -s -k https://localhost/MISP/users/login > /dev/null; then
        log_message "ERROR: MISP web interface not responding"
        exit 1
    fi

    # Check database connectivity
    if ! mysql -u misp -p misp -e "SELECT 1;" > /dev/null 2>&1; then
        log_message "ERROR: Database connection failed"
        exit 1
    fi

    # Check Redis connectivity
    if ! redis-cli ping > /dev/null 2>&1; then
        log_message "ERROR: Redis connection failed"
        exit 1
    fi

    # Check disk space
    DISK_USAGE=$(df /var/www/html | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -gt 80 ]; then
        log_message "WARNING: Disk usage is ${DISK_USAGE}%"
    fi

    log_message "Health check completed successfully"
}

# Main execution
case "$1" in
    backup)
        backup_database
        ;;
    update)
        backup_database
        update_misp
        ;;
    cleanup)
        cleanup_old_data
        ;;
    optimize)
        optimize_performance
        ;;
    health)
        health_check
        ;;
    full)
        backup_database
        cleanup_old_data
        optimize_performance
        health_check
        ;;
    *)
        echo "Usage: $0 {backup|update|cleanup|optimize|health|full}"
        exit 1
        ;;
esac
```

### **Monitoring and Alerting**

```python
# MISP monitoring script
import requests
import json
import smtplib
from email.mime.text import MIMEText
from datetime import datetime, timedelta
import mysql.connector
import redis

class MISPMonitor:
    def __init__(self, config):
        self.config = config
        self.alerts = []

    def check_web_interface(self):
        """Check if MISP web interface is responding"""
        try:
            response = requests.get(
                f"{self.config['misp_url']}/users/login",
                timeout=10,
                verify=False
            )
            if response.status_code != 200:
                self.alerts.append("MISP web interface not responding properly")
        except Exception as e:
            self.alerts.append(f"MISP web interface error: {str(e)}")

    def check_database(self):
        """Check database connectivity and performance"""
        try:
            conn = mysql.connector.connect(
                host=self.config['db_host'],
                user=self.config['db_user'],
                password=self.config['db_password'],
                database=self.config['db_name']
            )

            cursor = conn.cursor()

            # Check basic connectivity
            cursor.execute("SELECT 1")

            # Check recent activity
            cursor.execute("""
                SELECT COUNT(*) FROM events
                WHERE timestamp > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 24 HOUR))
            """)
            recent_events = cursor.fetchone()[0]

            if recent_events == 0:
                self.alerts.append("No new events in the last 24 hours")

            # Check database size
            cursor.execute("""
                SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB'
                FROM information_schema.tables
                WHERE table_schema = %s
            """, (self.config['db_name'],))

            db_size = cursor.fetchone()[0]
            if db_size > 10000:  # 10GB
                self.alerts.append(f"Database size is large: {db_size} MB")

            conn.close()

        except Exception as e:
            self.alerts.append(f"Database error: {str(e)}")

    def check_redis(self):
        """Check Redis connectivity"""
        try:
            r = redis.Redis(
                host=self.config['redis_host'],
                port=self.config['redis_port'],
                db=self.config['redis_db']
            )
            r.ping()
        except Exception as e:
            self.alerts.append(f"Redis error: {str(e)}")

    def check_feeds(self):
        """Check threat intelligence feed status"""
        try:
            # Use MISP API to check feed status
            headers = {
                'Authorization': self.config['api_key'],
                'Content-Type': 'application/json'
            }

            response = requests.get(
                f"{self.config['misp_url']}/feeds",
                headers=headers,
                verify=False
            )

            if response.status_code == 200:
                feeds = response.json()

                for feed in feeds:
                    if feed.get('enabled') and not feed.get('caching_enabled'):
                        self.alerts.append(f"Feed '{feed.get('name')}' is enabled but not caching")

                    # Check last fetch time
                    last_fetch = feed.get('source_format', {}).get('last_fetch')
                    if last_fetch:
                        last_fetch_time = datetime.fromtimestamp(int(last_fetch))
                        if datetime.now() - last_fetch_time > timedelta(days=2):
                            self.alerts.append(f"Feed '{feed.get('name')}' hasn't been updated in over 2 days")

        except Exception as e:
            self.alerts.append(f"Feed check error: {str(e)}")

    def send_alerts(self):
        """Send alert notifications"""
        if not self.alerts:
            return

        # Email notification
        msg = MIMEText(f"""
        MISP Health Check Alert - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

        The following issues were detected:

        {chr(10).join(f"- {alert}" for alert in self.alerts)}

        Please investigate and resolve these issues.
        """)

        msg['Subject'] = 'MISP Health Check Alert'
        msg['From'] = self.config['alert_from']
        msg['To'] = self.config['alert_to']

        try:
            smtp = smtplib.SMTP(self.config['smtp_server'])
            smtp.send_message(msg)
            smtp.quit()
        except Exception as e:
            print(f"Failed to send email alert: {e}")

    def run_checks(self):
        """Run all health checks"""
        self.check_web_interface()
        self.check_database()
        self.check_redis()
        self.check_feeds()

        if self.alerts:
            self.send_alerts()
            return False
        else:
            print("All MISP health checks passed")
            return True

# Configuration
config = {
    'misp_url': 'https://misp.goad-blue.local',
    'api_key': 'YOUR_API_KEY',
    'db_host': 'localhost',
    'db_user': 'misp',
    'db_password': 'secure_password',
    'db_name': 'misp',
    'redis_host': 'localhost',
    'redis_port': 6379,
    'redis_db': 13,
    'smtp_server': 'smtp.company.com',
    'alert_from': '<EMAIL>',
    'alert_to': '<EMAIL>'
}

# Run monitoring
if __name__ == "__main__":
    monitor = MISPMonitor(config)
    monitor.run_checks()
```

---

!!! tip "MISP Best Practices"
    - Regularly update MISP and its dependencies
    - Implement proper backup and recovery procedures
    - Use taxonomies and tags consistently for better organization
    - Set up automated feed synchronization for timely intelligence
    - Monitor system performance and optimize database regularly

!!! warning "Security Considerations"
    - Secure API keys and use proper authentication
    - Implement network segmentation for MISP server
    - Regular security updates and vulnerability assessments
    - Proper access controls and user management
    - Encrypt sensitive data and communications

!!! info "Learning Resources"
    - MISP official documentation and training materials
    - Threat intelligence best practices and frameworks
    - GOAD-Blue specific use cases and scenarios
    - Community sharing groups and collaboration opportunities
```
```
