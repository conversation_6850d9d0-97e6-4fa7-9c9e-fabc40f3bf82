<div align="center">
  <h1><img alt="GOAD-Blue (Game Of Active Directory - Blue Team Edition)" src="./docs/mkdocs/docs/img/logo_GOAD3.png"></h1>
  <br>
</div>

**GOAD-Blue (v3.1)**

:bookmark: Documentation : [https://orange-cyberdefense.github.io/GOAD/](https://orange-cyberdefense.github.io/GOAD/)

## Description
GOAD-Blue is a comprehensive cybersecurity training and testing platform that extends the original GOAD (Game of Active Directory) project. While GOAD focuses on red team Active Directory attack simulation, GOAD-Blue integrates defensive capabilities to create a full-spectrum environment for both offensive (red team) and defensive (blue team) operations.

The platform provides:
- **Original GOAD Environment**: Vulnerable Active Directory lab for penetration testing
- **Blue Team Stack**: Integrated SIEM, network monitoring, endpoint visibility, threat intelligence, and malware analysis
- **Modular Architecture**: User-selectable components via interactive CLI
- **Multi-Platform Support**: AWS, Azure, VMware ESXi, Proxmox, VirtualBox

> [!CAUTION]
> This lab is extremely vulnerable, do not reuse recipe to build your environment and do not deploy this environment on internet without isolation (this is a recommendation, use it as your own risk).<br>
> This repository was build for pentest practice.

![goad_screenshot](./docs/img/goad_screenshot.png)

## Licenses
This lab use free Windows VM only (180 days). After that delay enter a license on each server or rebuild all the lab (may be it's time for an update ;))

## Available labs

### Red Team Labs (Original GOAD)

- GOAD Lab family and extensions overview
<div align="center">
<img alt="GOAD" width="800" src="./docs/img/diagram-GOADv3-full.png">
</div>

- [GOAD](https://orange-cyberdefense.github.io/GOAD/labs/GOAD/) : 5 vms, 2 forests, 3 domains (full goad lab)
<div align="center">
<img alt="GOAD" width="800" src="./docs/img/GOAD_schema.png">
</div>

- [GOAD-Light](https://orange-cyberdefense.github.io/GOAD/labs/GOAD-Light/) : 3 vms, 1 forest, 2 domains (smaller goad lab for those with a smaller pc)
<div align="center">
<img alt="GOAD Light" width="600" src="./docs/img/GOAD-Light_schema.png">
</div>

- [MINILAB](https://orange-cyberdefense.github.io/GOAD/labs/MINILAB/): 2 vms, 1 forest, 1 domain (basic lab with one DC (windows server 2019) and one Workstation (windows 10))

- [SCCM](https://orange-cyberdefense.github.io/GOAD/labs/SCCM/) : 4 vms, 1 forest, 1 domain, with microsoft configuration manager installed
<div align="center">
<img alt="SCCM" width="600" src="./docs/img/SCCMLAB_overview.png">
</div>

- [NHA](https://orange-cyberdefense.github.io/GOAD/labs/NHA/) : A challenge with 5 vms and 2 domains. no schema provided, you will have to find out how break it.

### Blue Team Components (GOAD-Blue)

GOAD-Blue extends the original labs with defensive capabilities:

#### 🔎 Centralized SIEM (Select One)
- **Splunk Enterprise**: Complete SIEM with Security Essentials, custom TAs, and CIM normalization
- **Elastic Stack (ELK)**: Elasticsearch + Logstash + Kibana with ECS mapping

#### 🔐 Network & Host Monitoring
- **Security Onion**: Suricata, Zeek, Wazuh for network and host intrusion detection
- **Malcolm**: Network forensics platform (PCAP, Zeek, Suricata, Arkime, OpenSearch)
- **Velociraptor**: Endpoint visibility and DFIR capabilities

#### 🧠 Threat Intelligence
- **MISP**: Threat intelligence sharing platform with IOC correlation

#### 🧪 Malware Analysis
- **FLARE-VM**: Windows 10 analysis environment with comprehensive toolset
