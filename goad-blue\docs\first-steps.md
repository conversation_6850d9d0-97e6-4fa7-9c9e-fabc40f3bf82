# First Steps with GOAD-<PERSON>

Welcome to GOAD-<PERSON>! This guide will help you get started with your blue team cybersecurity lab in just a few steps.

## 🎯 What is GOAD-Blue?

GOAD-Blue is a comprehensive blue team training platform that integrates with the popular GOAD (Game of Active Directory) red team lab. It provides enterprise-grade security monitoring, detection, and response capabilities for hands-on cybersecurity training.

```mermaid
graph LR
    subgraph "🎮 GOAD (Red Team)"
        GOAD_AD[Active Directory<br/>Domain Controllers]
        GOAD_USERS[Domain Users<br/>Workstations]
        GOAD_SERVERS[Member Servers<br/>File Shares]
    end
    
    subgraph "🛡️ GOAD-Blue (Blue Team)"
        SPLUNK[📊 Splunk<br/>SIEM Platform]
        SO[🧅 Security Onion<br/>Network Monitoring]
        VELO[🦖 Velociraptor<br/>Endpoint Detection]
        MISP[🧠 MISP<br/>Threat Intelligence]
    end
    
    GOAD_AD --> SPLUNK
    GOAD_USERS --> VELO
    GOAD_SERVERS --> SO
    SO --> MISP
    
    classDef red fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef blue fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    
    class GOAD_AD,GOAD_USERS,GOAD_SERVERS red
    class SPLUNK,SO,VELO,MISP blue
```

## 🚀 Quick Start (5 Minutes)

### **Option 1: Cloud Deployment (Recommended for Beginners)**

```bash
# 1. Clone the repository
git clone https://github.com/your-org/goad-blue.git
cd goad-blue

# 2. Choose your cloud provider
cd terraform/providers/aws  # or azure, gcp

# 3. Configure your deployment
cp terraform.tfvars.example terraform.tfvars
nano terraform.tfvars  # Add your credentials and preferences

# 4. Deploy infrastructure
terraform init
terraform apply

# 5. Configure services
cd ../../../ansible
ansible-playbook -i inventory/aws_ec2.yml playbooks/aws/site.yml
```

### **Option 2: Local Deployment (VirtualBox)**

```bash
# 1. Install prerequisites
# - VirtualBox 7.0+
# - Vagrant 2.3+
# - 32GB+ RAM, 500GB+ storage

# 2. Clone and deploy
git clone https://github.com/your-org/goad-blue.git
cd goad-blue

# 3. Start deployment
vagrant up

# 4. Access services (after ~30 minutes)
# Splunk: https://**************:8000
# Security Onion: https://**************
# Velociraptor: https://**************:8889
```

## 📋 Prerequisites Checklist

### **System Requirements**

| Component | Minimum | Recommended |
|-----------|---------|-------------|
| **CPU** | 8 cores | 16+ cores |
| **RAM** | 32GB | 64GB+ |
| **Storage** | 500GB | 1TB+ SSD |
| **Network** | 100 Mbps | 1 Gbps |

### **Software Requirements**

- [ ] **Virtualization Platform**: VirtualBox, VMware, Proxmox, or Cloud Account
- [ ] **Infrastructure as Code**: Terraform 1.0+
- [ ] **Configuration Management**: Ansible 4.0+
- [ ] **Version Control**: Git
- [ ] **SSH Client**: OpenSSH or PuTTY

### **Knowledge Prerequisites**

- [ ] **Basic Linux Administration**: Command line, file systems, networking
- [ ] **Virtualization Concepts**: VMs, networks, storage
- [ ] **Security Fundamentals**: Logs, SIEM, network monitoring
- [ ] **Optional**: GOAD familiarity (helpful but not required)

## 🎯 Choose Your Deployment Path

### **🏠 Home Lab (Personal Learning)**

**Best For**: Individual learning, skill development, certification prep

**Recommended Setup**:
- **Platform**: VirtualBox or Proxmox
- **Resources**: 32GB RAM, 500GB storage
- **Components**: Core services only
- **Cost**: Free (open-source tools)

```bash
# VirtualBox deployment
cd goad-blue
vagrant up --provider=virtualbox

# Proxmox deployment
cd terraform/providers/proxmox
terraform apply -var-file="homelab.tfvars"
```

### **🏢 Enterprise Lab (Team Training)**

**Best For**: Corporate training, team exercises, advanced scenarios

**Recommended Setup**:
- **Platform**: VMware vSphere or Cloud (AWS/Azure)
- **Resources**: 64GB+ RAM, 1TB+ storage
- **Components**: Full deployment with HA
- **Cost**: Platform licensing + infrastructure

```bash
# VMware vSphere deployment
cd terraform/providers/vmware
terraform apply -var-file="enterprise.tfvars"

# AWS deployment
cd terraform/providers/aws
terraform apply -var-file="production.tfvars"
```

### **☁️ Cloud Lab (Scalable Training)**

**Best For**: Distributed teams, on-demand training, scalable exercises

**Recommended Setup**:
- **Platform**: AWS, Azure, or GCP
- **Resources**: Auto-scaling based on demand
- **Components**: Full deployment with monitoring
- **Cost**: Pay-per-use cloud resources

```bash
# AWS auto-scaling deployment
cd terraform/providers/aws
terraform apply -var-file="autoscaling.tfvars"
```

## 🛠️ Step-by-Step Setup

### **Step 1: Environment Preparation**

#### **Install Required Tools**

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y git curl wget unzip

# Install Terraform
wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
unzip terraform_1.6.0_linux_amd64.zip
sudo mv terraform /usr/local/bin/

# Install Ansible
sudo apt install -y python3-pip
pip3 install ansible

# Verify installations
terraform --version
ansible --version
```

#### **Clone Repository**

```bash
git clone https://github.com/your-org/goad-blue.git
cd goad-blue

# Explore the structure
ls -la
tree -L 2  # Optional: shows directory structure
```

### **Step 2: Platform Selection**

#### **Choose Your Platform**

```bash
# List available providers
ls terraform/providers/

# Available options:
# - aws/          # Amazon Web Services
# - azure/        # Microsoft Azure  
# - gcp/          # Google Cloud Platform
# - vmware/       # VMware vSphere
# - proxmox/      # Proxmox VE
# - virtualbox/   # VirtualBox (via Vagrant)
```

#### **Platform-Specific Setup**

=== "VirtualBox (Easiest)"
    ```bash
    # Install VirtualBox
    sudo apt install virtualbox virtualbox-ext-pack
    
    # Install Vagrant
    wget https://releases.hashicorp.com/vagrant/2.4.0/vagrant_2.4.0_linux_amd64.zip
    unzip vagrant_2.4.0_linux_amd64.zip
    sudo mv vagrant /usr/local/bin/
    
    # Deploy
    vagrant up
    ```

=== "AWS (Scalable)"
    ```bash
    # Install AWS CLI
    curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
    unzip awscliv2.zip
    sudo ./aws/install
    
    # Configure credentials
    aws configure
    
    # Deploy
    cd terraform/providers/aws
    terraform init && terraform apply
    ```

=== "Proxmox (Cost-Effective)"
    ```bash
    # Configure Proxmox credentials
    export PROXMOX_API_URL="https://your-proxmox:8006/api2/json"
    export PROXMOX_USER="root@pam"
    export PROXMOX_PASSWORD="your-password"
    
    # Deploy
    cd terraform/providers/proxmox
    terraform init && terraform apply
    ```

### **Step 3: Configuration**

#### **Customize Your Deployment**

```bash
# Copy example configuration
cp terraform.tfvars.example terraform.tfvars

# Edit configuration
nano terraform.tfvars
```

**Key Configuration Options**:

```hcl
# Basic settings
name_prefix = "goad-blue-lab"
environment = "training"

# Component selection
deploy_components = {
  splunk         = true   # SIEM platform
  security_onion = true   # Network monitoring
  velociraptor   = true   # Endpoint detection
  misp          = true   # Threat intelligence
}

# Resource allocation
vm_configs = {
  splunk = {
    cores  = 8
    memory = 16384  # 16GB
  }
  security_onion_manager = {
    cores  = 16
    memory = 32768  # 32GB
  }
}

# Network configuration
network_cidrs = {
  goad_blue = "192.168.100.0/24"
  goad      = "************/24"
}
```

### **Step 4: Deployment**

#### **Infrastructure Deployment**

```bash
# Initialize Terraform
terraform init

# Review deployment plan
terraform plan

# Deploy infrastructure
terraform apply

# Note the outputs
terraform output
```

#### **Service Configuration**

```bash
# Navigate to Ansible directory
cd ../../ansible

# Configure services
ansible-playbook -i inventory/[platform].yml playbooks/[platform]/site.yml

# Verify deployment
ansible all -i inventory/[platform].yml -m ping
```

### **Step 5: Verification**

#### **Access Web Interfaces**

```bash
# Get access URLs
terraform output quick_access_urls

# Example outputs:
# Splunk Web: https://**************:8000
# Security Onion: https://**************
# Velociraptor: https://**************:8889
# MISP: https://**************
```

#### **Test Connectivity**

```bash
# Test SSH access
ssh ubuntu@**************

# Test web interfaces
curl -k https://**************:8000
curl -k https://**************
curl -k https://**************:8889
```

## 🔗 GOAD Integration

### **Existing GOAD Environment**

If you already have GOAD running:

```bash
# Run integration script
./scripts/integrate-goad.sh --domain-password "Password123!"

# Manual integration
python3 goad-blue.py integrate_goad \
  --goad-network "************/24" \
  --domain-admin "sevenkingdoms\\administrator" \
  --domain-password "Password123!"
```

### **New GOAD Deployment**

If you need to deploy GOAD first:

```bash
# Clone GOAD repository
git clone https://github.com/Orange-Cyberdefense/GOAD.git
cd GOAD

# Deploy GOAD (follow GOAD documentation)
# Then return to GOAD-Blue integration
cd ../goad-blue
./scripts/integrate-goad.sh --domain-password "Password123!"
```

## 📚 Next Steps

### **Immediate Actions**

1. **[ ] Access Web Interfaces**: Log into Splunk, Security Onion, Velociraptor
2. **[ ] Change Default Passwords**: Update all default credentials
3. **[ ] Verify Data Flow**: Check that logs are flowing from GOAD to GOAD-Blue
4. **[ ] Run Health Checks**: Execute system health and connectivity tests

### **Learning Path**

1. **Week 1**: Familiarize with each platform's interface
2. **Week 2**: Configure basic detection rules and alerts
3. **Week 3**: Practice incident response scenarios
4. **Week 4**: Advanced threat hunting and analysis

### **Training Scenarios**

- **[Basic Scenarios](scenarios/basic.md)**: Authentication failures, privilege escalation
- **[Intermediate Scenarios](scenarios/intermediate.md)**: Lateral movement, persistence
- **[Advanced Scenarios](scenarios/advanced.md)**: APT simulation, complex attacks

## 🆘 Getting Help

### **Common Issues**

| Issue | Solution |
|-------|----------|
| **VMs won't start** | Check resource allocation and virtualization support |
| **Network connectivity** | Verify bridge/network configuration |
| **Service access** | Check firewall rules and service status |
| **Performance issues** | Increase resource allocation or use SSD storage |

### **Support Resources**

- **📖 Documentation**: Complete guides for each component
- **🐛 GitHub Issues**: Report bugs and request features
- **💬 Community Forum**: Get help from other users
- **📧 Support Email**: Direct support for enterprise users

### **Troubleshooting Commands**

```bash
# Check system status
python3 goad-blue.py --health-check

# Test network connectivity
python3 goad-blue.py test_network

# Validate service configuration
python3 goad-blue.py validate_services

# Generate diagnostic report
python3 goad-blue.py generate_report --output-dir ./diagnostics
```

---

!!! success "Congratulations!"
    You now have a fully functional GOAD-Blue environment! Start with the basic scenarios and gradually work your way up to advanced threat hunting exercises.

!!! tip "Pro Tips"
    - Take snapshots before major configuration changes
    - Document your custom rules and configurations
    - Practice regularly to maintain and improve your skills
    - Join the community to share experiences and learn from others

!!! info "What's Next?"
    - Explore the [User Guide](user-guide/index.md) for detailed usage instructions
    - Try the [Training Scenarios](scenarios/index.md) for hands-on practice
    - Check out [Advanced Configuration](configuration/advanced.md) for customization options
