---
- name: "Read data files"
  hosts: domain:extensions
  connection: local
  vars_files:
    - "{{data_path}}/config.json"
  tasks:
    - name: save the Json data to a Variable as a Fact
      set_fact:
        lab: "{{lab}}"
        cacheable: yes
      run_once: true

    - name: find domain_adapter
      set_fact:
       domain_adapter="{{item.connection_name}}"
      when: item.ipv4.address == hostvars[dict_key].ansible_host
      with_items: "{{ ansible_interfaces }}"
      run_once: true

    - name: find nat_adapter
      set_fact:
       nat_adapter="{{item.connection_name}}"
      when: item.ipv4.address != hostvars[dict_key].ansible_host
      with_items: "{{ ansible_interfaces }}"
      run_once: true

    - name: find number of interfaces
      set_fact:
       number_of_interfaces="{{ansible_interfaces|length}}"
      run_once: true

    - name: find if two adapters
      set_fact:
       two_adapters="{{ 'yes' if number_of_interfaces != '1' else 'no' }}"
      run_once: true

    - name: confirm nat_adapter
      set_fact:
       nat_adapter="{{domain_adapter}}"
      when: not two_adapters
      run_once: true

#    - debug:
#        msg: "domain interface : {{domain_adapter}}"
#    - debug:
#        msg: "number_of_interfaces : {{number_of_interfaces}}"
#    - debug:
#        msg: "two_adapters : {{two_adapters}}"
#    - debug:
#        msg: "nat interface : {{nat_adapter}}"
