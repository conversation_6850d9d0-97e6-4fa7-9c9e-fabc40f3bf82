# Network Configuration

This guide covers comprehensive network configuration for <PERSON>AD-<PERSON>, including network topology design, VLAN setup, traffic mirroring, and security policies.

## 🌐 Network Architecture Overview

GOAD-Blue requires careful network design to ensure proper traffic flow, security isolation, and monitoring capabilities.

```mermaid
graph TB
    subgraph "🌍 External Networks"
        INTERNET[🌐 Internet<br/>Updates & Feeds<br/>Remote Access]
        CORP_NET[🏢 Corporate Network<br/>Management Access<br/>User Connectivity]
    end
    
    subgraph "🔒 GOAD-Blue Management"
        MGMT_NET[⚙️ Management Network<br/>***********/24<br/>Admin Access Only]
        JUMP_HOST[🖥️ Jump Host<br/>Secure Access Point<br/>VPN Endpoint]
    end
    
    subgraph "🛡️ GOAD-Blue Security Stack"
        SIEM_NET[📊 SIEM Network<br/>*************/26<br/>Splunk, Elastic]
        MON_NET[📡 Monitoring Network<br/>**************/26<br/>Security Onion, Sensors]
        ANALYSIS_NET[🔬 Analysis Network<br/>*************28/26<br/>FLARE-VM, <PERSON><PERSON><PERSON>]
        INTEL_NET[🧠 Intelligence Network<br/>*************92/26<br/>MI<PERSON>, <PERSON>eds]
    end
    
    subgraph "🎮 GOAD Environment"
        GOAD_NET[🏰 GOAD Network<br/>************/24<br/>Target Environment]
        GOAD_DC[👑 Domain Controllers<br/>sevenkingdoms.local<br/>north.sevenkingdoms.local]
        GOAD_SERVERS[⚔️ Member Servers<br/>File Servers<br/>Application Servers]
        GOAD_CLIENTS[🖥️ Workstations<br/>User Endpoints<br/>Admin Workstations]
    end
    
    subgraph "🔍 Traffic Mirroring"
        MIRROR_SWITCH[🔄 Mirror Switch<br/>Traffic Replication<br/>Tap Aggregation]
        SPAN_PORTS[📡 SPAN Ports<br/>Port Mirroring<br/>Network Taps]
    end
    
    %% External Connections
    INTERNET --> JUMP_HOST
    CORP_NET --> MGMT_NET
    
    %% Management Connections
    MGMT_NET --> SIEM_NET
    MGMT_NET --> MON_NET
    MGMT_NET --> ANALYSIS_NET
    MGMT_NET --> INTEL_NET
    
    %% GOAD Integration
    GOAD_NET --> MIRROR_SWITCH
    MIRROR_SWITCH --> MON_NET
    SPAN_PORTS --> MON_NET
    
    %% Data Flow
    GOAD_DC --> SIEM_NET
    GOAD_SERVERS --> SIEM_NET
    GOAD_CLIENTS --> SIEM_NET
    
    classDef external fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef management fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef security fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef goad fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef monitoring fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class INTERNET,CORP_NET external
    class MGMT_NET,JUMP_HOST management
    class SIEM_NET,MON_NET,ANALYSIS_NET,INTEL_NET security
    class GOAD_NET,GOAD_DC,GOAD_SERVERS,GOAD_CLIENTS goad
    class MIRROR_SWITCH,SPAN_PORTS monitoring
```

## 🏗️ Network Topology Design

### **Network Segmentation Strategy**

```yaml
# Network segmentation configuration
network_topology:
  # Core network settings
  base_domain: "goad-blue.local"
  dns_suffix: "goad-blue.local"
  
  # Network segments
  segments:
    # Management segment - Administrative access
    management:
      name: "Management"
      cidr: "***********/24"
      vlan_id: 10
      gateway: "***********"
      purpose: "Administrative access and management"
      security_zone: "trusted"
      
    # SIEM segment - Log analysis and correlation
    siem:
      name: "SIEM"
      cidr: "*************/26"
      vlan_id: 100
      gateway: "*************"
      purpose: "SIEM platforms and log analysis"
      security_zone: "internal"
      
    # Monitoring segment - Network security monitoring
    monitoring:
      name: "Monitoring"
      cidr: "**************/26"
      vlan_id: 101
      gateway: "**************"
      purpose: "Network monitoring and IDS/IPS"
      security_zone: "internal"
      
    # Analysis segment - Malware analysis and research
    analysis:
      name: "Analysis"
      cidr: "*************28/26"
      vlan_id: 102
      gateway: "*************29"
      purpose: "Malware analysis and forensics"
      security_zone: "isolated"
      
    # Intelligence segment - Threat intelligence
    intelligence:
      name: "Intelligence"
      cidr: "*************92/26"
      vlan_id: 103
      gateway: "*************93"
      purpose: "Threat intelligence and feeds"
      security_zone: "internal"
      
    # GOAD segment - Target environment
    goad:
      name: "GOAD"
      cidr: "************/24"
      vlan_id: 200
      gateway: "************"
      purpose: "GOAD target environment"
      security_zone: "target"
```

### **IP Address Allocation**

```yaml
# Static IP assignments
static_assignments:
  # Infrastructure services
  infrastructure:
    dns_primary: "************"
    dns_secondary: "************"
    ntp_server: "***********2"
    jump_host: "************"
    
  # SIEM components
  siem:
    splunk_search_head: "**************"
    splunk_indexer_1: "*************1"
    splunk_indexer_2: "*************2"
    splunk_cluster_master: "*************3"
    elasticsearch_master: "*************5"
    elasticsearch_data_1: "*************6"
    elasticsearch_data_2: "*************7"
    kibana: "*************8"
    
  # Monitoring components
  monitoring:
    security_onion_manager: "**************"
    security_onion_search_1: "**************"
    security_onion_search_2: "**************"
    security_onion_sensor_1: "**************"
    security_onion_sensor_2: "**************"
    velociraptor_server: "**************"
    
  # Analysis components
  analysis:
    flare_vm: "*************30"
    jupyter_hub: "*************31"
    sandbox_1: "*************35"
    sandbox_2: "*************36"
    
  # Intelligence components
  intelligence:
    misp_server: "***************"
    threat_feeds: "***************"
    
# DHCP ranges for dynamic assignment
dhcp_ranges:
  management: "************0-*************"
  siem: "**************-**************"
  monitoring: "192.168.100.90-*************10"
  analysis: "*************50-*************70"
  intelligence: "192.168.100.210-192.168.100.230"
```

## 🔄 Traffic Mirroring and Monitoring

### **Network Tap Configuration**

```yaml
# Traffic mirroring configuration
traffic_mirroring:
  # Physical network taps
  physical_taps:
    - name: "GOAD-Core-Tap"
      location: "Core switch uplink"
      tap_type: "passive"
      destination: "**************"  # Security Onion sensor
      
    - name: "GOAD-DMZ-Tap"
      location: "DMZ segment"
      tap_type: "active"
      destination: "**************"  # Security Onion sensor
      
  # Virtual switch mirroring
  virtual_mirroring:
    vmware_vsphere:
      # Distributed switch configuration
      dvswitch:
        name: "GOAD-Blue-DVS"
        port_groups:
          - name: "GOAD-Production"
            vlan_id: 200
            promiscuous_mode: false
            
          - name: "GOAD-Monitoring"
            vlan_id: 101
            promiscuous_mode: true
            forged_transmits: true
            mac_changes: true
            
      # Port mirroring sessions
      mirror_sessions:
        - name: "GOAD-to-SecurityOnion"
          source_ports: ["GOAD-Production"]
          destination_port: "GOAD-Monitoring"
          session_type: "dvPortMirror"
          
    virtualbox:
      # VirtualBox network configuration
      host_only_networks:
        - name: "GOAD-Blue-HostOnly"
          ip_range: "*************/24"
          dhcp_enabled: false
          
      # Promiscuous mode for monitoring
      promiscuous_mode:
        network: "GOAD-Blue-HostOnly"
        policy: "allow-all"
```

### **Network Monitoring Points**

```yaml
# Monitoring point configuration
monitoring_points:
  # Ingress monitoring
  ingress:
    - location: "Internet gateway"
      monitor_type: "full_packet_capture"
      retention_days: 7
      
    - location: "Corporate network boundary"
      monitor_type: "metadata_only"
      retention_days: 30
      
  # Internal monitoring
  internal:
    - location: "GOAD network core"
      monitor_type: "full_packet_capture"
      retention_days: 14
      
    - location: "SIEM network"
      monitor_type: "flow_monitoring"
      retention_days: 30
      
  # Egress monitoring
  egress:
    - location: "Analysis network"
      monitor_type: "full_packet_capture"
      retention_days: 3
      alert_on_outbound: true
```

## 🔒 Network Security Policies

### **Firewall Rules Configuration**

```yaml
# Firewall configuration
firewall:
  # Default policies
  default_policies:
    ingress: "deny"
    egress: "allow"
    inter_vlan: "deny"
    
  # Zone-based rules
  zones:
    # Management zone rules
    management:
      ingress_rules:
        - name: "Allow SSH from corporate"
          source: "10.0.0.0/8"
          destination: "***********/24"
          protocol: "tcp"
          port: 22
          action: "allow"
          
        - name: "Allow HTTPS management"
          source: "10.0.0.0/8"
          destination: "***********/24"
          protocol: "tcp"
          port: 443
          action: "allow"
          
      egress_rules:
        - name: "Allow internet access"
          source: "***********/24"
          destination: "0.0.0.0/0"
          protocol: "any"
          action: "allow"
          
    # SIEM zone rules
    siem:
      ingress_rules:
        - name: "Allow log forwarding"
          source: "************/24"  # GOAD network
          destination: "*************/26"
          protocol: "tcp"
          ports: [514, 9997, 5044]
          action: "allow"
          
        - name: "Allow web access from management"
          source: "***********/24"
          destination: "*************/26"
          protocol: "tcp"
          ports: [8000, 5601, 443]
          action: "allow"
          
    # Monitoring zone rules
    monitoring:
      ingress_rules:
        - name: "Allow mirrored traffic"
          source: "any"
          destination: "**************/26"
          protocol: "any"
          action: "allow"
          
        - name: "Allow management access"
          source: "***********/24"
          destination: "**************/26"
          protocol: "tcp"
          ports: [443, 22]
          action: "allow"
          
    # Analysis zone rules (isolated)
    analysis:
      ingress_rules:
        - name: "Allow management access only"
          source: "***********/24"
          destination: "*************28/26"
          protocol: "tcp"
          ports: [3389, 22, 443]
          action: "allow"
          
      egress_rules:
        - name: "Block all internet access"
          source: "*************28/26"
          destination: "0.0.0.0/0"
          protocol: "any"
          action: "deny"
          
        - name: "Allow internal communication"
          source: "*************28/26"
          destination: "*************/24"
          protocol: "any"
          action: "allow"
```

### **Network Access Control**

```yaml
# Network access control configuration
network_access_control:
  # 802.1X authentication
  dot1x:
    enabled: true
    radius_server: "************"
    shared_secret: "RadiusSecret123!"
    
    # VLAN assignment based on authentication
    vlan_assignment:
      "GOAD-Blue-Admins": 10      # Management VLAN
      "SOC-Analysts": 100         # SIEM VLAN
      "Security-Team": 101        # Monitoring VLAN
      "Researchers": 102          # Analysis VLAN
      
  # MAC address filtering
  mac_filtering:
    enabled: true
    default_action: "deny"
    
    # Allowed devices
    allowed_devices:
      - mac: "00:50:56:12:34:56"
        description: "Security Onion Manager"
        vlan: 101
        
      - mac: "00:50:56:78:90:AB"
        description: "Splunk Search Head"
        vlan: 100
        
  # Port security
  port_security:
    enabled: true
    max_mac_addresses: 2
    violation_action: "shutdown"
    aging_time: 1440  # 24 hours
```

## 🌐 DNS and DHCP Configuration

### **DNS Configuration**

```yaml
# DNS server configuration
dns:
  # Primary DNS server
  primary:
    ip: "************"
    hostname: "dns1.goad-blue.local"
    
    # Forward zones
    forward_zones:
      - zone: "goad-blue.local"
        type: "master"
        file: "/etc/bind/zones/goad-blue.local.zone"
        
      - zone: "sevenkingdoms.local"
        type: "forward"
        forwarders: ["*************", "*************"]
        
    # Reverse zones
    reverse_zones:
      - zone: "1.168.192.in-addr.arpa"
        type: "master"
        file: "/etc/bind/zones/192.168.1.rev"
        
      - zone: "100.168.192.in-addr.arpa"
        type: "master"
        file: "/etc/bind/zones/192.168.100.rev"
        
  # Secondary DNS server
  secondary:
    ip: "************"
    hostname: "dns2.goad-blue.local"
    
  # DNS records
  records:
    # A records
    a_records:
      - name: "splunk"
        ip: "**************"
        
      - name: "security-onion"
        ip: "**************"
        
      - name: "velociraptor"
        ip: "**************"
        
      - name: "misp"
        ip: "***************"
        
    # CNAME records
    cname_records:
      - name: "siem"
        target: "splunk.goad-blue.local"
        
      - name: "ids"
        target: "security-onion.goad-blue.local"
        
    # SRV records
    srv_records:
      - name: "_syslog._tcp"
        target: "splunk.goad-blue.local"
        port: 514
        priority: 10
        weight: 5
```

### **DHCP Configuration**

```yaml
# DHCP server configuration
dhcp:
  # Global settings
  global:
    default_lease_time: 86400     # 24 hours
    max_lease_time: 604800        # 7 days
    authoritative: true
    
  # Subnet configurations
  subnets:
    # Management subnet
    management:
      network: "***********"
      netmask: "*************"
      range: "************0 *************"
      gateway: "***********"
      dns_servers: ["************", "************"]
      domain_name: "goad-blue.local"
      
    # SIEM subnet
    siem:
      network: "*************"
      netmask: "***************"
      range: "************** **************"
      gateway: "*************"
      dns_servers: ["************", "************"]
      
  # Static reservations
  reservations:
    - hostname: "goad-blue-mgmt"
      mac: "00:50:56:12:34:56"
      ip: "************"
      
    - hostname: "splunk-sh1"
      mac: "00:50:56:78:90:AB"
      ip: "**************"
```

## 🔧 Network Troubleshooting

### **Network Diagnostic Commands**

```bash
# Network connectivity tests
python3 goad-blue.py test-network --comprehensive

# VLAN configuration verification
python3 goad-blue.py verify-vlans --all-segments

# Traffic flow analysis
python3 goad-blue.py analyze-traffic --source goad --destination siem

# DNS resolution testing
python3 goad-blue.py test-dns --all-records

# Firewall rule verification
python3 goad-blue.py verify-firewall --test-connectivity
```

### **Common Network Issues**

| Issue | Symptoms | Solution |
|-------|----------|----------|
| **VLAN Misconfiguration** | Devices can't communicate | Verify VLAN assignments and trunk ports |
| **Routing Problems** | Inter-subnet communication fails | Check routing tables and gateway configuration |
| **DNS Resolution** | Hostnames don't resolve | Verify DNS server configuration and records |
| **Firewall Blocking** | Connections timeout | Review firewall rules and logs |
| **Traffic Mirroring** | No traffic in monitoring tools | Check mirror port configuration |

---

!!! tip "Network Planning"
    Plan your network topology carefully before deployment. Consider future growth and additional monitoring requirements.

!!! warning "Security Isolation"
    Ensure proper network segmentation, especially for the analysis network which should be isolated from production systems.

!!! info "Performance Considerations"
    Monitor network utilization, especially on mirror ports and high-traffic segments. Consider bandwidth requirements for log forwarding.
