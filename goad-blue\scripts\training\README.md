# GOAD-Blue Training and Use Case Scripts

This directory contains training scenarios, use case implementations, and automation scripts for GOAD-Blue cybersecurity training environments.

## 📁 Directory Structure

```
scripts/
├── training/                    # Training scenario scripts
│   ├── scenarios/              # Individual training scenarios
│   │   ├── kerberoasting/      # Kerberoasting detection scenario
│   │   ├── lateral-movement/   # Lateral movement detection
│   │   ├── malware-analysis/   # Malware analysis workshop
│   │   ├── apt-simulation/     # APT campaign simulation
│   │   └── incident-response/  # Incident response exercises
│   ├── automation/             # Training automation scripts
│   ├── data-generation/        # Test data generation
│   └── validation/             # Training validation scripts
├── use-cases/                  # Real-world use case implementations
│   ├── threat-hunting/         # Threat hunting scenarios
│   ├── compliance/             # Compliance monitoring
│   ├── forensics/              # Digital forensics cases
│   └── red-team/               # Red team exercises
├── deployment/                 # Deployment automation
│   ├── ansible/                # Ansible playbooks
│   ├── terraform/              # Infrastructure as code
│   └── docker/                 # Container deployments
└── utilities/                  # Utility scripts and tools
    ├── log-analysis/           # Log analysis tools
    ├── network-tools/          # Network analysis utilities
    └── reporting/              # Report generation scripts
```

## 🎯 Training Scenarios

### Available Scenarios

1. **Kerberoasting Detection**
   - Simulates Kerberoasting attacks
   - Teaches detection techniques
   - Includes SIEM queries and alerts

2. **Lateral Movement Detection**
   - Multi-stage attack simulation
   - Cross-platform analysis
   - Timeline reconstruction

3. **Malware Analysis Workshop**
   - Static and dynamic analysis
   - IOC extraction
   - YARA rule creation

4. **APT Campaign Simulation**
   - Advanced persistent threat simulation
   - Multi-day exercise
   - Attribution analysis

5. **Incident Response**
   - End-to-end incident handling
   - Team coordination exercises
   - Documentation and reporting

## 🚀 Quick Start

### Running a Training Scenario

```bash
# Navigate to training directory
cd scripts/training

# Run kerberoasting scenario
./scenarios/kerberoasting/run_scenario.py --mode interactive

# Run with specific configuration
./scenarios/kerberoasting/run_scenario.py --config custom_config.yml

# Generate training data
./data-generation/generate_training_data.py --scenario kerberoasting --duration 2h
```

### Automation Scripts

```bash
# Deploy training environment
./automation/deploy_training_env.py --scenario apt-simulation

# Validate training setup
./validation/validate_environment.py --comprehensive

# Generate training report
./utilities/reporting/generate_training_report.py --scenario-id 12345
```

## 📊 Use Cases

### Threat Hunting

- **Advanced Persistent Threats**: Hunt for APT indicators
- **Insider Threats**: Detect malicious insider activities
- **Zero-Day Exploits**: Identify unknown attack patterns

### Compliance Monitoring

- **PCI DSS**: Payment card industry compliance
- **HIPAA**: Healthcare data protection
- **SOX**: Financial reporting compliance

### Digital Forensics

- **Memory Analysis**: RAM dump investigation
- **Network Forensics**: Packet capture analysis
- **Timeline Analysis**: Event reconstruction

## 🔧 Configuration

### Environment Variables

```bash
export GOAD_BLUE_HOME="/opt/goad-blue"
export TRAINING_DATA_PATH="/opt/goad-blue/training-data"
export SCENARIO_CONFIG_PATH="/opt/goad-blue/config/scenarios"
```

### Configuration Files

- `config/training.yml` - Global training configuration
- `config/scenarios/` - Individual scenario configurations
- `config/environments/` - Environment-specific settings

## 📚 Documentation

- [Training Scenario Guide](../docs/training/scenarios/index.md)
- [Use Case Documentation](../docs/use-cases/index.md)
- [API Reference](../docs/api/training-api.md)
- [Troubleshooting](../docs/troubleshooting/training-issues.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your training scenario or use case
4. Include documentation and tests
5. Submit a pull request

### Scenario Development Guidelines

- Follow the scenario template structure
- Include comprehensive documentation
- Provide validation scripts
- Add cleanup procedures
- Test across different environments

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](../../LICENSE) file for details.

## 🆘 Support

- [Community Forum](https://github.com/goad-blue/community)
- [Issue Tracker](https://github.com/goad-blue/issues)
- [Documentation](https://docs.goad-blue.org)
- [Training Support](mailto:<EMAIL>)
