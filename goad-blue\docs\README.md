# GOAD-Blue Documentation

This directory contains the complete documentation for GOAD-Blue, built using [MkDocs](https://www.mkdocs.org/) with the [Material theme](https://squidfunk.github.io/mkdocs-material/).

## 📚 Documentation Structure

```
docs/
├── index.md                    # Main documentation homepage
├── overview.md                 # Project overview and vision
├── architecture.md             # Technical architecture details
├── quick-start.md              # 30-minute quick start guide
├── getting-started/            # Comprehensive getting started guide
│   ├── index.md
│   ├── prerequisites.md
│   ├── installation.md
│   ├── first-steps.md
│   └── verification.md
├── configuration/              # Configuration guides
├── components/                 # Individual component documentation
├── integration/                # GOAD integration guides
├── training/                   # Training scenarios and use cases
├── deployment/                 # Infrastructure as Code details
├── operations/                 # Day-to-day operations
├── api/                        # API reference
├── troubleshooting/            # Troubleshooting guides
├── development/                # Development and contribution guides
├── reference/                  # Reference materials
├── stylesheets/                # Custom CSS styles
└── javascripts/                # Custom JavaScript
```

## 🚀 Quick Start

### View Documentation Locally

**Quick Start (Recommended):**
```bash
# Install minimal dependencies (most reliable)
pip install -r requirements-docs-minimal.txt

# Serve documentation locally
mkdocs serve
```

**Using Build Scripts:**
```bash
# Linux/macOS
./scripts/build-docs.sh serve

# Windows
scripts\build-docs.bat serve
```

Access the documentation at: http://localhost:8000

!!! tip "Setup Issues?"
    If you encounter dependency issues, see the [Setup Guide](SETUP.md) for troubleshooting steps.

### Build Static Documentation

```bash
# Build static site
./scripts/build-docs.sh build

# Output will be in site/ directory
```

## 🛠️ Building Documentation

### Prerequisites

- Python 3.8+
- pip package manager
- Git (for deployment)

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd goad-blue

# Install documentation dependencies
pip install -r requirements-docs.txt
```

### Development Workflow

1. **Edit Documentation**: Modify `.md` files in the `docs/` directory
2. **Preview Changes**: Run `mkdocs serve` to see changes in real-time
3. **Build Site**: Run `mkdocs build` to generate static files
4. **Deploy**: Use `mkdocs gh-deploy` for GitHub Pages deployment

### Available Commands

```bash
# Development server with auto-reload
mkdocs serve

# Build static site
mkdocs build

# Deploy to GitHub Pages
mkdocs gh-deploy

# Validate configuration
mkdocs config-check
```

## 📝 Writing Documentation

### Markdown Guidelines

- Use **clear, descriptive headings**
- Include **code examples** with syntax highlighting
- Add **diagrams** using Mermaid syntax
- Use **admonitions** for important notes
- Include **cross-references** to related sections

### Code Examples

Use fenced code blocks with language specification:

````markdown
```bash
# Example bash command
python3 goad-blue.py --interactive
```

```yaml
# Example YAML configuration
goad_blue:
  name: "example-lab"
  provider: "vmware"
```
````

### Diagrams

Use Mermaid for diagrams:

````markdown
```mermaid
graph TB
    A[Start] --> B[Process]
    B --> C[End]
```
````

### Admonitions

Use admonitions for important information:

```markdown
!!! info "Information"
    This is an informational note.

!!! warning "Warning"
    This is a warning message.

!!! tip "Pro Tip"
    This is a helpful tip.
```

### Cross-References

Link to other documentation pages:

```markdown
See the [Installation Guide](getting-started/installation.md) for details.
```

## 🎨 Customization

### Theme Configuration

The documentation uses MkDocs Material with custom styling:

- **Primary Color**: Blue (#1976d2)
- **Accent Color**: Light Blue (#42a5f5)
- **Custom CSS**: `stylesheets/extra.css`
- **Custom JavaScript**: `javascripts/mathjax.js`

### Adding New Sections

1. Create new `.md` files in appropriate directories
2. Update `mkdocs.yml` navigation structure
3. Add cross-references from existing pages
4. Test locally before committing

### Custom Styling

Modify `docs/stylesheets/extra.css` to customize appearance:

```css
/* Custom component styling */
.component-card {
  background: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
```

## 🚀 Deployment

### GitHub Pages

Automatic deployment to GitHub Pages:

```bash
# Deploy to gh-pages branch
mkdocs gh-deploy --force
```

### Custom Domain

To use a custom domain:

1. Add `CNAME` file to `docs/` directory
2. Configure DNS settings
3. Update `site_url` in `mkdocs.yml`

### CI/CD Integration

Example GitHub Actions workflow:

```yaml
name: Deploy Documentation
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-python@v2
        with:
          python-version: 3.x
      - run: pip install -r requirements-docs.txt
      - run: mkdocs gh-deploy --force
```

## 📊 Analytics and Monitoring

### Google Analytics

Add Google Analytics to `mkdocs.yml`:

```yaml
extra:
  analytics:
    provider: google
    property: G-XXXXXXXXXX
```

### Search

The documentation includes built-in search functionality powered by MkDocs search plugin.

## 🤝 Contributing

### Documentation Contributions

1. **Fork the repository**
2. **Create a feature branch**
3. **Make your changes**
4. **Test locally**
5. **Submit a pull request**

### Style Guide

- Use **present tense** for instructions
- Keep **sentences concise** and clear
- Include **examples** for complex concepts
- Use **consistent terminology** throughout
- Add **screenshots** where helpful

### Review Process

All documentation changes go through review:

1. Automated checks for broken links
2. Style and grammar review
3. Technical accuracy verification
4. User experience testing

## 🔧 Troubleshooting

### Common Issues

**MkDocs not found:**
```bash
pip install mkdocs mkdocs-material
```

**Build errors:**
```bash
# Check configuration
mkdocs config-check

# Verbose build output
mkdocs build --verbose
```

**Broken links:**
```bash
# Use link checker plugin
pip install mkdocs-linkcheck
mkdocs build --strict
```

### Getting Help

- **Documentation Issues**: Create GitHub issue
- **MkDocs Help**: Check [MkDocs documentation](https://www.mkdocs.org/)
- **Material Theme**: See [Material documentation](https://squidfunk.github.io/mkdocs-material/)

## 📄 License

This documentation is part of the GOAD-Blue project and is licensed under the GPL-3.0 License.

---

For more information about GOAD-Blue, visit the [main documentation site](https://goad-blue.readthedocs.io/) or check out the [project repository](https://github.com/your-org/goad-blue).
