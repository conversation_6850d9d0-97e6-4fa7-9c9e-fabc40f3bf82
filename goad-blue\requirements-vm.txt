# GOAD-Blue VM Requirements
# Python packages required for VM automation and management

# Core Ansible
ansible>=6.0.0
ansible-core>=2.13.0
ansible-runner>=2.3.0

# Windows Management
pywinrm>=0.4.3
pywinrm[kerberos]>=0.4.3
pywinrm[credssp]>=0.4.3

# Core Python Libraries
requests>=2.28.0
pyyaml>=6.0
jinja2>=3.1.0
netaddr>=0.8.0
dnspython>=2.2.0
paramiko>=2.11.0

# Cloud Providers
boto3>=1.26.0
azure-cli>=2.50.0
google-cloud-sdk>=440.0.0

# Container Management
docker>=6.1.0
docker-compose>=1.29.0
kubernetes>=24.2.0
openshift>=0.13.0

# SIEM Integration
splunk-sdk>=1.7.0
elasticsearch>=8.8.0
elasticsearch-dsl>=8.8.0

# Security Tools
yara-python>=4.3.0
pefile>=2023.2.7
python-magic>=0.4.27
ssdeep>=3.4
pydeep>=0.4
volatility3>=2.4.0

# Analysis Tools
capstone>=5.0.0
unicorn>=2.0.0
keystone-engine>=0.9.2
r2pipe>=1.7.4

# Monitoring and Logging
prometheus-client>=0.17.0
grafana-api>=1.0.3
influxdb>=5.3.1

# Network Tools
scapy>=2.5.0
impacket>=0.10.0
ldap3>=2.9.0

# Cryptography
cryptography>=41.0.0
pycryptodome>=3.18.0

# Utilities
click>=8.1.0
rich>=13.4.0
tabulate>=0.9.0
colorama>=0.4.6
tqdm>=4.65.0

# Development and Testing
pytest>=7.4.0
pytest-ansible>=4.1.0
molecule>=5.1.0
molecule-vagrant>=2.0.0

# Optional but recommended
# These may require additional system dependencies
# psutil>=5.9.0  # System monitoring
# python-nmap>=0.7.1  # Network scanning
# shodan>=1.29.0  # Shodan API integration
# virustotal-api>=1.1.11  # VirusTotal integration
