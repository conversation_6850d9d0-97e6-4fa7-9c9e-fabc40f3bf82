# Filebeat Configuration for GOAD-Blue
# Generated by Ansible

# Filebeat inputs
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/syslog
    - /var/log/messages
    - /var/log/auth.log
    - /var/log/secure
  fields:
    logtype: system
    environment: goad-blue
  fields_under_root: true

- type: log
  enabled: true
  paths:
    - /var/log/apache2/*.log
    - /var/log/httpd/*.log
  fields:
    logtype: apache
    environment: goad-blue
  fields_under_root: true

- type: log
  enabled: true
  paths:
    - /var/log/nginx/*.log
  fields:
    logtype: nginx
    environment: goad-blue
  fields_under_root: true

- type: log
  enabled: true
  paths:
    - /var/log/mysql/*.log
    - /var/log/mariadb/*.log
  fields:
    logtype: mysql
    environment: goad-blue
  fields_under_root: true

# GOAD-Blue specific inputs
- type: log
  enabled: true
  paths:
    - /var/log/goad-blue/*.log
  fields:
    logtype: goad-blue
    environment: goad-blue
  fields_under_root: true

# Suricata logs
- type: log
  enabled: true
  paths:
    - /var/log/suricata/eve.json
  json.keys_under_root: true
  json.add_error_key: true
  fields:
    logtype: suricata
    environment: goad-blue
  fields_under_root: true

# Zeek logs
- type: log
  enabled: true
  paths:
    - /opt/zeek/logs/current/*.log
  fields:
    logtype: zeek
    environment: goad-blue
  fields_under_root: true

# Filebeat modules
filebeat.config.modules:
  path: ${path.config}/modules.d/*.yml
  reload.enabled: true
  reload.period: 10s

# Processors
processors:
- add_host_metadata:
    when.not.contains.tags: forwarded
- add_cloud_metadata: ~
- add_docker_metadata: ~
- add_kubernetes_metadata: ~

# Output configuration
{% if beats.filebeat.output.logstash is defined %}
output.logstash:
  hosts: {{ beats.filebeat.output.logstash.hosts | to_json }}
  compression_level: 3
  bulk_max_size: 2048
  template.name: "goad-blue"
  template.pattern: "goad-blue-*"
{% else %}
output.elasticsearch:
  hosts: ["{{ elasticsearch.network.host }}:{{ elasticsearch.network.port }}"]
  index: "goad-blue-%{[logtype]}-%{+yyyy.MM.dd}"
  template.name: "goad-blue"
  template.pattern: "goad-blue-*"
  template.settings:
    index.number_of_shards: 1
    index.number_of_replicas: 0
{% endif %}

# Index lifecycle management
setup.ilm.enabled: true
setup.ilm.rollover_alias: "goad-blue"
setup.ilm.pattern: "goad-blue-*"

# Template configuration
setup.template.name: "goad-blue"
setup.template.pattern: "goad-blue-*"
setup.template.settings:
  index.number_of_shards: 1
  index.number_of_replicas: 0

# Kibana configuration
setup.kibana:
  host: "{{ kibana.server.host }}:{{ kibana.server.port }}"

# Logging configuration
logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644

# Monitoring
monitoring.enabled: true
