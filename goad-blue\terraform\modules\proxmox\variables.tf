# Proxmox Module Variables

variable "name_prefix" {
  description = "Prefix for all VM names"
  type        = string
  default     = "goad-blue"
}

variable "target_node" {
  description = "Proxmox target node for VM deployment"
  type        = string
  default     = "pve-node-1"
}

variable "ubuntu_template_name" {
  description = "Name of Ubuntu template to clone from"
  type        = string
  default     = ""
}

variable "vm_user" {
  description = "Default VM user for cloud-init"
  type        = string
  default     = "ubuntu"
}

variable "vm_password" {
  description = "Default VM password for cloud-init"
  type        = string
  sensitive   = true
  default     = "goadblue123!"
}

variable "ssh_public_key" {
  description = "SSH public key for VM access"
  type        = string
}

# Component deployment flags
variable "deploy_splunk" {
  description = "Deploy Splunk Enterprise VM"
  type        = bool
  default     = true
}

variable "deploy_security_onion" {
  description = "Deploy Security Onion VMs"
  type        = bool
  default     = true
}

variable "deploy_velociraptor" {
  description = "Deploy Velociraptor Server VM"
  type        = bool
  default     = true
}

variable "deploy_misp" {
  description = "Deploy MISP Server VM"
  type        = bool
  default     = true
}

variable "security_onion_sensor_count" {
  description = "Number of Security Onion sensor VMs to deploy"
  type        = number
  default     = 1
}

# VM IDs
variable "vm_ids" {
  description = "VM IDs for each component"
  type = object({
    splunk                     = number
    security_onion_manager     = number
    security_onion_sensor_base = number
    velociraptor              = number
    misp                      = number
  })
  default = {
    splunk                     = 200
    security_onion_manager     = 270
    security_onion_sensor_base = 271
    velociraptor              = 285
    misp                      = 230
  }
}

# VM Configurations
variable "vm_configs" {
  description = "VM resource configurations"
  type = object({
    splunk = object({
      cores          = number
      memory         = number
      disk_size      = string
      data_disk_size = string
    })
    security_onion_manager = object({
      cores     = number
      memory    = number
      disk_size = string
    })
    security_onion_sensor = object({
      cores     = number
      memory    = number
      disk_size = string
    })
    velociraptor = object({
      cores     = number
      memory    = number
      disk_size = string
    })
    misp = object({
      cores     = number
      memory    = number
      disk_size = string
    })
  })
  default = {
    splunk = {
      cores          = 8
      memory         = 16384
      disk_size      = "100G"
      data_disk_size = "500G"
    }
    security_onion_manager = {
      cores     = 16
      memory    = 32768
      disk_size = "1000G"
    }
    security_onion_sensor = {
      cores     = 8
      memory    = 16384
      disk_size = "500G"
    }
    velociraptor = {
      cores     = 4
      memory    = 8192
      disk_size = "200G"
    }
    misp = {
      cores     = 4
      memory    = 8192
      disk_size = "200G"
    }
  }
}

# Network Configuration
variable "network_config" {
  description = "Network configuration for VMs"
  type = object({
    # Bridge configurations
    goad_blue_bridge = string
    goad_bridge      = string
    internet_bridge  = string
    
    # VLAN configurations
    goad_blue_vlan = number
    goad_vlan      = number
    
    # Network CIDRs
    goad_blue_cidr        = string
    goad_blue_cidr_suffix = number
    goad_cidr             = string
    goad_cidr_suffix      = number
    
    # Gateway and DNS
    gateway     = string
    dns_servers = string
    
    # Individual VM IP configurations
    splunk = object({
      ip   = string
      cidr = number
    })
    security_onion_manager = object({
      ip         = string
      cidr       = number
      monitor_ip = string
    })
    velociraptor = object({
      ip   = string
      cidr = number
    })
    misp = object({
      ip   = string
      cidr = number
    })
  })
  default = {
    # Bridge configurations
    goad_blue_bridge = "vmbr2"
    goad_bridge      = "vmbr1"
    internet_bridge  = "vmbr0"
    
    # VLAN configurations
    goad_blue_vlan = 100
    goad_vlan      = 56
    
    # Network CIDRs
    goad_blue_cidr        = "*************/24"
    goad_blue_cidr_suffix = 24
    goad_cidr             = "************/24"
    goad_cidr_suffix      = 24
    
    # Gateway and DNS
    gateway     = "*************"
    dns_servers = "*************,*******"
    
    # Individual VM IP configurations
    splunk = {
      ip   = "*************0"
      cidr = 24
    }
    security_onion_manager = {
      ip         = "**************"
      cidr       = 24
      monitor_ip = "*************"
    }
    velociraptor = {
      ip   = "**************"
      cidr = 24
    }
    misp = {
      ip   = "**************"
      cidr = 24
    }
  }
}

# Storage Configuration
variable "storage_config" {
  description = "Storage configuration for VMs"
  type = object({
    primary_storage = string
    data_storage    = string
    disk_format     = string
    ssd             = bool
  })
  default = {
    primary_storage = "local-lvm"
    data_storage    = "local-lvm"
    disk_format     = "qcow2"
    ssd             = true
  }
}

# Common Tags
variable "common_tags" {
  description = "Common tags for all resources"
  type = object({
    project     = string
    environment = string
  })
  default = {
    project     = "goad-blue"
    environment = "production"
  }
}

# Advanced Configuration
variable "enable_ha" {
  description = "Enable High Availability for VMs"
  type        = bool
  default     = false
}

variable "backup_schedule" {
  description = "Backup schedule configuration"
  type = object({
    enabled  = bool
    schedule = string
    storage  = string
    compress = string
  })
  default = {
    enabled  = true
    schedule = "daily"
    storage  = "backup-storage"
    compress = "lzo"
  }
}

variable "monitoring_config" {
  description = "Monitoring configuration"
  type = object({
    enable_prometheus = bool
    enable_grafana    = bool
    metrics_retention = string
  })
  default = {
    enable_prometheus = true
    enable_grafana    = true
    metrics_retention = "30d"
  }
}

# Security Configuration
variable "security_config" {
  description = "Security configuration"
  type = object({
    enable_firewall     = bool
    allowed_ssh_sources = list(string)
    enable_fail2ban     = bool
  })
  default = {
    enable_firewall     = true
    allowed_ssh_sources = ["*************/24", "***********/24"]
    enable_fail2ban     = true
  }
}
