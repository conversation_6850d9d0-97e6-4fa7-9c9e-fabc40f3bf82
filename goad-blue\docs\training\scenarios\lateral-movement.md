# Lateral Movement Detection Scenario

This advanced scenario focuses on detecting and analyzing lateral movement techniques used by attackers to traverse networks and escalate privileges within the GOAD Active Directory environment.

## 🎯 Scenario Overview

**Difficulty:** Advanced  
**Duration:** 4-6 hours  
**Skills Focus:** Network Analysis, Endpoint Detection, Attack Path Reconstruction  
**Tools Required:** Splunk/Elastic, Velocirap<PERSON>, Wireshark, Sysmon

### **Learning Objectives**
- Understand common lateral movement techniques and their indicators
- Detect credential theft and reuse across network systems
- Analyze network traffic patterns for lateral movement
- Reconstruct attack paths and timeline
- Develop comprehensive detection and response strategies

## 📚 Background Knowledge

### **Lateral Movement Attack Chain**

```mermaid
graph TB
    subgraph "🎯 Initial Compromise"
        PHISHING[📧 Phishing Email<br/>Credential Harvest<br/>Malware Delivery]
        EXPLOIT[🔓 Vulnerability Exploit<br/>Remote Code Execution<br/>Initial Foothold]
    end
    
    subgraph "🔍 Discovery & Reconnaissance"
        ENUM[🔍 Network Enumeration<br/>Service Discovery<br/>User Enumeration]
        CRED_DUMP[💾 Credential Dumping<br/>LSASS Access<br/>Registry Extraction]
    end
    
    subgraph "↔️ Lateral Movement Techniques"
        PTH[🔑 Pass-the-Hash<br/>NTLM Relay<br/>Hash Reuse]
        PTT[🎫 Pass-the-Ticket<br/>Kerberos Tickets<br/>Golden/Silver Tickets]
        REMOTE_EXEC[⚡ Remote Execution<br/>PsExec<br/>WMI<br/>PowerShell Remoting]
        RDP[🖥️ RDP Abuse<br/>Terminal Services<br/>Credential Reuse]
    end
    
    subgraph "🎯 Privilege Escalation"
        LOCAL_PRIV[⬆️ Local Privilege Escalation<br/>Token Manipulation<br/>Service Exploitation]
        DOMAIN_PRIV[👑 Domain Privilege Escalation<br/>DCSync<br/>AdminSDHolder<br/>GPO Abuse]
    end
    
    PHISHING --> ENUM
    EXPLOIT --> ENUM
    ENUM --> CRED_DUMP
    CRED_DUMP --> PTH
    CRED_DUMP --> PTT
    CRED_DUMP --> REMOTE_EXEC
    CRED_DUMP --> RDP
    PTH --> LOCAL_PRIV
    PTT --> LOCAL_PRIV
    REMOTE_EXEC --> DOMAIN_PRIV
    RDP --> DOMAIN_PRIV
    
    classDef initial fill:#ff5722,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef discovery fill:#2196f3,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef lateral fill:#ff9800,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef escalation fill:#4caf50,stroke:#ffffff,stroke-width:2px,color:#fff
    
    class PHISHING,EXPLOIT initial
    class ENUM,CRED_DUMP discovery
    class PTH,PTT,REMOTE_EXEC,RDP lateral
    class LOCAL_PRIV,DOMAIN_PRIV escalation
```

### **Common Lateral Movement Techniques**

| Technique | Description | Detection Indicators |
|-----------|-------------|---------------------|
| **Pass-the-Hash** | Reuse NTLM hashes without knowing plaintext password | Unusual authentication patterns, same hash across multiple systems |
| **Pass-the-Ticket** | Reuse Kerberos tickets for authentication | Ticket reuse, unusual ticket requests, golden/silver ticket indicators |
| **PsExec** | Remote execution via SMB and service creation | Service creation events, SMB connections, process execution |
| **WMI** | Windows Management Instrumentation for remote execution | WMI process creation, unusual WMI activity |
| **PowerShell Remoting** | Remote PowerShell execution | PowerShell remoting events, unusual script execution |
| **RDP** | Remote Desktop Protocol abuse | RDP logons, unusual RDP activity, credential reuse |

## 🎮 Scenario Setup

### **Environment Configuration**

```yaml
scenario_environment:
  domain: "sevenkingdoms.local"
  systems:
    domain_controller:
      hostname: "winterfell.sevenkingdoms.local"
      ip: "*************"
      role: "Domain Controller"
    
    file_server:
      hostname: "kingslanding.sevenkingdoms.local"
      ip: "*************"
      role: "File Server"
    
    web_server:
      hostname: "dragonstone.sevenkingdoms.local"
      ip: "*************"
      role: "Web Server"
    
    workstations:
      - hostname: "ws01.sevenkingdoms.local"
        ip: "*************"
        user: "jon.snow"
      - hostname: "ws02.sevenkingdoms.local"
        ip: "*************"
        user: "arya.stark"
      - hostname: "ws03.sevenkingdoms.local"
        ip: "*************"
        user: "tyrion.lannister"
  
  user_accounts:
    - name: "jon.snow"
      privileges: "Domain Users"
      initial_compromise: true
    - name: "arya.stark"
      privileges: "Local Admin on WS02"
    - name: "tyrion.lannister"
      privileges: "Server Operators"
    - name: "administrator"
      privileges: "Domain Admins"
      target: true
```

### **Attack Simulation Timeline**

```python
# Lateral movement attack simulation timeline
attack_timeline = {
    "00:00": {
        "phase": "Initial Compromise",
        "action": "Phishing email opens malware on WS01",
        "system": "ws01.sevenkingdoms.local",
        "user": "jon.snow",
        "artifacts": ["malware execution", "C2 beacon"]
    },
    
    "00:15": {
        "phase": "Credential Dumping",
        "action": "Mimikatz execution to dump credentials",
        "system": "ws01.sevenkingdoms.local",
        "artifacts": ["LSASS access", "credential extraction", "hash collection"]
    },
    
    "00:30": {
        "phase": "Network Discovery",
        "action": "Network enumeration and service discovery",
        "system": "ws01.sevenkingdoms.local",
        "artifacts": ["port scans", "SMB enumeration", "user enumeration"]
    },
    
    "00:45": {
        "phase": "Lateral Movement - Pass-the-Hash",
        "action": "PTH attack to access file server",
        "source": "ws01.sevenkingdoms.local",
        "target": "kingslanding.sevenkingdoms.local",
        "artifacts": ["NTLM authentication", "SMB connections", "service creation"]
    },
    
    "01:00": {
        "phase": "Persistence",
        "action": "Create scheduled task for persistence",
        "system": "kingslanding.sevenkingdoms.local",
        "artifacts": ["scheduled task creation", "registry modifications"]
    },
    
    "01:15": {
        "phase": "Privilege Escalation",
        "action": "Exploit service account to gain higher privileges",
        "system": "kingslanding.sevenkingdoms.local",
        "artifacts": ["service exploitation", "token manipulation"]
    },
    
    "01:30": {
        "phase": "Domain Controller Access",
        "action": "DCSync attack to extract domain credentials",
        "source": "kingslanding.sevenkingdoms.local",
        "target": "winterfell.sevenkingdoms.local",
        "artifacts": ["DCSync requests", "domain credential extraction"]
    }
}
```

## ⚔️ Attack Execution

### **Phase 1: Initial Compromise and Credential Dumping**

```powershell
# Simulate initial compromise and credential dumping
# This generates the initial detection events

# Simulate malware execution (logged by Sysmon)
Start-Process -FilePath "C:\Windows\System32\cmd.exe" -ArgumentList "/c echo Simulated malware execution" -WindowStyle Hidden

# Simulate Mimikatz credential dumping
# Note: This is simulation - actual Mimikatz not executed
$MimikatzSimulation = @"
Simulating credential dumping activities:
- LSASS process access
- Memory reading
- Credential extraction
- Hash collection
"@

Write-EventLog -LogName "Application" -Source "SecurityTraining" -EventId 1001 -Message $MimikatzSimulation

# Generate Sysmon events for LSASS access
$SysmonEvent = @{
    EventID = 10
    SourceImage = "C:\Tools\mimikatz.exe"
    TargetImage = "C:\Windows\System32\lsass.exe"
    GrantedAccess = "0x1010"
    CallTrace = "C:\Windows\System32\ntdll.dll+9d4c4"
}

Write-Host "Simulated LSASS access event: $($SysmonEvent | ConvertTo-Json)"
```

### **Phase 2: Network Discovery and Enumeration**

```powershell
# Simulate network discovery activities
# These commands generate network traffic and logs

# Network enumeration
$Targets = @("*************", "*************", "*************")

foreach ($Target in $Targets) {
    # Simulate port scanning
    Test-NetConnection -ComputerName $Target -Port 445 -InformationLevel Quiet
    Test-NetConnection -ComputerName $Target -Port 3389 -InformationLevel Quiet
    Test-NetConnection -ComputerName $Target -Port 135 -InformationLevel Quiet
    
    # Simulate SMB enumeration
    try {
        Get-SmbShare -CimSession $Target -ErrorAction SilentlyContinue
    } catch {
        Write-Host "SMB enumeration attempted on $Target"
    }
}

# Simulate user enumeration
net user /domain
net group "Domain Admins" /domain
net group "Enterprise Admins" /domain
```

### **Phase 3: Lateral Movement Execution**

```powershell
# Simulate lateral movement techniques
# This generates the key detection events for analysis

# Simulate Pass-the-Hash attack
$PTHSimulation = @{
    Technique = "Pass-the-Hash"
    SourceSystem = "ws01.sevenkingdoms.local"
    TargetSystem = "kingslanding.sevenkingdoms.local"
    User = "jon.snow"
    Hash = "aad3b435b51404eeaad3b435b51404ee:8846f7eaee8fb117ad06bdd830b7586c"
    Method = "SMB + Service Creation"
}

Write-EventLog -LogName "Application" -Source "SecurityTraining" -EventId 1002 -Message ($PTHSimulation | ConvertTo-Json)

# Simulate PsExec-style lateral movement
$PsExecSimulation = @{
    Technique = "PsExec"
    ServiceName = "PSEXESVC"
    TargetSystem = "kingslanding.sevenkingdoms.local"
    ExecutedCommand = "cmd.exe /c whoami"
    PipeName = "\\.\pipe\PSEXESVC"
}

Write-EventLog -LogName "Application" -Source "SecurityTraining" -EventId 1003 -Message ($PsExecSimulation | ConvertTo-Json)

# Simulate WMI lateral movement
$WMISimulation = @{
    Technique = "WMI"
    TargetSystem = "dragonstone.sevenkingdoms.local"
    WMIClass = "Win32_Process"
    Method = "Create"
    Command = "powershell.exe -enc <base64_encoded_command>"
}

Write-EventLog -LogName "Application" -Source "SecurityTraining" -EventId 1004 -Message ($WMISimulation | ConvertTo-Json)
```

## 🔍 Detection and Analysis Tasks

### **Task 1: Authentication Pattern Analysis (60 minutes)**

#### **Splunk Queries for Authentication Analysis**

```sql
-- Query 1: Detect Pass-the-Hash indicators
index=windows EventCode=4624 Logon_Type=3 
| where Authentication_Package="NTLM" AND Key_Length=0
| stats count by Account_Name, Workstation_Name, Source_Network_Address
| where count > 5
| eval risk_score = count * 2

-- Query 2: Identify unusual authentication patterns
index=windows EventCode=4624 
| bucket _time span=5m
| stats dc(Workstation_Name) as unique_systems by _time, Account_Name
| where unique_systems > 3
| eval lateral_movement_indicator = "Multiple systems accessed rapidly"

-- Query 3: Detect service account abuse
index=windows EventCode=4624 Logon_Type=5
| stats count by Account_Name, Workstation_Name
| where count > 10 AND match(Account_Name, "(?i)svc_.*")

-- Query 4: Correlate with process creation
index=windows (EventCode=4624 OR EventCode=4688)
| transaction Account_Name startswith=eval(EventCode=4624) endswith=eval(EventCode=4688) maxspan=5m
| where duration > 0
| table _time, Account_Name, Workstation_Name, Process_Name, Process_Command_Line
```

### **Task 2: Network Traffic Analysis (45 minutes)**

#### **Velociraptor Hunts for Lateral Movement**

```yaml
# Velociraptor hunt for lateral movement indicators
name: Windows.Detection.LateralMovement
description: Hunt for lateral movement indicators across GOAD environment

parameters:
  - name: TimeRange
    default: "24h"
    description: Time range to search

sources:
  - query: |
      -- Hunt for SMB connections indicating lateral movement
      SELECT timestamp, source_ip, dest_ip, dest_port, process_name
      FROM source(artifact="Windows.Network.Netstat")
      WHERE dest_port = 445 
        AND source_ip != dest_ip
        AND timestamp > now() - parse_duration(arg=TimeRange)
      
      -- Hunt for service creation events
      UNION ALL
      SELECT timestamp, "" as source_ip, "" as dest_ip, 0 as dest_port, service_name as process_name
      FROM source(artifact="Windows.EventLogs.ServiceCreation")
      WHERE service_name =~ "PSEXESVC|ADMIN\\$|C\\$"
        AND timestamp > now() - parse_duration(arg=TimeRange)
      
      -- Hunt for WMI process creation
      UNION ALL  
      SELECT timestamp, "" as source_ip, "" as dest_ip, 0 as dest_port, process_name
      FROM source(artifact="Windows.Events.ProcessCreation")
      WHERE parent_process_name =~ "(?i)wmiprvse.exe"
        AND timestamp > now() - parse_duration(arg=TimeRange)
```

#### **Network Traffic Analysis with Wireshark**

```bash
# Wireshark display filters for lateral movement detection

# Filter 1: SMB traffic analysis
smb2 || smb

# Filter 2: RPC/DCE-RPC traffic (often used in lateral movement)
dcerpc

# Filter 3: Kerberos authentication
kerberos

# Filter 4: NTLM authentication
ntlmssp

# Filter 5: Suspicious port activity
tcp.port in {135,139,445,3389,5985,5986}

# Filter 6: Large data transfers (potential data staging)
tcp.len > 1460 and ip.src == *************
```

### **Task 3: Endpoint Behavior Analysis (45 minutes)**

#### **Sysmon Event Analysis**

```powershell
# PowerShell script to analyze Sysmon events for lateral movement

# Function to analyze process creation events
function Analyze-ProcessCreation {
    $SuspiciousProcesses = Get-WinEvent -FilterHashtable @{LogName='Microsoft-Windows-Sysmon/Operational'; ID=1} |
        Where-Object {
            $_.Message -match "(psexec|wmic|powershell.*-enc|cmd.*\\\\.+\\.*)" -or
            $_.Message -match "parent.*wmiprvse\.exe" -or
            $_.Message -match "services\.exe.*psexesvc"
        } |
        Select-Object TimeCreated, Id, @{Name="ProcessInfo";Expression={
            if ($_.Message -match "Image: (.+)") { $matches[1] }
        }}, @{Name="CommandLine";Expression={
            if ($_.Message -match "CommandLine: (.+)") { $matches[1] }
        }}
    
    return $SuspiciousProcesses
}

# Function to analyze network connections
function Analyze-NetworkConnections {
    $SuspiciousConnections = Get-WinEvent -FilterHashtable @{LogName='Microsoft-Windows-Sysmon/Operational'; ID=3} |
        Where-Object {
            $_.Message -match "DestinationPort: (445|135|3389|5985)" -and
            $_.Message -notmatch "SourceIp: 127\.0\.0\.1"
        } |
        Select-Object TimeCreated, @{Name="ProcessInfo";Expression={
            if ($_.Message -match "Image: (.+)") { $matches[1] }
        }}, @{Name="Connection";Expression={
            $SourceIP = if ($_.Message -match "SourceIp: (.+)") { $matches[1] }
            $DestIP = if ($_.Message -match "DestinationIp: (.+)") { $matches[1] }
            $DestPort = if ($_.Message -match "DestinationPort: (.+)") { $matches[1] }
            "$SourceIP -> $DestIP`:$DestPort"
        }}
    
    return $SuspiciousConnections
}

# Function to analyze file access patterns
function Analyze-FileAccess {
    $SuspiciousFileAccess = Get-WinEvent -FilterHashtable @{LogName='Microsoft-Windows-Sysmon/Operational'; ID=11} |
        Where-Object {
            $_.Message -match "TargetFilename.*\\\\.*\\\\.*(ADMIN\$|C\$|IPC\$)" -or
            $_.Message -match "TargetFilename.*\\\\.*\\.*\\.*exe"
        } |
        Select-Object TimeCreated, @{Name="ProcessInfo";Expression={
            if ($_.Message -match "Image: (.+)") { $matches[1] }
        }}, @{Name="TargetFile";Expression={
            if ($_.Message -match "TargetFilename: (.+)") { $matches[1] }
        }}
    
    return $SuspiciousFileAccess
}

# Execute analysis functions
$ProcessEvents = Analyze-ProcessCreation
$NetworkEvents = Analyze-NetworkConnections  
$FileEvents = Analyze-FileAccess

Write-Host "Suspicious Process Creation Events: $($ProcessEvents.Count)"
Write-Host "Suspicious Network Connection Events: $($NetworkEvents.Count)"
Write-Host "Suspicious File Access Events: $($FileEvents.Count)"
```

## 📊 Investigation Questions

### **Timeline Reconstruction**
1. **What was the initial compromise vector?**
   - Analyze earliest suspicious events
   - Identify patient zero system
   - Determine attack entry point

2. **How did the attacker move laterally through the network?**
   - Map system-to-system movement
   - Identify techniques used
   - Document credential reuse patterns

3. **What credentials were compromised and reused?**
   - Identify dumped credentials
   - Track credential usage across systems
   - Assess scope of compromise

### **Technical Analysis**
1. **What lateral movement techniques were employed?**
   - Pass-the-Hash indicators
   - Remote execution methods
   - Service abuse patterns

2. **Which systems were compromised in the attack path?**
   - Create network diagram of compromise
   - Identify high-value targets
   - Assess business impact

3. **What persistence mechanisms were established?**
   - Scheduled tasks
   - Service installations
   - Registry modifications

## 🛡️ Detection Rule Development

### **Advanced Detection Rules**

```sql
-- Splunk: Comprehensive lateral movement detection
index=windows 
| eval lateral_movement_score = 0
| eval lateral_movement_score = if(match(_raw, "(?i)(psexec|wmic.*process|powershell.*-enc)"), lateral_movement_score + 20, lateral_movement_score)
| eval lateral_movement_score = if(EventCode=4624 AND Logon_Type=3 AND Authentication_Package="NTLM" AND Key_Length=0, lateral_movement_score + 15, lateral_movement_score)
| eval lateral_movement_score = if(EventCode=7045 AND match(Service_Name, "(?i)(psexesvc|admin\$)"), lateral_movement_score + 25, lateral_movement_score)
| eval lateral_movement_score = if(EventCode=5156 AND Destination_Port IN (445,135,3389), lateral_movement_score + 10, lateral_movement_score)
| where lateral_movement_score > 30
| stats sum(lateral_movement_score) as total_score, values(EventCode) as event_codes by Account_Name, Workstation_Name
| where total_score > 50
| sort - total_score
```

### **Sigma Rules for Lateral Movement**

```yaml
title: Lateral Movement via PsExec
id: 42b21a2c-7d5e-4c8e-9f1a-8d7c6b5a4e3d
description: Detects lateral movement using PsExec-like tools
author: GOAD-Blue Team
date: 2024/01/15
references:
    - https://attack.mitre.org/techniques/T1021/002/
logsource:
    product: windows
    service: system
detection:
    selection1:
        EventID: 7045
        ServiceName: 'PSEXESVC'
    selection2:
        EventID: 4624
        LogonType: 3
        ProcessName: '*\psexesvc.exe'
    condition: selection1 or selection2
falsepositives:
    - Legitimate administrative tools
    - Authorized remote administration
level: high
tags:
    - attack.lateral_movement
    - attack.t1021.002
```

## 📋 Scenario Assessment

### **Deliverables**
1. **Attack Timeline** (25 points)
   - Complete chronological reconstruction
   - System-to-system movement mapping
   - Technique identification and analysis

2. **Technical Analysis Report** (30 points)
   - Lateral movement technique analysis
   - Credential compromise assessment
   - Network traffic analysis findings

3. **Detection Rules** (25 points)
   - Custom detection rule development
   - False positive analysis
   - Performance optimization

4. **Response Recommendations** (20 points)
   - Immediate containment actions
   - Long-term security improvements
   - Monitoring enhancements

### **Assessment Rubric**

| Criterion | Weight | Excellent | Good | Satisfactory | Needs Improvement |
|-----------|--------|-----------|------|--------------|-------------------|
| **Timeline Accuracy** | 25% | Complete and accurate timeline | Minor gaps | Some inaccuracies | Major gaps |
| **Technique Identification** | 25% | All techniques identified | Most identified | Some missed | Many missed |
| **Evidence Correlation** | 25% | Strong correlation across sources | Good correlation | Some correlation | Weak correlation |
| **Detection Quality** | 25% | High-quality, tuned rules | Good rules | Basic rules | Poor rules |

---

!!! tip "Investigation Tips"
    - Focus on authentication events and their patterns
    - Correlate network traffic with endpoint events
    - Pay attention to timing and frequency of activities
    - Use multiple data sources for comprehensive analysis
    - Consider both successful and failed lateral movement attempts

!!! warning "Analysis Challenges"
    - Legitimate administrative activity can mask attacks
    - Encrypted traffic may hide lateral movement
    - Attackers may use living-off-the-land techniques
    - Time synchronization issues can complicate timeline reconstruction
    - Large volumes of data require efficient filtering

!!! info "Advanced Techniques"
    - Behavioral analysis for anomaly detection
    - Machine learning for pattern recognition
    - Graph analysis for attack path visualization
    - Threat intelligence integration for attribution
    - Automated response and containment
