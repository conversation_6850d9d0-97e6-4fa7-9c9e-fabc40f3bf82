@echo off
REM GOAD-Blue Windows Launcher Script

echo.
echo ====================================================
echo 🛡️  GOAD-Blue: Full-Spectrum Cybersecurity Training Platform 🛡️
echo ====================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if we're in the correct directory
if not exist "goad-blue.py" (
    echo [ERROR] goad-blue.py not found
    echo Please run this script from the GOAD-Blue directory
    pause
    exit /b 1
)

REM Run GOAD-Blue
python goad-blue.py %*

REM Pause if no arguments were provided (interactive mode)
if "%~1"=="" pause
