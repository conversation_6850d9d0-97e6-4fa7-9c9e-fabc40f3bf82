---
# GOAD-Blue Main Site Playbook
# Orchestrates deployment of all blue team components

- name: Deploy GOAD-Blue Infrastructure
  hosts: localhost
  gather_facts: false
  vars_files:
    - vars/goad-blue-config.yml
  
  tasks:
    - name: Display GOAD-Blue banner
      debug:
        msg: |
          ====================================================
          🛡️  GOAD-Blue Deployment Starting 🛡️
          ====================================================
          Lab: {{ goad_blue_config.goad_blue.name }}
          Provider: {{ goad_blue_config.goad_blue.provider }}
          SIEM: {{ goad_blue_config.siem.type }}
          ====================================================

    - name: Validate configuration
      include_tasks: tasks/validate_config.yml

    - name: Generate dynamic inventory
      include_tasks: tasks/generate_inventory.yml

# Deploy SIEM components
- name: Deploy SIEM Infrastructure
  import_playbook: goad-blue-siem.yml
  when: goad_blue_config.siem.enabled | default(false)

# Deploy monitoring components
- name: Deploy Security Onion
  import_playbook: goad-blue-security-onion.yml
  when: goad_blue_config.components.security_onion.enabled | default(false)

- name: Deploy Malcolm
  import_playbook: goad-blue-malcolm.yml
  when: goad_blue_config.components.malcolm.enabled | default(false)

- name: Deploy Velociraptor
  import_playbook: goad-blue-velociraptor.yml
  when: goad_blue_config.components.velociraptor.enabled | default(false)

# Deploy analysis components
- name: Deploy MISP
  import_playbook: goad-blue-misp.yml
  when: goad_blue_config.components.misp.enabled | default(false)

- name: Configure FLARE-VM
  import_playbook: goad-blue-flare-vm.yml
  when: goad_blue_config.components.flare_vm.enabled | default(false)

# Integration and configuration
- name: GOAD Integration
  import_playbook: goad-blue-integration.yml
  when: goad_blue_config.integration.goad_enabled | default(false)

# Final configuration and validation
- name: Final Configuration
  hosts: localhost
  gather_facts: false
  vars_files:
    - vars/goad-blue-config.yml
  
  tasks:
    - name: Configure inter-component communication
      include_tasks: tasks/configure_component_integration.yml

    - name: Create dashboards and visualizations
      include_tasks: tasks/create_dashboards.yml

    - name: Validate deployment
      include_tasks: tasks/validate_deployment.yml

    - name: Generate access information
      include_tasks: tasks/generate_access_info.yml

    - name: Display completion message
      debug:
        msg: |
          ====================================================
          🎉 GOAD-Blue Deployment Complete! 🎉
          ====================================================
          Access information saved to: {{ playbook_dir }}/output/access_info.yml
          
          Next steps:
          1. Access web interfaces using provided URLs
          2. Run GOAD attack simulations
          3. Monitor detections in SIEM
          4. Practice incident response
          ====================================================
