# SIEM Platforms

The Security Information and Event Management (SIEM) platform serves as the central nervous system of GOAD-Blue, collecting, analyzing, and correlating security events from all components.

## 🎯 SIEM Platform Options

GOAD-Blue supports two enterprise-grade SIEM platforms:

### **Splunk Enterprise** (Recommended)
- **Industry Standard**: Widely used in enterprise environments
- **Rich Ecosystem**: Extensive app marketplace and community
- **Advanced Analytics**: Built-in machine learning and statistical analysis
- **Mature Platform**: Proven scalability and reliability

### **Elastic Stack (ELK)**
- **Open Source**: No licensing costs for core features
- **High Performance**: Excellent search and aggregation capabilities
- **Flexible**: Highly customizable and extensible
- **Modern Architecture**: Cloud-native and container-friendly

## 📊 SIEM Architecture

```mermaid
graph TB
    subgraph "📥 Data Sources"
        GOAD[🎯 GOAD Environment<br/>Windows Event Logs]
        NETWORK[🌐 Network Monitoring<br/>Suricata, Zeek Logs]
        ENDPOINT[💻 Endpoint Agents<br/>Velociraptor, <PERSON>ysmon]
        INTEL[🧠 Threat Intelligence<br/>MISP IOCs]
    end
    
    subgraph "🚚 Collection Layer"
        UF[📦 Universal Forwarders<br/>Splunk Agents]
        BEATS[🥁 Elastic Beats<br/>Log Shippers]
        API[🔌 API Collectors<br/>Custom Integrations]
    end
    
    subgraph "⚙️ Processing Layer"
        INDEXERS[📚 Indexers/Nodes<br/>Data Processing]
        PARSERS[📋 Parsers<br/>Log Parsing]
        ENRICHERS[✨ Enrichers<br/>Data Enhancement]
    end
    
    subgraph "🔍 Search Layer"
        SEARCH_HEADS[🔍 Search Heads<br/>Query Processing]
        KIBANA[📊 Kibana<br/>Visualization]
    end
    
    subgraph "📈 Presentation Layer"
        DASHBOARDS[📈 Dashboards<br/>Real-time Views]
        ALERTS[🚨 Alerts<br/>Automated Detection]
        REPORTS[📄 Reports<br/>Scheduled Analysis]
    end
    
    %% Data Flow
    GOAD --> UF
    NETWORK --> BEATS
    ENDPOINT --> API
    INTEL --> UF
    
    UF --> INDEXERS
    BEATS --> INDEXERS
    API --> INDEXERS
    
    INDEXERS --> PARSERS
    PARSERS --> ENRICHERS
    ENRICHERS --> SEARCH_HEADS
    ENRICHERS --> KIBANA
    
    SEARCH_HEADS --> DASHBOARDS
    SEARCH_HEADS --> ALERTS
    SEARCH_HEADS --> REPORTS
    KIBANA --> DASHBOARDS
    
    %% Styling
    classDef sources fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef collection fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef processing fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef search fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef presentation fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    
    class GOAD,NETWORK,ENDPOINT,INTEL sources
    class UF,BEATS,API collection
    class INDEXERS,PARSERS,ENRICHERS processing
    class SEARCH_HEADS,KIBANA search
    class DASHBOARDS,ALERTS,REPORTS presentation
```

## 🔧 SIEM Selection Criteria

### **Choose Splunk Enterprise When:**
- ✅ **Enterprise Environment**: Deploying in corporate setting
- ✅ **Compliance Requirements**: Need for audit trails and reporting
- ✅ **Advanced Analytics**: Require ML and statistical analysis
- ✅ **Vendor Support**: Need commercial support and SLAs
- ✅ **Existing Investment**: Organization already uses Splunk

### **Choose Elastic Stack When:**
- ✅ **Cost Sensitivity**: Budget constraints or open-source preference
- ✅ **High Volume**: Need to process very large data volumes
- ✅ **Custom Development**: Require extensive customization
- ✅ **Cloud Native**: Deploying in containerized environments
- ✅ **Performance Focus**: Need maximum search performance

## 📋 Feature Comparison

| Feature | Splunk Enterprise | Elastic Stack |
|---------|------------------|---------------|
| **Licensing** | Commercial | Open Source Core |
| **Learning Curve** | Moderate | Steep |
| **Search Language** | SPL (Splunk Processing Language) | KQL (Kibana Query Language) |
| **Machine Learning** | Built-in MLTK | X-Pack ML (Commercial) |
| **Apps/Plugins** | Extensive Splunkbase | Growing ecosystem |
| **Scalability** | Excellent | Excellent |
| **Community** | Large enterprise | Large developer |
| **Documentation** | Comprehensive | Good |
| **Support** | Commercial | Community/Commercial |

## 🎯 GOAD-Blue SIEM Integration

### **Pre-configured Components**

Both SIEM platforms come pre-configured with:

#### **Data Sources**
- **Windows Event Logs** from GOAD VMs
- **Sysmon Events** for detailed process monitoring
- **Network Logs** from Security Onion/Malcolm
- **Endpoint Data** from Velociraptor
- **Threat Intelligence** from MISP

#### **Indexes/Indices**
- `goad_blue_windows` - Windows Event Logs
- `goad_blue_network` - Network monitoring data
- `goad_blue_endpoint` - Endpoint detection data
- `goad_blue_threat_intel` - Threat intelligence data

#### **Dashboards**
- **Executive Overview** - High-level security metrics
- **SOC Operations** - Real-time monitoring dashboard
- **Incident Response** - Investigation workspace
- **Threat Hunting** - Advanced analysis tools
- **Compliance** - Audit and compliance reporting

#### **Detection Rules**
- **Kerberoasting Detection** - Service ticket abuse
- **Lateral Movement** - Unusual authentication patterns
- **Privilege Escalation** - Administrative access changes
- **Data Exfiltration** - Large data transfers
- **Malware Activity** - Suspicious process behavior

### **Custom GOAD-Blue Apps**

#### **Splunk Apps**
- **GOAD-Blue Security Essentials** - Core security monitoring
- **GOAD-Blue Threat Hunting** - Advanced hunting capabilities
- **GOAD-Blue Compliance** - Audit and compliance tools
- **GOAD-Blue Integration** - MISP and external tool integration

#### **Elastic Integrations**
- **GOAD-Blue Security** - Security monitoring dashboards
- **GOAD-Blue Network** - Network analysis tools
- **GOAD-Blue Endpoint** - Endpoint monitoring views
- **GOAD-Blue Intelligence** - Threat intelligence integration

## 🔍 Search and Analysis

### **Common Search Patterns**

#### **Splunk SPL Examples**
```spl
# Detect Kerberoasting attempts
index=goad_blue_windows EventCode=4769 Ticket_Encryption_Type="0x17"
| stats count by Account_Name, Service_Name
| where count > 10

# Identify lateral movement
index=goad_blue_windows EventCode=4624 Logon_Type=3
| eval src_dest=src_ip + "->" + dest_ip
| stats count by src_dest, Account_Name
| where count > 5

# Hunt for suspicious PowerShell
index=goad_blue_windows EventCode=4688 Process_Name="*powershell*"
| rex field=Process_Command_Line "(?<encoded_command>-e[nc]* (?<encoded>[A-Za-z0-9+/=]+))"
| where isnotnull(encoded_command)
```

#### **Elastic KQL Examples**
```json
# Detect failed login attempts
{
  "query": {
    "bool": {
      "must": [
        {"term": {"winlog.event_id": 4625}},
        {"range": {"@timestamp": {"gte": "now-1h"}}}
      ]
    }
  },
  "aggs": {
    "failed_logins": {
      "terms": {"field": "winlog.event_data.TargetUserName"}
    }
  }
}

# Network connection analysis
{
  "query": {
    "bool": {
      "must": [
        {"term": {"event.dataset": "zeek.conn"}},
        {"range": {"zeek.conn.duration": {"gte": 300}}}
      ]
    }
  }
}
```

## 📊 Performance Optimization

### **Splunk Optimization**

#### **Index Configuration**
```conf
# indexes.conf
[goad_blue_windows]
homePath = $SPLUNK_DB/goad_blue_windows/db
coldPath = $SPLUNK_DB/goad_blue_windows/colddb
thawedPath = $SPLUNK_DB/goad_blue_windows/thaweddb
maxDataSize = 500MB
maxHotBuckets = 3
maxWarmDBCount = 20
```

#### **Search Optimization**
```spl
# Use time ranges
index=goad_blue_windows earliest=-1h latest=now

# Limit fields
index=goad_blue_windows | fields _time, EventCode, Computer_Name

# Use summary indexing for frequent searches
| collect index=summary_index source="daily_stats"
```

### **Elastic Optimization**

#### **Index Templates**
```json
{
  "index_patterns": ["goad-blue-*"],
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "refresh_interval": "30s"
  },
  "mappings": {
    "properties": {
      "@timestamp": {"type": "date"},
      "event.action": {"type": "keyword"},
      "host.name": {"type": "keyword"}
    }
  }
}
```

#### **Query Optimization**
```json
# Use filters instead of queries when possible
{
  "query": {
    "bool": {
      "filter": [
        {"term": {"event.dataset": "winlog"}},
        {"range": {"@timestamp": {"gte": "now-1h"}}}
      ]
    }
  }
}
```

## 🚨 Alerting and Response

### **Alert Categories**

#### **Critical Alerts**
- **Privilege Escalation** - Administrative access gained
- **Lateral Movement** - Unusual authentication patterns
- **Data Exfiltration** - Large data transfers
- **Malware Detection** - Known malicious activity

#### **High Priority Alerts**
- **Failed Authentication** - Multiple failed logins
- **Suspicious PowerShell** - Encoded commands
- **Network Anomalies** - Unusual traffic patterns
- **File Modifications** - Critical system files changed

#### **Medium Priority Alerts**
- **Policy Violations** - Security policy breaches
- **Configuration Changes** - System configuration modifications
- **User Behavior** - Unusual user activity
- **Performance Issues** - System performance degradation

### **Response Actions**

#### **Automated Responses**
- **Account Lockout** - Disable compromised accounts
- **Network Isolation** - Block suspicious IP addresses
- **Process Termination** - Kill malicious processes
- **Evidence Collection** - Gather forensic artifacts

#### **Manual Responses**
- **Investigation** - Detailed analysis of alerts
- **Containment** - Isolate affected systems
- **Eradication** - Remove threats and vulnerabilities
- **Recovery** - Restore normal operations

---

!!! info "Platform-Specific Guides"
    For detailed configuration and usage instructions, see:
    
    - [Splunk Enterprise Guide](splunk.md)
    - [Elastic Stack Guide](elastic.md)

!!! tip "SIEM Selection"
    Start with Splunk if you're new to SIEM platforms, as it has better documentation and learning resources. Consider Elastic for high-volume environments or when cost is a primary concern.

!!! warning "Resource Requirements"
    Both SIEM platforms require significant resources. Monitor performance and scale appropriately based on your data volume and retention requirements.
