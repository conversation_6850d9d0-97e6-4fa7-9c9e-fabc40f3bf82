# Proxmox Provider Outputs

# Module Outputs (pass-through from module)
output "splunk_vm_id" {
  description = "Splunk Enterprise VM ID"
  value       = module.goad_blue.splunk_vm_id
}

output "splunk_vm_name" {
  description = "Splunk Enterprise VM name"
  value       = module.goad_blue.splunk_vm_name
}

output "splunk_ip_address" {
  description = "Splunk Enterprise IP address"
  value       = module.goad_blue.splunk_ip_address
}

output "splunk_web_url" {
  description = "Splunk Web interface URL"
  value       = module.goad_blue.splunk_web_url
}

output "security_onion_manager_vm_id" {
  description = "Security Onion Manager VM ID"
  value       = module.goad_blue.security_onion_manager_vm_id
}

output "security_onion_manager_ip" {
  description = "Security Onion Manager IP address"
  value       = module.goad_blue.security_onion_manager_ip
}

output "security_onion_web_url" {
  description = "Security Onion web interface URL"
  value       = module.goad_blue.security_onion_web_url
}

output "security_onion_sensor_vm_ids" {
  description = "Security Onion Sensor VM IDs"
  value       = module.goad_blue.security_onion_sensor_vm_ids
}

output "security_onion_sensor_ips" {
  description = "Security Onion Sensor IP addresses"
  value       = module.goad_blue.security_onion_sensor_ips
}

output "velociraptor_vm_id" {
  description = "Velociraptor Server VM ID"
  value       = module.goad_blue.velociraptor_vm_id
}

output "velociraptor_ip_address" {
  description = "Velociraptor Server IP address"
  value       = module.goad_blue.velociraptor_ip_address
}

output "velociraptor_web_url" {
  description = "Velociraptor web interface URL"
  value       = module.goad_blue.velociraptor_web_url
}

output "misp_vm_id" {
  description = "MISP Server VM ID"
  value       = module.goad_blue.misp_vm_id
}

output "misp_ip_address" {
  description = "MISP Server IP address"
  value       = module.goad_blue.misp_ip_address
}

output "misp_web_url" {
  description = "MISP web interface URL"
  value       = module.goad_blue.misp_web_url
}

# Network Information
output "goad_blue_network" {
  description = "GOAD-Blue network configuration"
  value       = module.goad_blue.goad_blue_network
}

output "goad_network" {
  description = "GOAD network configuration"
  value       = module.goad_blue.goad_network
}

# Comprehensive VM Summary
output "vm_summary" {
  description = "Summary of all deployed VMs"
  value       = module.goad_blue.vm_summary
}

# Resource Summary
output "resource_summary" {
  description = "Summary of allocated resources"
  value       = module.goad_blue.resource_summary
}

# Access Information
output "access_information" {
  description = "Access information for all services"
  value       = module.goad_blue.access_information
  sensitive   = true
}

# Deployment Status
output "deployment_status" {
  description = "Deployment status of all components"
  value       = module.goad_blue.deployment_status
}

# Proxmox-Specific Information
output "proxmox_cluster_info" {
  description = "Proxmox cluster information"
  value = {
    api_url     = var.proxmox_api_url
    target_node = var.target_node
    user        = var.proxmox_user
  }
  sensitive = true
}

output "network_configuration" {
  description = "Network configuration details"
  value = {
    bridges = var.network_bridges
    vlans   = var.network_vlans
    cidrs   = var.network_cidrs
    gateway = var.network_gateway
    dns     = var.dns_servers
  }
}

output "storage_configuration" {
  description = "Storage configuration details"
  value = {
    pools       = var.storage_pools
    disk_format = var.disk_format
    ssd_enabled = var.enable_ssd
  }
}

# High Availability Information
output "ha_configuration" {
  description = "High Availability configuration"
  value = var.enable_ha ? {
    enabled      = var.enable_ha
    nofailback   = var.ha_nofailback
    max_restart  = var.ha_max_restart
    max_relocate = var.ha_max_relocate
  } : null
}

# Backup Configuration
output "backup_configuration" {
  description = "Backup configuration details"
  value = var.backup_schedule.enabled ? {
    schedule = var.backup_schedule.schedule
    storage  = var.backup_schedule.storage
    compress = var.backup_schedule.compress
  } : null
}

# Quick Access URLs
output "quick_access_urls" {
  description = "Quick access URLs for all services"
  value = {
    splunk_web = var.deploy_components.splunk ? "https://${var.vm_ip_addresses.splunk}:8000" : null
    security_onion = var.deploy_components.security_onion ? "https://${var.vm_ip_addresses.security_onion_manager}" : null
    velociraptor = var.deploy_components.velociraptor ? "https://${var.vm_ip_addresses.velociraptor}:8889" : null
    misp = var.deploy_components.misp ? "https://${var.vm_ip_addresses.misp}" : null
    proxmox_web = var.proxmox_api_url
  }
}

# SSH Connection Commands
output "ssh_commands" {
  description = "SSH connection commands for all VMs"
  value = {
    splunk = var.deploy_components.splunk ? "ssh ${var.vm_user}@${var.vm_ip_addresses.splunk}" : null
    security_onion_manager = var.deploy_components.security_onion ? "ssh ${var.vm_user}@${var.vm_ip_addresses.security_onion_manager}" : null
    velociraptor = var.deploy_components.velociraptor ? "ssh ${var.vm_user}@${var.vm_ip_addresses.velociraptor}" : null
    misp = var.deploy_components.misp ? "ssh ${var.vm_user}@${var.vm_ip_addresses.misp}" : null
  }
}

# Ansible Inventory Information
output "ansible_inventory" {
  description = "Ansible inventory information"
  value = {
    splunk = var.deploy_components.splunk ? {
      ansible_host = var.vm_ip_addresses.splunk
      ansible_user = var.vm_user
      component    = "splunk"
    } : null
    security_onion_manager = var.deploy_components.security_onion ? {
      ansible_host = var.vm_ip_addresses.security_onion_manager
      ansible_user = var.vm_user
      component    = "security-onion-manager"
    } : null
    velociraptor = var.deploy_components.velociraptor ? {
      ansible_host = var.vm_ip_addresses.velociraptor
      ansible_user = var.vm_user
      component    = "velociraptor"
    } : null
    misp = var.deploy_components.misp ? {
      ansible_host = var.vm_ip_addresses.misp
      ansible_user = var.vm_user
      component    = "misp"
    } : null
  }
}

# Next Steps Information
output "next_steps" {
  description = "Next steps after deployment"
  value = [
    "1. Wait for all VMs to complete cloud-init setup",
    "2. Verify SSH connectivity to all VMs",
    "3. Run Ansible playbooks for service configuration:",
    "   ansible-playbook -i inventory/proxmox.yml playbooks/proxmox/site.yml",
    "4. Access web interfaces using the provided URLs",
    "5. Configure GOAD integration using the integration scripts",
    "6. Test data flow between GOAD and GOAD-Blue networks"
  ]
}

# Troubleshooting Information
output "troubleshooting" {
  description = "Troubleshooting information"
  value = {
    vm_console_access = "Use Proxmox web interface to access VM consoles"
    network_testing = [
      "ping ${var.network_gateway}",
      "ping ${var.vm_ip_addresses.splunk}",
      "ping ${var.vm_ip_addresses.security_onion_manager}"
    ]
    log_locations = {
      cloud_init = "/var/log/cloud-init-output.log"
      syslog     = "/var/log/syslog"
    }
    common_issues = [
      "Check cloud-init completion: cloud-init status",
      "Verify network connectivity: ip route show",
      "Check DNS resolution: nslookup google.com"
    ]
  }
}
