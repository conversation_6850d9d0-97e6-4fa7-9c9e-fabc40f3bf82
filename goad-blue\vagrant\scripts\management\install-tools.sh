#!/bin/bash
# Management server tools installation for GOAD-Blue

set -e

echo "=== Installing Management Tools for GOAD-Blue ==="

# Update system first
export DEBIAN_FRONTEND=noninteractive
apt-get update -y

# Install essential management tools
echo "Installing essential management tools..."
apt-get install -y \
    ansible \
    terraform \
    packer \
    git \
    curl \
    wget \
    vim \
    htop \
    tree \
    jq \
    yq \
    unzip \
    zip \
    rsync \
    sshpass \
    python3 \
    python3-pip \
    python3-venv \
    python3-dev \
    build-essential \
    libssl-dev \
    libffi-dev \
    net-tools \
    nmap \
    tcpdump \
    wireshark-common \
    tshark \
    dnsutils \
    iputils-ping \
    traceroute \
    telnet \
    nc \
    socat

# Install Docker
echo "Installing Docker..."
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
usermod -aG docker vagrant
usermod -aG docker goad-blue

# Install Docker Compose
echo "Installing Docker Compose..."
DOCKER_COMPOSE_VERSION="2.21.0"
curl -L "https://github.com/docker/compose/releases/download/v${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Install kubectl
echo "Installing kubectl..."
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# Install Helm
echo "Installing Helm..."
curl https://baltocdn.com/helm/signing.asc | gpg --dearmor | tee /usr/share/keyrings/helm.gpg > /dev/null
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/helm.gpg] https://baltocdn.com/helm/stable/debian/ all main" | tee /etc/apt/sources.list.d/helm-stable-debian.list
apt-get update
apt-get install helm -y

# Install Python packages for management
echo "Installing Python packages..."
pip3 install --upgrade pip
pip3 install \
    ansible-core \
    ansible-runner \
    requests \
    pyyaml \
    jinja2 \
    netaddr \
    dnspython \
    paramiko \
    pywinrm \
    pywinrm[kerberos] \
    pywinrm[credssp] \
    boto3 \
    azure-cli \
    google-cloud-sdk \
    kubernetes \
    openshift \
    docker \
    docker-compose \
    splunk-sdk \
    elasticsearch \
    elasticsearch-dsl

# Install Ansible collections
echo "Installing Ansible collections..."
ansible-galaxy collection install \
    community.general \
    community.crypto \
    community.docker \
    community.vmware \
    community.windows \
    ansible.windows \
    kubernetes.core \
    amazon.aws \
    azure.azcollection \
    google.cloud

# Install Vagrant
echo "Installing Vagrant..."
wget -O- https://apt.releases.hashicorp.com/gpg | gpg --dearmor | tee /usr/share/keyrings/hashicorp-archive-keyring.gpg
echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(lsb_release -cs) main" | tee /etc/apt/sources.list.d/hashicorp.list
apt-get update && apt-get install vagrant -y

# Install VirtualBox
echo "Installing VirtualBox..."
wget -q https://www.virtualbox.org/download/oracle_vbox_2016.asc -O- | apt-key add -
echo "deb [arch=amd64] https://download.virtualbox.org/virtualbox/debian $(lsb_release -cs) contrib" | tee /etc/apt/sources.list.d/virtualbox.list
apt-get update
apt-get install virtualbox-7.0 -y

# Install VMware tools (if VMware is detected)
if lspci | grep -i vmware >/dev/null; then
    echo "Installing VMware tools..."
    apt-get install open-vm-tools open-vm-tools-desktop -y
fi

# Install monitoring tools
echo "Installing monitoring tools..."
apt-get install -y \
    htop \
    iotop \
    nethogs \
    iftop \
    vnstat \
    sysstat \
    lsof \
    strace \
    tcpdump \
    ngrep

# Install security tools
echo "Installing security tools..."
apt-get install -y \
    nmap \
    masscan \
    zmap \
    nikto \
    dirb \
    gobuster \
    sqlmap \
    john \
    hashcat \
    hydra \
    medusa \
    metasploit-framework \
    beef-xss \
    burpsuite \
    zaproxy \
    aircrack-ng \
    reaver \
    hashcat-utils

# Install YARA
echo "Installing YARA..."
apt-get install -y yara libyara-dev python3-yara

# Install additional analysis tools
echo "Installing analysis tools..."
pip3 install \
    yara-python \
    pefile \
    python-magic \
    ssdeep \
    pydeep \
    volatility3 \
    capstone \
    unicorn \
    keystone-engine \
    r2pipe \
    angr

# Create GOAD-Blue management directories
echo "Creating GOAD-Blue management directories..."
mkdir -p /opt/goad-blue/{scripts,tools,configs,logs,data,playbooks,inventories}
mkdir -p /opt/goad-blue/tools/{custom,third-party}
mkdir -p /opt/goad-blue/configs/{ansible,terraform,packer}
mkdir -p /opt/goad-blue/data/{exports,imports,backups}

# Install custom GOAD-Blue management tools
echo "Installing custom GOAD-Blue tools..."

# GOAD-Blue CLI tool
cat > /usr/local/bin/goad-blue << 'EOF'
#!/bin/bash
# GOAD-Blue Management CLI

GOAD_BLUE_HOME="/opt/goad-blue"
GOAD_BLUE_CONFIG="$GOAD_BLUE_HOME/configs/goad-blue.yml"

show_help() {
    cat << EOF
GOAD-Blue Management CLI

Usage: goad-blue [COMMAND] [OPTIONS]

Commands:
    status          Show status of all GOAD-Blue components
    deploy          Deploy GOAD-Blue environment
    destroy         Destroy GOAD-Blue environment
    backup          Backup GOAD-Blue configurations and data
    restore         Restore GOAD-Blue from backup
    logs            Show logs from GOAD-Blue components
    monitor         Start monitoring dashboard
    test            Run GOAD-Blue tests
    update          Update GOAD-Blue components
    help            Show this help message

Examples:
    goad-blue status
    goad-blue deploy --environment lab
    goad-blue logs --component splunk
    goad-blue backup --destination /backup/goad-blue

For more information, visit: https://github.com/goad-blue/goad-blue
EOF
}

case "$1" in
    status)
        echo "=== GOAD-Blue Status ==="
        systemctl status splunk --no-pager 2>/dev/null || echo "Splunk: Not installed"
        systemctl status velociraptor --no-pager 2>/dev/null || echo "Velociraptor: Not installed"
        docker ps --format "table {{.Names}}\t{{.Status}}" 2>/dev/null || echo "Docker: Not available"
        ;;
    deploy)
        echo "Deploying GOAD-Blue environment..."
        cd $GOAD_BLUE_HOME
        ansible-playbook -i inventories/hosts.yml playbooks/site.yml
        ;;
    destroy)
        echo "Destroying GOAD-Blue environment..."
        cd $GOAD_BLUE_HOME
        vagrant destroy -f
        ;;
    logs)
        echo "=== GOAD-Blue Logs ==="
        tail -f /var/log/goad-blue/*.log
        ;;
    help|*)
        show_help
        ;;
esac
EOF

chmod +x /usr/local/bin/goad-blue

# Create system monitoring script
cat > /opt/goad-blue/scripts/system-monitor.sh << 'EOF'
#!/bin/bash
# System monitoring script for GOAD-Blue management server

echo "=== GOAD-Blue Management Server Status ==="
echo "Timestamp: $(date)"
echo "Hostname: $(hostname)"
echo "Uptime: $(uptime -p)"
echo ""

echo "=== System Resources ==="
echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "Memory Usage: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')"
echo "Disk Usage: $(df -h / | tail -1 | awk '{print $5}')"
echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"
echo ""

echo "=== Network Interfaces ==="
ip addr show | grep -E '^[0-9]+:|inet ' | sed 's/^[[:space:]]*//'
echo ""

echo "=== Active Services ==="
systemctl list-units --type=service --state=active | grep -E "(splunk|velociraptor|docker|ansible)" || echo "No GOAD-Blue services active"
echo ""

echo "=== Docker Status ==="
if command -v docker >/dev/null 2>&1; then
    docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}"
else
    echo "Docker not installed"
fi
echo ""

echo "=== Vagrant Status ==="
if command -v vagrant >/dev/null 2>&1; then
    cd /opt/goad-blue/vagrant 2>/dev/null && vagrant status || echo "No Vagrant environment found"
else
    echo "Vagrant not installed"
fi
EOF

chmod +x /opt/goad-blue/scripts/system-monitor.sh

# Create backup script
cat > /opt/goad-blue/scripts/backup-goad-blue.sh << 'EOF'
#!/bin/bash
# Backup script for GOAD-Blue

BACKUP_DIR="/opt/goad-blue/data/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="goad-blue-backup-$TIMESTAMP"

echo "=== GOAD-Blue Backup Started ==="
echo "Backup directory: $BACKUP_DIR/$BACKUP_NAME"

mkdir -p "$BACKUP_DIR/$BACKUP_NAME"

# Backup configurations
echo "Backing up configurations..."
cp -r /opt/goad-blue/configs "$BACKUP_DIR/$BACKUP_NAME/"
cp -r /etc/ansible "$BACKUP_DIR/$BACKUP_NAME/ansible-system" 2>/dev/null || true

# Backup Vagrant files
echo "Backing up Vagrant environment..."
cp -r /opt/goad-blue/vagrant "$BACKUP_DIR/$BACKUP_NAME/"

# Backup custom scripts
echo "Backing up custom scripts..."
cp -r /opt/goad-blue/scripts "$BACKUP_DIR/$BACKUP_NAME/"

# Create archive
echo "Creating backup archive..."
cd "$BACKUP_DIR"
tar -czf "$BACKUP_NAME.tar.gz" "$BACKUP_NAME"
rm -rf "$BACKUP_NAME"

echo "=== GOAD-Blue Backup Completed ==="
echo "Backup file: $BACKUP_DIR/$BACKUP_NAME.tar.gz"
echo "Size: $(du -h $BACKUP_DIR/$BACKUP_NAME.tar.gz | cut -f1)"
EOF

chmod +x /opt/goad-blue/scripts/backup-goad-blue.sh

# Set ownership
chown -R goad-blue:goad-blue /opt/goad-blue

# Configure SSH for management
echo "Configuring SSH for management..."
mkdir -p /home/<USER>/.ssh
ssh-keygen -t rsa -b 4096 -f /home/<USER>/.ssh/id_rsa -N "" -C "goad-blue@management"
chown -R goad-blue:goad-blue /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
chmod 600 /home/<USER>/.ssh/id_rsa
chmod 644 /home/<USER>/.ssh/id_rsa.pub

# Create completion marker
touch /opt/goad-blue/.management-tools-installed

echo "=== Management Tools Installation Completed ==="
echo "GOAD-Blue CLI: goad-blue --help"
echo "System Monitor: /opt/goad-blue/scripts/system-monitor.sh"
echo "Backup Tool: /opt/goad-blue/scripts/backup-goad-blue.sh"
echo "SSH Key: /home/<USER>/.ssh/id_rsa.pub"
echo ""
echo "Available tools:"
echo "- Ansible: $(ansible --version | head -1)"
echo "- Terraform: $(terraform version | head -1)"
echo "- Docker: $(docker --version)"
echo "- Vagrant: $(vagrant --version)"
echo "- kubectl: $(kubectl version --client --short 2>/dev/null || echo 'kubectl installed')"
