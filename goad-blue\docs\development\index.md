# Development Guide

Welcome to the GOAD-Blue development guide. This section covers contributing to the project, extending functionality, and developing custom components.

## 🚀 Getting Started with Development

### **Development Environment Setup**

```bash
# Clone the repository
git clone https://github.com/your-org/goad-blue.git
cd goad-blue

# Create development environment
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# or
venv\Scripts\activate     # Windows

# Install development dependencies
pip install -r requirements-dev.txt

# Install pre-commit hooks
pre-commit install

# Run tests to verify setup
pytest tests/
```

### **Project Structure**

```
goad-blue/
├── src/                          # Source code
│   ├── goad_blue/               # Main package
│   │   ├── core/                # Core functionality
│   │   ├── components/          # Component implementations
│   │   ├── api/                 # REST API
│   │   └── cli/                 # Command-line interface
│   └── tests/                   # Test suite
├── ansible/                     # Ansible playbooks
├── terraform/                   # Terraform modules
├── packer/                      # Packer templates
├── docs/                        # Documentation
├── scripts/                     # Utility scripts
└── config/                      # Configuration files
```

## 🏗️ Architecture Overview

### **Core Components**

```mermaid
graph TB
    subgraph "🎯 Core Layer"
        CORE[🧠 Core Engine<br/>Main orchestration logic]
        CONFIG[⚙️ Configuration Manager<br/>Settings & validation]
        EVENTS[📡 Event System<br/>Inter-component communication]
    end
    
    subgraph "🔌 Component Layer"
        SIEM[📊 SIEM Components<br/>Splunk, Elastic]
        MONITOR[🔍 Monitoring Components<br/>Security Onion, Malcolm]
        ENDPOINT[💻 Endpoint Components<br/>Velociraptor, Sysmon]
        INTEL[🧠 Intelligence Components<br/>MISP, Feeds]
    end
    
    subgraph "🌐 Interface Layer"
        CLI[🖥️ CLI Interface<br/>Command-line tools]
        API[🔌 REST API<br/>HTTP endpoints]
        WEB[🌐 Web Interface<br/>Management dashboard]
    end
    
    subgraph "🏗️ Infrastructure Layer"
        DEPLOY[🚀 Deployment Engine<br/>Packer, Terraform, Ansible]
        CLOUD[☁️ Cloud Providers<br/>AWS, Azure, GCP]
        VIRT[💻 Virtualization<br/>VMware, VirtualBox]
    end
    
    CLI --> CORE
    API --> CORE
    WEB --> CORE
    
    CORE --> CONFIG
    CORE --> EVENTS
    
    EVENTS --> SIEM
    EVENTS --> MONITOR
    EVENTS --> ENDPOINT
    EVENTS --> INTEL
    
    CORE --> DEPLOY
    DEPLOY --> CLOUD
    DEPLOY --> VIRT
    
    classDef core fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef component fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef interface fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef infrastructure fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class CORE,CONFIG,EVENTS core
    class SIEM,MONITOR,ENDPOINT,INTEL component
    class CLI,API,WEB interface
    class DEPLOY,CLOUD,VIRT infrastructure
```

### **Design Principles**

1. **🔧 Modularity**: Components are loosely coupled and independently deployable
2. **🔌 Extensibility**: Easy to add new components and integrations
3. **📊 Observability**: Comprehensive logging, metrics, and monitoring
4. **🔒 Security**: Security-first design with defense in depth
5. **⚡ Performance**: Optimized for high-volume security data processing
6. **🧪 Testability**: Comprehensive test coverage and CI/CD integration

## 🔧 Component Development

### **Creating a New Component**

#### **1. Component Structure**

```python
# src/goad_blue/components/my_component.py
from goad_blue.core.component import BaseComponent
from goad_blue.core.config import ComponentConfig
from goad_blue.core.events import EventEmitter

class MyComponent(BaseComponent):
    """Custom component for GOAD-Blue."""
    
    def __init__(self, config: ComponentConfig):
        super().__init__(config)
        self.name = "my_component"
        self.version = "1.0.0"
        self.description = "Custom component description"
        
    async def initialize(self) -> bool:
        """Initialize the component."""
        self.logger.info(f"Initializing {self.name}")
        
        # Component-specific initialization
        await self._setup_connections()
        await self._configure_settings()
        
        self.logger.info(f"{self.name} initialized successfully")
        return True
        
    async def start(self) -> bool:
        """Start the component."""
        self.logger.info(f"Starting {self.name}")
        
        # Start component services
        await self._start_services()
        
        # Register event handlers
        self.event_emitter.on('data_received', self._handle_data)
        
        self.status = "running"
        return True
        
    async def stop(self) -> bool:
        """Stop the component."""
        self.logger.info(f"Stopping {self.name}")
        
        # Cleanup resources
        await self._cleanup()
        
        self.status = "stopped"
        return True
        
    async def health_check(self) -> dict:
        """Perform health check."""
        return {
            "status": self.status,
            "healthy": self.status == "running",
            "metrics": await self._get_metrics()
        }
        
    async def _handle_data(self, data: dict):
        """Handle incoming data."""
        # Process data
        processed_data = await self._process_data(data)
        
        # Emit processed data
        await self.event_emitter.emit('data_processed', processed_data)
```

#### **2. Component Configuration**

```yaml
# config/components/my_component.yml
my_component:
  enabled: true
  version: "1.0.0"
  
  settings:
    host: "localhost"
    port: 8080
    timeout: 30
    
  resources:
    cpu_limit: "2"
    memory_limit: "4Gi"
    
  dependencies:
    - "network_component"
    - "storage_component"
```

#### **3. Component Tests**

```python
# tests/components/test_my_component.py
import pytest
from unittest.mock import AsyncMock, MagicMock

from goad_blue.components.my_component import MyComponent
from goad_blue.core.config import ComponentConfig

@pytest.fixture
def component_config():
    return ComponentConfig({
        "name": "my_component",
        "enabled": True,
        "settings": {
            "host": "localhost",
            "port": 8080
        }
    })

@pytest.fixture
def my_component(component_config):
    return MyComponent(component_config)

@pytest.mark.asyncio
async def test_component_initialization(my_component):
    """Test component initialization."""
    result = await my_component.initialize()
    assert result is True
    assert my_component.status == "initialized"

@pytest.mark.asyncio
async def test_component_start(my_component):
    """Test component start."""
    await my_component.initialize()
    result = await my_component.start()
    assert result is True
    assert my_component.status == "running"

@pytest.mark.asyncio
async def test_health_check(my_component):
    """Test health check."""
    await my_component.initialize()
    await my_component.start()
    
    health = await my_component.health_check()
    assert health["status"] == "running"
    assert health["healthy"] is True
```

### **Component Registration**

```python
# src/goad_blue/components/__init__.py
from .my_component import MyComponent

# Register component
AVAILABLE_COMPONENTS = {
    "my_component": MyComponent,
    # ... other components
}
```

## 🔌 API Development

### **Adding New API Endpoints**

#### **1. API Route Definition**

```python
# src/goad_blue/api/routes/my_routes.py
from fastapi import APIRouter, Depends, HTTPException
from typing import List, Optional

from goad_blue.api.auth import get_current_user
from goad_blue.api.models import MyModel, MyResponse
from goad_blue.core.manager import ComponentManager

router = APIRouter(prefix="/api/v1/my-endpoint", tags=["my-endpoint"])

@router.get("/", response_model=List[MyResponse])
async def list_items(
    limit: int = 100,
    offset: int = 0,
    current_user = Depends(get_current_user)
):
    """List items with pagination."""
    try:
        manager = ComponentManager()
        items = await manager.get_component("my_component").list_items(
            limit=limit, 
            offset=offset
        )
        return [MyResponse.from_dict(item) for item in items]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/", response_model=MyResponse)
async def create_item(
    item: MyModel,
    current_user = Depends(get_current_user)
):
    """Create a new item."""
    try:
        manager = ComponentManager()
        result = await manager.get_component("my_component").create_item(
            item.dict()
        )
        return MyResponse.from_dict(result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{item_id}", response_model=MyResponse)
async def get_item(
    item_id: str,
    current_user = Depends(get_current_user)
):
    """Get item by ID."""
    try:
        manager = ComponentManager()
        item = await manager.get_component("my_component").get_item(item_id)
        if not item:
            raise HTTPException(status_code=404, detail="Item not found")
        return MyResponse.from_dict(item)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

#### **2. Data Models**

```python
# src/goad_blue/api/models/my_models.py
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

class MyModel(BaseModel):
    """Model for creating/updating items."""
    name: str = Field(..., description="Item name")
    description: Optional[str] = Field(None, description="Item description")
    tags: List[str] = Field(default_factory=list, description="Item tags")
    enabled: bool = Field(True, description="Whether item is enabled")

class MyResponse(BaseModel):
    """Response model for items."""
    id: str = Field(..., description="Item ID")
    name: str = Field(..., description="Item name")
    description: Optional[str] = Field(None, description="Item description")
    tags: List[str] = Field(default_factory=list, description="Item tags")
    enabled: bool = Field(..., description="Whether item is enabled")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    @classmethod
    def from_dict(cls, data: dict) -> "MyResponse":
        """Create response from dictionary."""
        return cls(**data)
```

#### **3. API Tests**

```python
# tests/api/test_my_routes.py
import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock

from goad_blue.api.main import app

@pytest.fixture
def client():
    return TestClient(app)

@pytest.fixture
def auth_headers():
    return {"Authorization": "Bearer test-token"}

def test_list_items(client, auth_headers):
    """Test listing items."""
    response = client.get("/api/v1/my-endpoint/", headers=auth_headers)
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_create_item(client, auth_headers):
    """Test creating an item."""
    item_data = {
        "name": "Test Item",
        "description": "Test description",
        "tags": ["test"],
        "enabled": True
    }
    response = client.post(
        "/api/v1/my-endpoint/", 
        json=item_data, 
        headers=auth_headers
    )
    assert response.status_code == 200
    assert response.json()["name"] == "Test Item"

def test_get_item(client, auth_headers):
    """Test getting an item by ID."""
    response = client.get("/api/v1/my-endpoint/test-id", headers=auth_headers)
    assert response.status_code in [200, 404]
```

## 🧪 Testing

### **Test Structure**

```
tests/
├── unit/                    # Unit tests
│   ├── core/               # Core functionality tests
│   ├── components/         # Component tests
│   └── api/                # API tests
├── integration/            # Integration tests
│   ├── component_integration/
│   └── api_integration/
├── e2e/                    # End-to-end tests
└── fixtures/               # Test fixtures and data
```

### **Running Tests**

```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/unit/
pytest tests/integration/
pytest tests/e2e/

# Run with coverage
pytest --cov=src/goad_blue --cov-report=html

# Run specific test file
pytest tests/unit/core/test_manager.py

# Run with verbose output
pytest -v

# Run tests matching pattern
pytest -k "test_component"
```

### **Test Configuration**

```python
# tests/conftest.py
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock

from goad_blue.core.config import Config
from goad_blue.core.manager import ComponentManager

@pytest.fixture(scope="session")
def event_loop():
    """Create event loop for async tests."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def test_config():
    """Test configuration."""
    return Config({
        "environment": "test",
        "components": {
            "test_component": {
                "enabled": True,
                "settings": {"test": True}
            }
        }
    })

@pytest.fixture
async def component_manager(test_config):
    """Component manager for testing."""
    manager = ComponentManager(test_config)
    await manager.initialize()
    yield manager
    await manager.shutdown()
```

## 📦 Packaging and Distribution

### **Building Packages**

```bash
# Build Python package
python setup.py sdist bdist_wheel

# Build Docker images
docker build -t goad-blue:latest .

# Build documentation
cd docs && mkdocs build

# Create release package
scripts/create-release.sh v1.0.0
```

### **Release Process**

```bash
# 1. Update version
scripts/update-version.sh 1.0.0

# 2. Run full test suite
pytest tests/

# 3. Build packages
scripts/build-packages.sh

# 4. Create release
git tag v1.0.0
git push origin v1.0.0

# 5. Deploy to package repositories
scripts/deploy-packages.sh
```

## 🤝 Contributing

### **Contribution Workflow**

1. **Fork the Repository**
   ```bash
   git clone https://github.com/your-username/goad-blue.git
   cd goad-blue
   git remote add upstream https://github.com/original-org/goad-blue.git
   ```

2. **Create Feature Branch**
   ```bash
   git checkout -b feature/my-new-feature
   ```

3. **Make Changes**
   - Follow coding standards
   - Add tests for new functionality
   - Update documentation

4. **Test Changes**
   ```bash
   pytest tests/
   pre-commit run --all-files
   ```

5. **Submit Pull Request**
   - Clear description of changes
   - Reference related issues
   - Include test results

### **Coding Standards**

#### **Python Code Style**
```python
# Use type hints
def process_data(data: dict) -> List[str]:
    """Process data and return results."""
    pass

# Use docstrings
class MyClass:
    """Class description.
    
    Args:
        param1: Description of param1
        param2: Description of param2
    """
    
    def my_method(self, arg: str) -> bool:
        """Method description.
        
        Args:
            arg: Description of argument
            
        Returns:
            Description of return value
        """
        pass
```

#### **Configuration Standards**
```yaml
# Use consistent naming
component_name:
  enabled: true
  version: "1.0.0"
  
  # Group related settings
  connection:
    host: "localhost"
    port: 8080
    timeout: 30
    
  # Use descriptive names
  max_retry_attempts: 3
  log_level: "INFO"
```

---

!!! info "Development Resources"
    For detailed development information, see:
    
    - [Component Development](components.md)
    - [API Development](api.md)
    - [Testing Guide](testing.md)
    - [Deployment Guide](deployment.md)

!!! tip "Getting Help"
    Join our developer community:
    
    - **Discord**: #goad-blue-dev channel
    - **GitHub Discussions**: Development topics
    - **Weekly Dev Calls**: Thursdays 2PM UTC

!!! warning "Security Considerations"
    Always consider security implications when developing:
    
    - Validate all inputs
    - Use secure defaults
    - Follow least privilege principles
    - Document security considerations
