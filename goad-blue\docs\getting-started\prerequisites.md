# Prerequisites

Before installing <PERSON>AD-Blue, ensure your system meets all requirements and has the necessary software installed.

## 💻 System Requirements

### **Minimum Requirements**
- **RAM**: 32GB
- **Storage**: 500GB available space
- **CPU**: 8 cores (Intel VT-x or AMD-V support)
- **Network**: Dedicated network interface (recommended)
- **OS**: Ubuntu 20.04+, CentOS 8+, Windows 10+, macOS 11+

### **Recommended Requirements**
- **RAM**: 64GB
- **Storage**: 1TB SSD
- **CPU**: 16 cores
- **Network**: Multiple network interfaces
- **OS**: Ubuntu 22.04 LTS (preferred)

### **Production Requirements**
- **RAM**: 128GB
- **Storage**: 2TB NVMe SSD
- **CPU**: 32 cores
- **Network**: 10Gbps network interfaces
- **OS**: Ubuntu 22.04 LTS with enterprise support

## 🛠️ Software Dependencies

### **Core Requirements**

#### **Python Environment**
```bash
# Check Python version (3.8+ required)
python3 --version

# Install Python if needed (Ubuntu/Debian)
sudo apt update
sudo apt install python3 python3-pip python3-venv

# Install Python (CentOS/RHEL)
sudo yum install python3 python3-pip

# Install Python (macOS)
brew install python3
```

#### **Infrastructure Tools**
```bash
# Packer (1.9.0+)
wget https://releases.hashicorp.com/packer/1.9.4/packer_1.9.4_linux_amd64.zip
unzip packer_1.9.4_linux_amd64.zip
sudo mv packer /usr/local/bin/

# Terraform (1.0+)
wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
unzip terraform_1.6.0_linux_amd64.zip
sudo mv terraform /usr/local/bin/

# Ansible (6.0+)
pip3 install ansible-core>=2.13

# Docker (20.10+)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

### **Virtualization Platform**

Choose one of the following virtualization platforms:

#### **VMware Workstation Pro (Recommended)**
```bash
# Download from VMware website
# Install VMware Workstation Pro 16.2+
# Ensure virtualization is enabled in BIOS

# Verify installation
vmrun list
```

#### **VMware ESXi**
```bash
# ESXi 7.0+ required
# vSphere API access needed
# Sufficient resources on ESXi host

# Install vSphere CLI tools
pip3 install pyvmomi
```

#### **VirtualBox**
```bash
# VirtualBox 6.1+ required
# Install VirtualBox and Extension Pack

# Ubuntu/Debian
sudo apt install virtualbox virtualbox-ext-pack

# Verify installation
VBoxManage --version
```

### **Cloud Platform Requirements**

#### **Amazon Web Services**
```bash
# Install AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Configure credentials
aws configure
# Enter: Access Key ID, Secret Access Key, Region, Output format
```

**Required AWS Permissions:**
- EC2 full access
- VPC full access
- IAM role creation
- S3 bucket access
- CloudFormation access

#### **Microsoft Azure**
```bash
# Install Azure CLI
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

# Login to Azure
az login

# Set subscription
az account set --subscription "Your Subscription Name"
```

**Required Azure Permissions:**
- Virtual Machine Contributor
- Network Contributor
- Storage Account Contributor
- Resource Group Contributor

#### **Google Cloud Platform**
```bash
# Install Google Cloud SDK
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Initialize and authenticate
gcloud init
gcloud auth application-default login
```

**Required GCP Permissions:**
- Compute Engine Admin
- VPC Admin
- Storage Admin
- Service Account Admin

#### **Proxmox VE**
```bash
# Install Proxmox API client
pip3 install proxmoxer requests

# Set environment variables
export PROXMOX_API_URL="https://your-proxmox:8006/api2/json"
export PROXMOX_USER="root@pam"
export PROXMOX_PASSWORD="your_password"
export PROXMOX_NODE="pve"
```

**Required Proxmox Setup:**
- Proxmox VE 7.4+ or 8.0+
- Sufficient storage (ZFS or LVM)
- Network bridges configured
- API access enabled
- Templates created (Ubuntu, Security Onion)

## 🎯 GOAD Integration Requirements

### **Existing GOAD Installation (Recommended)**

If you have an existing GOAD installation:

```bash
# Verify GOAD is working
cd /path/to/goad
python3 goad.py status

# Check GOAD version compatibility
python3 goad.py --version
# GOAD-Blue supports GOAD v3.0+
```

### **Network Requirements**

```bash
# Ensure network connectivity between GOAD and GOAD-Blue subnets
# Default GOAD network: ************/24
# Default GOAD-Blue network: *************/24

# Test connectivity
ping *************  # GOAD DC
ping ************** # GOAD-Blue SIEM
```

### **GOAD VM Requirements**

For GOAD integration, ensure GOAD VMs have:
- **PowerShell 5.0+** (Windows VMs)
- **WinRM enabled** (Windows VMs)
- **SSH access** (Linux VMs)
- **Administrative privileges** for agent installation

## 🔧 Development Environment Setup

### **Git Configuration**
```bash
# Install Git
sudo apt install git  # Ubuntu/Debian
sudo yum install git  # CentOS/RHEL
brew install git      # macOS

# Configure Git
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

### **IDE/Editor Setup**
```bash
# Visual Studio Code (recommended)
wget -qO- https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor > packages.microsoft.gpg
sudo install -o root -g root -m 644 packages.microsoft.gpg /etc/apt/trusted.gpg.d/
sudo sh -c 'echo "deb [arch=amd64,arm64,armhf signed-by=/etc/apt/trusted.gpg.d/packages.microsoft.gpg] https://packages.microsoft.com/repos/code stable main" > /etc/apt/sources.list.d/vscode.list'
sudo apt update
sudo apt install code

# Useful VS Code extensions:
# - Python
# - Ansible
# - HashiCorp Terraform
# - YAML
```

## 🌐 Network Configuration

### **Firewall Configuration**
```bash
# Ubuntu/Debian (UFW)
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 8000/tcp  # Splunk
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 8889/tcp  # Velociraptor

# CentOS/RHEL (firewalld)
sudo systemctl enable firewalld
sudo systemctl start firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

### **DNS Configuration**
```bash
# Ensure proper DNS resolution
# Add entries to /etc/hosts if needed
echo "************** goad-blue-siem" | sudo tee -a /etc/hosts
echo "************** goad-blue-so" | sudo tee -a /etc/hosts
```

## 📋 Pre-Installation Checklist

### **System Verification**
```bash
# Check system resources
free -h                    # Memory
df -h                     # Disk space
nproc                     # CPU cores
lscpu | grep Virtualization  # Virtualization support

# Check network connectivity
ping -c 4 *******         # Internet connectivity
ip addr show              # Network interfaces

# Verify software versions
python3 --version         # Python 3.8+
packer --version          # Packer 1.9.0+
terraform --version       # Terraform 1.0+
ansible --version         # Ansible 6.0+
docker --version          # Docker 20.10+
```

### **Permission Verification**
```bash
# Check sudo access
sudo whoami

# Check Docker access
docker run hello-world

# Check virtualization access
# For VMware:
vmrun list
# For VirtualBox:
VBoxManage list vms
```

### **Storage Preparation**
```bash
# Create dedicated storage for GOAD-Blue
sudo mkdir -p /opt/goad-blue
sudo chown $USER:$USER /opt/goad-blue

# Verify available space
df -h /opt/goad-blue
```

## ⚠️ Common Prerequisites Issues

### **Virtualization Not Enabled**
```bash
# Check if virtualization is enabled
egrep -c '(vmx|svm)' /proc/cpuinfo
# Should return > 0

# If 0, enable in BIOS:
# - Intel: Enable VT-x
# - AMD: Enable AMD-V
```

### **Insufficient Permissions**
```bash
# Add user to required groups
sudo usermod -aG docker $USER
sudo usermod -aG libvirt $USER  # For KVM/QEMU
sudo usermod -aG vboxusers $USER  # For VirtualBox

# Logout and login again for changes to take effect
```

### **Network Conflicts**
```bash
# Check for IP conflicts
ip route show
netstat -rn

# Resolve conflicts by changing GOAD-Blue network
# Edit goad-blue-config.yml:
# network:
#   base_cidr: "**********/24"  # Different from GOAD
```

## 🔍 Verification Commands

### **Final Prerequisites Check**
```bash
# Run GOAD-Blue prerequisites check
python3 goad-blue.py --check-prerequisites

# Manual verification
echo "=== System Information ==="
uname -a
echo "=== Python Version ==="
python3 --version
echo "=== Available Memory ==="
free -h
echo "=== Available Disk ==="
df -h
echo "=== CPU Cores ==="
nproc
echo "=== Virtualization Support ==="
egrep -c '(vmx|svm)' /proc/cpuinfo
echo "=== Network Interfaces ==="
ip addr show
```

---

!!! success "Prerequisites Complete"
    Once all prerequisites are met, proceed to the [Installation](installation.md) guide.

!!! tip "Resource Planning"
    Consider starting with a minimal deployment to test functionality before scaling up to full production requirements.

!!! warning "Security Considerations"
    Ensure your lab environment is properly isolated from production networks and follows your organization's security policies.
