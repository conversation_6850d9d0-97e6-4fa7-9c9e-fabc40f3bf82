#!/bin/bash
# Enhanced Splunk Monitoring Script for GOAD-Blue
# Provides comprehensive monitoring and health checks for Splunk Enterprise

set -e

# Configuration
SPLUNK_HOME="/opt/splunk"
SPLUNK_USER="splunk"
SPLUNK_ADMIN_USER="admin"
SPLUNK_ADMIN_PASS="${SPLUNK_ADMIN_PASSWORD:-ChangeMeNow!}"
SPLUNK_WEB_URL="https://localhost:8000"
SPLUNK_MGMT_URL="https://localhost:8089"
LOG_FILE="/var/log/goad-blue/splunk-monitor.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "OK")
            echo -e "${GREEN}[OK]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
    esac
}

# Check if Splunk is installed
check_splunk_installation() {
    echo "=== Splunk Installation Check ==="
    
    if [ -d "$SPLUNK_HOME" ]; then
        print_status "OK" "Splunk home directory exists: $SPLUNK_HOME"
        
        if [ -f "$SPLUNK_HOME/bin/splunk" ]; then
            local version=$($SPLUNK_HOME/bin/splunk version 2>/dev/null | head -1 || echo "Unknown")
            print_status "OK" "Splunk binary found - $version"
        else
            print_status "ERROR" "Splunk binary not found"
            return 1
        fi
    else
        print_status "ERROR" "Splunk home directory not found"
        return 1
    fi
    
    echo ""
}

# Check Splunk service status
check_service_status() {
    echo "=== Splunk Service Status ==="
    
    if systemctl is-active --quiet Splunkd; then
        print_status "OK" "Splunkd service is running"
        
        # Get service details
        local uptime=$(systemctl show Splunkd --property=ActiveEnterTimestamp --value)
        print_status "INFO" "Service started: $uptime"
        
        # Check if service is enabled
        if systemctl is-enabled --quiet Splunkd; then
            print_status "OK" "Splunkd service is enabled for boot"
        else
            print_status "WARNING" "Splunkd service is not enabled for boot"
        fi
    else
        print_status "ERROR" "Splunkd service is not running"
        
        # Show recent logs
        echo "Recent service logs:"
        journalctl -u Splunkd --no-pager -n 10
        return 1
    fi
    
    echo ""
}

# Check Splunk processes
check_processes() {
    echo "=== Splunk Process Information ==="
    
    local splunk_processes=$(ps aux | grep -E "(splunkd|splunkweb)" | grep -v grep)
    
    if [ -n "$splunk_processes" ]; then
        print_status "OK" "Splunk processes are running"
        echo "$splunk_processes" | while read line; do
            echo "  $line"
        done
        
        # Memory usage
        local memory_usage=$(ps aux | grep -E "(splunkd|splunkweb)" | grep -v grep | awk '{sum+=$6} END {print sum/1024}')
        print_status "INFO" "Total memory usage: ${memory_usage}MB"
    else
        print_status "ERROR" "No Splunk processes found"
        return 1
    fi
    
    echo ""
}

# Check network connectivity
check_network() {
    echo "=== Splunk Network Connectivity ==="
    
    # Check web interface
    if curl -k -s --connect-timeout 5 "$SPLUNK_WEB_URL" >/dev/null; then
        print_status "OK" "Splunk Web interface is accessible ($SPLUNK_WEB_URL)"
    else
        print_status "ERROR" "Splunk Web interface is not accessible"
    fi
    
    # Check management port
    if curl -k -s --connect-timeout 5 "$SPLUNK_MGMT_URL" >/dev/null; then
        print_status "OK" "Splunk Management interface is accessible ($SPLUNK_MGMT_URL)"
    else
        print_status "ERROR" "Splunk Management interface is not accessible"
    fi
    
    # Check forwarder port
    if netstat -ln | grep -q ":9997"; then
        print_status "OK" "Splunk forwarder port (9997) is listening"
    else
        print_status "WARNING" "Splunk forwarder port (9997) is not listening"
    fi
    
    # Check HEC port
    if netstat -ln | grep -q ":8088"; then
        print_status "OK" "HTTP Event Collector port (8088) is listening"
    else
        print_status "WARNING" "HTTP Event Collector port (8088) is not listening"
    fi
    
    echo ""
}

# Check disk usage
check_disk_usage() {
    echo "=== Splunk Disk Usage ==="
    
    # Splunk home directory
    local splunk_size=$(du -sh "$SPLUNK_HOME" 2>/dev/null | cut -f1)
    print_status "INFO" "Splunk home directory size: $splunk_size"
    
    # Splunk data directory
    if [ -d "$SPLUNK_HOME/var/lib/splunk" ]; then
        local data_size=$(du -sh "$SPLUNK_HOME/var/lib/splunk" 2>/dev/null | cut -f1)
        print_status "INFO" "Splunk data directory size: $data_size"
        
        # Check available space
        local available_space=$(df -h "$SPLUNK_HOME" | tail -1 | awk '{print $4}')
        local usage_percent=$(df -h "$SPLUNK_HOME" | tail -1 | awk '{print $5}' | sed 's/%//')
        
        if [ "$usage_percent" -lt 80 ]; then
            print_status "OK" "Disk usage: ${usage_percent}% (${available_space} available)"
        elif [ "$usage_percent" -lt 90 ]; then
            print_status "WARNING" "Disk usage: ${usage_percent}% (${available_space} available)"
        else
            print_status "ERROR" "Disk usage: ${usage_percent}% (${available_space} available) - Critical!"
        fi
    fi
    
    echo ""
}

# Check Splunk indexes
check_indexes() {
    echo "=== Splunk Index Status ==="
    
    # Use Splunk CLI to check indexes
    if command -v "$SPLUNK_HOME/bin/splunk" >/dev/null; then
        local index_output
        index_output=$($SPLUNK_HOME/bin/splunk list index -auth "$SPLUNK_ADMIN_USER:$SPLUNK_ADMIN_PASS" 2>/dev/null)
        
        if [ $? -eq 0 ]; then
            print_status "OK" "Successfully connected to Splunk API"
            
            # Count GOAD-Blue indexes
            local goad_indexes=$(echo "$index_output" | grep -c "goad_blue" || echo "0")
            print_status "INFO" "GOAD-Blue indexes found: $goad_indexes"
            
            # Show index details
            echo "Index details:"
            echo "$index_output" | grep -E "(goad_blue|main|_internal)" | head -10
        else
            print_status "ERROR" "Failed to connect to Splunk API"
        fi
    else
        print_status "ERROR" "Splunk CLI not found"
    fi
    
    echo ""
}

# Check license usage
check_license() {
    echo "=== Splunk License Usage ==="
    
    if command -v "$SPLUNK_HOME/bin/splunk" >/dev/null; then
        local license_output
        license_output=$($SPLUNK_HOME/bin/splunk list licenser-pools -auth "$SPLUNK_ADMIN_USER:$SPLUNK_ADMIN_PASS" 2>/dev/null)
        
        if [ $? -eq 0 ]; then
            print_status "OK" "License information retrieved"
            echo "$license_output"
        else
            print_status "WARNING" "Could not retrieve license information"
        fi
    fi
    
    echo ""
}

# Check recent events
check_recent_events() {
    echo "=== Recent Event Ingestion ==="
    
    # Check for recent events in main indexes
    local search_query='search index=goad_blue_* earliest=-1h | stats count by index'
    local search_output
    
    search_output=$(curl -k -u "$SPLUNK_ADMIN_USER:$SPLUNK_ADMIN_PASS" \
        -d "search=$search_query" \
        -d "output_mode=csv" \
        "$SPLUNK_MGMT_URL/services/search/jobs/oneshot" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$search_output" ]; then
        print_status "OK" "Recent events found in GOAD-Blue indexes"
        echo "$search_output" | head -10
    else
        print_status "WARNING" "No recent events found or search failed"
    fi
    
    echo ""
}

# Check forwarder connections
check_forwarders() {
    echo "=== Forwarder Connections ==="
    
    # Check for connected forwarders
    local forwarder_search='| rest /services/deployment/server/clients | table hostname, ip, lastPhoneHomeTime'
    local forwarder_output
    
    forwarder_output=$(curl -k -u "$SPLUNK_ADMIN_USER:$SPLUNK_ADMIN_PASS" \
        -d "search=$forwarder_search" \
        -d "output_mode=csv" \
        "$SPLUNK_MGMT_URL/services/search/jobs/oneshot" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$forwarder_output" ]; then
        local forwarder_count=$(echo "$forwarder_output" | wc -l)
        print_status "INFO" "Connected forwarders: $((forwarder_count - 1))"
        echo "$forwarder_output" | head -5
    else
        print_status "INFO" "No forwarders connected or deployment server not configured"
    fi
    
    echo ""
}

# Generate summary report
generate_summary() {
    echo "=== Splunk Health Summary ==="
    
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    print_status "INFO" "Report generated: $timestamp"
    
    # Overall health score (simplified)
    local health_score=100
    
    # Deduct points for issues
    if ! systemctl is-active --quiet Splunkd; then
        health_score=$((health_score - 50))
    fi
    
    if ! curl -k -s --connect-timeout 5 "$SPLUNK_WEB_URL" >/dev/null; then
        health_score=$((health_score - 20))
    fi
    
    local usage_percent=$(df -h "$SPLUNK_HOME" | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$usage_percent" -gt 90 ]; then
        health_score=$((health_score - 20))
    elif [ "$usage_percent" -gt 80 ]; then
        health_score=$((health_score - 10))
    fi
    
    # Display health score
    if [ "$health_score" -ge 90 ]; then
        print_status "OK" "Overall health score: ${health_score}% - Excellent"
    elif [ "$health_score" -ge 70 ]; then
        print_status "WARNING" "Overall health score: ${health_score}% - Good"
    else
        print_status "ERROR" "Overall health score: ${health_score}% - Needs attention"
    fi
    
    echo ""
}

# Main function
main() {
    echo "========================================"
    echo "    GOAD-Blue Splunk Monitor v1.0"
    echo "========================================"
    echo ""
    
    log_message "Starting Splunk monitoring check"
    
    # Run all checks
    check_splunk_installation || exit 1
    check_service_status || exit 1
    check_processes
    check_network
    check_disk_usage
    check_indexes
    check_license
    check_recent_events
    check_forwarders
    generate_summary
    
    log_message "Splunk monitoring check completed"
    
    echo "========================================"
    echo "For detailed logs, check: $LOG_FILE"
    echo "========================================"
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "GOAD-Blue Splunk Monitor"
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --quiet, -q    Quiet mode (minimal output)"
        echo "  --json         Output in JSON format"
        echo ""
        exit 0
        ;;
    --quiet|-q)
        # Redirect output to log file only
        main > "$LOG_FILE" 2>&1
        echo "Monitoring completed. Check $LOG_FILE for details."
        ;;
    --json)
        # TODO: Implement JSON output format
        echo '{"error": "JSON output not yet implemented"}'
        exit 1
        ;;
    *)
        main
        ;;
esac
