# Reference Documentation

This section provides comprehensive reference materials for GOAD-Blue, including glossaries, FAQs, configuration templates, and quick reference guides.

## 📚 Reference Overview

The reference section serves as your go-to resource for quick lookups, definitions, and detailed technical information about GOAD-Blue components and concepts.

```mermaid
graph TB
    subgraph "📖 Core References"
        GLOSSARY[📝 Glossary<br/>Terms & Definitions<br/>Technical Vocabulary]
        FAQ[❓ FAQ<br/>Common Questions<br/>Quick Answers]
        CHANGELOG[📋 Changelog<br/>Version History<br/>Release Notes]
    end
    
    subgraph "⚙️ Technical References"
        CONFIG_REF[🔧 Configuration Reference<br/>Settings & Parameters<br/>Default Values]
        API_REF[🔌 API Reference<br/>Endpoints & Methods<br/>Request/Response Formats]
        CLI_REF[💻 CLI Reference<br/>Commands & Options<br/>Usage Examples]
    end
    
    subgraph "📋 Templates & Examples"
        TEMPLATES[📄 Configuration Templates<br/>Pre-built Configs<br/>Best Practices]
        EXAMPLES[💡 Code Examples<br/>Integration Samples<br/>Use Case Demos]
        CHEATSHEETS[📊 Cheat Sheets<br/>Quick References<br/>Command Summaries]
    end
    
    subgraph "🔗 External Resources"
        STANDARDS[📐 Standards & Frameworks<br/>NIST, MITRE ATT&CK<br/>Industry Guidelines]
        TOOLS[🛠️ Tool Documentation<br/>Splunk, Security Onion<br/>Third-party Integrations]
        COMMUNITY[👥 Community Resources<br/>Forums, Blogs<br/>Training Materials]
    end
    
    GLOSSARY --> CONFIG_REF
    FAQ --> API_REF
    CHANGELOG --> CLI_REF
    
    CONFIG_REF --> TEMPLATES
    API_REF --> EXAMPLES
    CLI_REF --> CHEATSHEETS
    
    TEMPLATES --> STANDARDS
    EXAMPLES --> TOOLS
    CHEATSHEETS --> COMMUNITY
    
    classDef core fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef technical fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef templates fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef external fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class GLOSSARY,FAQ,CHANGELOG core
    class CONFIG_REF,API_REF,CLI_REF technical
    class TEMPLATES,EXAMPLES,CHEATSHEETS templates
    class STANDARDS,TOOLS,COMMUNITY external
```

## 📝 Core References

### **[Glossary](glossary.md)**
Comprehensive definitions of terms, acronyms, and concepts used throughout GOAD-Blue.

**Key Categories:**
- **Security Terms** - SOC, SIEM, IOC, TTPs, etc.
- **Technical Terms** - API, CLI, IaC, CI/CD, etc.
- **GOAD-Blue Specific** - Components, scenarios, integrations
- **Industry Standards** - MITRE ATT&CK, NIST, ISO 27001

**Quick Lookup:**
- **APT** - Advanced Persistent Threat
- **EDR** - Endpoint Detection and Response
- **IOC** - Indicator of Compromise
- **SIEM** - Security Information and Event Management
- **SOC** - Security Operations Center
- **TTP** - Tactics, Techniques, and Procedures

### **[FAQ](faq.md)**
Frequently asked questions and their answers, organized by topic.

**Categories:**
- **Installation & Setup** - Common deployment issues
- **Configuration** - Settings and customization
- **Integration** - GOAD connectivity and agent deployment
- **Training** - Scenario execution and progress tracking
- **Troubleshooting** - Problem resolution and support

**Popular Questions:**
- How do I integrate GOAD-Blue with an existing GOAD environment?
- What are the minimum system requirements?
- How do I reset a training scenario?
- Can I use GOAD-Blue in a production environment?
- How do I backup and restore configurations?

### **[Changelog](changelog.md)**
Complete version history with release notes, new features, and bug fixes.

**Version Format:** `MAJOR.MINOR.PATCH`
- **MAJOR** - Breaking changes, major new features
- **MINOR** - New features, backward compatible
- **PATCH** - Bug fixes, minor improvements

**Recent Releases:**
- **v1.2.0** - Added Proxmox support, enhanced scenarios
- **v1.1.5** - Bug fixes, performance improvements
- **v1.1.0** - New training scenarios, API enhancements
- **v1.0.0** - Initial stable release

## ⚙️ Technical References

### **[Configuration Reference](config-reference.md)**
Complete reference for all configuration options and parameters.

**Configuration Categories:**

#### **Global Settings**
```yaml
# Global configuration options
goad_blue:
  name: "my-goad-blue-lab"           # Environment name
  version: "1.2.0"                   # GOAD-Blue version
  environment: "production"          # Environment type
  provider: "vmware"                 # Infrastructure provider
  log_level: "info"                  # Logging level
  debug_mode: false                  # Debug mode flag
```

#### **Component Configuration**
```yaml
# SIEM configuration
siem:
  type: "splunk"                     # SIEM type: splunk, elastic
  enabled: true                     # Enable SIEM component
  version: "9.1.2"                  # Component version
  license_type: "enterprise"        # License type
  admin_password: "ChangeMePlease!"  # Admin password
  
# Network monitoring configuration
monitoring:
  security_onion:
    enabled: true                   # Enable Security Onion
    version: "2.4.60"              # Version
    deployment_type: "standalone"   # Deployment type
    sensors: 2                     # Number of sensors
```

#### **Network Configuration**
```yaml
# Network settings
network:
  base_cidr: "192.168.100.0/24"     # Base network CIDR
  dns_servers:                      # DNS servers
    - "8.8.8.8"
    - "8.8.4.4"
  firewall:
    enabled: true                   # Enable firewall
    default_policy: "deny"          # Default policy
```

### **[API Reference](api-reference.md)**
Complete API documentation with endpoints, parameters, and examples.

**API Categories:**
- **Management API** - System control and status
- **Configuration API** - Settings management
- **Component APIs** - SIEM, monitoring, endpoint
- **Training API** - Scenarios and simulations
- **Integration API** - GOAD connectivity

**Authentication Methods:**
- **API Key** - `X-API-Key: your-api-key`
- **JWT Token** - `Authorization: Bearer jwt-token`
- **Basic Auth** - `Authorization: Basic base64-credentials`

### **[CLI Reference](cli-reference.md)**
Complete command-line interface documentation.

**Command Categories:**

#### **System Commands**
```bash
python3 goad-blue.py status           # System status
python3 goad-blue.py health-check     # Health check
python3 goad-blue.py version          # Version info
python3 goad-blue.py logs             # View logs
```

#### **Component Commands**
```bash
python3 goad-blue.py start --component splunk     # Start component
python3 goad-blue.py stop --component splunk      # Stop component
python3 goad-blue.py restart --component splunk   # Restart component
python3 goad-blue.py configure --component splunk # Configure component
```

#### **Training Commands**
```bash
python3 goad-blue.py list-scenarios              # List scenarios
python3 goad-blue.py start-scenario --name test  # Start scenario
python3 goad-blue.py scenario-status             # Check status
python3 goad-blue.py stop-scenario               # Stop scenario
```

## 📋 Templates & Examples

### **[Configuration Templates](templates/)**
Pre-built configuration templates for common deployment scenarios.

**Available Templates:**

#### **Home Lab Template**
```yaml
# templates/homelab.yml
goad_blue:
  name: "homelab"
  environment: "development"
  provider: "virtualbox"

components:
  splunk:
    enabled: true
    license_type: "free"
  security_onion:
    enabled: true
    deployment_type: "standalone"
  velociraptor:
    enabled: true

resources:
  cpu_limit: "50%"
  memory_limit: "16GB"
  storage_limit: "500GB"
```

#### **Enterprise Template**
```yaml
# templates/enterprise.yml
goad_blue:
  name: "enterprise"
  environment: "production"
  provider: "vmware"

components:
  splunk:
    enabled: true
    license_type: "enterprise"
    clustering: true
  security_onion:
    enabled: true
    deployment_type: "distributed"
    sensors: 4
  velociraptor:
    enabled: true
    ha_enabled: true

security:
  ssl_enabled: true
  mfa_enabled: true
  audit_logging: true
```

### **[Code Examples](examples/)**
Practical code examples for integration and automation.

**Example Categories:**
- **Python Integration** - API usage, automation scripts
- **PowerShell Scripts** - Windows automation, agent deployment
- **Bash Scripts** - Linux automation, system management
- **Ansible Playbooks** - Configuration management
- **Terraform Modules** - Infrastructure as code

### **[Cheat Sheets](cheatsheets/)**
Quick reference guides for common tasks and commands.

**Available Cheat Sheets:**

#### **Splunk Search Cheat Sheet**
```splunk
# Common GOAD-Blue searches
index=goad_blue_windows EventCode=4624 | stats count by user
index=goad_blue_windows EventCode=4625 | stats count by src_ip
index=goad_blue_windows source="*Sysmon*" EventCode=1 | head 100
index=goad_blue_network | stats count by dest_port
```

#### **Security Onion Cheat Sheet**
```bash
# Security Onion commands
sudo so-status                    # Check status
sudo so-restart                   # Restart services
sudo so-rule-update              # Update rules
sudo so-elastic-restart          # Restart Elasticsearch
```

#### **Velociraptor Cheat Sheet**
```bash
# Velociraptor commands
velociraptor query "SELECT * FROM clients()"
velociraptor artifacts list
velociraptor hunts list
velociraptor collect Windows.System.Pslist
```

## 🔗 External Resources

### **[Standards & Frameworks](standards.md)**
References to industry standards and frameworks used in GOAD-Blue.

**Security Frameworks:**
- **[NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)** - Risk management framework
- **[MITRE ATT&CK](https://attack.mitre.org/)** - Adversary tactics and techniques
- **[ISO 27001](https://www.iso.org/isoiec-27001-information-security.html)** - Information security management
- **[SANS Top 20](https://www.sans.org/top20/)** - Critical security controls

**Technical Standards:**
- **[STIX/TAXII](https://oasis-open.github.io/cti-documentation/)** - Threat intelligence sharing
- **[OpenIOC](https://www.fireeye.com/services/freeware/openioc.html)** - Indicator of compromise format
- **[Sigma](https://github.com/SigmaHQ/sigma)** - Generic signature format
- **[YARA](https://virustotal.github.io/yara/)** - Malware identification rules

### **[Tool Documentation](tools.md)**
Links to official documentation for integrated tools.

**SIEM Platforms:**
- **[Splunk Documentation](https://docs.splunk.com/)** - Official Splunk docs
- **[Elastic Documentation](https://www.elastic.co/guide/)** - Elasticsearch, Kibana, Logstash

**Security Tools:**
- **[Security Onion Documentation](https://docs.securityonion.net/)** - Network security monitoring
- **[Velociraptor Documentation](https://docs.velociraptor.app/)** - Endpoint visibility
- **[MISP Documentation](https://www.misp-project.org/documentation/)** - Threat intelligence

**Infrastructure Tools:**
- **[Terraform Documentation](https://www.terraform.io/docs)** - Infrastructure as code
- **[Ansible Documentation](https://docs.ansible.com/)** - Configuration management
- **[Packer Documentation](https://www.packer.io/docs)** - Image building

### **[Community Resources](community.md)**
Community-contributed resources and external learning materials.

**Learning Resources:**
- **[SANS Training](https://www.sans.org/)** - Professional cybersecurity training
- **[Cybrary](https://www.cybrary.it/)** - Free cybersecurity training
- **[NIST Learning](https://www.nist.gov/itl/applied-cybersecurity/nice/resources)** - NICE framework resources

**Community Forums:**
- **[Reddit r/cybersecurity](https://www.reddit.com/r/cybersecurity/)** - General cybersecurity discussion
- **[SANS Community](https://www.sans.org/community/)** - Professional community
- **[Splunk Community](https://community.splunk.com/)** - Splunk-specific discussions

**Blogs and Publications:**
- **[SANS Reading Room](https://www.sans.org/reading-room/)** - White papers and research
- **[MITRE Blog](https://www.mitre.org/news-insights/news-releases)** - Threat intelligence insights
- **[Krebs on Security](https://krebsonsecurity.com/)** - Security news and analysis

## 🔍 Quick Reference

### **Common File Locations**
```
goad-blue/
├── goad-blue-config.yml          # Main configuration
├── logs/                         # Log files
├── backups/                      # Configuration backups
├── scripts/                      # Automation scripts
└── docs/                         # Documentation
```

### **Default Ports**
| Service | Port | Protocol | Description |
|---------|------|----------|-------------|
| Splunk Web | 8000 | HTTP/HTTPS | Web interface |
| Splunk Management | 8089 | HTTPS | Management API |
| Elasticsearch | 9200 | HTTP | REST API |
| Kibana | 5601 | HTTP | Web interface |
| Security Onion | 443 | HTTPS | Web interface |
| Velociraptor | 8000 | HTTPS | Web interface |
| MISP | 443 | HTTPS | Web interface |

### **Default Credentials**
| Component | Username | Default Password | Notes |
|-----------|----------|------------------|-------|
| Splunk | admin | changeme | Change on first login |
| Security Onion | admin | changeme | Change during setup |
| Velociraptor | admin | changeme | Generated during install |
| MISP | <EMAIL> | admin | Change immediately |

---

!!! tip "Bookmark This Page"
    This reference section is designed for quick lookups. Bookmark frequently used sections for easy access during your work.

!!! warning "Keep References Updated"
    Reference materials are updated with each release. Check the changelog for updates to commands, configurations, and procedures.

!!! info "Contribute to References"
    Found an error or have a suggestion? Contribute to the reference documentation through our GitHub repository.
