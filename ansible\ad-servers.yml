---
# Load datas
- import_playbook: data.yml
  vars:
    data_path: "../ad/{{domain_name}}/data/"
  tags: 'data'

# set AD datas ==================================================================================================

- name: Prepare servers set admin password, set hostname
  hosts: domain
  tags: 'prepare_servers'
  roles:
  - { role: 'settings/admin_password', tags: 'admin_password' }
  - { role: 'settings/hostname', tags: 'hostname' }
  vars:
    local_admin_password: "{{lab.hosts[dict_key].local_admin_password}}"
    hostname: "{{lab.hosts[dict_key].hostname}}"
