# FLARE-VM Installation Script for GOAD-Blue
# This script installs FLARE-VM and configures it for malware analysis

Write-Host "=== Installing FLARE-VM for GOAD-Blue ===" -ForegroundColor Green

# Set execution policy
Write-Host "Setting PowerShell execution policy..." -ForegroundColor Yellow
Set-ExecutionPolicy Unrestricted -Force
Set-ExecutionPolicy Unrestricted -Scope CurrentUser -Force

# Create directories
Write-Host "Creating analysis directories..." -ForegroundColor Yellow
$directories = @(
    "C:\MalwareAnalysis",
    "C:\MalwareAnalysis\Samples",
    "C:\MalwareAnalysis\Reports",
    "C:\MalwareAnalysis\IOCs",
    "C:\MalwareAnalysis\YARA",
    "C:\MalwareAnalysis\Scripts",
    "C:\MalwareAnalysis\Memory",
    "C:\MalwareAnalysis\Network",
    "C:\Tools",
    "C:\temp"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Cyan
    }
}

# Download FLARE-VM installation script
Write-Host "Downloading FLARE-VM installation script..." -ForegroundColor Yellow
$flareVmUrl = "https://raw.githubusercontent.com/mandiant/flare-vm/main/install.ps1"
$flareVmScript = "C:\temp\install-flarevm.ps1"

try {
    Invoke-WebRequest -Uri $flareVmUrl -OutFile $flareVmScript -UseBasicParsing
    Write-Host "FLARE-VM script downloaded successfully" -ForegroundColor Green
} catch {
    Write-Host "Error downloading FLARE-VM script: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Attempting alternative download method..." -ForegroundColor Yellow
    
    # Alternative download using .NET WebClient
    try {
        $webClient = New-Object System.Net.WebClient
        $webClient.DownloadFile($flareVmUrl, $flareVmScript)
        Write-Host "FLARE-VM script downloaded using alternative method" -ForegroundColor Green
    } catch {
        Write-Host "Failed to download FLARE-VM script: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Create custom FLARE-VM configuration
Write-Host "Creating custom FLARE-VM configuration..." -ForegroundColor Yellow
$customConfig = @"
# GOAD-Blue FLARE-VM Custom Configuration
# Core Analysis Tools
7zip
notepadplusplus
firefox
googlechrome
vlc
adobereader

# Hex Editors
hxd
010editor

# Disassemblers
ida-free
ghidra
x64dbg
ollydbg

# PE Analysis
peid
pestudio
peview
resourcehacker

# Network Analysis
wireshark
nmap
tcpview
fiddler

# System Monitoring
procmon
procexp
autoruns
regshot

# Forensics
volatility
autopsy
ftk-imager

# Malware Analysis
floss
capa
yara
strings

# Scripting
python3
git
vscode

# Utilities
sysinternals
nirsoft-package
putty
winscp
"@

$customConfig | Out-File -FilePath "C:\temp\goad-blue-flare-config.txt" -Encoding UTF8

# Configure Windows for malware analysis
Write-Host "Configuring Windows for malware analysis..." -ForegroundColor Yellow

# Show hidden files and extensions
Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" -Name "Hidden" -Value 1 -Type DWord -Force
Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" -Name "HideFileExt" -Value 0 -Type DWord -Force
Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced" -Name "ShowSuperHidden" -Value 1 -Type DWord -Force

# Disable Internet Explorer Enhanced Security
try {
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Active Setup\Installed Components\{A509B1A7-37EF-4b3f-8CFC-4F3A74704073}" -Name "IsInstalled" -Value 0 -Type DWord -Force
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Active Setup\Installed Components\{A509B1A8-37EF-4b3f-8CFC-4F3A74704073}" -Name "IsInstalled" -Value 0 -Type DWord -Force
    Write-Host "Internet Explorer Enhanced Security disabled" -ForegroundColor Cyan
} catch {
    Write-Host "Error disabling IE Enhanced Security: $($_.Exception.Message)" -ForegroundColor Red
}

# Configure Internet Explorer for malware analysis
Write-Host "Configuring Internet Explorer for malware analysis..." -ForegroundColor Yellow
try {
    # Disable protected mode
    Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings\Zones\3" -Name "2500" -Value 3 -Type DWord -Force
    # Disable download blocking
    Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings\Zones\3" -Name "1803" -Value 0 -Type DWord -Force
    # Allow active content
    Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings\Zones\3" -Name "1200" -Value 0 -Type DWord -Force
    Write-Host "Internet Explorer configured for malware analysis" -ForegroundColor Cyan
} catch {
    Write-Host "Error configuring Internet Explorer: $($_.Exception.Message)" -ForegroundColor Red
}

# Create analysis user
Write-Host "Creating analysis user..." -ForegroundColor Yellow
$analysisUser = "analyst"
$analysisPassword = "analyst"

try {
    $securePassword = ConvertTo-SecureString $analysisPassword -AsPlainText -Force
    New-LocalUser -Name $analysisUser -Password $securePassword -FullName "Malware Analyst" -Description "GOAD-Blue Malware Analysis User" -ErrorAction SilentlyContinue
    Add-LocalGroupMember -Group "Administrators" -Member $analysisUser -ErrorAction SilentlyContinue
    Write-Host "Analysis user created: $analysisUser" -ForegroundColor Cyan
} catch {
    Write-Host "Analysis user may already exist or error occurred: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Configure automatic logon
Write-Host "Configuring automatic logon..." -ForegroundColor Yellow
try {
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon" -Name "AutoAdminLogon" -Value "1" -Type String -Force
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon" -Name "DefaultUserName" -Value $analysisUser -Type String -Force
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon" -Name "DefaultPassword" -Value $analysisPassword -Type String -Force
    Write-Host "Automatic logon configured for $analysisUser" -ForegroundColor Cyan
} catch {
    Write-Host "Error configuring automatic logon: $($_.Exception.Message)" -ForegroundColor Red
}

# Install Chocolatey if not already installed
Write-Host "Installing Chocolatey..." -ForegroundColor Yellow
if (!(Get-Command choco -ErrorAction SilentlyContinue)) {
    try {
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        Write-Host "Chocolatey installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "Error installing Chocolatey: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "Chocolatey already installed" -ForegroundColor Cyan
}

# Install essential tools via Chocolatey
Write-Host "Installing essential tools via Chocolatey..." -ForegroundColor Yellow
$chocoPackages = @(
    "7zip",
    "notepadplusplus",
    "firefox",
    "python3",
    "git",
    "vscode",
    "wireshark",
    "putty",
    "winscp"
)

foreach ($package in $chocoPackages) {
    try {
        choco install $package -y --ignore-checksums
        Write-Host "Installed: $package" -ForegroundColor Cyan
    } catch {
        Write-Host "Error installing $package: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Run FLARE-VM installation
Write-Host "Starting FLARE-VM installation (this may take 2-4 hours)..." -ForegroundColor Yellow
Write-Host "The system will reboot multiple times during installation" -ForegroundColor Red

try {
    # Set parameters for FLARE-VM installation
    $env:FLARE_VM_PASSWORD = $analysisPassword
    
    # Run FLARE-VM installation script
    & $flareVmScript -password $analysisPassword -noWait
    
    Write-Host "FLARE-VM installation started successfully" -ForegroundColor Green
} catch {
    Write-Host "Error starting FLARE-VM installation: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Attempting manual installation..." -ForegroundColor Yellow
    
    # Manual installation of key tools
    $manualTools = @{
        "PEiD" = "https://www.aldeid.com/wiki/PEiD"
        "x64dbg" = "https://x64dbg.com/"
        "Process Monitor" = "https://docs.microsoft.com/en-us/sysinternals/downloads/procmon"
        "Process Explorer" = "https://docs.microsoft.com/en-us/sysinternals/downloads/process-explorer"
        "Autoruns" = "https://docs.microsoft.com/en-us/sysinternals/downloads/autoruns"
    }
    
    Write-Host "Manual tool installation URLs:" -ForegroundColor Cyan
    foreach ($tool in $manualTools.Keys) {
        Write-Host "$tool`: $($manualTools[$tool])" -ForegroundColor White
    }
}

# Create desktop shortcuts
Write-Host "Creating desktop shortcuts..." -ForegroundColor Yellow
$shortcuts = @{
    "Command Prompt" = "C:\Windows\System32\cmd.exe"
    "PowerShell" = "C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe"
    "Registry Editor" = "C:\Windows\regedit.exe"
    "Event Viewer" = "C:\Windows\System32\eventvwr.exe"
    "Malware Analysis" = "C:\MalwareAnalysis"
}

$shell = New-Object -ComObject WScript.Shell
$desktopPath = [Environment]::GetFolderPath("Desktop")

foreach ($shortcut in $shortcuts.Keys) {
    try {
        $shortcutPath = Join-Path $desktopPath "$shortcut.lnk"
        $link = $shell.CreateShortcut($shortcutPath)
        $link.TargetPath = $shortcuts[$shortcut]
        $link.Save()
        Write-Host "Created shortcut: $shortcut" -ForegroundColor Cyan
    } catch {
        Write-Host "Error creating shortcut $shortcut`: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Install Python packages for malware analysis
Write-Host "Installing Python packages for malware analysis..." -ForegroundColor Yellow
$pythonPackages = @(
    "pefile",
    "yara-python",
    "pycryptodome",
    "requests",
    "beautifulsoup4",
    "lxml",
    "oletools",
    "python-magic-bin",
    "capstone",
    "unicorn",
    "keystone-engine"
)

foreach ($package in $pythonPackages) {
    try {
        python -m pip install $package
        Write-Host "Installed Python package: $package" -ForegroundColor Cyan
    } catch {
        Write-Host "Error installing Python package $package`: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Create analysis scripts
Write-Host "Creating analysis scripts..." -ForegroundColor Yellow

# Basic malware analysis script
$analysisScript = @"
# Basic Malware Analysis Script for GOAD-Blue
import os
import sys
import hashlib
import pefile

def analyze_file(file_path):
    print(f"Analyzing: {file_path}")
    
    # Calculate hashes
    with open(file_path, 'rb') as f:
        data = f.read()
        md5 = hashlib.md5(data).hexdigest()
        sha1 = hashlib.sha1(data).hexdigest()
        sha256 = hashlib.sha256(data).hexdigest()
    
    print(f"MD5: {md5}")
    print(f"SHA1: {sha1}")
    print(f"SHA256: {sha256}")
    
    # PE analysis
    try:
        pe = pefile.PE(file_path)
        print(f"Entry Point: 0x{pe.OPTIONAL_HEADER.AddressOfEntryPoint:x}")
        print(f"Image Base: 0x{pe.OPTIONAL_HEADER.ImageBase:x}")
        print(f"Sections: {len(pe.sections)}")
        
        for section in pe.sections:
            print(f"  {section.Name.decode().rstrip(chr(0))}: 0x{section.VirtualAddress:x}")
    except:
        print("Not a valid PE file")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python analyze.py <file_path>")
        sys.exit(1)
    
    analyze_file(sys.argv[1])
"@

$analysisScript | Out-File -FilePath "C:\MalwareAnalysis\Scripts\analyze.py" -Encoding UTF8

# Create completion marker
New-Item -Path "C:\goad-blue-flare-vm-configured.txt" -ItemType File -Force | Out-Null

Write-Host "=== FLARE-VM Installation and Configuration Completed ===" -ForegroundColor Green
Write-Host "Analysis User: $analysisUser" -ForegroundColor Cyan
Write-Host "Analysis Password: $analysisPassword" -ForegroundColor Cyan
Write-Host "Analysis Directory: C:\MalwareAnalysis" -ForegroundColor Cyan
Write-Host "Tools Directory: C:\Tools" -ForegroundColor Cyan
Write-Host "" -ForegroundColor White
Write-Host "Note: The system may need to reboot to complete FLARE-VM installation" -ForegroundColor Yellow
Write-Host "After reboot, FLARE-VM tools will be available in C:\Tools" -ForegroundColor Yellow
