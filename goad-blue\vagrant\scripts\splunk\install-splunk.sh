#!/bin/bash
# Splunk Enterprise installation script for GOAD-Blue

set -e

echo "=== Installing Splunk Enterprise for GOAD-Blue ==="

# Configuration variables
SPLUNK_VERSION="9.1.2"
SPLUNK_BUILD="b6b9c8185839"
SPLUNK_HOME="/opt/splunk"
SPLUNK_USER="splunk"
SPLUNK_ADMIN_PASSWORD="${SPLUNK_ADMIN_PASSWORD:-ChangeMeNow!}"

# Create splunk user
echo "Creating Splunk user..."
if ! id "$SPLUNK_USER" &>/dev/null; then
    useradd -r -m -s /bin/bash -d $SPLUNK_HOME $SPLUNK_USER
fi

# Download Splunk Enterprise
echo "Downloading Splunk Enterprise ${SPLUNK_VERSION}..."
cd /tmp
SPLUNK_FILENAME="splunk-${SPLUNK_VERSION}-${SPLUNK_BUILD}-linux-2.6-x86_64.tgz"
SPLUNK_URL="https://download.splunk.com/products/splunk/releases/${SPLUNK_VERSION}/linux/${SPLUNK_FILENAME}"

if [ ! -f "$SPLUNK_FILENAME" ]; then
    wget -O "$SPLUNK_FILENAME" "$SPLUNK_URL" || {
        echo "Failed to download Splunk. Please check the URL or download manually."
        exit 1
    }
fi

# Extract Splunk
echo "Extracting Splunk..."
tar -xzf "$SPLUNK_FILENAME" -C /opt/
chown -R $SPLUNK_USER:$SPLUNK_USER $SPLUNK_HOME

# Configure system limits for Splunk
echo "Configuring system limits..."
cat >> /etc/security/limits.conf << EOF
$SPLUNK_USER soft nofile 65536
$SPLUNK_USER hard nofile 65536
$SPLUNK_USER soft nproc 16384
$SPLUNK_USER hard nproc 16384
EOF

# Configure systemd limits
mkdir -p /etc/systemd/system.conf.d
cat > /etc/systemd/system.conf.d/splunk.conf << 'EOF'
[Manager]
DefaultLimitNOFILE=65536
DefaultLimitNPROC=16384
EOF

# Start Splunk for initial setup
echo "Starting Splunk for initial configuration..."
sudo -u $SPLUNK_USER $SPLUNK_HOME/bin/splunk start --accept-license --answer-yes --no-prompt --seed-passwd "$SPLUNK_ADMIN_PASSWORD"

# Stop Splunk to configure systemd
echo "Stopping Splunk to configure systemd..."
sudo -u $SPLUNK_USER $SPLUNK_HOME/bin/splunk stop

# Enable Splunk to start at boot with systemd
echo "Configuring Splunk systemd service..."
sudo -u $SPLUNK_USER $SPLUNK_HOME/bin/splunk enable boot-start -systemd-managed 1 --accept-license --answer-yes --no-prompt

# Configure Splunk for GOAD-Blue
echo "Configuring Splunk for GOAD-Blue..."

# Create GOAD-Blue specific configuration directory
mkdir -p $SPLUNK_HOME/etc/system/local
chown $SPLUNK_USER:$SPLUNK_USER $SPLUNK_HOME/etc/system/local

# Configure server.conf
cat > $SPLUNK_HOME/etc/system/local/server.conf << EOF
[general]
serverName = splunk-server.goad-blue.local
pass4SymmKey = \$7\$goad-blue-splunk-key

[sslConfig]
enableSplunkdSSL = true
sslVersions = tls1.2

[diskUsage]
minFreeSpace = 1000

[clustering]
mode = searchhead
master_uri = https://127.0.0.1:8089

[replication_port://9887]

[indexing]
defaultDatabase = \$SPLUNK_DB/defaultdb/db
maxDataSize = auto_high_volume
maxHotBuckets = 10
maxWarmDBCount = 300
EOF

# Configure web.conf
cat > $SPLUNK_HOME/etc/system/local/web.conf << EOF
[settings]
httpport = 8000
mgmtHostPort = 127.0.0.1:8089
enableSplunkWebSSL = true
privKeyPath = \$SPLUNK_HOME/etc/auth/splunkweb/privkey.pem
caCertPath = \$SPLUNK_HOME/etc/auth/splunkweb/cert.pem
startwebserver = 1
splunkdConnectionTimeout = 30

[expose_appserver_endpoints]
allowEmbedTokenAuth = true
EOF

# Configure inputs.conf for GOAD-Blue
cat > $SPLUNK_HOME/etc/system/local/inputs.conf << EOF
[default]
host = splunk-server.goad-blue.local

[splunktcp://9997]
disabled = false

[udp://514]
disabled = false
sourcetype = syslog
index = goad_blue_network

[tcp://514]
disabled = false
sourcetype = syslog
index = goad_blue_network

[http://goad-blue-hec]
disabled = false
port = 8088
token = goad-blue-hec-token
index = goad_blue_main
sourcetype = _json

[monitor:///var/log/goad-blue/]
disabled = false
index = goad_blue_main
sourcetype = goad_blue_logs
EOF

# Create GOAD-Blue indexes
echo "Creating GOAD-Blue indexes..."
cat > $SPLUNK_HOME/etc/system/local/indexes.conf << EOF
[goad_blue_main]
homePath = \$SPLUNK_DB/goad_blue_main/db
coldPath = \$SPLUNK_DB/goad_blue_main/colddb
thawedPath = \$SPLUNK_DB/goad_blue_main/thaweddb
maxDataSize = 1024
maxHotBuckets = 3
maxWarmDBCount = 100

[goad_blue_windows]
homePath = \$SPLUNK_DB/goad_blue_windows/db
coldPath = \$SPLUNK_DB/goad_blue_windows/colddb
thawedPath = \$SPLUNK_DB/goad_blue_windows/thaweddb
maxDataSize = 2048
maxHotBuckets = 5
maxWarmDBCount = 150

[goad_blue_linux]
homePath = \$SPLUNK_DB/goad_blue_linux/db
coldPath = \$SPLUNK_DB/goad_blue_linux/colddb
thawedPath = \$SPLUNK_DB/goad_blue_linux/thaweddb
maxDataSize = 1024
maxHotBuckets = 3
maxWarmDBCount = 100

[goad_blue_network]
homePath = \$SPLUNK_DB/goad_blue_network/db
coldPath = \$SPLUNK_DB/goad_blue_network/colddb
thawedPath = \$SPLUNK_DB/goad_blue_network/thaweddb
maxDataSize = 2048
maxHotBuckets = 5
maxWarmDBCount = 200

[goad_blue_security]
homePath = \$SPLUNK_DB/goad_blue_security/db
coldPath = \$SPLUNK_DB/goad_blue_security/colddb
thawedPath = \$SPLUNK_DB/goad_blue_security/thaweddb
maxDataSize = 1024
maxHotBuckets = 3
maxWarmDBCount = 100
EOF

# Set ownership
chown -R $SPLUNK_USER:$SPLUNK_USER $SPLUNK_HOME

# Configure UFW for Splunk
echo "Configuring firewall for Splunk..."
ufw allow 8000/tcp   # Splunk Web
ufw allow 8089/tcp   # Splunk Management
ufw allow 9997/tcp   # Splunk Forwarder
ufw allow 8088/tcp   # HTTP Event Collector
ufw allow 514/tcp    # Syslog
ufw allow 514/udp    # Syslog

# Start Splunk service
echo "Starting Splunk service..."
systemctl daemon-reload
systemctl enable Splunkd
systemctl start Splunkd

# Wait for Splunk to be ready
echo "Waiting for Splunk to be ready..."
timeout=300
counter=0
while ! curl -k https://localhost:8000 >/dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo "Timeout waiting for Splunk to start"
        exit 1
    fi
    echo "Waiting for Splunk... ($counter/$timeout)"
    sleep 10
    counter=$((counter + 10))
done

# Download enhanced GOAD-Blue monitoring script
echo "Installing enhanced Splunk monitoring script..."
if [ -f "/opt/goad-blue/scripts/monitoring/splunk-monitor.sh" ]; then
    cp /opt/goad-blue/scripts/monitoring/splunk-monitor.sh /opt/goad-blue/scripts/splunk-monitor.sh
    chmod +x /opt/goad-blue/scripts/splunk-monitor.sh
    print_status "OK" "Enhanced monitoring script installed"
else
    # Fallback to basic monitoring script
    cat > /opt/goad-blue/scripts/splunk-monitor.sh << 'EOF'
#!/bin/bash
# Basic Splunk monitoring script for GOAD-Blue

echo "=== Splunk System Status ==="
systemctl status Splunkd --no-pager
echo ""

echo "=== Splunk Process Information ==="
ps aux | grep splunk | grep -v grep
echo ""

echo "=== Splunk Disk Usage ==="
du -sh /opt/splunk/var/lib/splunk/
echo ""

echo "=== Splunk Index Status ==="
/opt/splunk/bin/splunk list index -auth admin:ChangeMeNow!
echo ""

echo "=== Splunk License Usage ==="
/opt/splunk/bin/splunk list licenser-pools -auth admin:ChangeMeNow!
echo ""
EOF
    chmod +x /opt/goad-blue/scripts/splunk-monitor.sh
fi

# Create completion marker
touch /opt/goad-blue/.splunk-installed

echo "=== Splunk Installation Completed ==="
echo "Splunk Web Interface: https://$(hostname -I | awk '{print $1}'):8000"
echo "Username: admin"
echo "Password: $SPLUNK_ADMIN_PASSWORD"
echo "Management Port: 8089"
echo "Forwarder Port: 9997"
echo ""
echo "To monitor Splunk: /opt/goad-blue/scripts/splunk-monitor.sh"
