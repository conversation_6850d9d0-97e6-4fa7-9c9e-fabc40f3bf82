# Training Scenarios

GOAD-Blue training scenarios provide realistic, hands-on cybersecurity exercises that simulate real-world attack and defense situations. These scenarios are designed to develop practical skills across all security disciplines using the comprehensive GOAD-Blue platform.

## 🎯 Overview

Training scenarios in GOAD-Blue combine realistic attack simulations with comprehensive defensive exercises, providing immersive learning experiences that prepare security professionals for real-world challenges.

```mermaid
graph TB
    subgraph "🎮 Scenario Categories"
        DETECTION[🔍 Detection Scenarios<br/>Threat Identification<br/>Alert Analysis<br/>IOC Recognition]
        INVESTIGATION[🕵️ Investigation Scenarios<br/>Forensic Analysis<br/>Evidence Collection<br/>Timeline Reconstruction]
        RESPONSE[🛡️ Response Scenarios<br/>Incident Containment<br/>Threat Mitigation<br/>Recovery Operations]
        HUNTING[🎯 Hunting Scenarios<br/>Proactive Detection<br/>Advanced Analytics<br/>Threat Discovery]
    end
    
    subgraph "⚔️ Attack Simulations"
        KERBEROASTING[🎫 Kerberoasting<br/>Credential Access<br/>Service Account Targeting<br/>TGS Manipulation]
        LATERAL_MOVEMENT[↔️ Lateral Movement<br/>Network Traversal<br/>Privilege Escalation<br/>System Compromise]
        MALWARE[🦠 Malware Analysis<br/>Sample Investigation<br/>Behavioral Analysis<br/>IOC Extraction]
        APT[🎯 APT Simulation<br/>Advanced Persistence<br/>Multi-stage Attacks<br/>Stealth Techniques]
    end
    
    subgraph "🛠️ Skill Development"
        TECHNICAL[⚙️ Technical Skills<br/>Tool Proficiency<br/>Analysis Techniques<br/>Automation]
        ANALYTICAL[🧠 Analytical Skills<br/>Critical Thinking<br/>Pattern Recognition<br/>Problem Solving]
        COMMUNICATION[💬 Communication<br/>Report Writing<br/>Stakeholder Updates<br/>Presentation Skills]
        LEADERSHIP[👨‍💼 Leadership<br/>Team Coordination<br/>Decision Making<br/>Crisis Management]
    end
    
    subgraph "📊 Assessment Methods"
        PRACTICAL[🔬 Practical Assessment<br/>Hands-on Exercises<br/>Real-time Performance<br/>Skill Demonstration]
        WRITTEN[📝 Written Assessment<br/>Knowledge Testing<br/>Concept Understanding<br/>Procedure Validation]
        PEER[👥 Peer Assessment<br/>Team Evaluation<br/>Collaboration Skills<br/>Professional Behavior]
        CONTINUOUS[🔄 Continuous Assessment<br/>Progress Tracking<br/>Skill Development<br/>Performance Improvement]
    end
    
    DETECTION --> KERBEROASTING
    INVESTIGATION --> LATERAL_MOVEMENT
    RESPONSE --> MALWARE
    HUNTING --> APT
    
    KERBEROASTING --> TECHNICAL
    LATERAL_MOVEMENT --> ANALYTICAL
    MALWARE --> COMMUNICATION
    APT --> LEADERSHIP
    
    TECHNICAL --> PRACTICAL
    ANALYTICAL --> WRITTEN
    COMMUNICATION --> PEER
    LEADERSHIP --> CONTINUOUS
    
    classDef categories fill:#2196f3,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef attacks fill:#f44336,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef skills fill:#4caf50,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef assessment fill:#ff9800,stroke:#ffffff,stroke-width:2px,color:#fff
    
    class DETECTION,INVESTIGATION,RESPONSE,HUNTING categories
    class KERBEROASTING,LATERAL_MOVEMENT,MALWARE,APT attacks
    class TECHNICAL,ANALYTICAL,COMMUNICATION,LEADERSHIP skills
    class PRACTICAL,WRITTEN,PEER,CONTINUOUS assessment
```

## 📚 Scenario Library

### **🔍 Detection and Analysis Scenarios**

#### **[Kerberoasting Detection](kerberoasting.md)**
**Difficulty:** Intermediate | **Duration:** 3-4 hours | **Skills:** SIEM Analysis, Active Directory Security

Learn to detect and analyze Kerberoasting attacks targeting service accounts in the GOAD Active Directory environment. Practice identifying suspicious TGS requests, analyzing Kerberos logs, and developing detection rules.

**Key Learning Objectives:**
- Kerberos protocol understanding
- Service Principal Name (SPN) analysis
- Log correlation techniques
- Detection rule development

#### **[Lateral Movement Detection](lateral-movement.md)**
**Difficulty:** Advanced | **Duration:** 4-6 hours | **Skills:** Network Analysis, Endpoint Detection

Master the detection of lateral movement techniques including Pass-the-Hash, Pass-the-Ticket, and remote service execution across the GOAD domain environment.

**Key Learning Objectives:**
- Network traffic analysis
- Endpoint behavior monitoring
- Credential theft detection
- Attack path reconstruction

### **🦠 Malware Analysis Scenarios**

#### **[Malware Analysis Workshop](malware-analysis.md)**
**Difficulty:** Intermediate to Advanced | **Duration:** 6-8 hours | **Skills:** Reverse Engineering, Forensics

Comprehensive malware analysis using FLARE-VM to examine real-world malware samples, extract IOCs, and develop detection signatures for deployment across GOAD-Blue.

**Key Learning Objectives:**
- Static and dynamic analysis
- Behavioral analysis techniques
- IOC extraction and validation
- Detection rule creation

### **🎯 Advanced Persistent Threat Scenarios**

#### **[APT Campaign Simulation](apt-simulation.md)**
**Difficulty:** Expert | **Duration:** 8-12 hours | **Skills:** Threat Hunting, Incident Response

Experience a full APT campaign simulation from initial compromise through data exfiltration, practicing advanced detection, investigation, and response techniques.

**Key Learning Objectives:**
- Multi-stage attack analysis
- Advanced threat hunting
- Attribution techniques
- Comprehensive incident response

## 🎓 Scenario-Based Learning Framework

### **Progressive Skill Development**

```yaml
skill_progression:
  foundation_level:
    duration: "2-4 hours per scenario"
    focus: "Basic detection and analysis skills"
    scenarios:
      - Basic malware detection
      - Simple network intrusion
      - Phishing email analysis
      - Basic log analysis
    
    assessment_criteria:
      - Tool navigation proficiency
      - Basic analysis techniques
      - Documentation quality
      - Time management
  
  intermediate_level:
    duration: "4-6 hours per scenario"
    focus: "Advanced analysis and correlation"
    scenarios:
      - Kerberoasting detection
      - Lateral movement analysis
      - Memory forensics
      - Network traffic analysis
    
    assessment_criteria:
      - Advanced tool usage
      - Correlation techniques
      - Investigation methodology
      - Report quality
  
  advanced_level:
    duration: "6-8 hours per scenario"
    focus: "Complex investigations and hunting"
    scenarios:
      - APT campaign analysis
      - Advanced malware analysis
      - Insider threat investigation
      - Supply chain compromise
    
    assessment_criteria:
      - Investigation leadership
      - Advanced analytics
      - Strategic thinking
      - Knowledge transfer
  
  expert_level:
    duration: "8-12 hours per scenario"
    focus: "Research and development"
    scenarios:
      - Zero-day analysis
      - Custom tool development
      - Novel attack techniques
      - Research projects
    
    assessment_criteria:
      - Innovation and creativity
      - Research methodology
      - Tool development
      - Community contribution
```

### **Scenario Customization Framework**

```python
# Scenario customization and deployment framework
class ScenarioManager:
    def __init__(self, goad_environment):
        self.environment = goad_environment
        self.scenarios = {}
        self.active_scenarios = []
        
    def create_scenario(self, scenario_config):
        """Create a new training scenario"""
        scenario = {
            'id': scenario_config['id'],
            'name': scenario_config['name'],
            'difficulty': scenario_config['difficulty'],
            'duration': scenario_config['duration'],
            'learning_objectives': scenario_config['objectives'],
            'prerequisites': scenario_config.get('prerequisites', []),
            'tools_required': scenario_config.get('tools', []),
            'environment_setup': scenario_config.get('setup', {}),
            'assessment_criteria': scenario_config.get('assessment', {}),
            'artifacts': scenario_config.get('artifacts', [])
        }
        
        self.scenarios[scenario['id']] = scenario
        return scenario
    
    def deploy_scenario(self, scenario_id, participants):
        """Deploy scenario to GOAD environment"""
        if scenario_id not in self.scenarios:
            raise ValueError(f"Scenario {scenario_id} not found")
        
        scenario = self.scenarios[scenario_id]
        
        # Setup environment
        self.setup_environment(scenario['environment_setup'])
        
        # Deploy artifacts
        self.deploy_artifacts(scenario['artifacts'])
        
        # Initialize monitoring
        self.initialize_monitoring(scenario_id, participants)
        
        # Add to active scenarios
        self.active_scenarios.append({
            'scenario_id': scenario_id,
            'participants': participants,
            'start_time': datetime.now(),
            'status': 'active'
        })
        
        return f"Scenario {scenario_id} deployed successfully"
    
    def setup_environment(self, setup_config):
        """Configure GOAD environment for scenario"""
        # Configure network settings
        if 'network' in setup_config:
            self.configure_network(setup_config['network'])
        
        # Deploy malware samples (safely)
        if 'malware_samples' in setup_config:
            self.deploy_malware_samples(setup_config['malware_samples'])
        
        # Configure user accounts
        if 'user_accounts' in setup_config:
            self.configure_user_accounts(setup_config['user_accounts'])
        
        # Setup monitoring
        if 'monitoring' in setup_config:
            self.configure_monitoring(setup_config['monitoring'])
    
    def deploy_artifacts(self, artifacts):
        """Deploy scenario artifacts to environment"""
        for artifact in artifacts:
            if artifact['type'] == 'log_entries':
                self.inject_log_entries(artifact['data'])
            elif artifact['type'] == 'network_traffic':
                self.replay_network_traffic(artifact['data'])
            elif artifact['type'] == 'file_system':
                self.create_file_artifacts(artifact['data'])
            elif artifact['type'] == 'registry':
                self.create_registry_artifacts(artifact['data'])
    
    def assess_performance(self, scenario_id, participant_id):
        """Assess participant performance in scenario"""
        scenario = self.scenarios[scenario_id]
        assessment_criteria = scenario['assessment_criteria']
        
        performance_data = self.collect_performance_data(scenario_id, participant_id)
        
        scores = {}
        for criterion, weight in assessment_criteria.items():
            scores[criterion] = self.calculate_criterion_score(
                performance_data, criterion, weight
            )
        
        overall_score = sum(scores.values()) / len(scores)
        
        return {
            'participant_id': participant_id,
            'scenario_id': scenario_id,
            'scores': scores,
            'overall_score': overall_score,
            'feedback': self.generate_feedback(scores, scenario),
            'recommendations': self.generate_recommendations(scores)
        }

# Example scenario configurations
kerberoasting_scenario = {
    'id': 'kerberoasting_detection',
    'name': 'Kerberoasting Attack Detection',
    'difficulty': 'intermediate',
    'duration': '3-4 hours',
    'objectives': [
        'Detect Kerberoasting attacks',
        'Analyze Kerberos logs',
        'Develop detection rules',
        'Implement countermeasures'
    ],
    'tools': ['Splunk', 'Wireshark', 'PowerShell'],
    'setup': {
        'user_accounts': [
            {'name': 'svc_sql', 'spn': 'MSSQLSvc/sql.sevenkingdoms.local:1433'},
            {'name': 'svc_web', 'spn': 'HTTP/web.sevenkingdoms.local'}
        ],
        'monitoring': ['kerberos_logs', 'network_traffic']
    },
    'assessment': {
        'detection_speed': 25,
        'analysis_accuracy': 30,
        'rule_quality': 25,
        'documentation': 20
    }
}
```

## 📊 Scenario Metrics and Analytics

### **Performance Tracking**

```yaml
performance_metrics:
  detection_metrics:
    - Time to initial detection
    - False positive rate
    - True positive rate
    - Alert quality score
  
  investigation_metrics:
    - Evidence collection completeness
    - Timeline accuracy
    - Root cause identification
    - Investigation methodology
  
  response_metrics:
    - Containment effectiveness
    - Recovery time
    - Communication quality
    - Stakeholder satisfaction
  
  learning_metrics:
    - Skill improvement rate
    - Knowledge retention
    - Practical application
    - Peer collaboration

analytics_dashboard:
  individual_progress:
    - Scenario completion rates
    - Skill development tracking
    - Performance trends
    - Certification progress
  
  cohort_analysis:
    - Group performance comparison
    - Collaboration effectiveness
    - Knowledge sharing patterns
    - Team dynamics
  
  scenario_effectiveness:
    - Learning objective achievement
    - Difficulty calibration
    - Engagement levels
    - Feedback analysis
  
  organizational_impact:
    - Security posture improvement
    - Incident response effectiveness
    - Threat detection enhancement
    - Cost-benefit analysis
```

## 🎯 Getting Started

### **Scenario Selection Guide**

1. **Assess Current Skill Level**
   - Complete skills assessment
   - Review prerequisites
   - Identify learning objectives

2. **Choose Appropriate Scenarios**
   - Start with foundation level
   - Progress systematically
   - Focus on role-specific skills

3. **Prepare Environment**
   - Verify tool access
   - Review documentation
   - Set up monitoring

4. **Execute Scenario**
   - Follow guided instructions
   - Document findings
   - Seek assistance when needed

5. **Complete Assessment**
   - Submit deliverables
   - Participate in debrief
   - Plan next steps

---

!!! tip "Scenario Best Practices"
    - Start with scenarios matching your current skill level
    - Focus on understanding concepts, not just completing tasks
    - Document your methodology and findings thoroughly
    - Collaborate with peers and seek feedback
    - Apply learned skills to real-world situations

!!! warning "Safety Considerations"
    - All scenarios run in isolated training environments
    - Never use training techniques on production systems
    - Follow ethical guidelines and legal requirements
    - Report any security concerns immediately
    - Maintain confidentiality of training materials

!!! info "Continuous Learning"
    - Scenarios are regularly updated with new threats
    - Feedback is incorporated to improve effectiveness
    - Advanced scenarios are added based on industry trends
    - Community contributions are welcomed and encouraged
    - Certification pathways align with industry standards
