[Identification]
Action=InstallPrimarySite


[Options]
ProductID=EVAL
SiteCode="{{site_code}}"
SiteName="{{domain}} Site"
SMSInstallDir=C:\Program Files\Microsoft Configuration Manager
SDKServer="{{sccm_server}}.{{domain}}"
RoleCommunicationProtocol=HTTPorHTTPS
ClientsUsePKICertificate=0
PrerequisiteComp=0
PrerequisitePath=C:\updates
ManagementPoint="{{sccm_server}}.{{domain}}"
ManagementPointProtocol=HTTP
DistributionPoint="{{sccm_server}}.{{domain}}"
DistributionPointProtocol=HTTP
DistributionPointInstallIIS=0
AdminConsole=1
JoinCEIP=0

[SQLConfigOptions]
SQLServerName="{{sccm_mssql_server}}.{{domain}}"
DatabaseName=CM_P01
SQLSSBPort=4022

[CloudConnectorOptions]
CloudConnector=1
CloudConnectorServer="{{sccm_server}}.{{domain}}"
UseProxy=0
ProxyName=
ProxyPort=

[SystemCenterOptions]
SysCenterId=TPy9JI2owGLVpOdtunFRk8tF5Kitq5kmIYHrbBm+pIU=

[HierarchyExpansionOption]
