# Log Analysis for Troubleshooting

This guide covers comprehensive log analysis techniques for troubleshooting GOAD-Blue components, identifying issues, and monitoring system health through log examination.

## 🎯 Overview

Log analysis is crucial for understanding system behavior, identifying problems, and maintaining GOAD-Blue environments. This guide covers log collection, analysis techniques, and automated monitoring.

```mermaid
graph TB
    subgraph "📊 Log Sources"
        SYSTEM[🖥️ System Logs<br/>syslog, auth.log<br/>kernel messages<br/>boot logs]
        APPLICATION[📱 Application Logs<br/>Splunk, Elasticsearch<br/>Velociraptor, Suricata<br/>Custom applications]
        SECURITY[🔒 Security Logs<br/>Windows Event Logs<br/>Authentication logs<br/>Audit trails]
        NETWORK[🌐 Network Logs<br/>Firewall logs<br/>DNS queries<br/>Traffic analysis]
    end
    
    subgraph "🔍 Analysis Techniques"
        PARSING[📝 Log Parsing<br/>Pattern matching<br/>Field extraction<br/>Normalization]
        CORRELATION[🔗 Correlation<br/>Event correlation<br/>Timeline analysis<br/>Cross-reference]
        AGGREGATION[📈 Aggregation<br/>Statistical analysis<br/>Trend identification<br/>Anomaly detection]
        VISUALIZATION[📊 Visualization<br/>Dashboards<br/>Charts and graphs<br/>Heat maps]
    end
    
    subgraph "🛠️ Analysis Tools"
        CLI_TOOLS[⌨️ CLI Tools<br/>grep, awk, sed<br/>jq, cut, sort<br/>tail, head]
        SIEM_TOOLS[🔍 SIEM Tools<br/>Splunk SPL<br/>Elasticsearch DSL<br/>Kibana queries]
        SCRIPTS[📜 Scripts<br/>Python analysis<br/>Bash automation<br/>Custom parsers]
        MONITORING[📡 Monitoring<br/>Real-time alerts<br/>Automated analysis<br/>Health checks]
    end
    
    SYSTEM --> PARSING
    APPLICATION --> CORRELATION
    SECURITY --> AGGREGATION
    NETWORK --> VISUALIZATION
    
    PARSING --> CLI_TOOLS
    CORRELATION --> SIEM_TOOLS
    AGGREGATION --> SCRIPTS
    VISUALIZATION --> MONITORING
    
    classDef sources fill:#2196f3,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef techniques fill:#4caf50,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef tools fill:#ff9800,stroke:#ffffff,stroke-width:2px,color:#fff
    
    class SYSTEM,APPLICATION,SECURITY,NETWORK sources
    class PARSING,CORRELATION,AGGREGATION,VISUALIZATION techniques
    class CLI_TOOLS,SIEM_TOOLS,SCRIPTS,MONITORING tools
```

## 📂 Log Locations and Types

### **System Logs**

```bash
# Primary system log locations
/var/log/syslog          # General system messages
/var/log/auth.log        # Authentication events
/var/log/kern.log        # Kernel messages
/var/log/dmesg           # Boot messages
/var/log/messages        # General messages (CentOS/RHEL)
/var/log/secure          # Security events (CentOS/RHEL)

# Service-specific logs
/var/log/apache2/        # Apache web server
/var/log/nginx/          # Nginx web server
/var/log/mysql/          # MySQL database
/var/log/postgresql/     # PostgreSQL database
```

### **GOAD-Blue Component Logs**

```yaml
goad_blue_logs:
  splunk:
    main_log: "/opt/splunk/var/log/splunk/splunkd.log"
    web_log: "/opt/splunk/var/log/splunk/web_service.log"
    metrics: "/opt/splunk/var/log/splunk/metrics.log"
    audit: "/opt/splunk/var/log/splunk/audit.log"
    
  elasticsearch:
    main_log: "/var/log/elasticsearch/elasticsearch.log"
    slow_log: "/var/log/elasticsearch/elasticsearch_index_search_slowlog.log"
    gc_log: "/var/log/elasticsearch/gc.log"
    
  velociraptor:
    server_log: "/var/log/velociraptor/server.log"
    client_log: "/var/log/velociraptor/client.log"
    
  suricata:
    main_log: "/var/log/suricata/suricata.log"
    eve_json: "/var/log/suricata/eve.json"
    fast_log: "/var/log/suricata/fast.log"
    
  goad_blue:
    main_log: "/var/log/goad-blue/goad-blue.log"
    deployment: "/var/log/goad-blue/deployment.log"
    integration: "/var/log/goad-blue/integration.log"
    performance: "/var/log/goad-blue/performance.log"
```

### **Windows Event Logs**

```powershell
# Windows Event Log locations
Get-WinEvent -ListLog * | Where-Object {$_.RecordCount -gt 0} | 
    Select-Object LogName, RecordCount, FileSize

# Key Windows logs for GOAD-Blue
$ImportantLogs = @(
    "System",
    "Application", 
    "Security",
    "Microsoft-Windows-Sysmon/Operational",
    "Microsoft-Windows-PowerShell/Operational",
    "Microsoft-Windows-WinRM/Operational"
)

foreach ($Log in $ImportantLogs) {
    Write-Host "=== $Log ==="
    Get-WinEvent -LogName $Log -MaxEvents 5 | 
        Select-Object TimeCreated, Id, LevelDisplayName, Message
}
```

## 🔍 Log Analysis Techniques

### **Command Line Analysis**

#### **Basic Log Examination**

```bash
# View recent log entries
tail -f /var/log/syslog
tail -n 100 /var/log/goad-blue/goad-blue.log

# Search for specific patterns
grep -i "error" /var/log/syslog
grep -E "(error|warning|critical)" /var/log/goad-blue/goad-blue.log

# Count occurrences
grep -c "failed" /var/log/auth.log
grep -c "connection refused" /var/log/goad-blue/goad-blue.log

# Time-based filtering
grep "$(date '+%Y-%m-%d %H')" /var/log/syslog
awk '/2024-01-15 14:/' /var/log/goad-blue/goad-blue.log
```

#### **Advanced Pattern Matching**

```bash
# Extract IP addresses
grep -oE '\b([0-9]{1,3}\.){3}[0-9]{1,3}\b' /var/log/auth.log

# Extract timestamps and errors
awk '/ERROR/ {print $1, $2, $3, $0}' /var/log/goad-blue/goad-blue.log

# Complex pattern matching
sed -n '/ERROR/,/^$/p' /var/log/goad-blue/goad-blue.log

# JSON log parsing
jq '.timestamp, .level, .message' /var/log/goad-blue/structured.log

# Multi-file analysis
grep -r "connection timeout" /var/log/goad-blue/
find /var/log -name "*.log" -exec grep -l "splunk" {} \;
```

### **Statistical Analysis**

```bash
#!/bin/bash
# Log statistics analysis script

LOG_FILE="/var/log/goad-blue/goad-blue.log"

echo "=== Log Analysis Statistics ==="
echo "Total lines: $(wc -l < $LOG_FILE)"
echo "File size: $(du -h $LOG_FILE | cut -f1)"
echo "Date range: $(head -1 $LOG_FILE | cut -d' ' -f1-2) to $(tail -1 $LOG_FILE | cut -d' ' -f1-2)"

echo -e "\n=== Log Level Distribution ==="
grep -o '\[INFO\]\|\[WARNING\]\|\[ERROR\]\|\[DEBUG\]' $LOG_FILE | sort | uniq -c | sort -nr

echo -e "\n=== Top Error Messages ==="
grep '\[ERROR\]' $LOG_FILE | cut -d']' -f2- | sort | uniq -c | sort -nr | head -10

echo -e "\n=== Hourly Activity ==="
awk '{print $2}' $LOG_FILE | cut -d':' -f1 | sort | uniq -c | sort -k2n

echo -e "\n=== Component Activity ==="
grep -o 'component=[a-zA-Z_]*' $LOG_FILE | sort | uniq -c | sort -nr
```

### **Timeline Analysis**

```python
#!/usr/bin/env python3
# Log timeline analysis

import re
import json
from datetime import datetime
from collections import defaultdict

class LogTimelineAnalyzer:
    def __init__(self, log_file):
        self.log_file = log_file
        self.events = []
        self.timeline = defaultdict(list)
        
    def parse_log_entry(self, line):
        """Parse a single log entry"""
        # Example log format: 2024-01-15 14:30:25 [INFO] component=splunk message="Service started"
        pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \[(\w+)\] (.+)'
        match = re.match(pattern, line.strip())
        
        if match:
            timestamp_str, level, message = match.groups()
            timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
            
            # Extract component if present
            component_match = re.search(r'component=(\w+)', message)
            component = component_match.group(1) if component_match else 'unknown'
            
            return {
                'timestamp': timestamp,
                'level': level,
                'component': component,
                'message': message,
                'raw_line': line.strip()
            }
        return None
    
    def load_logs(self):
        """Load and parse log file"""
        with open(self.log_file, 'r') as f:
            for line in f:
                event = self.parse_log_entry(line)
                if event:
                    self.events.append(event)
                    hour_key = event['timestamp'].strftime('%Y-%m-%d %H:00')
                    self.timeline[hour_key].append(event)
    
    def analyze_error_patterns(self):
        """Analyze error patterns over time"""
        error_events = [e for e in self.events if e['level'] == 'ERROR']
        
        # Group errors by component
        component_errors = defaultdict(list)
        for event in error_events:
            component_errors[event['component']].append(event)
        
        print("=== Error Analysis ===")
        for component, errors in component_errors.items():
            print(f"\n{component}: {len(errors)} errors")
            
            # Find error clusters (multiple errors within 5 minutes)
            clusters = self.find_error_clusters(errors, minutes=5)
            if clusters:
                print(f"  Error clusters found: {len(clusters)}")
                for i, cluster in enumerate(clusters):
                    start_time = cluster[0]['timestamp']
                    end_time = cluster[-1]['timestamp']
                    print(f"    Cluster {i+1}: {start_time} - {end_time} ({len(cluster)} errors)")
    
    def find_error_clusters(self, errors, minutes=5):
        """Find clusters of errors occurring within specified time window"""
        if not errors:
            return []
        
        # Sort errors by timestamp
        sorted_errors = sorted(errors, key=lambda x: x['timestamp'])
        
        clusters = []
        current_cluster = [sorted_errors[0]]
        
        for error in sorted_errors[1:]:
            time_diff = (error['timestamp'] - current_cluster[-1]['timestamp']).total_seconds()
            
            if time_diff <= minutes * 60:  # Within time window
                current_cluster.append(error)
            else:
                if len(current_cluster) > 1:  # Only consider clusters with multiple errors
                    clusters.append(current_cluster)
                current_cluster = [error]
        
        # Don't forget the last cluster
        if len(current_cluster) > 1:
            clusters.append(current_cluster)
        
        return clusters
    
    def generate_timeline_report(self):
        """Generate timeline report"""
        print("=== Timeline Analysis ===")
        
        for hour, events in sorted(self.timeline.items()):
            level_counts = defaultdict(int)
            component_counts = defaultdict(int)
            
            for event in events:
                level_counts[event['level']] += 1
                component_counts[event['component']] += 1
            
            print(f"\n{hour}:")
            print(f"  Total events: {len(events)}")
            print(f"  Levels: {dict(level_counts)}")
            print(f"  Components: {dict(component_counts)}")
            
            # Highlight significant events
            errors = [e for e in events if e['level'] == 'ERROR']
            if errors:
                print(f"  Errors:")
                for error in errors[:3]:  # Show first 3 errors
                    print(f"    {error['timestamp'].strftime('%H:%M:%S')} - {error['message'][:80]}...")
    
    def export_analysis(self, output_file):
        """Export analysis results to JSON"""
        analysis_data = {
            'total_events': len(self.events),
            'date_range': {
                'start': min(e['timestamp'] for e in self.events).isoformat() if self.events else None,
                'end': max(e['timestamp'] for e in self.events).isoformat() if self.events else None
            },
            'level_distribution': {},
            'component_distribution': {},
            'hourly_activity': {}
        }
        
        # Calculate distributions
        for event in self.events:
            level = event['level']
            component = event['component']
            hour = event['timestamp'].strftime('%H')
            
            analysis_data['level_distribution'][level] = analysis_data['level_distribution'].get(level, 0) + 1
            analysis_data['component_distribution'][component] = analysis_data['component_distribution'].get(component, 0) + 1
            analysis_data['hourly_activity'][hour] = analysis_data['hourly_activity'].get(hour, 0) + 1
        
        with open(output_file, 'w') as f:
            json.dump(analysis_data, f, indent=2)
        
        print(f"Analysis exported to {output_file}")

# Example usage
if __name__ == "__main__":
    analyzer = LogTimelineAnalyzer('/var/log/goad-blue/goad-blue.log')
    analyzer.load_logs()
    analyzer.analyze_error_patterns()
    analyzer.generate_timeline_report()
    analyzer.export_analysis('/tmp/log_analysis.json')
```

## 🔧 Component-Specific Log Analysis

### **Splunk Log Analysis**

```bash
# Splunk service status from logs
grep -E "(started|stopped|restarted)" /opt/splunk/var/log/splunk/splunkd.log | tail -10

# Splunk indexing performance
grep "IndexProcessor" /opt/splunk/var/log/splunk/splunkd.log | grep -E "(events|MB)"

# Splunk search performance
grep "Search" /opt/splunk/var/log/splunk/splunkd.log | grep -E "(completed|duration)"

# Splunk license usage
grep -i "license" /opt/splunk/var/log/splunk/splunkd.log | tail -5

# Splunk forwarder connectivity
grep -E "(connect|disconnect)" /opt/splunk/var/log/splunk/splunkd.log
```

**Splunk SPL Queries for Log Analysis:**

```sql
-- Splunk internal logs analysis
index=_internal source=*splunkd.log* 
| stats count by component, log_level 
| sort - count

-- Indexing performance
index=_internal source=*metrics.log* group=per_index_thruput 
| timechart span=1h avg(kb) by series

-- Search performance
index=_audit action=search 
| eval search_duration=total_run_time 
| stats avg(search_duration) max(search_duration) by user

-- Error analysis
index=_internal log_level=ERROR 
| stats count by component, message 
| sort - count
```

### **Elasticsearch Log Analysis**

```bash
# Elasticsearch cluster health from logs
grep -E "(cluster.*health|yellow|red)" /var/log/elasticsearch/elasticsearch.log

# Elasticsearch indexing performance
grep -E "(index.*took|refresh.*took)" /var/log/elasticsearch/elasticsearch.log

# Elasticsearch memory issues
grep -E "(OutOfMemoryError|heap|gc)" /var/log/elasticsearch/elasticsearch.log

# Elasticsearch slow queries
tail -f /var/log/elasticsearch/elasticsearch_index_search_slowlog.log

# Elasticsearch node communication
grep -E "(node.*joined|node.*left|master)" /var/log/elasticsearch/elasticsearch.log
```

**Elasticsearch Query DSL for Log Analysis:**

```json
{
  "query": {
    "bool": {
      "must": [
        {"range": {"@timestamp": {"gte": "now-1h"}}},
        {"term": {"level": "ERROR"}}
      ]
    }
  },
  "aggs": {
    "error_types": {
      "terms": {
        "field": "logger.keyword",
        "size": 10
      }
    },
    "error_timeline": {
      "date_histogram": {
        "field": "@timestamp",
        "interval": "5m"
      }
    }
  }
}
```

### **Velociraptor Log Analysis**

```bash
# Velociraptor server status
grep -E "(Starting|Stopping|Error)" /var/log/velociraptor/server.log

# Client connectivity
grep -E "(client.*connected|client.*disconnected)" /var/log/velociraptor/server.log

# Hunt performance
grep -E "(hunt.*started|hunt.*completed)" /var/log/velociraptor/server.log

# Artifact collection
grep -E "(artifact.*collected|collection.*completed)" /var/log/velociraptor/server.log
```

### **Suricata Log Analysis**

```bash
# Suricata engine status
grep -E "(engine.*started|engine.*stopped)" /var/log/suricata/suricata.log

# Rule loading
grep -E "(rules.*loaded|signatures.*loaded)" /var/log/suricata/suricata.log

# Performance statistics
grep -E "(packets.*processed|alerts.*generated)" /var/log/suricata/suricata.log

# Parse EVE JSON logs
jq '.event_type, .timestamp, .alert.signature' /var/log/suricata/eve.json | head -20
```

## 📊 Automated Log Monitoring

### **Real-time Log Monitoring Script**

```bash
#!/bin/bash
# Real-time log monitoring for GOAD-Blue

ALERT_EMAIL="<EMAIL>"
LOG_DIR="/var/log/goad-blue"
ALERT_THRESHOLD=5

# Function to send alert
send_alert() {
    local message="$1"
    local severity="$2"
    
    echo "ALERT [$severity]: $message" | tee -a /var/log/goad-blue-alerts.log
    
    # Send email if configured
    if command -v mail &> /dev/null; then
        echo "$message" | mail -s "GOAD-Blue Alert [$severity]" "$ALERT_EMAIL"
    fi
}

# Monitor for critical errors
monitor_errors() {
    local log_file="$1"
    local component="$2"
    
    # Count errors in last 5 minutes
    error_count=$(grep -c "$(date -d '5 minutes ago' '+%Y-%m-%d %H:%M').*ERROR" "$log_file" 2>/dev/null || echo 0)
    
    if [ "$error_count" -gt "$ALERT_THRESHOLD" ]; then
        send_alert "High error rate in $component: $error_count errors in 5 minutes" "HIGH"
    fi
}

# Monitor disk space for log directories
monitor_disk_space() {
    local usage=$(df "$LOG_DIR" | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [ "$usage" -gt 90 ]; then
        send_alert "Log directory disk usage critical: ${usage}%" "CRITICAL"
    elif [ "$usage" -gt 80 ]; then
        send_alert "Log directory disk usage warning: ${usage}%" "WARNING"
    fi
}

# Monitor log file growth
monitor_log_growth() {
    local log_file="$1"
    local max_size_mb="$2"
    
    if [ -f "$log_file" ]; then
        local size_mb=$(du -m "$log_file" | cut -f1)
        
        if [ "$size_mb" -gt "$max_size_mb" ]; then
            send_alert "Log file $log_file exceeds size limit: ${size_mb}MB" "WARNING"
        fi
    fi
}

# Main monitoring loop
main() {
    echo "Starting GOAD-Blue log monitoring..."
    
    while true; do
        # Monitor component logs
        monitor_errors "/var/log/goad-blue/goad-blue.log" "GOAD-Blue"
        monitor_errors "/opt/splunk/var/log/splunk/splunkd.log" "Splunk"
        monitor_errors "/var/log/elasticsearch/elasticsearch.log" "Elasticsearch"
        monitor_errors "/var/log/velociraptor/server.log" "Velociraptor"
        
        # Monitor disk space
        monitor_disk_space
        
        # Monitor log file sizes
        monitor_log_growth "/var/log/goad-blue/goad-blue.log" 1000
        monitor_log_growth "/opt/splunk/var/log/splunk/splunkd.log" 500
        
        sleep 300  # Check every 5 minutes
    done
}

# Run monitoring
main
```

### **Log Rotation and Cleanup**

```bash
#!/bin/bash
# Log rotation and cleanup script

# Configure logrotate for GOAD-Blue
cat > /etc/logrotate.d/goad-blue << 'EOF'
/var/log/goad-blue/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 goad-blue goad-blue
    postrotate
        systemctl reload goad-blue 2>/dev/null || true
    endscript
}

/opt/splunk/var/log/splunk/*.log {
    daily
    rotate 14
    compress
    delaycompress
    missingok
    notifempty
    copytruncate
    postrotate
        /opt/splunk/bin/splunk _internal call /services/server/logger -post:name=splunkd -post:level=INFO 2>/dev/null || true
    endscript
}

/var/log/elasticsearch/*.log {
    daily
    rotate 14
    compress
    delaycompress
    missingok
    notifempty
    copytruncate
}
EOF

# Clean old logs manually
find /var/log/goad-blue -name "*.log.*" -mtime +30 -delete
find /opt/splunk/var/log/splunk -name "*.log.*" -mtime +14 -delete
find /var/log/elasticsearch -name "*.log.*" -mtime +14 -delete

echo "Log rotation configured and old logs cleaned"
```

---

!!! tip "Log Analysis Best Practices"
    - Use structured logging formats (JSON) when possible
    - Implement log rotation to prevent disk space issues
    - Set up automated monitoring for critical errors
    - Correlate logs across multiple components for better insights
    - Maintain consistent timestamp formats across all logs

!!! warning "Log Analysis Pitfalls"
    - Don't ignore warning messages - they often precede errors
    - Be aware of log timezone differences when correlating events
    - Large log files can impact system performance during analysis
    - Sensitive information may be logged - ensure proper access controls
    - Log analysis tools may miss patterns in unstructured logs

!!! info "Advanced Log Analysis"
    - Use machine learning for anomaly detection in logs
    - Implement real-time log streaming for immediate analysis
    - Create custom parsers for application-specific log formats
    - Set up centralized logging for distributed environments
    - Integrate log analysis with incident response workflows

## 🔬 Advanced Log Analysis Techniques

### **Log Correlation and Pattern Detection**

```python
#!/usr/bin/env python3
# Advanced log correlation engine

import re
import json
import pandas as pd
from datetime import datetime, timedelta
from collections import defaultdict

class LogCorrelationEngine:
    def __init__(self):
        self.events = []
        self.correlation_rules = []
        self.patterns = {}

    def add_correlation_rule(self, name, pattern, time_window=300):
        """Add correlation rule for detecting related events"""
        self.correlation_rules.append({
            'name': name,
            'pattern': pattern,
            'time_window': time_window,
            'matches': []
        })

    def parse_multi_source_logs(self, log_sources):
        """Parse logs from multiple sources"""
        for source_name, log_file in log_sources.items():
            with open(log_file, 'r') as f:
                for line_num, line in enumerate(f, 1):
                    event = self.parse_log_line(line, source_name, line_num)
                    if event:
                        self.events.append(event)

    def parse_log_line(self, line, source, line_num):
        """Parse individual log line"""
        # Handle different log formats
        formats = {
            'syslog': r'(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})\s+(\S+)\s+(\S+):\s*(.*)',
            'goad_blue': r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+\[(\w+)\]\s+(.*)',
            'json': None  # Handle JSON separately
        }

        # Try JSON first
        if line.strip().startswith('{'):
            try:
                data = json.loads(line.strip())
                return {
                    'timestamp': datetime.fromisoformat(data.get('timestamp', '')),
                    'source': source,
                    'level': data.get('level', 'INFO'),
                    'message': data.get('message', ''),
                    'raw_data': data,
                    'line_number': line_num
                }
            except:
                pass

        # Try structured formats
        for format_name, pattern in formats.items():
            if pattern and source.lower().find(format_name) != -1:
                match = re.match(pattern, line.strip())
                if match:
                    groups = match.groups()
                    return {
                        'timestamp': self.parse_timestamp(groups[0]),
                        'source': source,
                        'level': groups[1] if len(groups) > 1 else 'INFO',
                        'message': groups[-1],
                        'raw_line': line.strip(),
                        'line_number': line_num
                    }

        return None

    def parse_timestamp(self, timestamp_str):
        """Parse various timestamp formats"""
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%b %d %H:%M:%S',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%d %H:%M:%S.%f'
        ]

        for fmt in formats:
            try:
                return datetime.strptime(timestamp_str, fmt)
            except ValueError:
                continue

        return datetime.now()  # Fallback

    def detect_attack_patterns(self):
        """Detect common attack patterns in logs"""
        attack_patterns = {
            'brute_force': {
                'pattern': r'(failed|invalid|incorrect).*(?:login|password|authentication)',
                'threshold': 5,
                'time_window': 300
            },
            'sql_injection': {
                'pattern': r'(union|select|insert|update|delete|drop).*(?:from|where|table)',
                'threshold': 3,
                'time_window': 60
            },
            'xss_attempt': {
                'pattern': r'<script|javascript:|onload=|onerror=',
                'threshold': 2,
                'time_window': 120
            },
            'directory_traversal': {
                'pattern': r'\.\./|\.\.\\\|%2e%2e%2f|%2e%2e%5c',
                'threshold': 3,
                'time_window': 180
            }
        }

        detected_attacks = []

        for attack_type, config in attack_patterns.items():
            pattern = re.compile(config['pattern'], re.IGNORECASE)
            matches = []

            for event in self.events:
                if pattern.search(event['message']):
                    matches.append(event)

            # Group matches by time windows
            time_groups = self.group_by_time_window(matches, config['time_window'])

            for group in time_groups:
                if len(group) >= config['threshold']:
                    detected_attacks.append({
                        'attack_type': attack_type,
                        'events': group,
                        'start_time': min(e['timestamp'] for e in group),
                        'end_time': max(e['timestamp'] for e in group),
                        'event_count': len(group)
                    })

        return detected_attacks

    def group_by_time_window(self, events, window_seconds):
        """Group events by time windows"""
        if not events:
            return []

        sorted_events = sorted(events, key=lambda x: x['timestamp'])
        groups = []
        current_group = [sorted_events[0]]

        for event in sorted_events[1:]:
            time_diff = (event['timestamp'] - current_group[0]['timestamp']).total_seconds()

            if time_diff <= window_seconds:
                current_group.append(event)
            else:
                groups.append(current_group)
                current_group = [event]

        groups.append(current_group)
        return groups

    def analyze_user_behavior(self):
        """Analyze user behavior patterns"""
        user_activities = defaultdict(list)

        # Extract user information from logs
        user_patterns = [
            r'user[=:\s]+([a-zA-Z0-9._-]+)',
            r'login[=:\s]+([a-zA-Z0-9._-]+)',
            r'account[=:\s]+([a-zA-Z0-9._-]+)'
        ]

        for event in self.events:
            for pattern in user_patterns:
                match = re.search(pattern, event['message'], re.IGNORECASE)
                if match:
                    username = match.group(1)
                    user_activities[username].append(event)
                    break

        # Analyze each user's activity
        user_analysis = {}
        for username, activities in user_activities.items():
            analysis = {
                'total_events': len(activities),
                'time_span': self.calculate_time_span(activities),
                'activity_hours': self.analyze_activity_hours(activities),
                'suspicious_patterns': self.detect_suspicious_user_patterns(activities)
            }
            user_analysis[username] = analysis

        return user_analysis

    def calculate_time_span(self, events):
        """Calculate time span of events"""
        if len(events) < 2:
            return 0

        timestamps = [e['timestamp'] for e in events]
        return (max(timestamps) - min(timestamps)).total_seconds()

    def analyze_activity_hours(self, events):
        """Analyze activity by hour of day"""
        hour_counts = defaultdict(int)
        for event in events:
            hour = event['timestamp'].hour
            hour_counts[hour] += 1

        return dict(hour_counts)

    def detect_suspicious_user_patterns(self, events):
        """Detect suspicious patterns in user activity"""
        suspicious = []

        # Check for off-hours activity (outside 8 AM - 6 PM)
        off_hours_events = [e for e in events if e['timestamp'].hour < 8 or e['timestamp'].hour > 18]
        if len(off_hours_events) > len(events) * 0.3:  # More than 30% off-hours
            suspicious.append('high_off_hours_activity')

        # Check for rapid successive actions
        sorted_events = sorted(events, key=lambda x: x['timestamp'])
        rapid_actions = 0
        for i in range(1, len(sorted_events)):
            time_diff = (sorted_events[i]['timestamp'] - sorted_events[i-1]['timestamp']).total_seconds()
            if time_diff < 5:  # Less than 5 seconds between actions
                rapid_actions += 1

        if rapid_actions > len(events) * 0.2:  # More than 20% rapid actions
            suspicious.append('rapid_successive_actions')

        # Check for failed authentication patterns
        failed_auth = [e for e in events if re.search(r'fail|invalid|incorrect', e['message'], re.IGNORECASE)]
        if len(failed_auth) > 10:
            suspicious.append('multiple_failed_authentications')

        return suspicious

    def generate_correlation_report(self):
        """Generate comprehensive correlation report"""
        report = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_events_analyzed': len(self.events),
            'time_range': {
                'start': min(e['timestamp'] for e in self.events).isoformat() if self.events else None,
                'end': max(e['timestamp'] for e in self.events).isoformat() if self.events else None
            },
            'attack_patterns': self.detect_attack_patterns(),
            'user_behavior': self.analyze_user_behavior(),
            'source_distribution': self.analyze_source_distribution()
        }

        return report

    def analyze_source_distribution(self):
        """Analyze event distribution by source"""
        source_counts = defaultdict(int)
        source_levels = defaultdict(lambda: defaultdict(int))

        for event in self.events:
            source_counts[event['source']] += 1
            source_levels[event['source']][event['level']] += 1

        return {
            'counts': dict(source_counts),
            'levels': {k: dict(v) for k, v in source_levels.items()}
        }

# Example usage
def run_correlation_analysis():
    """Run comprehensive log correlation analysis"""

    correlator = LogCorrelationEngine()

    # Define log sources
    log_sources = {
        'goad_blue': '/var/log/goad-blue/goad-blue.log',
        'syslog': '/var/log/syslog',
        'auth': '/var/log/auth.log',
        'splunk': '/opt/splunk/var/log/splunk/splunkd.log'
    }

    # Parse logs
    print("Parsing logs from multiple sources...")
    correlator.parse_multi_source_logs(log_sources)

    # Generate report
    print("Generating correlation report...")
    report = correlator.generate_correlation_report()

    # Save report
    with open('/tmp/log_correlation_report.json', 'w') as f:
        json.dump(report, f, indent=2)

    # Print summary
    print(f"Analysis complete. Processed {report['total_events_analyzed']} events.")
    print(f"Detected {len(report['attack_patterns'])} potential attack patterns.")
    print(f"Analyzed {len(report['user_behavior'])} user accounts.")

    return report

if __name__ == "__main__":
    run_correlation_analysis()
```

### **Machine Learning-Based Log Analysis**

```python
#!/usr/bin/env python3
# ML-based log anomaly detection

import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import DBSCAN
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import joblib
import re
from datetime import datetime

class LogAnomalyDetector:
    def __init__(self):
        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        self.scaler = StandardScaler()
        self.isolation_forest = IsolationForest(contamination=0.1, random_state=42)
        self.dbscan = DBSCAN(eps=0.5, min_samples=5)
        self.is_trained = False

    def preprocess_logs(self, log_data):
        """Preprocess log data for ML analysis"""
        processed_logs = []

        for log_entry in log_data:
            # Extract features
            features = {
                'message_length': len(log_entry.get('message', '')),
                'hour_of_day': log_entry.get('timestamp', datetime.now()).hour,
                'day_of_week': log_entry.get('timestamp', datetime.now()).weekday(),
                'log_level_numeric': self.encode_log_level(log_entry.get('level', 'INFO')),
                'source_hash': hash(log_entry.get('source', '')) % 1000,
                'message_entropy': self.calculate_entropy(log_entry.get('message', '')),
                'special_char_ratio': self.calculate_special_char_ratio(log_entry.get('message', '')),
                'numeric_ratio': self.calculate_numeric_ratio(log_entry.get('message', '')),
                'message_text': log_entry.get('message', '')
            }

            processed_logs.append(features)

        return pd.DataFrame(processed_logs)

    def encode_log_level(self, level):
        """Encode log levels as numeric values"""
        level_map = {
            'DEBUG': 1,
            'INFO': 2,
            'WARNING': 3,
            'ERROR': 4,
            'CRITICAL': 5
        }
        return level_map.get(level.upper(), 2)

    def calculate_entropy(self, text):
        """Calculate Shannon entropy of text"""
        if not text:
            return 0

        # Count character frequencies
        char_counts = {}
        for char in text:
            char_counts[char] = char_counts.get(char, 0) + 1

        # Calculate entropy
        entropy = 0
        text_length = len(text)
        for count in char_counts.values():
            probability = count / text_length
            if probability > 0:
                entropy -= probability * np.log2(probability)

        return entropy

    def calculate_special_char_ratio(self, text):
        """Calculate ratio of special characters"""
        if not text:
            return 0

        special_chars = re.findall(r'[^a-zA-Z0-9\s]', text)
        return len(special_chars) / len(text)

    def calculate_numeric_ratio(self, text):
        """Calculate ratio of numeric characters"""
        if not text:
            return 0

        numeric_chars = re.findall(r'\d', text)
        return len(numeric_chars) / len(text)

    def train_models(self, training_data):
        """Train anomaly detection models"""
        df = self.preprocess_logs(training_data)

        # Prepare text features
        text_features = self.vectorizer.fit_transform(df['message_text'])

        # Prepare numeric features
        numeric_columns = ['message_length', 'hour_of_day', 'day_of_week',
                          'log_level_numeric', 'source_hash', 'message_entropy',
                          'special_char_ratio', 'numeric_ratio']
        numeric_features = self.scaler.fit_transform(df[numeric_columns])

        # Combine features
        combined_features = np.hstack([text_features.toarray(), numeric_features])

        # Train models
        self.isolation_forest.fit(combined_features)
        self.dbscan.fit(combined_features)

        self.is_trained = True
        print(f"Models trained on {len(training_data)} log entries")

    def detect_anomalies(self, log_data):
        """Detect anomalies in log data"""
        if not self.is_trained:
            raise ValueError("Models must be trained before detecting anomalies")

        df = self.preprocess_logs(log_data)

        # Prepare features
        text_features = self.vectorizer.transform(df['message_text'])
        numeric_columns = ['message_length', 'hour_of_day', 'day_of_week',
                          'log_level_numeric', 'source_hash', 'message_entropy',
                          'special_char_ratio', 'numeric_ratio']
        numeric_features = self.scaler.transform(df[numeric_columns])
        combined_features = np.hstack([text_features.toarray(), numeric_features])

        # Detect anomalies
        isolation_predictions = self.isolation_forest.predict(combined_features)
        cluster_labels = self.dbscan.fit_predict(combined_features)

        # Combine results
        anomalies = []
        for i, (iso_pred, cluster_label) in enumerate(zip(isolation_predictions, cluster_labels)):
            is_anomaly = iso_pred == -1 or cluster_label == -1

            if is_anomaly:
                anomaly_info = {
                    'index': i,
                    'log_entry': log_data[i],
                    'anomaly_score': self.isolation_forest.decision_function([combined_features[i]])[0],
                    'isolation_forest_anomaly': iso_pred == -1,
                    'clustering_anomaly': cluster_label == -1,
                    'features': df.iloc[i].to_dict()
                }
                anomalies.append(anomaly_info)

        return anomalies

    def save_models(self, model_path):
        """Save trained models"""
        model_data = {
            'vectorizer': self.vectorizer,
            'scaler': self.scaler,
            'isolation_forest': self.isolation_forest,
            'is_trained': self.is_trained
        }
        joblib.dump(model_data, model_path)
        print(f"Models saved to {model_path}")

    def load_models(self, model_path):
        """Load trained models"""
        model_data = joblib.load(model_path)
        self.vectorizer = model_data['vectorizer']
        self.scaler = model_data['scaler']
        self.isolation_forest = model_data['isolation_forest']
        self.is_trained = model_data['is_trained']
        print(f"Models loaded from {model_path}")

    def generate_anomaly_report(self, anomalies):
        """Generate detailed anomaly report"""
        if not anomalies:
            return {"message": "No anomalies detected"}

        report = {
            'total_anomalies': len(anomalies),
            'anomaly_types': {
                'isolation_forest': sum(1 for a in anomalies if a['isolation_forest_anomaly']),
                'clustering': sum(1 for a in anomalies if a['clustering_anomaly']),
                'both': sum(1 for a in anomalies if a['isolation_forest_anomaly'] and a['clustering_anomaly'])
            },
            'severity_distribution': {},
            'time_distribution': {},
            'source_distribution': {},
            'top_anomalies': []
        }

        # Analyze anomaly characteristics
        for anomaly in anomalies:
            log_entry = anomaly['log_entry']

            # Severity distribution
            level = log_entry.get('level', 'UNKNOWN')
            report['severity_distribution'][level] = report['severity_distribution'].get(level, 0) + 1

            # Time distribution
            hour = log_entry.get('timestamp', datetime.now()).hour
            report['time_distribution'][hour] = report['time_distribution'].get(hour, 0) + 1

            # Source distribution
            source = log_entry.get('source', 'UNKNOWN')
            report['source_distribution'][source] = report['source_distribution'].get(source, 0) + 1

        # Top anomalies by score
        sorted_anomalies = sorted(anomalies, key=lambda x: x['anomaly_score'])
        report['top_anomalies'] = [
            {
                'timestamp': a['log_entry'].get('timestamp', '').isoformat() if hasattr(a['log_entry'].get('timestamp', ''), 'isoformat') else str(a['log_entry'].get('timestamp', '')),
                'message': a['log_entry'].get('message', '')[:100] + '...' if len(a['log_entry'].get('message', '')) > 100 else a['log_entry'].get('message', ''),
                'source': a['log_entry'].get('source', ''),
                'level': a['log_entry'].get('level', ''),
                'anomaly_score': float(a['anomaly_score'])
            }
            for a in sorted_anomalies[:10]
        ]

        return report

# Example usage
def run_ml_log_analysis():
    """Run ML-based log analysis"""

    detector = LogAnomalyDetector()

    # Load training data (normal logs)
    # This would typically be a large dataset of normal log entries
    training_data = [
        {
            'timestamp': datetime.now(),
            'level': 'INFO',
            'source': 'goad-blue',
            'message': 'Service started successfully'
        },
        # ... more training data
    ]

    # Train models
    if len(training_data) > 100:  # Need sufficient training data
        detector.train_models(training_data)
        detector.save_models('/tmp/log_anomaly_models.pkl')
    else:
        print("Insufficient training data for ML models")
        return

    # Analyze new logs for anomalies
    new_logs = [
        {
            'timestamp': datetime.now(),
            'level': 'ERROR',
            'source': 'unknown',
            'message': 'Unusual system behavior detected with multiple failed authentication attempts from *************'
        },
        # ... more logs to analyze
    ]

    anomalies = detector.detect_anomalies(new_logs)
    report = detector.generate_anomaly_report(anomalies)

    print(f"Detected {len(anomalies)} anomalies")
    print(f"Report: {report}")

    return anomalies, report

if __name__ == "__main__":
    run_ml_log_analysis()

## 📈 Log Analysis Dashboards and Visualization

### **Splunk Dashboard for GOAD-Blue Logs**

```xml
<!-- GOAD-Blue Monitoring Dashboard -->
<dashboard>
  <label>GOAD-Blue System Health</label>
  <description>Comprehensive monitoring dashboard for GOAD-Blue components</description>

  <row>
    <panel>
      <title>System Overview</title>
      <single>
        <search>
          <query>
            index=goad_blue_main earliest=-1h
            | stats count as total_events
          </query>
        </search>
        <option name="drilldown">none</option>
        <option name="colorBy">value</option>
        <option name="colorMode">none</option>
        <option name="rangeColors">["0x65A637","0x6DB7C6","0xF7BC38","0xF58F39","0xD93F3C"]</option>
        <option name="rangeValues">[0,1000,5000,10000,50000]</option>
        <option name="underLabel">Events (1h)</option>
      </single>
    </panel>

    <panel>
      <title>Error Rate</title>
      <single>
        <search>
          <query>
            index=goad_blue_main earliest=-1h level=ERROR
            | stats count as error_count
            | appendcols [search index=goad_blue_main earliest=-1h | stats count as total_count]
            | eval error_rate=round((error_count/total_count)*100,2)
            | fields error_rate
          </query>
        </search>
        <option name="drilldown">none</option>
        <option name="numberPrecision">0.00</option>
        <option name="unit">%</option>
        <option name="underLabel">Error Rate</option>
      </single>
    </panel>
  </row>

  <row>
    <panel>
      <title>Log Volume Over Time</title>
      <chart>
        <search>
          <query>
            index=goad_blue_main earliest=-24h
            | timechart span=1h count by level
          </query>
        </search>
        <option name="charting.chart">column</option>
        <option name="charting.chart.stackMode">stacked</option>
        <option name="charting.legend.placement">bottom</option>
      </chart>
    </panel>
  </row>

  <row>
    <panel>
      <title>Component Health</title>
      <table>
        <search>
          <query>
            index=goad_blue_main earliest=-1h
            | rex field=message "component=(?&lt;component&gt;\w+)"
            | stats count as events,
                    count(eval(level="ERROR")) as errors,
                    count(eval(level="WARNING")) as warnings
                    by component
            | eval health_score = case(
                errors > 10, "Critical",
                errors > 5, "Warning",
                warnings > 20, "Warning",
                1=1, "Healthy"
              )
            | sort - errors
          </query>
        </search>
        <option name="drilldown">cell</option>
        <format type="color" field="health_score">
          <colorPalette type="map">{"Healthy":#65A637,"Warning":#F7BC38,"Critical":#D93F3C}</colorPalette>
        </format>
      </table>
    </panel>
  </row>

  <row>
    <panel>
      <title>Recent Errors</title>
      <table>
        <search>
          <query>
            index=goad_blue_main level=ERROR earliest=-4h
            | rex field=message "component=(?&lt;component&gt;\w+)"
            | table _time, component, message
            | sort - _time
            | head 20
          </query>
        </search>
        <option name="drilldown">cell</option>
        <option name="wrap">true</option>
        <option name="rowNumbers">false</option>
      </table>
    </panel>
  </row>
</dashboard>
```

### **Elasticsearch/Kibana Visualizations**

```json
{
  "version": "7.15.0",
  "objects": [
    {
      "id": "goad-blue-overview",
      "type": "dashboard",
      "attributes": {
        "title": "GOAD-Blue Log Analysis",
        "description": "Comprehensive log analysis dashboard",
        "panelsJSON": "[{\"version\":\"7.15.0\",\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15},\"panelIndex\":\"1\",\"embeddableConfig\":{},\"panelRefName\":\"panel_1\"}]",
        "timeRestore": false,
        "kibanaSavedObjectMeta": {
          "searchSourceJSON": "{\"query\":{\"match_all\":{}},\"filter\":[]}"
        }
      }
    },
    {
      "id": "log-level-distribution",
      "type": "visualization",
      "attributes": {
        "title": "Log Level Distribution",
        "visState": "{\"title\":\"Log Level Distribution\",\"type\":\"pie\",\"params\":{\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\"},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"terms\",\"schema\":\"segment\",\"params\":{\"field\":\"level.keyword\",\"size\":10,\"order\":\"desc\",\"orderBy\":\"1\"}}]}",
        "uiStateJSON": "{}",
        "kibanaSavedObjectMeta": {
          "searchSourceJSON": "{\"index\":\"goad-blue-*\",\"query\":{\"match_all\":{}},\"filter\":[]}"
        }
      }
    },
    {
      "id": "error-timeline",
      "type": "visualization",
      "attributes": {
        "title": "Error Timeline",
        "visState": "{\"title\":\"Error Timeline\",\"type\":\"histogram\",\"params\":{\"grid\":{\"categoryLines\":false,\"style\":{\"color\":\"#eee\"}},\"categoryAxes\":[{\"id\":\"CategoryAxis-1\",\"type\":\"category\",\"position\":\"bottom\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\"},\"labels\":{\"show\":true,\"truncate\":100},\"title\":{}}],\"valueAxes\":[{\"id\":\"ValueAxis-1\",\"name\":\"LeftAxis-1\",\"type\":\"value\",\"position\":\"left\",\"show\":true,\"style\":{},\"scale\":{\"type\":\"linear\",\"mode\":\"normal\"},\"labels\":{\"show\":true,\"rotate\":0,\"filter\":false,\"truncate\":100},\"title\":{\"text\":\"Count\"}}],\"seriesParams\":[{\"show\":true,\"type\":\"histogram\",\"mode\":\"stacked\",\"data\":{\"label\":\"Count\",\"id\":\"1\"},\"valueAxis\":\"ValueAxis-1\",\"drawLinesBetweenPoints\":true,\"showCircles\":true}],\"addTooltip\":true,\"addLegend\":true,\"legendPosition\":\"right\",\"times\":[],\"addTimeMarker\":false},\"aggs\":[{\"id\":\"1\",\"enabled\":true,\"type\":\"count\",\"schema\":\"metric\",\"params\":{}},{\"id\":\"2\",\"enabled\":true,\"type\":\"date_histogram\",\"schema\":\"segment\",\"params\":{\"field\":\"@timestamp\",\"interval\":\"auto\",\"customInterval\":\"2h\",\"min_doc_count\":1,\"extended_bounds\":{}}}]}",
        "uiStateJSON": "{}",
        "kibanaSavedObjectMeta": {
          "searchSourceJSON": "{\"index\":\"goad-blue-*\",\"query\":{\"bool\":{\"must\":[{\"term\":{\"level.keyword\":\"ERROR\"}}]}},\"filter\":[]}"
        }
      }
    }
  ]
}
```

### **Custom Log Analysis Dashboard**

```python
#!/usr/bin/env python3
# Custom log analysis dashboard

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json
import re

class LogAnalysisDashboard:
    def __init__(self):
        self.log_data = []

    def load_log_data(self, log_files):
        """Load log data from multiple files"""
        all_logs = []

        for log_file in log_files:
            try:
                with open(log_file, 'r') as f:
                    for line in f:
                        log_entry = self.parse_log_line(line)
                        if log_entry:
                            all_logs.append(log_entry)
            except Exception as e:
                st.error(f"Error loading {log_file}: {e}")

        self.log_data = pd.DataFrame(all_logs)
        return len(all_logs)

    def parse_log_line(self, line):
        """Parse log line into structured data"""
        # Handle JSON logs
        if line.strip().startswith('{'):
            try:
                return json.loads(line.strip())
            except:
                pass

        # Handle structured logs
        pattern = r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\s+\[(\w+)\]\s+(.*)'
        match = re.match(pattern, line.strip())

        if match:
            timestamp_str, level, message = match.groups()
            return {
                'timestamp': datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S'),
                'level': level,
                'message': message
            }

        return None

    def create_overview_metrics(self):
        """Create overview metrics"""
        if self.log_data.empty:
            return

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            total_logs = len(self.log_data)
            st.metric("Total Logs", f"{total_logs:,}")

        with col2:
            error_count = len(self.log_data[self.log_data['level'] == 'ERROR'])
            error_rate = (error_count / total_logs * 100) if total_logs > 0 else 0
            st.metric("Error Rate", f"{error_rate:.2f}%", delta=f"{error_count} errors")

        with col3:
            warning_count = len(self.log_data[self.log_data['level'] == 'WARNING'])
            st.metric("Warnings", f"{warning_count:,}")

        with col4:
            if 'timestamp' in self.log_data.columns:
                time_span = (self.log_data['timestamp'].max() - self.log_data['timestamp'].min()).total_seconds() / 3600
                st.metric("Time Span", f"{time_span:.1f} hours")

    def create_level_distribution(self):
        """Create log level distribution chart"""
        if self.log_data.empty:
            return

        level_counts = self.log_data['level'].value_counts()

        fig = px.pie(
            values=level_counts.values,
            names=level_counts.index,
            title="Log Level Distribution",
            color_discrete_map={
                'ERROR': '#ff4444',
                'WARNING': '#ffaa00',
                'INFO': '#00aa00',
                'DEBUG': '#0088cc'
            }
        )

        st.plotly_chart(fig, use_container_width=True)

    def create_timeline_chart(self):
        """Create timeline chart"""
        if self.log_data.empty or 'timestamp' not in self.log_data.columns:
            return

        # Resample data by hour
        self.log_data.set_index('timestamp', inplace=True)
        hourly_data = self.log_data.groupby([pd.Grouper(freq='H'), 'level']).size().reset_index(name='count')

        fig = px.line(
            hourly_data,
            x='timestamp',
            y='count',
            color='level',
            title="Log Volume Over Time",
            color_discrete_map={
                'ERROR': '#ff4444',
                'WARNING': '#ffaa00',
                'INFO': '#00aa00',
                'DEBUG': '#0088cc'
            }
        )

        st.plotly_chart(fig, use_container_width=True)

    def create_error_analysis(self):
        """Create error analysis section"""
        if self.log_data.empty:
            return

        errors = self.log_data[self.log_data['level'] == 'ERROR']

        if errors.empty:
            st.info("No errors found in the selected time range")
            return

        st.subheader("Error Analysis")

        # Error frequency
        error_messages = errors['message'].str[:100]  # Truncate for grouping
        error_freq = error_messages.value_counts().head(10)

        fig = px.bar(
            x=error_freq.values,
            y=error_freq.index,
            orientation='h',
            title="Top 10 Error Messages",
            labels={'x': 'Frequency', 'y': 'Error Message'}
        )
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)

        # Recent errors table
        st.subheader("Recent Errors")
        recent_errors = errors.sort_values('timestamp', ascending=False).head(20)
        st.dataframe(recent_errors[['timestamp', 'message']], use_container_width=True)

    def create_search_interface(self):
        """Create log search interface"""
        st.subheader("Log Search")

        col1, col2 = st.columns(2)

        with col1:
            search_term = st.text_input("Search in messages:")
            level_filter = st.multiselect("Filter by level:",
                                        options=self.log_data['level'].unique() if not self.log_data.empty else [],
                                        default=[])

        with col2:
            if 'timestamp' in self.log_data.columns and not self.log_data.empty:
                min_date = self.log_data['timestamp'].min().date()
                max_date = self.log_data['timestamp'].max().date()
                date_range = st.date_input("Date range:",
                                         value=(min_date, max_date),
                                         min_value=min_date,
                                         max_value=max_date)

        # Apply filters
        filtered_data = self.log_data.copy()

        if search_term:
            filtered_data = filtered_data[filtered_data['message'].str.contains(search_term, case=False, na=False)]

        if level_filter:
            filtered_data = filtered_data[filtered_data['level'].isin(level_filter)]

        if 'timestamp' in filtered_data.columns and len(date_range) == 2:
            start_date = pd.Timestamp(date_range[0])
            end_date = pd.Timestamp(date_range[1]) + pd.Timedelta(days=1)
            filtered_data = filtered_data[(filtered_data['timestamp'] >= start_date) &
                                        (filtered_data['timestamp'] < end_date)]

        st.write(f"Found {len(filtered_data)} matching log entries")

        if not filtered_data.empty:
            st.dataframe(filtered_data, use_container_width=True)

def main():
    """Main dashboard function"""
    st.set_page_config(page_title="GOAD-Blue Log Analysis", layout="wide")

    st.title("🔍 GOAD-Blue Log Analysis Dashboard")
    st.markdown("Comprehensive log analysis and monitoring for GOAD-Blue environments")

    dashboard = LogAnalysisDashboard()

    # Sidebar for file upload
    st.sidebar.header("Data Source")
    uploaded_files = st.sidebar.file_uploader(
        "Upload log files",
        accept_multiple_files=True,
        type=['log', 'txt', 'json']
    )

    if uploaded_files:
        # Save uploaded files temporarily
        temp_files = []
        for uploaded_file in uploaded_files:
            temp_path = f"/tmp/{uploaded_file.name}"
            with open(temp_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            temp_files.append(temp_path)

        # Load data
        with st.spinner("Loading log data..."):
            log_count = dashboard.load_log_data(temp_files)

        st.success(f"Loaded {log_count:,} log entries from {len(temp_files)} files")

        # Create dashboard sections
        dashboard.create_overview_metrics()

        col1, col2 = st.columns(2)

        with col1:
            dashboard.create_level_distribution()

        with col2:
            dashboard.create_timeline_chart()

        dashboard.create_error_analysis()
        dashboard.create_search_interface()

    else:
        st.info("Please upload log files to begin analysis")

        # Show example log formats
        st.subheader("Supported Log Formats")

        st.code("""
# Structured format
2024-01-15 14:30:25 [INFO] component=splunk message="Service started"
2024-01-15 14:30:26 [ERROR] component=elasticsearch message="Connection failed"

# JSON format
{"timestamp": "2024-01-15T14:30:25", "level": "INFO", "message": "Service started"}
{"timestamp": "2024-01-15T14:30:26", "level": "ERROR", "message": "Connection failed"}
        """)

if __name__ == "__main__":
    main()
```

## 🚨 Automated Log Alerting

### **Real-time Alert System**

```python
#!/usr/bin/env python3
# Real-time log alerting system

import time
import re
import smtplib
import json
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from datetime import datetime, timedelta
from collections import defaultdict, deque
import threading
import queue

class LogAlertSystem:
    def __init__(self, config_file):
        self.config = self.load_config(config_file)
        self.alert_rules = self.config.get('alert_rules', [])
        self.notification_config = self.config.get('notifications', {})
        self.alert_history = deque(maxlen=1000)
        self.rate_limiters = defaultdict(lambda: deque(maxlen=100))
        self.running = False

    def load_config(self, config_file):
        """Load alerting configuration"""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading config: {e}")
            return self.get_default_config()

    def get_default_config(self):
        """Get default alerting configuration"""
        return {
            "alert_rules": [
                {
                    "name": "High Error Rate",
                    "pattern": r"\[ERROR\]",
                    "threshold": 10,
                    "time_window": 300,
                    "severity": "high",
                    "description": "High number of errors detected"
                },
                {
                    "name": "Authentication Failures",
                    "pattern": r"(authentication failed|login failed|invalid credentials)",
                    "threshold": 5,
                    "time_window": 180,
                    "severity": "medium",
                    "description": "Multiple authentication failures"
                },
                {
                    "name": "Service Unavailable",
                    "pattern": r"(service unavailable|connection refused|timeout)",
                    "threshold": 3,
                    "time_window": 120,
                    "severity": "critical",
                    "description": "Service availability issues"
                }
            ],
            "notifications": {
                "email": {
                    "enabled": True,
                    "smtp_server": "localhost",
                    "smtp_port": 587,
                    "username": "",
                    "password": "",
                    "from_address": "goad-blue-alerts@localhost",
                    "to_addresses": ["admin@localhost"]
                },
                "webhook": {
                    "enabled": False,
                    "url": "http://localhost:8080/webhook",
                    "headers": {"Content-Type": "application/json"}
                }
            }
        }

    def process_log_line(self, log_line, source="unknown"):
        """Process a single log line for alerts"""
        timestamp = datetime.now()

        for rule in self.alert_rules:
            if self.check_rule_match(log_line, rule, timestamp, source):
                self.trigger_alert(rule, log_line, timestamp, source)

    def check_rule_match(self, log_line, rule, timestamp, source):
        """Check if log line matches alert rule"""
        pattern = rule.get('pattern', '')
        if not re.search(pattern, log_line, re.IGNORECASE):
            return False

        # Add to rate limiter
        rule_key = f"{rule['name']}_{source}"
        self.rate_limiters[rule_key].append(timestamp)

        # Check threshold within time window
        time_window = rule.get('time_window', 300)
        threshold = rule.get('threshold', 1)

        # Remove old entries
        cutoff_time = timestamp - timedelta(seconds=time_window)
        while (self.rate_limiters[rule_key] and
               self.rate_limiters[rule_key][0] < cutoff_time):
            self.rate_limiters[rule_key].popleft()

        return len(self.rate_limiters[rule_key]) >= threshold

    def trigger_alert(self, rule, log_line, timestamp, source):
        """Trigger alert for matched rule"""
        alert = {
            'id': f"{rule['name']}_{timestamp.isoformat()}",
            'rule_name': rule['name'],
            'severity': rule.get('severity', 'medium'),
            'description': rule.get('description', ''),
            'timestamp': timestamp.isoformat(),
            'source': source,
            'log_line': log_line,
            'count': len(self.rate_limiters[f"{rule['name']}_{source}"])
        }

        self.alert_history.append(alert)
        self.send_notifications(alert)

        print(f"ALERT: {alert['rule_name']} - {alert['description']}")

    def send_notifications(self, alert):
        """Send alert notifications"""
        if self.notification_config.get('email', {}).get('enabled', False):
            self.send_email_alert(alert)

        if self.notification_config.get('webhook', {}).get('enabled', False):
            self.send_webhook_alert(alert)

    def send_email_alert(self, alert):
        """Send email alert"""
        try:
            email_config = self.notification_config['email']

            msg = MimeMultipart()
            msg['From'] = email_config['from_address']
            msg['To'] = ', '.join(email_config['to_addresses'])
            msg['Subject'] = f"GOAD-Blue Alert: {alert['rule_name']} [{alert['severity'].upper()}]"

            body = f"""
GOAD-Blue Alert Notification

Rule: {alert['rule_name']}
Severity: {alert['severity'].upper()}
Description: {alert['description']}
Timestamp: {alert['timestamp']}
Source: {alert['source']}
Count: {alert['count']} occurrences

Log Entry:
{alert['log_line']}

Alert ID: {alert['id']}
            """

            msg.attach(MimeText(body, 'plain'))

            server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            if email_config.get('username'):
                server.starttls()
                server.login(email_config['username'], email_config['password'])

            server.send_message(msg)
            server.quit()

        except Exception as e:
            print(f"Error sending email alert: {e}")

    def send_webhook_alert(self, alert):
        """Send webhook alert"""
        try:
            import requests

            webhook_config = self.notification_config['webhook']

            payload = {
                'alert_type': 'goad_blue_log_alert',
                'alert': alert
            }

            response = requests.post(
                webhook_config['url'],
                json=payload,
                headers=webhook_config.get('headers', {}),
                timeout=10
            )

            response.raise_for_status()

        except Exception as e:
            print(f"Error sending webhook alert: {e}")

    def monitor_log_file(self, log_file, source_name):
        """Monitor a log file for new entries"""
        try:
            with open(log_file, 'r') as f:
                # Go to end of file
                f.seek(0, 2)

                while self.running:
                    line = f.readline()
                    if line:
                        self.process_log_line(line.strip(), source_name)
                    else:
                        time.sleep(1)

        except Exception as e:
            print(f"Error monitoring {log_file}: {e}")

    def start_monitoring(self, log_files):
        """Start monitoring multiple log files"""
        self.running = True
        threads = []

        for log_file, source_name in log_files.items():
            thread = threading.Thread(
                target=self.monitor_log_file,
                args=(log_file, source_name)
            )
            thread.daemon = True
            thread.start()
            threads.append(thread)

        print(f"Started monitoring {len(log_files)} log files")

        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("Stopping log monitoring...")
            self.running = False

            for thread in threads:
                thread.join(timeout=5)

    def get_alert_summary(self, hours=24):
        """Get alert summary for specified time period"""
        cutoff_time = datetime.now() - timedelta(hours=hours)

        recent_alerts = [
            alert for alert in self.alert_history
            if datetime.fromisoformat(alert['timestamp']) > cutoff_time
        ]

        summary = {
            'total_alerts': len(recent_alerts),
            'by_severity': defaultdict(int),
            'by_rule': defaultdict(int),
            'by_source': defaultdict(int)
        }

        for alert in recent_alerts:
            summary['by_severity'][alert['severity']] += 1
            summary['by_rule'][alert['rule_name']] += 1
            summary['by_source'][alert['source']] += 1

        return summary

# Example usage
def main():
    """Main alerting function"""

    # Create alert system
    alert_system = LogAlertSystem('/etc/goad-blue/alert_config.json')

    # Define log files to monitor
    log_files = {
        '/var/log/goad-blue/goad-blue.log': 'goad_blue',
        '/opt/splunk/var/log/splunk/splunkd.log': 'splunk',
        '/var/log/elasticsearch/elasticsearch.log': 'elasticsearch',
        '/var/log/syslog': 'system'
    }

    # Start monitoring
    alert_system.start_monitoring(log_files)

if __name__ == "__main__":
    main()
```

---

!!! tip "Advanced Log Analysis Tips"
    - Implement log aggregation for distributed environments
    - Use machine learning for anomaly detection in log patterns
    - Create custom parsers for application-specific log formats
    - Set up real-time alerting for critical security events
    - Maintain log retention policies to balance storage and compliance needs

!!! warning "Log Analysis Challenges"
    - Large log volumes can impact analysis performance
    - Unstructured logs are harder to analyze automatically
    - Time zone differences can complicate correlation
    - Sensitive data in logs requires careful handling
    - False positives in automated alerting can cause alert fatigue

!!! info "Best Practices for Log Management"
    - Use structured logging formats (JSON) whenever possible
    - Implement centralized logging for distributed systems
    - Set up automated log rotation and archival
    - Create standardized log levels and formats across components
    - Regularly review and tune alerting rules to reduce noise
```
