# Special Thanks to

- [@<PERSON>jiEndo<PERSON>](https://x.com/KenjiEndo15) for all his work on the SCCM ansible roles and his contribution to the SCCM Lab
- [aleemladha](https://github.com/aleemladha) for exchange and ludus ansible roles
- [<PERSON> @badsectorlabs](https://x.com/badsectorlabs) for his advises and tests during the [Ludus](https://docs.ludus.cloud/) provider creation
- [@ArnC_CarN](https://github.com/ArnCo) for his PR on the aws provider
- [@Sant0rryu](https://x.com/Sant0rryu) for his help during ADCS vulnerabilities creation
- All the Orange Cyberdefense Toulouse team for the beta tests on the NHA lab ;)
- <PERSON> (For the Azure recipes creation)
- <PERSON> (For his tests & some vulns writing during v1 creation)
- <PERSON> (For his tests during v1 creation)

And of course to all the [project contributors](https://github.com/Orange-Cyberdefense/GOAD/graphs/contributors) !

## Enterprise

- [Orange Cyberdefense](https://www.orangecyberdefense.com/fr/) to give me time to work on the project, and let me put the project in GPL on the Orange Cyberdefense's GitHub repository.