#!/bin/bash
# GOAD-Blue Splunk Dashboard and CIM Installation Script
# Installs Common Information Model, Technical Add-ons, and GOAD-Blue specific dashboards

set -e

# Configuration
SPLUNK_HOME="/opt/splunk"
SPLUNK_USER="splunk"
SPLUNK_ADMIN_USER="admin"
SPLUNK_ADMIN_PASS="${SPLUNK_ADMIN_PASSWORD:-ChangeMeNow!}"
SPLUNK_APPS_DIR="$SPLUNK_HOME/etc/apps"
GOAD_BLUE_HOME="/opt/goad-blue"
TEMP_DIR="/tmp/splunk-goad-blue"
LOG_FILE="/var/log/goad-blue/splunk-dashboard-install.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "OK")
            echo -e "${GREEN}[OK]${NC} $message"
            log_message "OK: $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            log_message "WARNING: $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            log_message "ERROR: $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            log_message "INFO: $message"
            ;;
    esac
}

# Check if Splunk is running
check_splunk_status() {
    if ! systemctl is-active --quiet Splunkd; then
        print_status "ERROR" "Splunk is not running. Please start Splunk first."
        exit 1
    fi
    
    # Wait for Splunk to be fully ready
    local timeout=60
    local counter=0
    while ! curl -k -s https://localhost:8000 >/dev/null 2>&1; do
        if [ $counter -ge $timeout ]; then
            print_status "ERROR" "Timeout waiting for Splunk to be ready"
            exit 1
        fi
        sleep 5
        counter=$((counter + 5))
    done
    
    print_status "OK" "Splunk is running and ready"
}

# Create temporary directory
setup_temp_directory() {
    print_status "INFO" "Setting up temporary directory..."
    rm -rf "$TEMP_DIR"
    mkdir -p "$TEMP_DIR"
    cd "$TEMP_DIR"
}

# Download and install Splunk app
install_splunk_app() {
    local app_name=$1
    local app_url=$2
    local app_filename=$3
    
    print_status "INFO" "Installing $app_name..."
    
    # Download app
    if [ -n "$app_url" ]; then
        wget -O "$app_filename" "$app_url" || {
            print_status "WARNING" "Failed to download $app_name from $app_url"
            return 1
        }
    fi
    
    # Extract and install
    if [ -f "$app_filename" ]; then
        tar -xzf "$app_filename" -C "$SPLUNK_APPS_DIR/"
        chown -R $SPLUNK_USER:$SPLUNK_USER "$SPLUNK_APPS_DIR/"
        print_status "OK" "$app_name installed successfully"
        return 0
    else
        print_status "ERROR" "App file not found: $app_filename"
        return 1
    fi
}

# Install Common Information Model (CIM)
install_cim() {
    print_status "INFO" "Installing Splunk Common Information Model (CIM)..."
    
    # CIM Add-on
    local cim_url="https://splunkbase.splunk.com/app/1621/"
    local cim_filename="splunk-common-information-model-cim_5.3.2.tgz"
    
    # Note: In production, download from Splunkbase with proper authentication
    # For now, create a basic CIM configuration
    
    mkdir -p "$SPLUNK_APPS_DIR/Splunk_SA_CIM"
    cat > "$SPLUNK_APPS_DIR/Splunk_SA_CIM/default/app.conf" << 'EOF'
[install]
is_configured = 1
state = enabled

[ui]
is_visible = 1
label = Splunk Common Information Model (CIM)

[launcher]
author = Splunk Inc.
description = The Splunk Common Information Model (CIM) Add-on
version = 5.3.2
EOF

    # Create basic CIM data models
    mkdir -p "$SPLUNK_APPS_DIR/Splunk_SA_CIM/default/data/models"
    
    # Authentication data model
    cat > "$SPLUNK_APPS_DIR/Splunk_SA_CIM/default/data/models/Authentication.json" << 'EOF'
{
    "modelName": "Authentication",
    "displayName": "Authentication",
    "description": "Authentication events data model",
    "objectSummary": {
        "Event-Based": {
            "Authentication": {
                "displayName": "Authentication",
                "parentName": "BaseEvent",
                "comment": "Authentication events",
                "fields": [
                    {"fieldName": "action", "displayName": "Action", "type": "string"},
                    {"fieldName": "app", "displayName": "Application", "type": "string"},
                    {"fieldName": "dest", "displayName": "Destination", "type": "string"},
                    {"fieldName": "src", "displayName": "Source", "type": "string"},
                    {"fieldName": "user", "displayName": "User", "type": "string"},
                    {"fieldName": "signature", "displayName": "Signature", "type": "string"}
                ]
            }
        }
    }
}
EOF

    # Network Traffic data model
    cat > "$SPLUNK_APPS_DIR/Splunk_SA_CIM/default/data/models/Network_Traffic.json" << 'EOF'
{
    "modelName": "Network_Traffic",
    "displayName": "Network Traffic",
    "description": "Network traffic events data model",
    "objectSummary": {
        "Event-Based": {
            "All_Traffic": {
                "displayName": "All Traffic",
                "parentName": "BaseEvent",
                "comment": "All network traffic events",
                "fields": [
                    {"fieldName": "src", "displayName": "Source IP", "type": "string"},
                    {"fieldName": "dest", "displayName": "Destination IP", "type": "string"},
                    {"fieldName": "src_port", "displayName": "Source Port", "type": "number"},
                    {"fieldName": "dest_port", "displayName": "Destination Port", "type": "number"},
                    {"fieldName": "protocol", "displayName": "Protocol", "type": "string"},
                    {"fieldName": "bytes", "displayName": "Bytes", "type": "number"},
                    {"fieldName": "packets", "displayName": "Packets", "type": "number"}
                ]
            }
        }
    }
}
EOF

    chown -R $SPLUNK_USER:$SPLUNK_USER "$SPLUNK_APPS_DIR/Splunk_SA_CIM"
    print_status "OK" "CIM installed successfully"
}

# Install Technical Add-ons
install_technical_addons() {
    print_status "INFO" "Installing Technical Add-ons..."
    
    # Create TA directory structure
    local ta_list=(
        "TA-microsoft-windows"
        "TA-nix"
        "TA-microsoft-sysmon"
        "TA-suricata"
        "TA-zeek"
        "TA-linux-auditd"
        "TA-powershell"
        "TA-dns"
        "TA-apache"
        "TA-nginx"
    )
    
    for ta in "${ta_list[@]}"; do
        create_technical_addon "$ta"
    done
}

# Create individual Technical Add-on
create_technical_addon() {
    local ta_name=$1
    local ta_dir="$SPLUNK_APPS_DIR/$ta_name"
    
    print_status "INFO" "Creating $ta_name..."
    
    mkdir -p "$ta_dir/default"
    mkdir -p "$ta_dir/metadata"
    
    # Create app.conf
    cat > "$ta_dir/default/app.conf" << EOF
[install]
is_configured = 1
state = enabled

[ui]
is_visible = 0
label = $ta_name

[launcher]
author = GOAD-Blue
description = Technical Add-on for $ta_name
version = 1.0.0
EOF

    # Create specific configurations based on TA type
    case $ta_name in
        "TA-microsoft-windows")
            create_windows_ta "$ta_dir"
            ;;
        "TA-nix")
            create_nix_ta "$ta_dir"
            ;;
        "TA-microsoft-sysmon")
            create_sysmon_ta "$ta_dir"
            ;;
        "TA-suricata")
            create_suricata_ta "$ta_dir"
            ;;
        "TA-zeek")
            create_zeek_ta "$ta_dir"
            ;;
        "TA-linux-auditd")
            create_auditd_ta "$ta_dir"
            ;;
        "TA-powershell")
            create_powershell_ta "$ta_dir"
            ;;
        "TA-dns")
            create_dns_ta "$ta_dir"
            ;;
        "TA-apache")
            create_apache_ta "$ta_dir"
            ;;
        "TA-nginx")
            create_nginx_ta "$ta_dir"
            ;;
    esac
    
    chown -R $SPLUNK_USER:$SPLUNK_USER "$ta_dir"
    print_status "OK" "$ta_name created successfully"
}

# Create Windows TA
create_windows_ta() {
    local ta_dir=$1
    
    # props.conf for Windows events
    cat > "$ta_dir/default/props.conf" << 'EOF'
[WinEventLog:Security]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = SystemTime='
TIME_FORMAT = %Y-%m-%d %H:%M:%S
MAX_TIMESTAMP_LOOKAHEAD = 28
KV_MODE = xml
category = Operating System
description = Windows Security Event Log
pulldown_type = 1

[WinEventLog:System]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = SystemTime='
TIME_FORMAT = %Y-%m-%d %H:%M:%S
MAX_TIMESTAMP_LOOKAHEAD = 28
KV_MODE = xml
category = Operating System
description = Windows System Event Log

[WinEventLog:Application]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = SystemTime='
TIME_FORMAT = %Y-%m-%d %H:%M:%S
MAX_TIMESTAMP_LOOKAHEAD = 28
KV_MODE = xml
category = Application
description = Windows Application Event Log
EOF

    # transforms.conf for field extractions
    cat > "$ta_dir/default/transforms.conf" << 'EOF'
[windows_eventcode]
REGEX = <EventID>(\d+)</EventID>
FORMAT = EventCode::$1

[windows_user]
REGEX = <Data Name='TargetUserName'>([^<]+)</Data>
FORMAT = user::$1

[windows_src_ip]
REGEX = <Data Name='IpAddress'>([^<]+)</Data>
FORMAT = src_ip::$1
EOF

    # eventtypes.conf
    cat > "$ta_dir/default/eventtypes.conf" << 'EOF'
[windows_logon]
search = source="WinEventLog:Security" EventCode=4624

[windows_logoff]
search = source="WinEventLog:Security" EventCode=4634

[windows_failed_logon]
search = source="WinEventLog:Security" EventCode=4625

[windows_account_lockout]
search = source="WinEventLog:Security" EventCode=4740
EOF
}

# Create Suricata TA
create_suricata_ta() {
    local ta_dir=$1
    
    # props.conf for Suricata
    cat > "$ta_dir/default/props.conf" << 'EOF'
[suricata]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = timestamp":"
TIME_FORMAT = %Y-%m-%dT%H:%M:%S.%6N%z
MAX_TIMESTAMP_LOOKAHEAD = 30
KV_MODE = json
category = Network & Security
description = Suricata IDS/IPS Events

[suricata:alert]
SHOULD_LINEMERGE = false
KV_MODE = json
category = Network & Security
description = Suricata Alert Events

[suricata:eve]
SHOULD_LINEMERGE = false
KV_MODE = json
category = Network & Security
description = Suricata EVE JSON Events
EOF

    # transforms.conf
    cat > "$ta_dir/default/transforms.conf" << 'EOF'
[suricata_alert_signature]
REGEX = "signature":"([^"]+)"
FORMAT = signature::$1

[suricata_src_ip]
REGEX = "src_ip":"([^"]+)"
FORMAT = src_ip::$1

[suricata_dest_ip]
REGEX = "dest_ip":"([^"]+)"
FORMAT = dest_ip::$1

[suricata_proto]
REGEX = "proto":"([^"]+)"
FORMAT = protocol::$1
EOF

    # eventtypes.conf
    cat > "$ta_dir/default/eventtypes.conf" << 'EOF'
[suricata_alert]
search = sourcetype=suricata:alert OR (sourcetype=suricata event_type=alert)

[suricata_dns]
search = sourcetype=suricata event_type=dns

[suricata_http]
search = sourcetype=suricata event_type=http

[suricata_tls]
search = sourcetype=suricata event_type=tls

[suricata_flow]
search = sourcetype=suricata event_type=flow
EOF
}

# Create Linux Auditd TA
create_auditd_ta() {
    local ta_dir=$1
    
    # props.conf for Linux auditd
    cat > "$ta_dir/default/props.conf" << 'EOF'
[linux:audit]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = audit\(
TIME_FORMAT = %s.%3N:%f
MAX_TIMESTAMP_LOOKAHEAD = 20
EXTRACT-audit_type = type=(?<audit_type>\w+)
EXTRACT-audit_msg = msg=audit\((?<audit_timestamp>[^:]+):(?<audit_serial>\d+)\)
category = Operating System
description = Linux Audit Events

[auditd]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = audit\(
TIME_FORMAT = %s.%3N:%f
MAX_TIMESTAMP_LOOKAHEAD = 20
category = Operating System
description = Linux Audit Daemon Events
EOF

    # transforms.conf
    cat > "$ta_dir/default/transforms.conf" << 'EOF'
[auditd_user]
REGEX = \buid=(\d+)
FORMAT = uid::$1

[auditd_command]
REGEX = \bcomm="([^"]+)"
FORMAT = process::$1

[auditd_exe]
REGEX = \bexe="([^"]+)"
FORMAT = process_exec::$1

[auditd_syscall]
REGEX = \bsyscall=(\d+)
FORMAT = syscall::$1
EOF

    # eventtypes.conf
    cat > "$ta_dir/default/eventtypes.conf" << 'EOF'
[linux_auditd_syscall]
search = sourcetype="linux:audit" type=SYSCALL

[linux_auditd_execve]
search = sourcetype="linux:audit" type=EXECVE

[linux_auditd_path]
search = sourcetype="linux:audit" type=PATH

[linux_auditd_user_auth]
search = sourcetype="linux:audit" type=USER_AUTH

[linux_auditd_user_start]
search = sourcetype="linux:audit" type=USER_START
EOF
}

# Create GOAD-Blue specific app
create_goad_blue_app() {
    print_status "INFO" "Creating GOAD-Blue Security App..."
    
    local app_dir="$SPLUNK_APPS_DIR/goad_blue_security"
    mkdir -p "$app_dir/default"
    mkdir -p "$app_dir/metadata"
    mkdir -p "$app_dir/local/data/ui/views"
    
    # app.conf
    cat > "$app_dir/default/app.conf" << 'EOF'
[install]
is_configured = 1
state = enabled

[ui]
is_visible = 1
label = GOAD-Blue Security

[launcher]
author = GOAD-Blue Team
description = GOAD-Blue Cybersecurity Training Platform
version = 1.0.0

[package]
id = goad_blue_security
check_for_updates = 0
EOF

    # Create main dashboard
    create_main_dashboard "$app_dir"
    
    # Create specific dashboards
    create_authentication_dashboard "$app_dir"
    create_network_dashboard "$app_dir"
    create_threat_hunting_dashboard "$app_dir"
    create_incident_response_dashboard "$app_dir"
    
    # Create saved searches
    create_saved_searches "$app_dir"
    
    # Create macros
    create_macros "$app_dir"
    
    chown -R $SPLUNK_USER:$SPLUNK_USER "$app_dir"
    print_status "OK" "GOAD-Blue Security App created successfully"
}

# Create main dashboard
create_main_dashboard() {
    local app_dir=$1
    
    cat > "$app_dir/local/data/ui/views/goad_blue_overview.xml" << 'EOF'
<form version="1.1">
  <label>GOAD-Blue Security Overview</label>
  <description>Main security monitoring dashboard for GOAD-Blue environment</description>
  
  <fieldset submitButton="false" autoRun="true">
    <input type="time" token="time_picker">
      <label>Time Range</label>
      <default>
        <earliest>-24h@h</earliest>
        <latest>now</latest>
      </default>
    </input>
  </fieldset>
  
  <row>
    <panel>
      <title>Environment Health</title>
      <single>
        <search>
          <query>
            | rest /services/server/info 
            | eval health_score=if(isnotnull(version), 100, 0)
            | fields health_score
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
        <option name="drilldown">none</option>
        <option name="colorBy">value</option>
        <option name="colorMode">block</option>
        <option name="rangeColors">["0x65A637","0x6DB7C6","0xF7BC38","0xF58F39","0xD93F3C"]</option>
        <option name="rangeValues">[0,30,70,90,100]</option>
        <option name="unit">%</option>
      </single>
    </panel>
    
    <panel>
      <title>Total Events (24h)</title>
      <single>
        <search>
          <query>
            index=goad_blue_* earliest=-24h@h latest=now 
            | stats count
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
        <option name="drilldown">none</option>
        <option name="numberPrecision">0</option>
      </single>
    </panel>
    
    <panel>
      <title>Security Alerts</title>
      <single>
        <search>
          <query>
            index=goad_blue_security earliest=-24h@h latest=now 
            | stats count
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
        <option name="drilldown">none</option>
        <option name="colorBy">value</option>
        <option name="colorMode">block</option>
        <option name="rangeColors">["0x65A637","0xF7BC38","0xD93F3C"]</option>
        <option name="rangeValues">[0,10,50]</option>
      </single>
    </panel>
  </row>
  
  <row>
    <panel>
      <title>Event Timeline</title>
      <chart>
        <search>
          <query>
            index=goad_blue_* earliest=$time_picker.earliest$ latest=$time_picker.latest$
            | timechart span=1h count by index
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
        <option name="charting.chart">column</option>
        <option name="charting.chart.stackMode">stacked</option>
        <option name="charting.legend.placement">bottom</option>
      </chart>
    </panel>
  </row>
  
  <row>
    <panel>
      <title>Top Source IPs</title>
      <table>
        <search>
          <query>
            index=goad_blue_network earliest=$time_picker.earliest$ latest=$time_picker.latest$
            | stats count by src_ip
            | sort -count
            | head 10
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
      </table>
    </panel>
    
    <panel>
      <title>Authentication Events</title>
      <table>
        <search>
          <query>
            index=goad_blue_windows EventCode=4624 OR EventCode=4625 earliest=$time_picker.earliest$ latest=$time_picker.latest$
            | eval event_type=case(EventCode=4624, "Successful Logon", EventCode=4625, "Failed Logon")
            | stats count by event_type, user
            | sort -count
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
      </table>
    </panel>
  </row>
</form>
EOF
}

# Create additional TA functions (continuing from previous TAs)
create_nix_ta() {
    local ta_dir=$1

    cat > "$ta_dir/default/props.conf" << 'EOF'
[linux_secure]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = ^
TIME_FORMAT = %b %d %H:%M:%S
MAX_TIMESTAMP_LOOKAHEAD = 20
category = Operating System
description = Linux Secure Log

[syslog]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = ^
TIME_FORMAT = %b %d %H:%M:%S
MAX_TIMESTAMP_LOOKAHEAD = 20
category = Operating System
description = Linux Syslog
EOF

    cat > "$ta_dir/default/transforms.conf" << 'EOF'
[linux_user]
REGEX = \buser\s+(\w+)
FORMAT = user::$1

[linux_process]
REGEX = \b(\w+)\[\d+\]:
FORMAT = process::$1
EOF
}

create_sysmon_ta() {
    local ta_dir=$1

    cat > "$ta_dir/default/props.conf" << 'EOF'
[XmlWinEventLog:Microsoft-Windows-Sysmon/Operational]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = SystemTime='
TIME_FORMAT = %Y-%m-%d %H:%M:%S.%3N
MAX_TIMESTAMP_LOOKAHEAD = 28
KV_MODE = xml
category = Application
description = Sysmon Events

[WinEventLog:Microsoft-Windows-Sysmon/Operational]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = SystemTime='
TIME_FORMAT = %Y-%m-%d %H:%M:%S.%3N
MAX_TIMESTAMP_LOOKAHEAD = 28
KV_MODE = xml
category = Application
description = Sysmon Events
EOF

    cat > "$ta_dir/default/eventtypes.conf" << 'EOF'
[sysmon_process_creation]
search = source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=1

[sysmon_network_connection]
search = source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=3

[sysmon_process_terminate]
search = source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=5

[sysmon_file_create]
search = source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=11
EOF
}

create_zeek_ta() {
    local ta_dir=$1

    cat > "$ta_dir/default/props.conf" << 'EOF'
[zeek]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = ^
TIME_FORMAT = %s.%6N
MAX_TIMESTAMP_LOOKAHEAD = 20
FIELD_DELIMITER = \t
FIELD_QUOTE = "
category = Network & Security
description = Zeek Network Analysis

[zeek:conn]
SHOULD_LINEMERGE = false
FIELD_DELIMITER = \t
category = Network & Security
description = Zeek Connection Log

[zeek:dns]
SHOULD_LINEMERGE = false
FIELD_DELIMITER = \t
category = Network & Security
description = Zeek DNS Log

[zeek:http]
SHOULD_LINEMERGE = false
FIELD_DELIMITER = \t
category = Network & Security
description = Zeek HTTP Log
EOF
}

create_powershell_ta() {
    local ta_dir=$1

    cat > "$ta_dir/default/props.conf" << 'EOF'
[WinEventLog:Microsoft-Windows-PowerShell/Operational]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = SystemTime='
TIME_FORMAT = %Y-%m-%d %H:%M:%S.%3N
MAX_TIMESTAMP_LOOKAHEAD = 28
KV_MODE = xml
category = Application
description = PowerShell Operational Events

[powershell]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
category = Application
description = PowerShell Events
EOF

    cat > "$ta_dir/default/eventtypes.conf" << 'EOF'
[powershell_script_block]
search = source="WinEventLog:Microsoft-Windows-PowerShell/Operational" EventCode=4104

[powershell_command_execution]
search = source="WinEventLog:Microsoft-Windows-PowerShell/Operational" EventCode=4103

[powershell_module_logging]
search = source="WinEventLog:Microsoft-Windows-PowerShell/Operational" EventCode=4103
EOF
}

create_dns_ta() {
    local ta_dir=$1

    cat > "$ta_dir/default/props.conf" << 'EOF'
[dns]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
category = Network & Security
description = DNS Events

[bind]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = ^
TIME_FORMAT = %d-%b-%Y %H:%M:%S.%3N
category = Network & Security
description = BIND DNS Server
EOF
}

create_apache_ta() {
    local ta_dir=$1

    cat > "$ta_dir/default/props.conf" << 'EOF'
[access_combined]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = \[
TIME_FORMAT = %d/%b/%Y:%H:%M:%S %z
MAX_TIMESTAMP_LOOKAHEAD = 30
category = Web
description = Apache Access Log

[apache_error]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = \[
TIME_FORMAT = %a %b %d %H:%M:%S.%6N %Y
category = Web
description = Apache Error Log
EOF
}

create_nginx_ta() {
    local ta_dir=$1

    cat > "$ta_dir/default/props.conf" << 'EOF'
[nginx_access]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = \[
TIME_FORMAT = %d/%b/%Y:%H:%M:%S %z
MAX_TIMESTAMP_LOOKAHEAD = 30
category = Web
description = Nginx Access Log

[nginx_error]
SHOULD_LINEMERGE = false
TRUNCATE = 8388608
TIME_PREFIX = ^
TIME_FORMAT = %Y/%m/%d %H:%M:%S
category = Web
description = Nginx Error Log
EOF
}

# Create authentication dashboard
create_authentication_dashboard() {
    local app_dir=$1

    cat > "$app_dir/local/data/ui/views/authentication_monitoring.xml" << 'EOF'
<form version="1.1">
  <label>Authentication Monitoring</label>
  <description>Monitor authentication events across GOAD-Blue environment</description>

  <fieldset submitButton="false" autoRun="true">
    <input type="time" token="time_picker">
      <label>Time Range</label>
      <default>
        <earliest>-4h@h</earliest>
        <latest>now</latest>
      </default>
    </input>
  </fieldset>

  <row>
    <panel>
      <title>Authentication Summary</title>
      <table>
        <search>
          <query>
            index=goad_blue_windows (EventCode=4624 OR EventCode=4625 OR EventCode=4648)
            | eval auth_result=case(
                EventCode=4624, "Success",
                EventCode=4625, "Failure",
                EventCode=4648, "Explicit Logon"
              )
            | stats count by auth_result
            | sort -count
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
      </table>
    </panel>

    <panel>
      <title>Failed Logon Attempts</title>
      <chart>
        <search>
          <query>
            index=goad_blue_windows EventCode=4625
            | timechart span=30m count
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
        <option name="charting.chart">line</option>
        <option name="charting.chart.nullValueMode">connect</option>
      </chart>
    </panel>
  </row>

  <row>
    <panel>
      <title>Top Failed Logon Users</title>
      <table>
        <search>
          <query>
            index=goad_blue_windows EventCode=4625
            | rex field=_raw "Account Name:\s+(?&lt;failed_user&gt;[^\s]+)"
            | stats count by failed_user
            | sort -count
            | head 10
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
      </table>
    </panel>

    <panel>
      <title>Logon Types Distribution</title>
      <chart>
        <search>
          <query>
            index=goad_blue_windows EventCode=4624
            | rex field=_raw "Logon Type:\s+(?&lt;logon_type&gt;\d+)"
            | eval logon_type_desc=case(
                logon_type=2, "Interactive",
                logon_type=3, "Network",
                logon_type=4, "Batch",
                logon_type=5, "Service",
                logon_type=7, "Unlock",
                logon_type=8, "NetworkCleartext",
                logon_type=9, "NewCredentials",
                logon_type=10, "RemoteInteractive",
                logon_type=11, "CachedInteractive",
                1=1, "Other (".logon_type.")"
              )
            | stats count by logon_type_desc
            | sort -count
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
        <option name="charting.chart">pie</option>
      </chart>
    </panel>
  </row>
</form>
EOF
}

# Create network dashboard
create_network_dashboard() {
    local app_dir=$1

    cat > "$app_dir/local/data/ui/views/network_monitoring.xml" << 'EOF'
<form version="1.1">
  <label>Network Monitoring</label>
  <description>Network traffic and security monitoring for GOAD-Blue</description>

  <fieldset submitButton="false" autoRun="true">
    <input type="time" token="time_picker">
      <label>Time Range</label>
      <default>
        <earliest>-1h@h</earliest>
        <latest>now</latest>
      </default>
    </input>
  </fieldset>

  <row>
    <panel>
      <title>Suricata Alerts</title>
      <table>
        <search>
          <query>
            index=goad_blue_network sourcetype=suricata:alert
            | stats count by signature, src_ip, dest_ip
            | sort -count
            | head 20
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
      </table>
    </panel>
  </row>

  <row>
    <panel>
      <title>Top Talkers (Source IPs)</title>
      <chart>
        <search>
          <query>
            index=goad_blue_network
            | stats count by src_ip
            | sort -count
            | head 10
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
        <option name="charting.chart">bar</option>
        <option name="charting.chart.orientation">x</option>
      </chart>
    </panel>

    <panel>
      <title>Protocol Distribution</title>
      <chart>
        <search>
          <query>
            index=goad_blue_network
            | stats count by protocol
            | sort -count
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
        <option name="charting.chart">pie</option>
      </chart>
    </panel>
  </row>

  <row>
    <panel>
      <title>DNS Queries</title>
      <table>
        <search>
          <query>
            index=goad_blue_network sourcetype=suricata event_type=dns
            | stats count by dns.query, src_ip
            | sort -count
            | head 15
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
      </table>
    </panel>
  </row>
</form>
EOF
}

# Create threat hunting dashboard
create_threat_hunting_dashboard() {
    local app_dir=$1

    cat > "$app_dir/local/data/ui/views/threat_hunting.xml" << 'EOF'
<form version="1.1">
  <label>Threat Hunting</label>
  <description>Advanced threat hunting and detection for GOAD-Blue</description>

  <fieldset submitButton="false" autoRun="true">
    <input type="time" token="time_picker">
      <label>Time Range</label>
      <default>
        <earliest>-24h@h</earliest>
        <latest>now</latest>
      </default>
    </input>
  </fieldset>

  <row>
    <panel>
      <title>Suspicious PowerShell Activity</title>
      <table>
        <search>
          <query>
            index=goad_blue_windows source="WinEventLog:Microsoft-Windows-PowerShell/Operational"
            | search "Invoke-" OR "DownloadString" OR "EncodedCommand" OR "bypass" OR "hidden"
            | stats count by ComputerName, User, ScriptBlockText
            | sort -count
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
      </table>
    </panel>
  </row>

  <row>
    <panel>
      <title>Lateral Movement Indicators</title>
      <table>
        <search>
          <query>
            index=goad_blue_windows (EventCode=4648 OR EventCode=4624)
            | eval movement_type=case(
                EventCode=4648, "Explicit Credential Use",
                EventCode=4624 AND match(_raw, "Logon Type:\s+3"), "Network Logon"
              )
            | where isnotnull(movement_type)
            | stats count by movement_type, src_ip, user, dest
            | sort -count
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
      </table>
    </panel>
  </row>

  <row>
    <panel>
      <title>Privilege Escalation Events</title>
      <table>
        <search>
          <query>
            index=goad_blue_windows (EventCode=4672 OR EventCode=4673 OR EventCode=4674)
            | eval privilege_event=case(
                EventCode=4672, "Special Privileges Assigned",
                EventCode=4673, "Privileged Service Called",
                EventCode=4674, "Privileged Object Operation"
              )
            | stats count by privilege_event, user, ComputerName
            | sort -count
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
      </table>
    </panel>
  </row>

  <row>
    <panel>
      <title>Suspicious Network Connections</title>
      <table>
        <search>
          <query>
            index=goad_blue_network
            | where (dest_port=443 OR dest_port=80 OR dest_port=8080 OR dest_port=8443)
            | stats count, values(dest_port) as ports by src_ip, dest_ip
            | where count > 100
            | sort -count
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
      </table>
    </panel>
  </row>
</form>
EOF
}

# Create incident response dashboard
create_incident_response_dashboard() {
    local app_dir=$1

    cat > "$app_dir/local/data/ui/views/incident_response.xml" << 'EOF'
<form version="1.1">
  <label>Incident Response</label>
  <description>Incident response and forensics dashboard for GOAD-Blue</description>

  <fieldset submitButton="false" autoRun="true">
    <input type="time" token="time_picker">
      <label>Time Range</label>
      <default>
        <earliest>-4h@h</earliest>
        <latest>now</latest>
      </default>
    </input>
    <input type="text" token="host_filter">
      <label>Host Filter</label>
      <default>*</default>
    </input>
    <input type="text" token="user_filter">
      <label>User Filter</label>
      <default>*</default>
    </input>
  </fieldset>

  <row>
    <panel>
      <title>Timeline of Events</title>
      <table>
        <search>
          <query>
            index=goad_blue_* host=$host_filter$ user=$user_filter$
            | eval event_category=case(
                match(source, "Security"), "Security",
                match(source, "System"), "System",
                match(source, "Application"), "Application",
                match(sourcetype, "suricata"), "Network",
                1=1, "Other"
              )
            | table _time, host, user, event_category, EventCode, _raw
            | sort -_time
            | head 50
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
      </table>
    </panel>
  </row>

  <row>
    <panel>
      <title>Process Creation Events</title>
      <table>
        <search>
          <query>
            index=goad_blue_windows EventCode=4688 host=$host_filter$
            | rex field=_raw "Process Name:\s+(?&lt;process_name&gt;[^\r\n]+)"
            | rex field=_raw "Process Command Line:\s+(?&lt;command_line&gt;[^\r\n]+)"
            | table _time, ComputerName, user, process_name, command_line
            | sort -_time
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
      </table>
    </panel>
  </row>

  <row>
    <panel>
      <title>File Access Events</title>
      <table>
        <search>
          <query>
            index=goad_blue_windows EventCode=4663 host=$host_filter$ user=$user_filter$
            | rex field=_raw "Object Name:\s+(?&lt;object_name&gt;[^\r\n]+)"
            | rex field=_raw "Access Mask:\s+(?&lt;access_mask&gt;[^\r\n]+)"
            | table _time, ComputerName, user, object_name, access_mask
            | sort -_time
          </query>
          <earliest>$time_picker.earliest$</earliest>
          <latest>$time_picker.latest$</latest>
        </search>
      </table>
    </panel>
  </row>
</form>
EOF
}

# Create saved searches
create_saved_searches() {
    local app_dir=$1

    cat > "$app_dir/default/savedsearches.conf" << 'EOF'
[GOAD-Blue - Failed Logon Alert]
search = index=goad_blue_windows EventCode=4625 | stats count by user, src_ip | where count > 5
dispatch.earliest_time = -15m
dispatch.latest_time = now
cron_schedule = */15 * * * *
enableSched = 1
alert.track = 1
alert.severity = 3
action.email = 1
action.email.to = <EMAIL>
action.email.subject = GOAD-Blue Alert: Multiple Failed Logons

[GOAD-Blue - Privilege Escalation Alert]
search = index=goad_blue_windows EventCode=4672 | stats count by user | where count > 3
dispatch.earliest_time = -30m
dispatch.latest_time = now
cron_schedule = */30 * * * *
enableSched = 1
alert.track = 1
alert.severity = 2
action.email = 1
action.email.to = <EMAIL>
action.email.subject = GOAD-Blue Alert: Privilege Escalation Detected

[GOAD-Blue - Suricata High Severity Alert]
search = index=goad_blue_network sourcetype=suricata:alert severity<=2
dispatch.earliest_time = -5m
dispatch.latest_time = now
cron_schedule = */5 * * * *
enableSched = 1
alert.track = 1
alert.severity = 1
action.email = 1
action.email.to = <EMAIL>
action.email.subject = GOAD-Blue Alert: High Severity Network Alert

[GOAD-Blue - PowerShell Suspicious Activity]
search = index=goad_blue_windows source="WinEventLog:Microsoft-Windows-PowerShell/Operational" ("Invoke-" OR "DownloadString" OR "EncodedCommand")
dispatch.earliest_time = -10m
dispatch.latest_time = now
cron_schedule = */10 * * * *
enableSched = 1
alert.track = 1
alert.severity = 2
action.email = 1
action.email.to = <EMAIL>
action.email.subject = GOAD-Blue Alert: Suspicious PowerShell Activity
EOF
}

# Create macros
create_macros() {
    local app_dir=$1

    cat > "$app_dir/default/macros.conf" << 'EOF'
[goad_blue_windows_events]
definition = index=goad_blue_windows
iseval = 0

[goad_blue_network_events]
definition = index=goad_blue_network
iseval = 0

[goad_blue_security_events]
definition = index=goad_blue_security
iseval = 0

[goad_blue_all_events]
definition = index=goad_blue_*
iseval = 0

[authentication_events]
definition = index=goad_blue_windows (EventCode=4624 OR EventCode=4625 OR EventCode=4648 OR EventCode=4634)
iseval = 0

[privilege_events]
definition = index=goad_blue_windows (EventCode=4672 OR EventCode=4673 OR EventCode=4674)
iseval = 0

[process_events]
definition = index=goad_blue_windows (EventCode=4688 OR EventCode=4689)
iseval = 0

[network_alerts]
definition = index=goad_blue_network sourcetype=suricata:alert
iseval = 0

[dns_events]
definition = index=goad_blue_network sourcetype=suricata event_type=dns
iseval = 0

[http_events]
definition = index=goad_blue_network sourcetype=suricata event_type=http
iseval = 0
EOF
}

# Restart Splunk to apply changes
restart_splunk() {
    print_status "INFO" "Restarting Splunk to apply changes..."

    systemctl restart Splunkd

    # Wait for Splunk to be ready
    local timeout=120
    local counter=0
    while ! curl -k -s https://localhost:8000 >/dev/null 2>&1; do
        if [ $counter -ge $timeout ]; then
            print_status "ERROR" "Timeout waiting for Splunk to restart"
            exit 1
        fi
        sleep 5
        counter=$((counter + 5))
    done

    print_status "OK" "Splunk restarted successfully"
}

# Main installation function
main() {
    echo "========================================"
    echo "  GOAD-Blue Splunk Dashboard Installer"
    echo "========================================"
    echo ""

    log_message "Starting GOAD-Blue dashboard installation"

    # Pre-installation checks
    check_splunk_status
    setup_temp_directory

    # Install components
    install_cim
    install_technical_addons
    create_goad_blue_app

    # Restart Splunk
    restart_splunk

    # Cleanup
    rm -rf "$TEMP_DIR"

    print_status "OK" "GOAD-Blue dashboard installation completed successfully!"
    echo ""
    echo "Access your dashboards at:"
    echo "  Main Dashboard: https://localhost:8000/en-US/app/goad_blue_security/goad_blue_overview"
    echo "  Authentication: https://localhost:8000/en-US/app/goad_blue_security/authentication_monitoring"
    echo "  Network: https://localhost:8000/en-US/app/goad_blue_security/network_monitoring"
    echo "  Threat Hunting: https://localhost:8000/en-US/app/goad_blue_security/threat_hunting"
    echo "  Incident Response: https://localhost:8000/en-US/app/goad_blue_security/incident_response"
    echo ""

    log_message "GOAD-Blue dashboard installation completed"
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "GOAD-Blue Splunk Dashboard Installer"
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --cim-only     Install only CIM"
        echo "  --ta-only      Install only Technical Add-ons"
        echo "  --dashboards-only Install only dashboards"
        echo ""
        exit 0
        ;;
    --cim-only)
        check_splunk_status
        setup_temp_directory
        install_cim
        restart_splunk
        ;;
    --ta-only)
        check_splunk_status
        setup_temp_directory
        install_technical_addons
        restart_splunk
        ;;
    --dashboards-only)
        check_splunk_status
        setup_temp_directory
        create_goad_blue_app
        restart_splunk
        ;;
    *)
        main
        ;;
esac
