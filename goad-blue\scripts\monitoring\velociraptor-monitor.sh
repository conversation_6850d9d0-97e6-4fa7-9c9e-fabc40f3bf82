#!/bin/bash
# Enhanced Velociraptor Monitoring Script for GOAD-Blue
# Provides comprehensive monitoring and health checks for Velociraptor EDR

set -e

# Configuration
VELOCIRAPTOR_HOME="/opt/velociraptor"
VELOCIRAPTOR_USER="velociraptor"
VELOCIRAPTOR_CONFIG="/etc/velociraptor/server.config.yaml"
VELOCIRAPTOR_GUI_URL="https://localhost:8889"
VELOCIRAPTOR_API_URL="https://localhost:8001"
VELOCIRAPTOR_FRONTEND_URL="https://localhost:8000"
LOG_FILE="/var/log/goad-blue/velociraptor-monitor.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "OK")
            echo -e "${GREEN}[OK]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
    esac
}

# Check Velociraptor installation
check_installation() {
    echo "=== Velociraptor Installation Check ==="
    
    if [ -f "/usr/local/bin/velociraptor" ]; then
        local version=$(/usr/local/bin/velociraptor version 2>/dev/null | head -1 || echo "Unknown")
        print_status "OK" "Velociraptor binary found - $version"
    else
        print_status "ERROR" "Velociraptor binary not found"
        return 1
    fi
    
    if [ -f "$VELOCIRAPTOR_CONFIG" ]; then
        print_status "OK" "Configuration file exists: $VELOCIRAPTOR_CONFIG"
    else
        print_status "ERROR" "Configuration file not found"
        return 1
    fi
    
    echo ""
}

# Check service status
check_service() {
    echo "=== Velociraptor Service Status ==="
    
    if systemctl is-active --quiet velociraptor; then
        print_status "OK" "Velociraptor service is running"
        
        local uptime=$(systemctl show velociraptor --property=ActiveEnterTimestamp --value)
        print_status "INFO" "Service started: $uptime"
        
        if systemctl is-enabled --quiet velociraptor; then
            print_status "OK" "Service is enabled for boot"
        else
            print_status "WARNING" "Service is not enabled for boot"
        fi
    else
        print_status "ERROR" "Velociraptor service is not running"
        echo "Recent service logs:"
        journalctl -u velociraptor --no-pager -n 10
        return 1
    fi
    
    echo ""
}

# Check processes
check_processes() {
    echo "=== Velociraptor Process Information ==="
    
    local processes=$(ps aux | grep velociraptor | grep -v grep)
    
    if [ -n "$processes" ]; then
        print_status "OK" "Velociraptor processes are running"
        echo "$processes"
        
        local memory_usage=$(ps aux | grep velociraptor | grep -v grep | awk '{sum+=$6} END {print sum/1024}')
        print_status "INFO" "Memory usage: ${memory_usage}MB"
    else
        print_status "ERROR" "No Velociraptor processes found"
        return 1
    fi
    
    echo ""
}

# Check network connectivity
check_network() {
    echo "=== Velociraptor Network Connectivity ==="
    
    # Check GUI interface
    if curl -k -s --connect-timeout 5 "$VELOCIRAPTOR_GUI_URL" >/dev/null; then
        print_status "OK" "GUI interface accessible ($VELOCIRAPTOR_GUI_URL)"
    else
        print_status "ERROR" "GUI interface not accessible"
    fi
    
    # Check API interface
    if curl -k -s --connect-timeout 5 "$VELOCIRAPTOR_API_URL" >/dev/null; then
        print_status "OK" "API interface accessible ($VELOCIRAPTOR_API_URL)"
    else
        print_status "ERROR" "API interface not accessible"
    fi
    
    # Check Frontend interface
    if curl -k -s --connect-timeout 5 "$VELOCIRAPTOR_FRONTEND_URL" >/dev/null; then
        print_status "OK" "Frontend interface accessible ($VELOCIRAPTOR_FRONTEND_URL)"
    else
        print_status "ERROR" "Frontend interface not accessible"
    fi
    
    # Check listening ports
    local ports=("8000" "8001" "8889")
    for port in "${ports[@]}"; do
        if netstat -ln | grep -q ":$port"; then
            print_status "OK" "Port $port is listening"
        else
            print_status "WARNING" "Port $port is not listening"
        fi
    done
    
    echo ""
}

# Check disk usage
check_disk_usage() {
    echo "=== Velociraptor Disk Usage ==="
    
    if [ -d "$VELOCIRAPTOR_HOME" ]; then
        local home_size=$(du -sh "$VELOCIRAPTOR_HOME" 2>/dev/null | cut -f1)
        print_status "INFO" "Velociraptor home size: $home_size"
        
        # Check datastore size
        if [ -d "$VELOCIRAPTOR_HOME/datastore" ]; then
            local datastore_size=$(du -sh "$VELOCIRAPTOR_HOME/datastore" 2>/dev/null | cut -f1)
            print_status "INFO" "Datastore size: $datastore_size"
        fi
        
        # Check filestore size
        if [ -d "$VELOCIRAPTOR_HOME/filestore" ]; then
            local filestore_size=$(du -sh "$VELOCIRAPTOR_HOME/filestore" 2>/dev/null | cut -f1)
            print_status "INFO" "Filestore size: $filestore_size"
        fi
        
        # Check available space
        local available_space=$(df -h "$VELOCIRAPTOR_HOME" | tail -1 | awk '{print $4}')
        local usage_percent=$(df -h "$VELOCIRAPTOR_HOME" | tail -1 | awk '{print $5}' | sed 's/%//')
        
        if [ "$usage_percent" -lt 80 ]; then
            print_status "OK" "Disk usage: ${usage_percent}% (${available_space} available)"
        elif [ "$usage_percent" -lt 90 ]; then
            print_status "WARNING" "Disk usage: ${usage_percent}% (${available_space} available)"
        else
            print_status "ERROR" "Disk usage: ${usage_percent}% - Critical!"
        fi
    fi
    
    echo ""
}

# Check client connections
check_clients() {
    echo "=== Velociraptor Client Status ==="
    
    # Try to get client count via API
    local client_response
    client_response=$(curl -k -s --connect-timeout 10 \
        -H "Content-Type: application/json" \
        "$VELOCIRAPTOR_API_URL/api/v1/GetClients" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$client_response" ]; then
        # Parse JSON response to count clients
        local client_count=$(echo "$client_response" | jq '.items | length' 2>/dev/null || echo "0")
        
        if [ "$client_count" -gt 0 ]; then
            print_status "OK" "Connected clients: $client_count"
            
            # Show recent clients
            echo "Recent client connections:"
            echo "$client_response" | jq -r '.items[0:5][] | "\(.client_id) - \(.os_info.hostname) - \(.last_seen_at)"' 2>/dev/null || echo "Unable to parse client details"
        else
            print_status "WARNING" "No clients connected"
        fi
    else
        print_status "WARNING" "Unable to retrieve client information (API may require authentication)"
    fi
    
    echo ""
}

# Check hunts
check_hunts() {
    echo "=== Velociraptor Hunt Status ==="
    
    # Try to get hunt information
    local hunt_response
    hunt_response=$(curl -k -s --connect-timeout 10 \
        -H "Content-Type: application/json" \
        "$VELOCIRAPTOR_API_URL/api/v1/ListHunts" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$hunt_response" ]; then
        local hunt_count=$(echo "$hunt_response" | jq '.items | length' 2>/dev/null || echo "0")
        
        if [ "$hunt_count" -gt 0 ]; then
            print_status "INFO" "Total hunts: $hunt_count"
            
            # Show recent hunts
            echo "Recent hunts:"
            echo "$hunt_response" | jq -r '.items[0:3][] | "\(.hunt_id) - \(.hunt_description) - \(.state)"' 2>/dev/null || echo "Unable to parse hunt details"
        else
            print_status "INFO" "No hunts configured"
        fi
    else
        print_status "WARNING" "Unable to retrieve hunt information"
    fi
    
    echo ""
}

# Check artifacts
check_artifacts() {
    echo "=== Velociraptor Artifacts ==="
    
    if [ -d "$VELOCIRAPTOR_HOME/artifacts" ]; then
        local artifact_count=$(find "$VELOCIRAPTOR_HOME/artifacts" -name "*.yaml" | wc -l)
        print_status "INFO" "Custom artifacts found: $artifact_count"
        
        if [ "$artifact_count" -gt 0 ]; then
            echo "Custom artifacts:"
            find "$VELOCIRAPTOR_HOME/artifacts" -name "*.yaml" -exec basename {} \; | head -5
        fi
    else
        print_status "INFO" "No custom artifacts directory found"
    fi
    
    echo ""
}

# Check logs
check_logs() {
    echo "=== Velociraptor Logs ==="
    
    local log_dir="/var/log/velociraptor"
    if [ -d "$log_dir" ]; then
        local log_files=$(find "$log_dir" -name "*.log" | wc -l)
        print_status "INFO" "Log files found: $log_files"
        
        # Check for recent errors
        local recent_errors=$(find "$log_dir" -name "*.log" -mtime -1 -exec grep -l "ERROR\|FATAL" {} \; 2>/dev/null | wc -l)
        
        if [ "$recent_errors" -gt 0 ]; then
            print_status "WARNING" "Recent error logs found: $recent_errors files"
            echo "Recent errors:"
            find "$log_dir" -name "*.log" -mtime -1 -exec grep "ERROR\|FATAL" {} \; 2>/dev/null | tail -3
        else
            print_status "OK" "No recent errors in logs"
        fi
    else
        print_status "WARNING" "Log directory not found: $log_dir"
    fi
    
    echo ""
}

# Generate summary
generate_summary() {
    echo "=== Velociraptor Health Summary ==="
    
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    print_status "INFO" "Report generated: $timestamp"
    
    # Calculate health score
    local health_score=100
    
    if ! systemctl is-active --quiet velociraptor; then
        health_score=$((health_score - 50))
    fi
    
    if ! curl -k -s --connect-timeout 5 "$VELOCIRAPTOR_GUI_URL" >/dev/null; then
        health_score=$((health_score - 20))
    fi
    
    if ! curl -k -s --connect-timeout 5 "$VELOCIRAPTOR_API_URL" >/dev/null; then
        health_score=$((health_score - 15))
    fi
    
    local usage_percent=$(df -h "$VELOCIRAPTOR_HOME" 2>/dev/null | tail -1 | awk '{print $5}' | sed 's/%//' || echo "0")
    if [ "$usage_percent" -gt 90 ]; then
        health_score=$((health_score - 15))
    elif [ "$usage_percent" -gt 80 ]; then
        health_score=$((health_score - 5))
    fi
    
    # Display health score
    if [ "$health_score" -ge 90 ]; then
        print_status "OK" "Overall health score: ${health_score}% - Excellent"
    elif [ "$health_score" -ge 70 ]; then
        print_status "WARNING" "Overall health score: ${health_score}% - Good"
    else
        print_status "ERROR" "Overall health score: ${health_score}% - Needs attention"
    fi
    
    echo ""
}

# Main function
main() {
    echo "========================================"
    echo "  GOAD-Blue Velociraptor Monitor v1.0"
    echo "========================================"
    echo ""
    
    check_installation || exit 1
    check_service || exit 1
    check_processes
    check_network
    check_disk_usage
    check_clients
    check_hunts
    check_artifacts
    check_logs
    generate_summary
    
    echo "========================================"
    echo "For detailed logs, check: $LOG_FILE"
    echo "========================================"
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "GOAD-Blue Velociraptor Monitor"
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --quiet, -q    Quiet mode (minimal output)"
        echo ""
        exit 0
        ;;
    --quiet|-q)
        main > "$LOG_FILE" 2>&1
        echo "Monitoring completed. Check $LOG_FILE for details."
        ;;
    *)
        main
        ;;
esac
