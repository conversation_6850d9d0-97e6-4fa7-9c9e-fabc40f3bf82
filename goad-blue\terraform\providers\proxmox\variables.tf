# Proxmox Provider Variables

# Proxmox Connection Configuration
variable "proxmox_api_url" {
  description = "Proxmox API URL"
  type        = string
  default     = "https://************:8006/api2/json"
}

variable "proxmox_user" {
  description = "Proxmox username"
  type        = string
  default     = "root@pam"
}

variable "proxmox_password" {
  description = "Proxmox password"
  type        = string
  sensitive   = true
}

variable "proxmox_tls_insecure" {
  description = "Allow insecure TLS connections"
  type        = bool
  default     = true
}

variable "proxmox_parallel" {
  description = "Number of parallel operations"
  type        = number
  default     = 4
}

variable "proxmox_timeout" {
  description = "API timeout in seconds"
  type        = number
  default     = 300
}

variable "proxmox_debug" {
  description = "Enable debug logging"
  type        = bool
  default     = false
}

# Project Configuration
variable "project_name" {
  description = "Project name"
  type        = string
  default     = "goad-blue"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "created_by" {
  description = "Creator identifier"
  type        = string
  default     = "terraform"
}

variable "name_prefix" {
  description = "Prefix for all VM names"
  type        = string
  default     = "goad-blue"
}

variable "target_node" {
  description = "Proxmox target node for VM deployment"
  type        = string
  default     = "pve-node-1"
}

variable "ubuntu_template_name" {
  description = "Name of Ubuntu template to clone from"
  type        = string
  default     = "ubuntu-22.04-template"
}

# VM Authentication
variable "vm_user" {
  description = "Default VM user for cloud-init"
  type        = string
  default     = "ubuntu"
}

variable "vm_password" {
  description = "Default VM password for cloud-init"
  type        = string
  sensitive   = true
  default     = "goadblue123!"
}

variable "ssh_public_key" {
  description = "SSH public key for VM access"
  type        = string
}

# Component Deployment Configuration
variable "deploy_components" {
  description = "Components to deploy"
  type = object({
    splunk         = bool
    security_onion = bool
    velociraptor   = bool
    misp          = bool
  })
  default = {
    splunk         = true
    security_onion = true
    velociraptor   = true
    misp          = true
  }
}

variable "security_onion_sensor_count" {
  description = "Number of Security Onion sensor VMs to deploy"
  type        = number
  default     = 1
  
  validation {
    condition     = var.security_onion_sensor_count >= 1 && var.security_onion_sensor_count <= 10
    error_message = "Security Onion sensor count must be between 1 and 10."
  }
}

# VM IDs
variable "vm_ids" {
  description = "VM IDs for each component"
  type = object({
    splunk                     = number
    security_onion_manager     = number
    security_onion_sensor_base = number
    velociraptor              = number
    misp                      = number
  })
  default = {
    splunk                     = 200
    security_onion_manager     = 270
    security_onion_sensor_base = 271
    velociraptor              = 285
    misp                      = 230
  }
}

# VM Resource Configurations
variable "vm_configs" {
  description = "VM resource configurations"
  type = object({
    splunk = object({
      cores          = number
      memory         = number
      disk_size      = string
      data_disk_size = string
    })
    security_onion_manager = object({
      cores     = number
      memory    = number
      disk_size = string
    })
    security_onion_sensor = object({
      cores     = number
      memory    = number
      disk_size = string
    })
    velociraptor = object({
      cores     = number
      memory    = number
      disk_size = string
    })
    misp = object({
      cores     = number
      memory    = number
      disk_size = string
    })
  })
  default = {
    splunk = {
      cores          = 8
      memory         = 16384
      disk_size      = "100G"
      data_disk_size = "500G"
    }
    security_onion_manager = {
      cores     = 16
      memory    = 32768
      disk_size = "1000G"
    }
    security_onion_sensor = {
      cores     = 8
      memory    = 16384
      disk_size = "500G"
    }
    velociraptor = {
      cores     = 4
      memory    = 8192
      disk_size = "200G"
    }
    misp = {
      cores     = 4
      memory    = 8192
      disk_size = "200G"
    }
  }
}

# Network Configuration
variable "network_bridges" {
  description = "Network bridge configurations"
  type = object({
    goad_blue = string
    goad      = string
    internet  = string
  })
  default = {
    goad_blue = "vmbr2"
    goad      = "vmbr1"
    internet  = "vmbr0"
  }
}

variable "network_vlans" {
  description = "VLAN configurations"
  type = object({
    goad_blue = number
    goad      = number
  })
  default = {
    goad_blue = 100
    goad      = 56
  }
}

variable "network_cidrs" {
  description = "Network CIDR configurations"
  type = object({
    goad_blue = string
    goad      = string
  })
  default = {
    goad_blue = "*************/24"
    goad      = "************/24"
  }
}

variable "network_gateway" {
  description = "Network gateway IP"
  type        = string
  default     = "*************"
}

variable "dns_servers" {
  description = "DNS server IPs"
  type        = list(string)
  default     = ["*************", "*******"]
}

variable "vm_ip_addresses" {
  description = "Static IP addresses for VMs"
  type = object({
    splunk                           = string
    security_onion_manager           = string
    security_onion_manager_monitor   = string
    velociraptor                     = string
    misp                            = string
  })
  default = {
    splunk                           = "*************0"
    security_onion_manager           = "**************"
    security_onion_manager_monitor   = "*************"
    velociraptor                     = "**************"
    misp                            = "**************"
  }
}

# Storage Configuration
variable "storage_pools" {
  description = "Storage pool configurations"
  type = object({
    primary = string
    data    = string
  })
  default = {
    primary = "local-lvm"
    data    = "local-lvm"
  }
}

variable "disk_format" {
  description = "Disk format for VMs"
  type        = string
  default     = "qcow2"
  
  validation {
    condition     = contains(["qcow2", "raw", "vmdk"], var.disk_format)
    error_message = "Disk format must be one of: qcow2, raw, vmdk."
  }
}

variable "enable_ssd" {
  description = "Enable SSD optimization"
  type        = bool
  default     = true
}

# High Availability Configuration
variable "enable_ha" {
  description = "Enable High Availability for VMs"
  type        = bool
  default     = false
}

variable "ha_nofailback" {
  description = "Disable automatic failback in HA"
  type        = bool
  default     = false
}

variable "ha_max_restart" {
  description = "Maximum restart attempts in HA"
  type        = number
  default     = 2
}

variable "ha_max_relocate" {
  description = "Maximum relocate attempts in HA"
  type        = number
  default     = 2
}

# Backup Configuration
variable "backup_schedule" {
  description = "Backup schedule configuration"
  type = object({
    enabled  = bool
    schedule = string
    storage  = string
    compress = string
  })
  default = {
    enabled  = true
    schedule = "daily"
    storage  = "backup-storage"
    compress = "lzo"
  }
}

variable "create_backup_storage" {
  description = "Create backup storage configuration"
  type        = bool
  default     = false
}

variable "backup_storage_path" {
  description = "Path for backup storage"
  type        = string
  default     = "/backup/goad-blue"
}

variable "backup_storage_shared" {
  description = "Enable shared backup storage"
  type        = bool
  default     = false
}

# Monitoring Configuration
variable "monitoring_config" {
  description = "Monitoring configuration"
  type = object({
    enable_prometheus = bool
    enable_grafana    = bool
    metrics_retention = string
  })
  default = {
    enable_prometheus = true
    enable_grafana    = true
    metrics_retention = "30d"
  }
}

# Security Configuration
variable "security_config" {
  description = "Security configuration"
  type = object({
    enable_firewall     = bool
    allowed_ssh_sources = list(string)
    enable_fail2ban     = bool
  })
  default = {
    enable_firewall     = true
    allowed_ssh_sources = ["*************/24", "***********/24"]
    enable_fail2ban     = true
  }
}

variable "enable_firewall" {
  description = "Enable Proxmox firewall rules"
  type        = bool
  default     = false
}

variable "allowed_ssh_sources" {
  description = "Allowed SSH source networks"
  type        = list(string)
  default     = ["*************/24", "***********/24"]
}
