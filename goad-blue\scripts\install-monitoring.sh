#!/bin/bash
# Install GOAD-Blue monitoring scripts
# This script deploys all monitoring scripts and sets up monitoring infrastructure

set -e

# Configuration
GOAD_BLUE_HOME="/opt/goad-blue"
MONITOR_DIR="$GOAD_BLUE_HOME/scripts/monitoring"
LOG_DIR="/var/log/goad-blue"
CRON_DIR="/etc/cron.d"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "OK")
            echo -e "${GREEN}[OK]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
    esac
}

echo "=== Installing GOAD-Blue Monitoring Scripts ==="

# Create directories
print_status "INFO" "Creating monitoring directories..."
mkdir -p "$MONITOR_DIR"
mkdir -p "$LOG_DIR"
mkdir -p "$GOAD_BLUE_HOME/reports"

# Set permissions
chown -R goad-blue:goad-blue "$GOAD_BLUE_HOME" 2>/dev/null || true
chown -R goad-blue:goad-blue "$LOG_DIR" 2>/dev/null || true

# Make all monitoring scripts executable
print_status "INFO" "Setting script permissions..."
find "$MONITOR_DIR" -name "*.sh" -exec chmod +x {} \; 2>/dev/null || true

# Install jq for JSON parsing (required for some monitoring scripts)
if ! command -v jq >/dev/null 2>&1; then
    print_status "INFO" "Installing jq for JSON parsing..."
    if command -v apt-get >/dev/null 2>&1; then
        apt-get update && apt-get install -y jq
    elif command -v yum >/dev/null 2>&1; then
        yum install -y jq
    else
        print_status "WARNING" "Could not install jq automatically"
    fi
fi

# Create monitoring configuration file
print_status "INFO" "Creating monitoring configuration..."
cat > "$GOAD_BLUE_HOME/config/monitoring.conf" << 'EOF'
# GOAD-Blue Monitoring Configuration

# Monitoring intervals (in minutes)
MONITOR_INTERVAL_QUICK=5
MONITOR_INTERVAL_STANDARD=15
MONITOR_INTERVAL_DETAILED=60

# Alert thresholds
CPU_THRESHOLD=80
MEMORY_THRESHOLD=85
DISK_THRESHOLD=90

# Component URLs
SPLUNK_URL="https://localhost:8000"
VELOCIRAPTOR_URL="https://localhost:8889"
MALCOLM_URL="https://localhost"
ELASTICSEARCH_URL="http://localhost:9200"
KIBANA_URL="http://localhost:5601"

# Notification settings
ENABLE_EMAIL_ALERTS=false
ADMIN_EMAIL="<EMAIL>"
ENABLE_SYSLOG_ALERTS=true
SYSLOG_FACILITY="local0"

# Log retention (days)
LOG_RETENTION_DAYS=30
REPORT_RETENTION_DAYS=90
EOF

# Create monitoring wrapper script
print_status "INFO" "Creating monitoring wrapper script..."
cat > "$GOAD_BLUE_HOME/scripts/monitor-wrapper.sh" << 'EOF'
#!/bin/bash
# GOAD-Blue Monitoring Wrapper Script
# Provides unified interface for all monitoring functions

GOAD_BLUE_HOME="/opt/goad-blue"
MONITOR_DIR="$GOAD_BLUE_HOME/scripts/monitoring"

# Source configuration
if [ -f "$GOAD_BLUE_HOME/config/monitoring.conf" ]; then
    source "$GOAD_BLUE_HOME/config/monitoring.conf"
fi

# Function to run component monitoring
monitor_component() {
    local component=$1
    local script="$MONITOR_DIR/${component}-monitor.sh"
    
    if [ -f "$script" ]; then
        echo "Monitoring $component..."
        bash "$script" --quiet
        return $?
    else
        echo "Monitor script not found for $component"
        return 1
    fi
}

# Function to run master monitoring
monitor_all() {
    local script="$MONITOR_DIR/goad-blue-monitor.sh"
    
    if [ -f "$script" ]; then
        bash "$script" "$@"
        return $?
    else
        echo "Master monitor script not found"
        return 1
    fi
}

# Main execution
case "${1:-all}" in
    "splunk")
        monitor_component "splunk"
        ;;
    "velociraptor")
        monitor_component "velociraptor"
        ;;
    "malcolm")
        monitor_component "malcolm"
        ;;
    "all"|"")
        monitor_all "${@:2}"
        ;;
    *)
        echo "Usage: $0 [splunk|velociraptor|malcolm|all] [options]"
        echo "  all: Run complete monitoring (default)"
        echo "  component: Monitor specific component"
        exit 1
        ;;
esac
EOF

chmod +x "$GOAD_BLUE_HOME/scripts/monitor-wrapper.sh"

# Create systemd service for monitoring
print_status "INFO" "Creating monitoring systemd service..."
cat > /etc/systemd/system/goad-blue-monitor.service << 'EOF'
[Unit]
Description=GOAD-Blue Monitoring Service
After=network.target

[Service]
Type=oneshot
User=goad-blue
Group=goad-blue
ExecStart=/opt/goad-blue/scripts/monitor-wrapper.sh all --quiet
WorkingDirectory=/opt/goad-blue
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# Create systemd timer for regular monitoring
cat > /etc/systemd/system/goad-blue-monitor.timer << 'EOF'
[Unit]
Description=GOAD-Blue Monitoring Timer
Requires=goad-blue-monitor.service

[Timer]
OnCalendar=*:0/15
Persistent=true

[Install]
WantedBy=timers.target
EOF

# Enable and start the timer
systemctl daemon-reload
systemctl enable goad-blue-monitor.timer
systemctl start goad-blue-monitor.timer

print_status "OK" "Monitoring timer enabled (runs every 15 minutes)"

# Create log rotation configuration
print_status "INFO" "Setting up log rotation..."
cat > /etc/logrotate.d/goad-blue-monitoring << 'EOF'
/var/log/goad-blue/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 goad-blue goad-blue
    postrotate
        systemctl reload rsyslog > /dev/null 2>&1 || true
    endscript
}

/opt/goad-blue/reports/*.txt {
    weekly
    missingok
    rotate 12
    compress
    delaycompress
    notifempty
    create 644 goad-blue goad-blue
}
EOF

# Create monitoring dashboard script
print_status "INFO" "Creating monitoring dashboard..."
cat > "$GOAD_BLUE_HOME/scripts/monitoring-dashboard.sh" << 'EOF'
#!/bin/bash
# GOAD-Blue Monitoring Dashboard
# Provides real-time monitoring dashboard

GOAD_BLUE_HOME="/opt/goad-blue"
MONITOR_DIR="$GOAD_BLUE_HOME/scripts/monitoring"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

show_dashboard() {
    clear
    echo -e "${CYAN}========================================"
    echo -e "    GOAD-Blue Monitoring Dashboard"
    echo -e "========================================"
    echo -e "${NC}"
    
    # System overview
    echo -e "${BLUE}System Overview:${NC}"
    echo "Time: $(date)"
    echo "Uptime: $(uptime -p)"
    echo "Load: $(uptime | awk -F'load average:' '{print $2}')"
    echo ""
    
    # Component status
    echo -e "${BLUE}Component Status:${NC}"
    
    # Check Splunk
    if systemctl is-active --quiet Splunkd 2>/dev/null; then
        echo -e "${GREEN}[●]${NC} Splunk Enterprise"
    else
        echo -e "${RED}[●]${NC} Splunk Enterprise"
    fi
    
    # Check Velociraptor
    if systemctl is-active --quiet velociraptor 2>/dev/null; then
        echo -e "${GREEN}[●]${NC} Velociraptor EDR"
    else
        echo -e "${RED}[●]${NC} Velociraptor EDR"
    fi
    
    # Check Malcolm (Docker)
    if docker ps | grep -q malcolm 2>/dev/null; then
        echo -e "${GREEN}[●]${NC} Malcolm Network Analysis"
    else
        echo -e "${RED}[●]${NC} Malcolm Network Analysis"
    fi
    
    # Check Elasticsearch
    if systemctl is-active --quiet elasticsearch 2>/dev/null; then
        echo -e "${GREEN}[●]${NC} Elasticsearch"
    else
        echo -e "${RED}[●]${NC} Elasticsearch"
    fi
    
    echo ""
    
    # Resource usage
    echo -e "${BLUE}Resource Usage:${NC}"
    echo "CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
    echo "Memory: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')"
    echo "Disk: $(df -h / | tail -1 | awk '{print $5}')"
    echo ""
    
    # Recent alerts
    echo -e "${BLUE}Recent Alerts:${NC}"
    if [ -d "/var/log/goad-blue" ]; then
        find /var/log/goad-blue -name "*.log" -mtime -1 -exec grep -l "ERROR\|WARNING" {} \; 2>/dev/null | head -3 | while read logfile; do
            echo "$(basename "$logfile"): $(grep -c "ERROR\|WARNING" "$logfile" 2>/dev/null || echo 0) issues"
        done
    else
        echo "No recent alerts"
    fi
    
    echo ""
    echo -e "${CYAN}Press Ctrl+C to exit, any key to refresh${NC}"
}

# Main loop
while true; do
    show_dashboard
    read -t 10 -n 1 || true
done
EOF

chmod +x "$GOAD_BLUE_HOME/scripts/monitoring-dashboard.sh"

# Create monitoring CLI tool
print_status "INFO" "Creating monitoring CLI tool..."
cat > /usr/local/bin/goad-monitor << 'EOF'
#!/bin/bash
# GOAD-Blue Monitoring CLI Tool

GOAD_BLUE_HOME="/opt/goad-blue"

case "${1:-help}" in
    "status"|"s")
        "$GOAD_BLUE_HOME/scripts/monitor-wrapper.sh" all --overview
        ;;
    "dashboard"|"d")
        "$GOAD_BLUE_HOME/scripts/monitoring-dashboard.sh"
        ;;
    "splunk")
        "$GOAD_BLUE_HOME/scripts/monitor-wrapper.sh" splunk
        ;;
    "velociraptor"|"vr")
        "$GOAD_BLUE_HOME/scripts/monitor-wrapper.sh" velociraptor
        ;;
    "malcolm"|"m")
        "$GOAD_BLUE_HOME/scripts/monitor-wrapper.sh" malcolm
        ;;
    "all"|"a")
        "$GOAD_BLUE_HOME/scripts/monitor-wrapper.sh" all
        ;;
    "report"|"r")
        "$GOAD_BLUE_HOME/scripts/monitor-wrapper.sh" all --report
        ;;
    "help"|"h"|*)
        echo "GOAD-Blue Monitoring CLI"
        echo "Usage: goad-monitor [command]"
        echo ""
        echo "Commands:"
        echo "  status, s       Show system status"
        echo "  dashboard, d    Show monitoring dashboard"
        echo "  splunk          Monitor Splunk"
        echo "  velociraptor, vr Monitor Velociraptor"
        echo "  malcolm, m      Monitor Malcolm"
        echo "  all, a          Monitor all components"
        echo "  report, r       Generate health report"
        echo "  help, h         Show this help"
        ;;
esac
EOF

chmod +x /usr/local/bin/goad-monitor

# Create completion marker
touch "$GOAD_BLUE_HOME/.monitoring-installed"

print_status "OK" "GOAD-Blue monitoring installation completed!"
echo ""
echo "Available monitoring commands:"
echo "  goad-monitor status      - Quick status check"
echo "  goad-monitor dashboard   - Real-time dashboard"
echo "  goad-monitor all         - Complete monitoring"
echo "  goad-monitor report      - Generate health report"
echo ""
echo "Monitoring service:"
echo "  systemctl status goad-blue-monitor.timer"
echo "  journalctl -u goad-blue-monitor.service"
echo ""
echo "Log files:"
echo "  /var/log/goad-blue/"
echo "  /opt/goad-blue/reports/"
