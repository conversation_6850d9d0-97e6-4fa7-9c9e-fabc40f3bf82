# https://docs.ansible.com/ansible/latest/collections/community/windows/win_scheduled_task_module.html
- name: Create a task that will be repeated every minute
  community.windows.win_scheduled_task:
    name: "{{item.value.name}}"
    description: ansible schedule task
    actions:
    - path: cmd.exe
      arguments: "/c {{item.value.cmd}}"
    triggers:
    - type: registration
      repetition:
        interval: "{{item.value.interval}}"
    - type: boot
      repetition:
        interval: "{{item.value.interval}}"
    username: SYSTEM
  with_dict: "{{ vulns_vars }}"