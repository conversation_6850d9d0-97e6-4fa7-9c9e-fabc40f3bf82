---
# Start Malcolm services

- name: <PERSON><PERSON> <PERSON>er images
  docker_image:
    name: "{{ item }}"
    source: pull
    state: present
  loop:
    - "malcolmnetsec/elasticsearch:{{ malcolm.version }}"
    - "malcolmnetsec/kibana-oss:{{ malcolm.version }}"
    - "malcolmnetsec/logstash-oss:{{ malcolm.version }}"
    - "malcolmnetsec/filebeat-oss:{{ malcolm.version }}"
    - "malcolmnetsec/nginx-proxy:{{ malcolm.version }}"
    - "malcolmnetsec/zeek:{{ malcolm.version }}"
    - "malcolmnetsec/suricata:{{ malcolm.version }}"
    - "malcolmnetsec/file-monitor:{{ malcolm.version }}"
    - "malcolmnetsec/pcap-capture:{{ malcolm.version }}"
    - "malcolmnetsec/pcap-monitor:{{ malcolm.version }}"
    - "malcolmnetsec/upload:{{ malcolm.version }}"
    - "malcolmnetsec/htadmin:{{ malcolm.version }}"
    - "malcolmnetsec/freq:{{ malcolm.version }}"
    - "malcolmnetsec/name-map-ui:{{ malcolm.version }}"
  become_user: malcolm

- name: Start <PERSON> with docker-compose
  docker_compose:
    project_src: "{{ malcolm.install_path }}"
    state: present
    pull: yes
    build: no
  become_user: malcolm
  register: malcolm_compose_result

- name: Wait for Elasticsearch to be ready
  uri:
    url: "http://localhost:9200/_cluster/health"
    method: GET
    status_code: 200
  register: elasticsearch_health
  until: elasticsearch_health.status == 200
  retries: 30
  delay: 10

- name: Wait for Kibana to be ready
  uri:
    url: "http://localhost:5601/api/status"
    method: GET
    status_code: 200
  register: kibana_health
  until: kibana_health.status == 200
  retries: 30
  delay: 10

- name: Wait for Malcolm web interface to be ready
  uri:
    url: "https://localhost/mapi/ping"
    method: GET
    status_code: 200
    validate_certs: no
  register: malcolm_web_health
  until: malcolm_web_health.status == 200
  retries: 30
  delay: 10

- name: Check Malcolm container status
  docker_container_info:
    name: "{{ item }}"
  register: malcolm_containers
  loop:
    - malcolm_elasticsearch_1
    - malcolm_kibana_1
    - malcolm_logstash_1
    - malcolm_filebeat_1
    - malcolm_nginx-proxy_1
    - malcolm_zeek_1
    - malcolm_suricata_1
    - malcolm_file-monitor_1
    - malcolm_pcap-capture_1
    - malcolm_pcap-monitor_1
    - malcolm_upload_1
    - malcolm_htadmin_1
    - malcolm_freq_1
    - malcolm_name-map-ui_1

- name: Display Malcolm container status
  debug:
    msg: "Container {{ item.item }} status: {{ item.container.State.Status if item.container else 'Not found' }}"
  loop: "{{ malcolm_containers.results }}"

- name: Enable Malcolm systemd service
  systemd:
    name: malcolm
    enabled: yes
    state: started
    daemon_reload: yes

- name: Create Malcolm health check script
  template:
    src: malcolm-health-check.sh.j2
    dest: /usr/local/bin/malcolm-health-check.sh
    mode: '0755'

- name: Create Malcolm health check cron job
  cron:
    name: "Malcolm health check"
    minute: "*/5"
    job: "/usr/local/bin/malcolm-health-check.sh"
    user: malcolm

- name: Configure Malcolm log forwarding to SIEM
  template:
    src: malcolm-siem-forwarder.conf.j2
    dest: "{{ malcolm.install_path }}/logstash/pipelines/siem-forwarder.conf"
    owner: malcolm
    group: malcolm
    mode: '0644'
  when: malcolm.siem_integration.enabled | default(false)

- name: Restart Logstash to apply SIEM forwarding
  docker_compose:
    project_src: "{{ malcolm.install_path }}"
    services:
      - logstash
    state: present
    restarted: yes
  become_user: malcolm
  when: malcolm.siem_integration.enabled | default(false)

- name: Display Malcolm access information
  debug:
    msg: |
      Malcolm deployment completed successfully!
      
      Web Interface: https://{{ ansible_default_ipv4.address }}
      Kibana: https://{{ ansible_default_ipv4.address }}/kibana
      Upload Interface: https://{{ ansible_default_ipv4.address }}/upload
      
      Default credentials:
      Username: {{ malcolm.admin_user }}
      Password: {{ malcolm.admin_password }}
      
      Services Status:
      {% for result in malcolm_containers.results %}
      - {{ result.item }}: {{ result.container.State.Status if result.container else 'Not found' }}
      {% endfor %}
