{"variables": {"so_version": "2.4.60", "vm_name": "goad-blue-security-onion", "iso_url": "https://github.com/Security-Onion-Solutions/securityonion/releases/download/v2.4.60/securityonion-2.4.60.iso", "iso_checksum": "sha256:placeholder_checksum_here"}, "builders": [{"type": "vmware-iso", "vm_name": "{{user `vm_name`}}", "guest_os_type": "centos-64", "iso_url": "{{user `iso_url`}}", "iso_checksum": "{{user `iso_checksum`}}", "ssh_username": "onion", "ssh_password": "onion", "ssh_timeout": "30m", "disk_size": 200000, "memory": 16384, "cpus": 8, "network_adapter_type": "vmxnet3", "sound": false, "usb": false, "boot_wait": "10s"}], "provisioners": [{"type": "shell", "script": "../scripts/goad-blue-security-onion-setup.sh"}, {"type": "file", "source": "../configs/security-onion/", "destination": "/tmp/so-configs/"}, {"type": "shell", "inline": ["sudo cp -r /tmp/so-configs/* /opt/so/saltstack/local/pillar/", "sudo chown -R socore:socore /opt/so/saltstack/local/pillar/", "sudo chmod -R 644 /opt/so/saltstack/local/pillar/*"]}], "post-processors": [{"type": "manifest", "output": "manifest.json", "strip_path": true}]}