# Proxmox Connection Variables
variable "proxmox_api_url" {
  description = "Proxmox API URL"
  type        = string
  default     = "https://*************:8006/api2/json"
}

variable "proxmox_user" {
  description = "Proxmox username"
  type        = string
  default     = "root@pam"
}

variable "proxmox_password" {
  description = "Proxmox password"
  type        = string
  sensitive   = true
}

variable "proxmox_tls_insecure" {
  description = "Disable TLS verification"
  type        = bool
  default     = true
}

variable "proxmox_node" {
  description = "Proxmox node name"
  type        = string
  default     = "pve"
}

# General Configuration
variable "name_prefix" {
  description = "Prefix for all VM names"
  type        = string
  default     = "goad-blue"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "lab"
}

# Storage Configuration
variable "storage_pool" {
  description = "Proxmox storage pool for VM disks"
  type        = string
  default     = "local-lvm"
}

# Network Configuration
variable "network_bridge" {
  description = "Main network bridge for management"
  type        = string
  default     = "vmbr1"
}

variable "monitoring_bridge" {
  description = "Network bridge for monitoring traffic"
  type        = string
  default     = "vmbr1"
}

variable "analysis_bridge" {
  description = "Isolated network bridge for analysis VMs"
  type        = string
  default     = "vmbr2"
}

variable "network" {
  description = "Network configuration"
  type = object({
    gateway          = string
    nameserver       = string
    search_domain    = string
    splunk_ip        = string
    so_manager_ip    = string
    sensor_subnet    = string
    velociraptor_ip  = string
    misp_ip          = string
    flare_ip         = string
    analysis_gateway = string
  })
  default = {
    gateway          = "*************"
    nameserver       = "*******"
    search_domain    = "goad-blue.local"
    splunk_ip        = "*************0"
    so_manager_ip    = "**************"
    sensor_subnet    = "**************/26"
    velociraptor_ip  = "**************"
    misp_ip          = "**************"
    flare_ip         = "*************0"
    analysis_gateway = "*************"
  }
}

# SSH Configuration
variable "ssh_public_key_path" {
  description = "Path to SSH public key file"
  type        = string
  default     = "~/.ssh/id_rsa.pub"
}

# Template Configuration
variable "templates" {
  description = "VM template names"
  type = object({
    ubuntu  = string
    windows = string
  })
  default = {
    ubuntu  = "ubuntu-22.04-template"
    windows = "windows-10-template"
  }
}

# Component Configuration
variable "components" {
  description = "Component configuration"
  type = object({
    splunk = object({
      enabled   = bool
      cores     = number
      memory    = number
      disk_size = string
    })
    security_onion = object({
      enabled            = bool
      manager_cores      = number
      manager_memory     = number
      manager_disk_size  = string
      sensor_count       = number
      sensor_cores       = number
      sensor_memory      = number
      sensor_disk_size   = string
    })
    velociraptor = object({
      enabled   = bool
      cores     = number
      memory    = number
      disk_size = string
    })
    misp = object({
      enabled   = bool
      cores     = number
      memory    = number
      disk_size = string
    })
    flare_vm = object({
      enabled   = bool
      cores     = number
      memory    = number
      disk_size = string
    })
  })
  default = {
    splunk = {
      enabled   = true
      cores     = 4
      memory    = 8192
      disk_size = "100G"
    }
    security_onion = {
      enabled            = true
      manager_cores      = 8
      manager_memory     = 16384
      manager_disk_size  = "200G"
      sensor_count       = 2
      sensor_cores       = 4
      sensor_memory      = 8192
      sensor_disk_size   = "100G"
    }
    velociraptor = {
      enabled   = true
      cores     = 2
      memory    = 4096
      disk_size = "50G"
    }
    misp = {
      enabled   = true
      cores     = 2
      memory    = 4096
      disk_size = "50G"
    }
    flare_vm = {
      enabled   = false
      cores     = 4
      memory    = 8192
      disk_size = "100G"
    }
  }
}

# Resource Limits
variable "resource_limits" {
  description = "Resource limits and quotas"
  type = object({
    max_cpu_cores    = number
    max_memory_mb    = number
    max_storage_gb   = number
    max_vms          = number
  })
  default = {
    max_cpu_cores  = 32
    max_memory_mb  = 65536
    max_storage_gb = 1000
    max_vms        = 10
  }
}

# Backup Configuration
variable "backup_config" {
  description = "Backup configuration"
  type = object({
    enabled           = bool
    schedule          = string
    retention_days    = number
    storage_pool      = string
    compression       = string
  })
  default = {
    enabled        = true
    schedule       = "daily"
    retention_days = 30
    storage_pool   = "backup-storage"
    compression    = "lzo"
  }
}

# Monitoring Configuration
variable "monitoring_config" {
  description = "Monitoring and alerting configuration"
  type = object({
    enabled              = bool
    prometheus_enabled   = bool
    grafana_enabled      = bool
    alert_email          = string
    slack_webhook        = string
  })
  default = {
    enabled            = true
    prometheus_enabled = true
    grafana_enabled    = true
    alert_email        = ""
    slack_webhook      = ""
  }
}

# Security Configuration
variable "security_config" {
  description = "Security configuration"
  type = object({
    firewall_enabled     = bool
    ssh_key_only         = bool
    disable_root_login   = bool
    fail2ban_enabled     = bool
    automatic_updates    = bool
  })
  default = {
    firewall_enabled   = true
    ssh_key_only       = true
    disable_root_login = true
    fail2ban_enabled   = true
    automatic_updates  = true
  }
}

# GOAD Integration
variable "goad_integration" {
  description = "GOAD integration configuration"
  type = object({
    enabled           = bool
    goad_network      = string
    auto_discover     = bool
    agent_deployment  = bool
  })
  default = {
    enabled          = true
    goad_network     = "************/24"
    auto_discover    = true
    agent_deployment = true
  }
}

# Development/Testing Options
variable "development_mode" {
  description = "Enable development mode with reduced resources"
  type        = bool
  default     = false
}

variable "skip_provisioning" {
  description = "Skip Ansible provisioning (for testing)"
  type        = bool
  default     = false
}

variable "enable_console_access" {
  description = "Enable VNC console access"
  type        = bool
  default     = true
}

# Tags
variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default = {
    Project     = "GOAD-Blue"
    Environment = "lab"
    ManagedBy   = "Terraform"
    Owner       = "Security-Team"
  }
}
