# Training Scenarios

GOAD-<PERSON> provides comprehensive training scenarios designed to develop blue team skills through hands-on practice with real attack techniques and defensive tools.

## 🎯 Training Philosophy

### **Learn by Doing**
- **Practical Experience**: Hands-on exercises with real tools
- **Realistic Scenarios**: Based on actual attack techniques
- **Progressive Difficulty**: From beginner to advanced levels
- **Immediate Feedback**: Real-time detection and analysis

### **Comprehensive Coverage**
- **Detection**: Identifying threats and anomalies
- **Investigation**: Analyzing and understanding attacks
- **Response**: Containing and mitigating threats
- **Prevention**: Implementing defensive measures

## 📚 Training Curriculum

```mermaid
graph TB
    subgraph "🎓 Beginner Level"
        B1[📊 SIEM Basics<br/>Log Analysis & Search]
        B2[🔍 Alert Triage<br/>Understanding Alerts]
        B3[📈 Dashboard Usage<br/>Monitoring & Metrics]
        B4[📋 Basic Investigation<br/>Timeline Analysis]
    end
    
    subgraph "🎯 Intermediate Level"
        I1[🔗 Event Correlation<br/>Multi-source Analysis]
        I2[🎯 Threat Hunting<br/>Proactive Detection]
        I3[🌐 Network Analysis<br/>Traffic Investigation]
        I4[💻 Endpoint Forensics<br/>Host-based Analysis]
    end
    
    subgraph "🚀 Advanced Level"
        A1[🧠 Threat Intelligence<br/>IOC Development]
        A2[🔬 Malware Analysis<br/>Sample Investigation]
        A3[⚡ Incident Response<br/>Full IR Lifecycle]
        A4[🛡️ Defense Engineering<br/>Custom Detection]
    end
    
    B1 --> B2 --> B3 --> B4
    B4 --> I1 --> I2 --> I3 --> I4
    I4 --> A1 --> A2 --> A3 --> A4
    
    classDef beginner fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef intermediate fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef advanced fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class B1,B2,B3,B4 beginner
    class I1,I2,I3,I4 intermediate
    class A1,A2,A3,A4 advanced
```

## 🎮 Scenario Categories

### **🔍 Detection Scenarios**
Focus on identifying threats and understanding attack patterns:

- **Kerberoasting Detection** - Service ticket abuse
- **Golden Ticket Attacks** - Forged Kerberos tickets
- **Lateral Movement** - Network propagation techniques
- **Privilege Escalation** - Administrative access abuse
- **Data Exfiltration** - Unauthorized data access

### **📊 Investigation Scenarios**
Develop analytical skills for incident investigation:

- **Timeline Reconstruction** - Event sequencing and correlation
- **Attribution Analysis** - Identifying attack sources
- **Impact Assessment** - Determining scope and damage
- **Evidence Collection** - Gathering forensic artifacts
- **Root Cause Analysis** - Understanding attack vectors

### **🛡️ Response Scenarios**
Practice incident response and containment:

- **Containment Strategies** - Isolating threats
- **Eradication Techniques** - Removing threats
- **Recovery Procedures** - Restoring operations
- **Lessons Learned** - Improving defenses
- **Communication** - Stakeholder updates

### **🎯 Hunting Scenarios**
Proactive threat hunting and analysis:

- **Hypothesis Development** - Creating hunt theories
- **Data Mining** - Finding hidden threats
- **Behavioral Analysis** - Identifying anomalies
- **IOC Development** - Creating detection signatures
- **Threat Modeling** - Understanding adversaries

## 📋 Scenario Structure

### **Standard Scenario Format**

Each training scenario includes:

1. **📖 Background** - Context and learning objectives
2. **🎯 Attack Simulation** - Automated red team activity
3. **🔍 Detection Challenge** - Find and analyze the attack
4. **📊 Investigation Tasks** - Deep dive analysis
5. **🛡️ Response Actions** - Containment and mitigation
6. **📚 Lessons Learned** - Key takeaways and improvements

### **Scenario Difficulty Levels**

#### **🟢 Beginner (Level 1-3)**
- **Clear Indicators** - Obvious signs of compromise
- **Single Attack Vector** - One primary technique
- **Guided Analysis** - Step-by-step instructions
- **Basic Tools** - Standard SIEM searches

#### **🟡 Intermediate (Level 4-6)**
- **Subtle Indicators** - Requires deeper analysis
- **Multiple Techniques** - Combined attack methods
- **Independent Analysis** - Minimal guidance
- **Advanced Tools** - Custom queries and scripts

#### **🔴 Advanced (Level 7-10)**
- **Hidden Threats** - Sophisticated evasion
- **Complex Campaigns** - Multi-stage attacks
- **Expert Analysis** - No guidance provided
- **Custom Development** - Create new detection methods

## 🎯 Featured Training Scenarios

### **Scenario 1: Kerberoasting Detection (Beginner)**

**📖 Background:**
Learn to detect Kerberoasting attacks where adversaries request service tickets to crack offline.

**🎯 Attack Simulation:**
```bash
# Automated attack execution
python3 goad-blue.py simulate_attack kerberoasting \
  --target-services "MSSQL,HTTP" \
  --duration 300 \
  --stealth-level low
```

**🔍 Detection Challenge:**
- Find Event ID 4769 with RC4 encryption
- Identify unusual service ticket requests
- Correlate with user behavior

**📊 Investigation Tasks:**
- Timeline the attack progression
- Identify compromised accounts
- Assess potential impact

**🛡️ Response Actions:**
- Reset affected service account passwords
- Implement strong service account policies
- Monitor for continued activity

### **Scenario 2: Lateral Movement Hunt (Intermediate)**

**📖 Background:**
Detect adversaries moving laterally through the network using various techniques.

**🎯 Attack Simulation:**
```bash
# Multi-stage lateral movement
python3 goad-blue.py simulate_attack lateral_movement \
  --techniques "psexec,wmi,rdp" \
  --targets "multiple" \
  --duration 600
```

**🔍 Detection Challenge:**
- Identify authentication anomalies
- Find process creation patterns
- Detect network connections

**📊 Investigation Tasks:**
- Map the attack path
- Identify patient zero
- Determine persistence mechanisms

### **Scenario 3: Advanced Persistent Threat (Advanced)**

**📖 Background:**
Investigate a sophisticated multi-month campaign with custom malware and living-off-the-land techniques.

**🎯 Attack Simulation:**
```bash
# Complex APT simulation
python3 goad-blue.py simulate_attack apt_campaign \
  --duration 2592000 \  # 30 days
  --techniques "spearphishing,watering_hole,supply_chain" \
  --persistence "registry,service,scheduled_task" \
  --stealth-level high
```

**🔍 Detection Challenge:**
- Find subtle behavioral anomalies
- Identify command and control traffic
- Detect data staging activities

## 🛠️ Training Tools and Resources

### **Scenario Management**

```bash
# List available scenarios
python3 goad-blue.py list_scenarios

# Start a specific scenario
python3 goad-blue.py start_scenario --name kerberoasting_detection

# Check scenario progress
python3 goad-blue.py scenario_status

# Get hints for current scenario
python3 goad-blue.py scenario_hint

# Complete scenario and get results
python3 goad-blue.py complete_scenario
```

### **Attack Simulation Engine**

```bash
# Manual attack simulation
python3 goad-blue.py simulate_attack \
  --technique kerberoasting \
  --target "GOAD-DC01" \
  --duration 300 \
  --stealth-level medium

# Automated scenario execution
python3 goad-blue.py auto_scenario \
  --scenario lateral_movement \
  --participants 5 \
  --competitive-mode
```

### **Training Dashboard**

Access the training dashboard at: `https://goad-blue-siem:8000/app/goad_blue_training`

**Features:**
- **Scenario Progress** - Track completion status
- **Performance Metrics** - Detection accuracy and speed
- **Leaderboards** - Competitive training rankings
- **Skill Assessment** - Competency evaluation
- **Certification Tracking** - Training completion certificates

## 📊 Assessment and Certification

### **Skill Assessment Framework**

#### **Detection Skills**
- **Alert Triage** - Correctly classify alerts (90% accuracy)
- **Threat Identification** - Identify attack techniques (85% accuracy)
- **Timeline Analysis** - Reconstruct attack sequences (80% accuracy)

#### **Investigation Skills**
- **Evidence Collection** - Gather relevant artifacts (95% completeness)
- **Root Cause Analysis** - Identify attack vectors (90% accuracy)
- **Impact Assessment** - Determine scope and damage (85% accuracy)

#### **Response Skills**
- **Containment** - Properly isolate threats (100% success rate)
- **Eradication** - Remove threats completely (95% success rate)
- **Recovery** - Restore normal operations (90% success rate)

### **Certification Levels**

#### **🥉 GOAD-Blue Associate**
- Complete 10 beginner scenarios
- Pass written assessment (80% score)
- Demonstrate basic SIEM skills

#### **🥈 GOAD-Blue Professional**
- Complete 15 intermediate scenarios
- Pass practical assessment (85% score)
- Demonstrate threat hunting skills

#### **🥇 GOAD-Blue Expert**
- Complete 10 advanced scenarios
- Pass expert assessment (90% score)
- Demonstrate custom detection development

## 🎓 Learning Paths

### **SOC Analyst Path**
1. **SIEM Fundamentals** (Scenarios 1-5)
2. **Alert Investigation** (Scenarios 6-10)
3. **Incident Response** (Scenarios 11-15)
4. **Threat Hunting** (Scenarios 16-20)

### **Incident Responder Path**
1. **Forensic Fundamentals** (Scenarios 1-3, 8-10)
2. **Malware Analysis** (Scenarios 21-25)
3. **Network Forensics** (Scenarios 26-30)
4. **Advanced Investigations** (Scenarios 31-35)

### **Threat Hunter Path**
1. **Hunting Fundamentals** (Scenarios 5-7, 16-18)
2. **Behavioral Analysis** (Scenarios 36-40)
3. **Custom Detection** (Scenarios 41-45)
4. **Threat Intelligence** (Scenarios 46-50)

---

!!! info "Training Resources"
    For specific training scenarios and detailed guides, see:
    
    - [Beginner Scenarios](scenarios/beginner/)
    - [Intermediate Scenarios](scenarios/intermediate/)
    - [Advanced Scenarios](scenarios/advanced/)
    - [Custom Scenario Development](development/)

!!! tip "Start with Basics"
    Even experienced professionals should start with beginner scenarios to understand the GOAD-Blue environment and tools before progressing to advanced scenarios.

!!! success "Continuous Learning"
    New scenarios are added regularly. Check the training dashboard for updates and participate in community challenges to enhance your skills.
