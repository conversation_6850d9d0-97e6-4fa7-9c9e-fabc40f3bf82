# Security Onion

Security Onion is a comprehensive network security monitoring platform that integrates multiple open-source tools for threat detection, analysis, and incident response. In GOAD-Blue, Security Onion serves as the primary network monitoring and analysis platform.

## 🎯 Overview

Security Onion provides a complete network security monitoring solution with integrated tools for intrusion detection, network analysis, and security orchestration.

```mermaid
graph TB
    subgraph "🧅 Security Onion Architecture"
        MANAGER[⚙️ Manager Node<br/>Central Management<br/>Web Interface]
        SEARCH[🔍 Search Nodes<br/>Elasticsearch<br/>Kibana Analytics]
        SENSOR[📡 Sensor Nodes<br/>Traffic Analysis<br/>Detection Engines]
        FLEET[🚢 Fleet Server<br/>Agent Management<br/>Configuration]
    end
    
    subgraph "🔍 Detection Engines"
        SURICATA[🦅 Suricata<br/>IDS/IPS Engine<br/>Signature Detection]
        ZEEK[🐝 Zeek<br/>Network Analysis<br/>Protocol Parsing]
        STENO[📼 Stenographer<br/>Full Packet Capture<br/>PCAP Storage]
        WAZUH[🛡️ Wazuh<br/>Host-based Detection<br/>Log Analysis]
    end
    
    subgraph "📊 Analysis & Visualization"
        KIBANA[📈 Kibana<br/>Security Dashboards<br/>Data Visualization]
        ELASTIC[🔍 Elasticsearch<br/>Log Storage<br/>Search Engine]
        PLAYBOOK[📋 Playbook<br/>SOAR Platform<br/>Automated Response]
        NAVIGATOR[🗺️ ATT&CK Navigator<br/>Threat Mapping<br/>Technique Tracking]
    end
    
    subgraph "🎮 GOAD Network"
        GOAD_TRAFFIC[🏰 GOAD Traffic<br/>Domain Communications<br/>Attack Simulations]
        MIRROR_PORT[📡 Mirror Port<br/>Traffic Mirroring<br/>Passive Collection]
    end
    
    GOAD_TRAFFIC --> MIRROR_PORT
    MIRROR_PORT --> SENSOR
    
    SENSOR --> SURICATA
    SENSOR --> ZEEK
    SENSOR --> STENO
    SENSOR --> WAZUH
    
    MANAGER --> SEARCH
    MANAGER --> FLEET
    
    SURICATA --> ELASTIC
    ZEEK --> ELASTIC
    WAZUH --> ELASTIC
    
    ELASTIC --> KIBANA
    ELASTIC --> PLAYBOOK
    ELASTIC --> NAVIGATOR
    
    SEARCH --> KIBANA
    
    classDef so fill:#ff6b35,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef detection fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef analysis fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef goad fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class MANAGER,SEARCH,SENSOR,FLEET so
    class SURICATA,ZEEK,STENO,WAZUH detection
    class KIBANA,ELASTIC,PLAYBOOK,NAVIGATOR analysis
    class GOAD_TRAFFIC,MIRROR_PORT goad
```

## 🚀 Installation and Setup

### **Automated Installation**

```bash
# Install Security Onion using GOAD-Blue automation
python3 goad-blue.py install --component security-onion --deployment-type standalone

# Configure Security Onion for GOAD integration
python3 goad-blue.py configure --component security-onion --enable-goad-monitoring

# Start Security Onion services
python3 goad-blue.py start --component security-onion
```

### **Manual Installation**

```bash
# Download Security Onion ISO
wget https://download.securityonion.net/file/securityonion/securityonion-2.4.60-20231201.iso

# Verify ISO integrity
wget https://github.com/Security-Onion-Solutions/securityonion/raw/2.4/VERIFY_ISO.sh
bash VERIFY_ISO.sh securityonion-2.4.60-20231201.iso

# Create bootable USB or mount ISO for installation
# Follow the interactive installation process

# Post-installation setup
sudo so-setup
```

### **Configuration Files**

```yaml
# /opt/so/saltstack/local/pillar/global.sls
global:
  # Network configuration
  interface:
    management: ens160
    monitor: ens192
    
  # Node configuration
  node_type: standalone
  
  # GOAD-Blue specific settings
  goad_integration:
    enabled: true
    goad_network: "************/24"
    monitor_interfaces:
      - ens192
      - ens224
      
  # Elasticsearch configuration
  elasticsearch:
    heap_size: "4g"
    cluster_name: "goad-blue-cluster"
    
  # Kibana configuration
  kibana:
    defaultappid: "securitySolution:overview"
    
  # Suricata configuration
  suricata:
    ruleset: "ETOPEN"
    custom_rules: true
    
  # Zeek configuration
  zeek:
    custom_scripts: true
    intel_feeds: true
```

## 🔧 Component Configuration

### **Suricata IDS/IPS Setup**

```yaml
# /opt/so/conf/suricata/suricata.yaml
# GOAD-Blue specific Suricata configuration

# Network configuration
af-packet:
  - interface: ens192
    threads: 4
    cluster-id: 99
    cluster-type: cluster_flow
    defrag: yes
    use-mmap: yes
    tpacket-v3: yes

# Detection settings
detect:
  profile: high
  custom-values:
    toclient-groups: 3
    toserver-groups: 25

# Rule configuration
rule-files:
  - suricata.rules
  - goad-blue-custom.rules
  - emerging-threats.rules

# Output configuration
outputs:
  - eve-log:
      enabled: yes
      filetype: regular
      filename: eve.json
      types:
        - alert:
            payload: yes
            payload-buffer-size: 4kb
            payload-printable: yes
            packet: yes
            metadata: yes
        - http:
            extended: yes
        - dns:
            query: yes
            answer: yes
        - tls:
            extended: yes
        - files:
            force-magic: no
        - smtp:
        - ssh:
        - flow:
```

### **Zeek Network Analysis**

```bash
# /opt/zeek/share/zeek/site/local.zeek
# GOAD-Blue specific Zeek configuration

# Load additional scripts for GOAD monitoring
@load policy/protocols/smb
@load policy/protocols/rdp
@load policy/protocols/ssl/validate-certs
@load policy/frameworks/intel/seen
@load policy/frameworks/intel/do_notice

# Custom GOAD-Blue scripts
@load ./goad-blue-detection.zeek
@load ./lateral-movement-detection.zeek
@load ./credential-harvesting.zeek

# Network configuration
redef ignore_checksums = T;
redef LogAscii::use_json = T;

# Intel framework configuration
redef Intel::read_files += {
    "/opt/so/conf/intel/misp-feed.txt",
    "/opt/so/conf/intel/custom-iocs.txt"
};

# Custom detection logic
event smb2_tree_connect_request(c: connection, hdr: SMB2::Header, path: string) {
    if ( /\\\\.*\\ADMIN\$/ in path || /\\\\.*\\C\$/ in path ) {
        NOTICE([$note=SMB::Admin_Share_Access,
                $conn=c,
                $msg=fmt("Administrative share access: %s", path)]);
    }
}

event rdp_connect_request(c: connection) {
    NOTICE([$note=RDP::Connection_Attempt,
            $conn=c,
            $msg="RDP connection attempt detected"]);
}
```

### **Custom Detection Rules**

```bash
# /opt/so/rules/goad-blue-custom.rules
# Custom Suricata rules for GOAD-Blue

# Lateral movement detection
alert tcp any any -> $HOME_NET 445 (msg:"GOAD-Blue SMB Admin Share Access"; flow:to_server,established; content:"|00|ADMIN$|00|"; nocase; classtype:lateral-movement; sid:1000001; rev:1;)

alert tcp any any -> $HOME_NET 3389 (msg:"GOAD-Blue RDP Brute Force"; flow:to_server,established; threshold:type both, track by_src, count 10, seconds 60; classtype:brute-force; sid:1000002; rev:1;)

# Credential harvesting
alert tcp any any -> any any (msg:"GOAD-Blue Mimikatz Sekurlsa"; flow:established; content:"sekurlsa"; nocase; classtype:credential-theft; sid:1000003; rev:1;)

alert tcp any any -> any any (msg:"GOAD-Blue DCSync Attack"; flow:established; content:"lsadump::dcsync"; nocase; classtype:credential-theft; sid:1000004; rev:1;)

# Persistence mechanisms
alert tcp any any -> $HOME_NET any (msg:"GOAD-Blue Scheduled Task Creation"; flow:to_server,established; content:"schtasks"; nocase; content:"/create"; nocase; classtype:persistence; sid:1000005; rev:1;)

# Data exfiltration
alert tcp $HOME_NET any -> !$HOME_NET any (msg:"GOAD-Blue Large Data Transfer"; flow:to_server; dsize:>1000000; threshold:type threshold, track by_src, count 1, seconds 60; classtype:data-exfiltration; sid:1000006; rev:1;)

# DNS tunneling
alert udp any any -> any 53 (msg:"GOAD-Blue DNS Tunneling"; flow:to_server; content:"|01 00 00 01 00 00 00 00 00 00|"; offset:2; depth:10; dsize:>100; classtype:covert-channel; sid:1000007; rev:1;)
```

## 📊 Security Onion Dashboards

### **GOAD-Blue Overview Dashboard**

```json
{
  "dashboard": {
    "id": "goad-blue-overview",
    "title": "GOAD-Blue Security Overview",
    "type": "dashboard",
    "description": "Comprehensive security monitoring for GOAD environment",
    "panelsJSON": "[{\"version\":\"8.5.0\",\"gridData\":{\"x\":0,\"y\":0,\"w\":24,\"h\":15},\"panelIndex\":\"1\",\"embeddableConfig\":{\"attributes\":{\"title\":\"Alert Timeline\",\"type\":\"lens\",\"visualizationType\":\"lnsXY\"}}}]",
    "timeRestore": true,
    "timeTo": "now",
    "timeFrom": "now-24h",
    "refreshInterval": {
      "pause": false,
      "value": 30000
    }
  }
}
```

### **Network Traffic Analysis**

```bash
# Kibana Dev Tools queries for GOAD-Blue analysis

# Top talkers in GOAD network
GET /logstash-*/_search
{
  "query": {
    "bool": {
      "must": [
        {"range": {"@timestamp": {"gte": "now-1h"}}},
        {"term": {"event.dataset": "zeek.conn"}}
      ]
    }
  },
  "aggs": {
    "top_sources": {
      "terms": {
        "field": "source.ip",
        "size": 10
      },
      "aggs": {
        "total_bytes": {
          "sum": {
            "field": "source.bytes"
          }
        }
      }
    }
  }
}

# SMB activity monitoring
GET /logstash-*/_search
{
  "query": {
    "bool": {
      "must": [
        {"range": {"@timestamp": {"gte": "now-24h"}}},
        {"term": {"event.dataset": "zeek.smb_files"}}
      ]
    }
  },
  "aggs": {
    "smb_activity": {
      "terms": {
        "field": "zeek.smb_files.path",
        "size": 20
      }
    }
  }
}

# Authentication failures
GET /logstash-*/_search
{
  "query": {
    "bool": {
      "must": [
        {"range": {"@timestamp": {"gte": "now-1h"}}},
        {"term": {"event.dataset": "suricata.eve"}},
        {"term": {"suricata.eve.event_type": "alert"}},
        {"wildcard": {"suricata.eve.alert.signature": "*authentication*failed*"}}
      ]
    }
  }
}
```

## 🚨 Alert Management

### **Alert Correlation Rules**

```yaml
# /opt/so/conf/elastalert/rules/goad-blue-correlation.yml
name: GOAD-Blue Lateral Movement Detection
type: frequency
index: logstash-*
num_events: 5
timeframe:
  minutes: 10

filter:
- terms:
    event.dataset: ["suricata.eve", "zeek.conn"]
- bool:
    should:
    - term:
        destination.port: 445
    - term:
        destination.port: 3389
    - term:
        destination.port: 139

alert:
- "email"
- "slack"

email:
- "<EMAIL>"

slack:
webhook_url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
slack_channel_override: "#security-alerts"

alert_text: |
  Potential lateral movement detected in GOAD environment
  
  Time: {0}
  Source IP: {1}
  Destination IPs: {2}
  Protocols: {3}
  
  This may indicate an active attack in progress.

alert_text_args:
  - "@timestamp"
  - "source.ip"
  - "destination.ip"
  - "destination.port"
```

### **Automated Response Playbooks**

```python
# /opt/so/conf/playbook/goad-blue-response.py
import requests
import json
from datetime import datetime

class GOADBluePlaybook:
    def __init__(self):
        self.velociraptor_url = "https://velociraptor.goad-blue.local"
        self.misp_url = "https://misp.goad-blue.local"
        self.api_key = "YOUR_API_KEY"
        
    def isolate_host(self, hostname):
        """Isolate compromised host using Velociraptor"""
        hunt_config = {
            "artifacts": ["Windows.Remediation.Quarantine"],
            "condition": f"label(client_id) =~ '{hostname}'"
        }
        
        response = requests.post(
            f"{self.velociraptor_url}/api/v1/hunts",
            headers={"Authorization": f"Bearer {self.api_key}"},
            json=hunt_config
        )
        
        return response.status_code == 200
        
    def create_misp_event(self, alert_data):
        """Create MISP event from Security Onion alert"""
        event = {
            "info": f"GOAD-Blue Alert: {alert_data.get('signature')}",
            "threat_level_id": "2",  # Medium
            "analysis": "1",  # Ongoing
            "distribution": "1",  # This community only
            "Attribute": [
                {
                    "type": "ip-src",
                    "value": alert_data.get('src_ip'),
                    "category": "Network activity"
                },
                {
                    "type": "ip-dst", 
                    "value": alert_data.get('dest_ip'),
                    "category": "Network activity"
                }
            ]
        }
        
        response = requests.post(
            f"{self.misp_url}/events/add",
            headers={"Authorization": self.api_key},
            json=event
        )
        
        return response.json()
        
    def block_ip_at_firewall(self, ip_address):
        """Block malicious IP at network firewall"""
        # Implementation depends on firewall API
        pass
        
    def execute_response(self, alert_type, alert_data):
        """Execute appropriate response based on alert type"""
        if alert_type == "lateral_movement":
            # Isolate source host
            self.isolate_host(alert_data.get('src_hostname'))
            
            # Create MISP event
            self.create_misp_event(alert_data)
            
            # Block source IP
            self.block_ip_at_firewall(alert_data.get('src_ip'))
            
        elif alert_type == "data_exfiltration":
            # Block external communication
            self.block_ip_at_firewall(alert_data.get('dest_ip'))
            
            # Collect additional forensics
            self.collect_forensics(alert_data.get('src_hostname'))
```

## 🔍 Investigation Workflows

### **Incident Investigation Process**

```bash
# Security Onion investigation commands

# 1. Initial alert triage
so-elastic-query "suricata.eve.alert.signature:*lateral*movement* AND @timestamp:[now-1h TO now]"

# 2. Pivot to related events
so-elastic-query "source.ip:************* AND @timestamp:[2023-01-01T10:00:00 TO 2023-01-01T11:00:00]"

# 3. Extract PCAP for detailed analysis
so-pcap-extract --start="2023-01-01 10:30:00" --end="2023-01-01 10:35:00" --src-ip=************* --output=investigation.pcap

# 4. Analyze extracted PCAP
tshark -r investigation.pcap -Y "smb2" -T fields -e ip.src -e ip.dst -e smb2.cmd -e smb2.filename

# 5. Check for IOCs in MISP
curl -H "Authorization: YOUR_API_KEY" "https://misp.goad-blue.local/attributes/restSearch" -d '{"value":"*************"}'
```

### **Threat Hunting Queries**

```bash
# Hunt for credential dumping
so-elastic-query "zeek.smb_files.path:*\\\\*\\\\ADMIN$ OR zeek.smb_files.path:*\\\\*\\\\C$"

# Hunt for persistence mechanisms
so-elastic-query "suricata.eve.alert.signature:*scheduled*task* OR suricata.eve.alert.signature:*registry*run*"

# Hunt for data staging
so-elastic-query "zeek.files.filename:*.zip OR zeek.files.filename:*.rar AND source.bytes:>10000000"

# Hunt for DNS tunneling
so-elastic-query "zeek.dns.query.length:>50 AND zeek.dns.qtype_name:TXT"

# Hunt for unusual network protocols
so-elastic-query "NOT (destination.port:80 OR destination.port:443 OR destination.port:53 OR destination.port:445 OR destination.port:3389) AND network.transport:tcp"
```

## 📈 Performance Monitoring

### **System Health Monitoring**

```bash
# Security Onion status check
sudo so-status

# Individual component status
sudo so-elasticsearch-status
sudo so-kibana-status
sudo so-suricata-status
sudo so-zeek-status

# Performance metrics
sudo so-elastic-query-performance
sudo so-sensor-performance

# Storage utilization
df -h /nsm
du -sh /nsm/elasticsearch/
du -sh /nsm/pcap/
```

### **Optimization Commands**

```bash
# Elasticsearch optimization
sudo so-elasticsearch-optimize

# Index management
sudo so-elasticsearch-index-policy-update

# PCAP cleanup
sudo so-pcap-clean --days=30

# Log rotation
sudo so-log-rotate

# Performance tuning
sudo so-tune --sensor-performance
```

## 🎓 Training Scenarios

### **Security Onion Fundamentals**

1. **Platform Navigation**
   - Kibana dashboard usage
   - Alert investigation workflows
   - PCAP analysis techniques

2. **Detection Tuning**
   - Rule customization
   - False positive reduction
   - Performance optimization

3. **Incident Response**
   - Alert triage procedures
   - Evidence collection
   - Timeline reconstruction

### **Advanced Security Operations**

1. **Threat Hunting**
   - Proactive threat identification
   - Custom query development
   - Behavioral analysis

2. **Custom Detection Development**
   - Suricata rule writing
   - Zeek script development
   - Correlation rule creation

3. **Integration and Automation**
   - SOAR platform integration
   - API automation
   - Custom playbook development

---

!!! tip "Security Onion Best Practices"
    - Regularly update detection rules and signatures
    - Monitor sensor performance and tune as needed
    - Implement proper data retention policies
    - Use distributed deployment for high availability
    - Integrate with external threat intelligence feeds

!!! warning "Resource Requirements"
    Security Onion requires significant resources, especially for Elasticsearch. Plan for adequate CPU, memory, and storage based on your network traffic volume.

!!! info "Community Resources"
    - Security Onion documentation and training
    - Community-contributed detection rules
    - Integration guides for third-party tools
    - Regular webinars and training sessions
