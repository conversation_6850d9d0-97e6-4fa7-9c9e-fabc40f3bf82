---
# Load datas
- import_playbook: data.yml
  vars:
    data_path: "../ad/{{domain_name}}/data/"
  tags: 'data'

# set AD datas ==================================================================================================

- name: "Adjust rights configuration"
  hosts: domain
  roles:
    - { role: "settings/adjust_rights", tags: 'adjust_rights'}
    - { role: "settings/user_rights", tags: 'adjust_rights'}
  vars:
    local_groups: "{{lab.hosts[dict_key].local_groups  | default({}) }}"


# doesen't work see : https://github.com/ansible-collections/community.windows/blob/main/plugins/modules/win_domain_group_membership.ps1
# ligne 62 : use the given credentials to distant domain -> wrong
- name: cross domain groups
  hosts: dc
  roles:
  - { role: 'groups_domains', tags: 'groups_domains'}
  vars:
    domain: "{{lab.hosts[dict_key].domain}}"
    domain_server: "{{lab.hosts[dict_key].hostname}}.{{domain}}"
    domain_username: "{{domain}}\\{{admin_user}}"
    domain_password: "{{lab.domains[domain].domain_password}}"
    domain_groups_members: "{{lab.domains[domain].multi_domain_groups_member | default({}) }}"
