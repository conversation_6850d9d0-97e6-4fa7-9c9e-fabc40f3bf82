# Basic Configuration

This guide covers the essential configuration settings needed to get GO<PERSON><PERSON><PERSON> up and running quickly. These are the minimum settings required for a functional deployment.

## 🎯 Quick Start Configuration

### **Main Configuration File**

The primary configuration file is `goad-blue-config.yml` located in the root directory:

```yaml
# goad-blue-config.yml - Basic Configuration
goad_blue:
  name: "my-goad-blue-lab"
  version: "1.0.0"
  environment: "development"  # development, testing, production
  provider: "vmware"          # vmware, virtualbox, aws, azure, proxmox
  
# Component Selection - Choose which tools to deploy
components:
  # SIEM Platform (choose one)
  splunk:
    enabled: true
    version: "9.1.2"
    license_type: "free"      # free, enterprise
    
  elastic:
    enabled: false
    version: "8.10.0"
    
  # Network Monitoring
  security_onion:
    enabled: true
    version: "2.4.60"
    deployment_type: "standalone"  # standalone, distributed
    
  # Endpoint Visibility
  velociraptor:
    enabled: true
    version: "0.7.0"
    
  # Threat Intelligence
  misp:
    enabled: true
    version: "2.4.170"
    
  # Malware Analysis (optional)
  flare_vm:
    enabled: false
    version: "latest"

# Network Configuration
network:
  base_cidr: "*************/24"
  subnets:
    management: "*************/26"     # .1-.62
    monitoring: "**************/26"    # .65-.126
    production: "*************28/26"   # .129-.190
    analysis: "*************92/26"     # .193-.254
  
  dns:
    primary: "*******"
    secondary: "*******"

# GOAD Integration
integration:
  goad_enabled: true
  auto_discover: true
  goad_path: "../"
  goad_network: "************/24"

# Basic Security Settings
security:
  ssl_enabled: false          # Set to true for production
  change_default_passwords: true
  enable_audit_logging: true
```

## 🔧 Component Configuration

### **SIEM Configuration**

#### **Splunk Basic Setup**
```yaml
splunk:
  enabled: true
  version: "9.1.2"
  license_type: "free"        # 500MB/day limit
  
  # Basic server settings
  server:
    hostname: "goad-blue-splunk"
    web_port: 8000
    management_port: 8089
    
  # Essential indexes
  indexes:
    - name: "goad_blue_windows"
      max_data_size: "100MB"
      retention_days: 30
      
    - name: "goad_blue_network"
      max_data_size: "200MB"
      retention_days: 7
      
  # Basic authentication
  authentication:
    admin_password: "ChangeMePlease123!"
```

#### **Elastic Stack Basic Setup**
```yaml
elastic:
  enabled: false              # Alternative to Splunk
  version: "8.10.0"
  
  elasticsearch:
    cluster_name: "goad-blue-cluster"
    heap_size: "2g"
    
  kibana:
    server_port: 5601
    
  logstash:
    pipeline_workers: 2
```

### **Network Monitoring Configuration**

#### **Security Onion Basic Setup**
```yaml
security_onion:
  enabled: true
  version: "2.4.60"
  deployment_type: "standalone"
  
  # Manager node
  manager:
    hostname: "goad-blue-so-manager"
    ip_address: "**************"
    
  # Basic sensor configuration
  sensors:
    - hostname: "goad-blue-so-sensor1"
      ip_address: "**************"
      interface: "eth1"
      
  # Essential rules
  suricata:
    rules:
      - "emerging-threats"
    rule_update_frequency: "daily"
```

### **Endpoint Monitoring Configuration**

#### **Velociraptor Basic Setup**
```yaml
velociraptor:
  enabled: true
  version: "0.7.0"
  
  # Server configuration
  server:
    hostname: "goad-blue-velociraptor"
    frontend_port: 8000
    gui_port: 8889
    
  # Client settings
  client:
    poll_frequency: 60
    max_poll_frequency: 600
    
  # Basic artifacts
  artifacts:
    - "Windows.Events.EventLogs"
    - "Windows.System.Pslist"
    - "Windows.Network.Netstat"
```

### **Threat Intelligence Configuration**

#### **MISP Basic Setup**
```yaml
misp:
  enabled: true
  version: "2.4.170"
  
  # Server settings
  server:
    hostname: "goad-blue-misp"
    web_port: 443
    
  # Basic authentication
  authentication:
    admin_email: "<EMAIL>"
    admin_password: "ChangeMePlease123!"
    
  # Essential feeds (free sources)
  feeds:
    - name: "CIRCL OSINT Feed"
      url: "https://www.circl.lu/doc/misp/feed-osint/"
      enabled: true
```

## 🌐 Network Configuration

### **Basic Network Setup**

```yaml
network:
  # Main network for GOAD-Blue components
  base_cidr: "*************/24"
  gateway: "*************"
  
  # Subnet allocation
  subnets:
    # Management subnet for admin access
    management:
      cidr: "*************/26"
      gateway: "*************"
      dhcp_range: "*************0-**************"
      
    # Monitoring subnet for security tools
    monitoring:
      cidr: "**************/26"
      gateway: "**************"
      
    # Production subnet for GOAD integration
    production:
      cidr: "*************28/26"
      gateway: "*************29"
      
  # DNS configuration
  dns:
    primary: "*******"
    secondary: "*******"
    search_domains:
      - "goad-blue.local"
      - "sevenkingdoms.local"
```

### **Static IP Assignments**

```yaml
# Static IP assignments for components
static_ips:
  splunk: "*************0"
  elastic: "*************1"
  security_onion_manager: "**************"
  security_onion_sensor1: "**************"
  velociraptor: "**************"
  misp: "**************"
  flare_vm: "***************"
```

## 🔗 GOAD Integration

### **Basic Integration Settings**

```yaml
integration:
  # Enable GOAD integration
  goad_enabled: true
  
  # Auto-discovery settings
  auto_discover: true
  discovery_networks:
    - "************/24"
    - "10.0.0.0/24"
    
  # GOAD environment details
  goad_path: "../"              # Path to GOAD directory
  goad_network: "************/24"
  
  # Agent deployment
  deploy_agents: true
  agents:
    - "sysmon"
    - "winlogbeat"
    - "velociraptor"
    
  # Domain credentials for agent deployment
  domain_admin:
    username: "sevenkingdoms\\administrator"
    password: "Password123!"
```

## 🔒 Basic Security Settings

### **Essential Security Configuration**

```yaml
security:
  # SSL/TLS settings
  ssl_enabled: false            # Set to true for production
  generate_certificates: true
  
  # Authentication
  change_default_passwords: true
  password_policy:
    min_length: 12
    require_special_chars: true
    
  # Logging and auditing
  enable_audit_logging: true
  log_retention_days: 90
  
  # Basic firewall rules
  firewall:
    enabled: true
    default_policy: "deny"
    allow_ssh: true
    allow_web_interfaces: true
```

## 📊 Resource Configuration

### **Basic Resource Allocation**

```yaml
resources:
  # Global resource limits
  cpu_limit: "80%"
  memory_limit: "80%"
  disk_limit: "85%"
  
  # Component-specific resources
  component_resources:
    splunk:
      cpu_cores: 2
      memory_gb: 4
      disk_gb: 50
      
    security_onion:
      cpu_cores: 4
      memory_gb: 8
      disk_gb: 100
      
    velociraptor:
      cpu_cores: 1
      memory_gb: 2
      disk_gb: 20
      
    misp:
      cpu_cores: 1
      memory_gb: 2
      disk_gb: 20
```

## 🚀 Quick Configuration Commands

### **Generate Basic Configuration**

```bash
# Generate basic configuration file
python3 goad-blue.py configure --template basic --output goad-blue-config.yml

# Validate configuration
python3 goad-blue.py validate-config --file goad-blue-config.yml

# Apply configuration
python3 goad-blue.py apply-config --file goad-blue-config.yml
```

### **Interactive Configuration**

```bash
# Interactive configuration wizard
python3 goad-blue.py configure --interactive

# Component-specific configuration
python3 goad-blue.py configure --component splunk --interactive
python3 goad-blue.py configure --component security-onion --interactive
```

### **Configuration Templates**

```bash
# List available templates
python3 goad-blue.py list-templates

# Use specific template
python3 goad-blue.py configure --template homelab
python3 goad-blue.py configure --template enterprise
python3 goad-blue.py configure --template cloud
```

## ✅ Configuration Checklist

Before deploying, ensure you have:

- [ ] **Main configuration file** created and validated
- [ ] **Component selection** appropriate for your environment
- [ ] **Network configuration** that doesn't conflict with existing networks
- [ ] **Resource allocation** sufficient for selected components
- [ ] **GOAD integration** settings configured if using existing GOAD
- [ ] **Security settings** appropriate for your environment
- [ ] **DNS and routing** configured for network connectivity

## 🔧 Next Steps

After completing basic configuration:

1. **[Advanced Configuration](advanced.md)** - Performance tuning and advanced features
2. **[Network Setup](network.md)** - Detailed network configuration
3. **[Security Settings](security.md)** - Enhanced security configuration
4. **[Provider-Specific Configuration](providers/)** - Platform-specific settings

---

!!! tip "Start Simple"
    Begin with the basic configuration and gradually add advanced features as you become more familiar with GOAD-Blue.

!!! warning "Backup Configuration"
    Always backup your configuration files before making changes. Use `python3 goad-blue.py backup-config` to create backups.

!!! info "Configuration Validation"
    Use the built-in validation tools to check your configuration before deployment to avoid common issues.
