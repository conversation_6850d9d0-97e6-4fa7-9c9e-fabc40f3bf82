# Common Issues and Solutions

This page covers the most frequently encountered issues and their solutions.

## 🔧 Installation and Setup Issues

### **Dependency Installation Failures**

**Problem:** Package installation fails with dependency errors.

**Solutions:**
```bash
# Update package managers first
sudo apt update && sudo apt upgrade  # Ubuntu/Debian
sudo yum update                      # CentOS/RHEL

# Install missing system dependencies
sudo apt install python3-dev python3-pip build-essential

# Use virtual environment
python3 -m venv goad-blue-env
source goad-blue-env/bin/activate
pip install -r requirements.txt
```

**Common Dependency Issues:**
- **Python version too old**: Requires Python 3.8+
- **Missing build tools**: Install `build-essential` on Ubuntu
- **Permission errors**: Use `--user` flag or virtual environment

### **Insufficient Resources**

**Problem:** Installation fails due to insufficient system resources.

**Symptoms:**
- Out of memory errors during VM creation
- Disk space errors during image building
- Slow performance or timeouts

**Solutions:**
```bash
# Check available resources
free -h                    # Memory
df -h                     # Disk space
nproc                     # CPU cores

# Adjust deployment for limited resources
python3 goad-blue.py --configure --minimal-deployment
```

**Resource Recommendations:**
- **Minimum**: 32GB RAM, 500GB storage, 8 CPU cores
- **Recommended**: 64GB RAM, 1TB storage, 16 CPU cores
- **Production**: 128GB RAM, 2TB storage, 32 CPU cores

## 🌐 Network Connectivity Issues

### **Cannot Access Web Interfaces**

**Problem:** Unable to access Splunk, Security Onion, or other web interfaces.

**Troubleshooting Steps:**
```bash
# Check if services are running
python3 goad-blue.py -t status

# Test network connectivity
ping **************        # SIEM server
telnet ************** 8000 # Splunk web port

# Check firewall rules
sudo ufw status
sudo iptables -L

# Verify port bindings
sudo netstat -tlnp | grep :8000
```

**Common Solutions:**
```bash
# Open firewall ports
sudo ufw allow 8000/tcp    # Splunk
sudo ufw allow 443/tcp     # HTTPS
sudo ufw allow 22/tcp      # SSH

# Restart networking
sudo systemctl restart networking

# Check service status
sudo systemctl status splunk
```

### **GOAD VMs Not Reachable**

**Problem:** Cannot connect to GOAD virtual machines.

**Diagnostic Commands:**
```bash
# Check VM status
python3 goad-blue.py check_goad_connectivity

# Test network paths
traceroute ***************  # GOAD DC
nmap -p 22,3389 ***************

# Verify network configuration
ip route show
ip addr show
```

**Solutions:**
```bash
# Fix routing issues
sudo ip route add *************/24 via *************

# Restart network services
sudo systemctl restart systemd-networkd

# Check virtualization network
# For VMware:
vmrun list
# For VirtualBox:
VBoxManage list runningvms
```

## 📊 SIEM Issues

### **No Data Appearing in SIEM**

**Problem:** Logs are not appearing in Splunk or Elastic.

**Troubleshooting Steps:**
```bash
# Check forwarder status
sudo /opt/splunkforwarder/bin/splunk list forward-server

# Test log generation
logger "Test message from $(hostname)"

# Check agent connectivity
python3 goad-blue.py check_agents

# Verify index configuration
# In Splunk: Settings > Indexes
# Check for goad_blue_* indexes
```

**Common Fixes:**
```bash
# Restart Splunk Universal Forwarder
sudo systemctl restart SplunkForwarder

# Check Splunk configuration
sudo /opt/splunkforwarder/bin/splunk show config

# Verify network connectivity to indexer
telnet ************** 9997

# Check disk space on indexer
df -h /opt/splunk/var/lib/splunk
```

### **High False Positive Rate**

**Problem:** Too many false positive alerts.

**Solutions:**
```spl
# Tune detection rules in Splunk
index=goad_blue_windows EventCode=4625
| stats count by Account_Name, Computer_Name
| where count > 10  # Adjust threshold

# Whitelist known good activity
index=goad_blue_windows EventCode=4624 Account_Name="service_account"
| eval whitelist="true"

# Implement time-based rules
index=goad_blue_windows EventCode=4720
| eval hour=strftime(_time, "%H")
| where hour >= 9 AND hour <= 17  # Business hours only
```

## 🔍 Monitoring Issues

### **Security Onion Not Detecting Traffic**

**Problem:** Security Onion sensors not seeing network traffic.

**Diagnostic Steps:**
```bash
# Check sensor status
sudo so-status

# Verify network interface configuration
sudo so-sensor-status

# Check traffic on monitoring interface
sudo tcpdump -i eth1 -c 10

# Review Suricata logs
sudo tail -f /opt/so/log/suricata/suricata.log
```

**Solutions:**
```bash
# Restart Security Onion services
sudo so-restart

# Reconfigure network monitoring
sudo so-setup

# Check SPAN port configuration
# Ensure traffic is being mirrored to sensor interface

# Verify Suricata rules
sudo suricata-update
sudo systemctl restart suricata
```

### **Velociraptor Agents Not Connecting**

**Problem:** Endpoint agents not communicating with Velociraptor server.

**Troubleshooting:**
```bash
# Check Velociraptor server status
sudo systemctl status velociraptor

# Test agent connectivity
telnet 192.168.100.85 8000

# Check agent logs on endpoints
# Windows: C:\Program Files\Velociraptor\logs\
# Linux: /var/log/velociraptor/

# Verify server configuration
sudo cat /etc/velociraptor/server.config.yaml
```

**Solutions:**
```bash
# Restart Velociraptor server
sudo systemctl restart velociraptor

# Regenerate client configuration
sudo velociraptor config generate

# Check firewall on server
sudo ufw allow 8000/tcp
sudo ufw allow 8889/tcp

# Redeploy agents
python3 goad-blue.py redeploy_agents
```

## 🔗 Integration Issues

### **GOAD Discovery Fails**

**Problem:** GOAD-Blue cannot find existing GOAD installation.

**Solutions:**
```bash
# Manually specify GOAD path
python3 goad-blue.py --goad-path /path/to/goad

# Check GOAD workspace
ls -la /path/to/goad/workspace/

# Verify GOAD is running
cd /path/to/goad
python3 goad.py status

# Force rediscovery
python3 goad-blue.py --rediscover-goad
```

### **Agent Deployment Fails**

**Problem:** Cannot deploy monitoring agents to GOAD VMs.

**Troubleshooting:**
```bash
# Check SSH/WinRM connectivity
ssh user@***************
# or for Windows:
winrm quickconfig

# Verify credentials
python3 goad-blue.py test_credentials

# Check Ansible inventory
cat ansible/inventory/goad_hosts.yml

# Test Ansible connectivity
ansible all -i inventory/ -m ping
```

**Solutions:**
```bash
# Update credentials
python3 goad-blue.py update_credentials

# Regenerate SSH keys
ssh-keygen -t rsa -b 4096

# Fix WinRM configuration
# On Windows VMs:
winrm set winrm/config/service/auth '@{Basic="true"}'
winrm set winrm/config/service '@{AllowUnencrypted="true"}'

# Redeploy agents
ansible-playbook -i inventory/ deploy-agents.yml
```

## ⚡ Performance Issues

### **Slow SIEM Performance**

**Problem:** Splunk or Elastic searches are slow.

**Optimization Steps:**
```bash
# Check resource usage
top
htop
iotop

# Monitor Splunk performance
# In Splunk: Settings > Monitoring Console

# Optimize indexes
# Reduce retention period
# Increase bucket size
# Add more indexers
```

**Splunk Optimization:**
```conf
# indexes.conf
[goad_blue_windows]
maxDataSize = 500MB
maxHotBuckets = 3
maxWarmDBCount = 20
```

### **High CPU Usage**

**Problem:** System experiencing high CPU utilization.

**Investigation:**
```bash
# Identify CPU-intensive processes
top -o %CPU
ps aux --sort=-%cpu | head -10

# Check specific services
systemctl status splunk
systemctl status suricata
systemctl status velociraptor

# Monitor over time
sar -u 1 10
```

**Solutions:**
```bash
# Adjust service priorities
sudo renice -10 $(pgrep splunk)

# Limit resource usage
# Edit service files to add:
# CPUQuota=50%
# MemoryLimit=4G

# Scale horizontally
# Add more indexers/sensors
python3 goad-blue.py scale_out --component siem
```

## 🔒 Security Issues

### **Certificate Errors**

**Problem:** SSL/TLS certificate warnings or errors.

**Solutions:**
```bash
# Generate new certificates
sudo openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365

# Update certificate in Splunk
# Copy to $SPLUNK_HOME/etc/auth/

# Restart services
sudo systemctl restart splunk
sudo systemctl restart nginx
```

### **Authentication Failures**

**Problem:** Cannot log into web interfaces.

**Troubleshooting:**
```bash
# Reset Splunk admin password
sudo /opt/splunk/bin/splunk edit user admin -password newpassword -auth admin:oldpassword

# Check LDAP/AD integration
# Review authentication logs

# Verify user accounts
sudo /opt/splunk/bin/splunk list user
```

## 📋 Known Issues

### **Current Known Issues**

1. **VMware Workstation 17 Compatibility**
   - **Issue**: Some Packer templates fail with VMware Workstation 17
   - **Workaround**: Use VMware Workstation 16 or ESXi
   - **Status**: Fix in development

2. **Windows 11 Agent Issues**
   - **Issue**: Velociraptor agents may fail on Windows 11
   - **Workaround**: Use Windows 10 or Server 2019/2022
   - **Status**: Under investigation

3. **Large PCAP Files**
   - **Issue**: Malcolm may struggle with PCAP files > 10GB
   - **Workaround**: Implement PCAP rotation
   - **Status**: Optimization in progress

### **Workarounds and Temporary Fixes**

```bash
# VMware Workstation 17 workaround
export PACKER_LOG=1
packer build -var 'vmware_version=16' template.json

# Windows 11 agent workaround
# Use compatibility mode
velociraptor.exe --config client.config.yaml --compat-mode

# Large PCAP workaround
# Implement rotation
find /pcap -name "*.pcap" -size +10G -delete
```

---

!!! tip "Still Having Issues?"
    If your problem isn't covered here, check the specific troubleshooting sections:
    
    - [Installation Problems](installation.md)
    - [Component Issues](components.md)
    - [Integration Problems](integration.md)
    - [Performance Issues](performance.md)
    - [Log Analysis](logs.md)

!!! info "Report New Issues"
    Found a new issue? Please report it on our [GitHub Issues](https://github.com/your-org/goad-blue/issues) page to help improve this documentation.
