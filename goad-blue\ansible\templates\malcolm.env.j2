# Malcolm Environment Configuration
# Generated by GOAD-Blue Ansible

# Malcolm version
MALCOLM_VERSION={{ malcolm.version }}

# Network configuration
MALCOLM_HOSTNAME={{ ansible_hostname }}
NGINX_PORT=443
NGINX_SSL_PORT=443

# Elasticsearch configuration
ELASTICSEARCH_URL=http://elasticsearch:9200
ELASTICSEARCH_HEAP_SIZE=2g
ELASTICSEARCH_MEMORY_LOCK=true

# Kibana configuration
KIBANA_URL=http://kibana:5601
KIBANA_ELASTICSEARCH_URL=http://elasticsearch:9200

# Logstash configuration
LOGSTASH_HEAP_SIZE=1g
LOGSTASH_JAVA_OPTS="-Xms1g -Xmx1g"

# Zeek configuration
ZEEK_LIVE_CAPTURE={{ malcolm.zeek.live_capture | default('false') }}
ZEEK_ROTATED_PCAP={{ malcolm.zeek.rotated_pcap | default('true') }}
ZEEK_UPLOAD_PCAP={{ malcolm.zeek.upload_pcap | default('true') }}

# Suricata configuration
SURICATA_LIVE_CAPTURE={{ malcolm.suricata.live_capture | default('false') }}
SURICATA_ROTATED_PCAP={{ malcolm.suricata.rotated_pcap | default('true') }}
SURICATA_UPLOAD_PCAP={{ malcolm.suricata.upload_pcap | default('true') }}

# File extraction
EXTRACTED_FILE_ENABLE={{ malcolm.file_extraction.enabled | default('true') }}
EXTRACTED_FILE_PRESERVATION={{ malcolm.file_extraction.preservation | default('quarantined') }}
EXTRACTED_FILE_HTTP_SERVER_ENABLE={{ malcolm.file_extraction.http_server | default('true') }}

# PCAP processing
PCAP_PIPELINE_VERBOSITY={{ malcolm.pcap.verbosity | default('3') }}
PCAP_MONITOR_HOST={{ malcolm.pcap.monitor_host | default('pcap-monitor') }}

# Auto-analysis
AUTO_ANALYZE_PCAP_FILES={{ malcolm.auto_analyze | default('true') }}
AUTO_ANALYZE_PCAP_THREADS={{ malcolm.analyze_threads | default('1') }}

# Logging
LOG_LEVEL={{ malcolm.log_level | default('info') }}

# Authentication
NGINX_BASIC_AUTH={{ malcolm.auth.basic_auth | default('true') }}
NGINX_LDAP_TLS_STUNNEL={{ malcolm.auth.ldap_tls | default('false') }}

# GOAD-Blue Integration
GOAD_BLUE_INTEGRATION={{ malcolm.goad_integration.enabled | default('true') }}
GOAD_BLUE_SIEM_HOST={{ malcolm.goad_integration.siem_host | default('**************') }}
GOAD_BLUE_SIEM_PORT={{ malcolm.goad_integration.siem_port | default('9997') }}
