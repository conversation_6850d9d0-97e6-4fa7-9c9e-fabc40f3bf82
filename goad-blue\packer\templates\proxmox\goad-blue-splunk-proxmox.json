{"variables": {"proxmox_url": "{{env `PROXMOX_API_URL`}}", "proxmox_username": "{{env `PROXMOX_USER`}}", "proxmox_password": "{{env `PROXMOX_PASSWORD`}}", "proxmox_node": "{{env `PROXMOX_NODE`}}", "vm_name": "goad-blue-splunk-template", "vm_id": "9100", "template_name": "ubuntu-22.04-template", "ssh_username": "ubuntu", "ssh_private_key_file": "~/.ssh/id_rsa"}, "builders": [{"type": "proxmox-clone", "proxmox_url": "{{user `proxmox_url`}}", "username": "{{user `proxmox_username`}}", "password": "{{user `proxmox_password`}}", "node": "{{user `proxmox_node`}}", "insecure_skip_tls_verify": true, "vm_name": "{{user `vm_name`}}", "vm_id": "{{user `vm_id`}}", "clone_vm": "{{user `template_name`}}", "full_clone": true, "cores": 4, "memory": 8192, "os": "l26", "network_adapters": [{"bridge": "vmbr1", "model": "virtio"}], "disks": [{"type": "scsi", "disk_size": "100G", "storage_pool": "local-lvm", "storage_pool_type": "lvm"}], "ssh_username": "{{user `ssh_username`}}", "ssh_private_key_file": "{{user `ssh_private_key_file`}}", "ssh_timeout": "20m", "cloud_init": true, "cloud_init_storage_pool": "local-lvm"}], "provisioners": [{"type": "shell", "inline": ["echo 'Waiting for cloud-init to complete...'", "cloud-init status --wait", "sudo apt-get update", "sudo apt-get upgrade -y"]}, {"type": "shell", "script": "../../scripts/ubuntu/base-setup.sh", "execute_command": "sudo sh -c '{{ .Vars }} {{ .Path }}'"}, {"type": "file", "source": "../../files/splunk/", "destination": "/tmp/splunk-files/"}, {"type": "shell", "script": "../../scripts/ubuntu/splunk-install.sh", "execute_command": "sudo sh -c '{{ .Vars }} {{ .Path }}'"}, {"type": "shell", "script": "../../scripts/ubuntu/splunk-configure.sh", "execute_command": "sudo sh -c '{{ .Vars }} {{ .Path }}'"}, {"type": "file", "source": "../../files/goad-blue/splunk-apps/", "destination": "/tmp/goad-blue-apps/"}, {"type": "shell", "script": "../../scripts/ubuntu/goad-blue-splunk-setup.sh", "execute_command": "sudo sh -c '{{ .Vars }} {{ .Path }}'"}, {"type": "shell", "inline": ["echo 'Cleaning up for template creation...'", "sudo apt-get autoremove -y", "sudo apt-get autoclean", "sudo rm -rf /tmp/*", "sudo rm -rf /var/tmp/*", "sudo rm -f /var/log/wtmp /var/log/btmp", "sudo truncate -s 0 /var/log/*log", "history -c", "cat /dev/null > ~/.bash_history", "sudo rm -f /root/.bash_history", "sudo rm -f /home/<USER>/.bash_history", "sudo cloud-init clean --logs", "sudo sync"]}], "post-processors": [{"type": "manifest", "output": "manifest.json", "strip_path": true, "custom_data": {"vm_name": "{{user `vm_name`}}", "vm_id": "{{user `vm_id`}}", "proxmox_node": "{{user `proxmox_node`}}", "build_time": "{{timestamp}}", "component": "splunk", "goad_blue_version": "1.0.0"}}]}