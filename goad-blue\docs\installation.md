# GOAD-Blue Installation Guide

## Prerequisites

### System Requirements

**Minimum Requirements:**
- 32GB RAM
- 500GB available storage
- 8 CPU cores
- Virtualization support (Intel VT-x/AMD-V)

**Recommended Requirements:**
- 64GB RAM
- 1TB SSD storage
- 16 CPU cores
- Dedicated network interface for lab traffic

### Software Dependencies

**Required Tools:**
- [Packer](https://www.packer.io/) >= 1.9.0
- [Terraform](https://www.terraform.io/) >= 1.0.0
- [Ansible](https://www.ansible.com/) >= 6.0.0
- [Docker](https://www.docker.com/) >= 20.10.0
- Python 3.8+

**Platform-Specific Requirements:**

#### VMware Workstation/ESXi
- VMware Workstation Pro 16+ or ESXi 7.0+
- VMware vSphere API access (for ESXi)

#### VirtualBox
- VirtualBox 6.1+
- VirtualBox Extension Pack

#### Cloud Providers
- **AWS**: Valid AWS account with appropriate permissions
- **Azure**: Valid Azure subscription with contributor access

### GOAD Integration

GOAD-Blue is designed to integrate with existing GOAD installations:

- **Required**: Working GOAD environment (any lab variant)
- **Recommended**: GOAD v3.0+ for best compatibility
- **Network**: Ensure network connectivity between GOAD and GOAD-Blue subnets

## Installation Methods

### Method 1: Interactive Installer (Recommended)

The interactive installer provides a guided setup experience:

```bash
# Clone or navigate to GOAD-Blue directory
cd goad-blue

# Run interactive installer
./scripts/goad-blue-installer.sh
```

The installer will:
1. Check dependencies
2. Detect existing GOAD installation
3. Guide you through component selection
4. Build VM images with Packer
5. Deploy infrastructure with Terraform
6. Configure systems with Ansible
7. Integrate with GOAD environment

### Method 2: Python CLI

Use the Python CLI for more control:

```bash
# Interactive configuration
python3 goad-blue.py --interactive

# Quick deployment with specific components
python3 goad-blue.py -t install -s splunk -c security_onion,velociraptor,misp

# Custom configuration
python3 goad-blue.py --configure
```

### Method 3: Manual Deployment

For advanced users who want full control:

#### Step 1: Configuration

```bash
# Copy and edit configuration
cp goad-blue-config.yml.example goad-blue-config.yml
vim goad-blue-config.yml
```

#### Step 2: Build Images

```bash
cd packer

# Build Splunk server image
packer build goad-blue-splunk-server.json

# Build Security Onion image
packer build goad-blue-security-onion.json

# Build other component images as needed
```

#### Step 3: Deploy Infrastructure

```bash
cd terraform

# Initialize Terraform
terraform init

# Plan deployment
terraform plan -var-file="../goad-blue-config.yml"

# Apply configuration
terraform apply -var-file="../goad-blue-config.yml"
```

#### Step 4: Configure Systems

```bash
cd ansible

# Deploy all components
ansible-playbook -i inventory/ goad-blue-site.yml

# Or deploy specific components
ansible-playbook -i inventory/ goad-blue-splunk-server.yml
```

#### Step 5: Integrate with GOAD

```bash
# Run integration playbook
ansible-playbook -i inventory/ goad-blue-integration.yml
```

## Configuration Options

### SIEM Selection

Choose between Splunk Enterprise and Elastic Stack:

```yaml
siem:
  type: "splunk"  # or "elastic"
  enabled: true
```

**Splunk Enterprise:**
- Full-featured SIEM with Security Essentials
- Pre-configured apps for Windows, Linux, Suricata
- MISP integration via MISP42 app
- Custom GOAD-Blue dashboards

**Elastic Stack:**
- Open-source ELK stack
- Beats for log collection
- ECS (Elastic Common Schema) mapping
- Kibana dashboards for visualization

### Component Selection

Enable/disable components based on your needs:

```yaml
components:
  security_onion:
    enabled: true      # Network monitoring with Suricata/Zeek
  malcolm:
    enabled: false     # Alternative network forensics platform
  velociraptor:
    enabled: true      # Endpoint visibility and DFIR
  misp:
    enabled: true      # Threat intelligence platform
  flare_vm:
    enabled: true      # Malware analysis environment
```

### Network Configuration

Customize network layout:

```yaml
network:
  base_cidr: "192.168.100.0/24"
  siem_subnet: "192.168.100.0/26"        # SIEM and central services
  monitoring_subnet: "192.168.100.64/26"  # Security tools
  red_team_subnet: "***************/26"   # GOAD environment
  analysis_subnet: "***************/26"   # Malware analysis
```

## Provider-Specific Setup

### VMware Workstation

```yaml
goad_blue:
  provider: "vmware"
```

No additional configuration required for Workstation.

### VMware ESXi

```yaml
goad_blue:
  provider: "vmware"
```

Set environment variables:
```bash
export VSPHERE_USER="<EMAIL>"
export VSPHERE_PASSWORD="your_password"
export VSPHERE_SERVER="your_esxi_host"
```

### AWS

```yaml
goad_blue:
  provider: "aws"
```

Configure AWS credentials:
```bash
aws configure
# or
export AWS_ACCESS_KEY_ID="your_key"
export AWS_SECRET_ACCESS_KEY="your_secret"
export AWS_DEFAULT_REGION="us-east-1"
```

### Azure

```yaml
goad_blue:
  provider: "azure"
```

Login to Azure:
```bash
az login
```

## Post-Installation

### Verification

1. **Check component status:**
   ```bash
   python3 goad-blue.py -t status
   ```

2. **Access web interfaces:**
   - Splunk: https://**************:8000
   - Security Onion: https://**************
   - Velociraptor: https://**************:8889
   - MISP: https://**************

3. **Validate GOAD integration:**
   ```bash
   python3 goad-blue.py --integrate-goad
   ```

### Initial Configuration

1. **Change default passwords** for all components
2. **Configure SSL certificates** for production use
3. **Set up backup procedures** for critical data
4. **Review firewall rules** and network access

### Testing

1. **Run attack simulation:**
   ```bash
   python3 goad-blue.py simulate_attack kerberoasting
   ```

2. **Check detection capabilities:**
   ```bash
   python3 goad-blue.py test_detection
   ```

3. **Generate test report:**
   ```bash
   python3 goad-blue.py generate_report
   ```

## Troubleshooting

### Common Issues

**Packer build failures:**
- Check ISO URLs and checksums
- Verify virtualization platform connectivity
- Ensure sufficient disk space

**Terraform deployment errors:**
- Validate provider credentials
- Check resource quotas and limits
- Review network configuration

**Ansible configuration failures:**
- Verify SSH/WinRM connectivity
- Check inventory file accuracy
- Ensure proper permissions

**GOAD integration issues:**
- Confirm GOAD is running and accessible
- Verify network connectivity between subnets
- Check agent deployment logs

### Log Locations

- **GOAD-Blue logs:** `logs/goad-blue.log`
- **Packer logs:** `packer/logs/`
- **Terraform logs:** `terraform/terraform.log`
- **Ansible logs:** `ansible/logs/`

### Getting Help

1. Check the [troubleshooting guide](troubleshooting.md)
2. Review component-specific documentation
3. Search existing GitHub issues
4. Create a new issue with detailed logs

## Next Steps

After successful installation:

1. Read the [Configuration Reference](configuration.md)
2. Explore [Use Cases & Scenarios](use-cases.md)
3. Review [Component Details](components/)
4. Practice with attack simulations
