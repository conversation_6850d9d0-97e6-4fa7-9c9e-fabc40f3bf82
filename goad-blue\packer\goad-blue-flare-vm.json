{"variables": {"vm_name": "goad-blue-flare-vm", "iso_url": "https://software-download.microsoft.com/download/pr/19041.508.200927-1902.20h2_release_svc_refresh_CLIENTENTERPRISEEVAL_OEMRET_x64FRE_en-us.iso", "iso_checksum": "sha256:placeholder_checksum_here", "winrm_username": "Administrator", "winrm_password": "ChangeMePlease123!"}, "builders": [{"type": "vmware-iso", "vm_name": "{{user `vm_name`}}", "guest_os_type": "windows10-64", "iso_url": "{{user `iso_url`}}", "iso_checksum": "{{user `iso_checksum`}}", "communicator": "winrm", "winrm_username": "{{user `winrm_username`}}", "winrm_password": "{{user `winrm_password`}}", "winrm_timeout": "30m", "disk_size": 100000, "memory": 8192, "cpus": 4, "network_adapter_type": "vmxnet3", "sound": false, "usb": false, "floppy_files": ["../scripts/windows/autounattend.xml", "../scripts/windows/setup-winrm.ps1"]}], "provisioners": [{"type": "powershell", "scripts": ["../scripts/windows/install-chocolatey.ps1", "../scripts/windows/install-flare-vm.ps1"]}, {"type": "windows-restart", "restart_timeout": "15m"}, {"type": "powershell", "scripts": ["../scripts/windows/configure-goad-blue-monitoring.ps1"]}, {"type": "powershell", "inline": ["# Clean up temporary files", "Remove-Item -Path C:\\temp\\* -Recurse -Force -ErrorAction SilentlyContinue", "# Clear event logs", "wevtutil el | Foreach-Object {wevtutil cl \"$_\"}", "# Clear PowerShell history", "Remove-Item (Get-PSReadlineOption).HistorySavePath -ErrorAction SilentlyContinue"]}], "post-processors": [{"type": "manifest", "output": "manifest.json", "strip_path": true}]}