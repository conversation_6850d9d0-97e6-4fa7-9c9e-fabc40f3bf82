@echo off
REM GOAD-Blue Documentation Build Script for Windows

setlocal enabledelayedexpansion

set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."

echo.
echo ====================================================
echo 📚 GOAD-Blue Documentation Builder
echo ====================================================
echo.

REM Parse command line arguments
set "COMMAND=%~1"
if "%COMMAND%"=="" set "COMMAND=serve"

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if pip is available
pip --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] pip is not installed or not in PATH
    echo Please install pip and try again
    pause
    exit /b 1
)

REM Change to project root
cd /d "%PROJECT_ROOT%"

if "%COMMAND%"=="install" goto :install
if "%COMMAND%"=="build" goto :build
if "%COMMAND%"=="serve" goto :serve
if "%COMMAND%"=="deploy" goto :deploy
if "%COMMAND%"=="validate" goto :validate
if "%COMMAND%"=="clean" goto :clean
if "%COMMAND%"=="help" goto :help
if "%COMMAND%"=="-h" goto :help
if "%COMMAND%"=="--help" goto :help

echo [ERROR] Unknown command: %COMMAND%
goto :help

:install
echo [INFO] Installing documentation dependencies...
if exist "requirements-docs-minimal.txt" (
    echo [INFO] Installing minimal requirements first...
    pip install -r requirements-docs-minimal.txt

    if exist "requirements-docs.txt" (
        echo [INFO] Attempting to install full requirements...
        pip install -r requirements-docs.txt
        if errorlevel 1 (
            echo [WARNING] Some optional plugins failed to install, continuing with minimal setup
        ) else (
            echo [SUCCESS] Full documentation requirements installed
        )
    )
) else (
    if exist "requirements-docs.txt" (
        echo [INFO] Installing requirements from requirements-docs.txt...
        pip install -r requirements-docs.txt
        if errorlevel 1 (
            echo [WARNING] Some requirements failed, installing basic requirements
            pip install mkdocs mkdocs-material pymdown-extensions
        ) else (
            echo [SUCCESS] Documentation requirements installed
        )
    ) else (
        echo [WARNING] No requirements file found, installing basic requirements
        pip install mkdocs mkdocs-material pymdown-extensions
    )
)
goto :end

:validate
echo [INFO] Validating MkDocs configuration...
if not exist "mkdocs.yml" (
    echo [ERROR] mkdocs.yml not found in project root
    exit /b 1
)
mkdocs config-check
if errorlevel 1 (
    echo [ERROR] MkDocs configuration validation failed
    exit /b 1
) else (
    echo [SUCCESS] MkDocs configuration is valid
)
goto :end

:build
echo [INFO] Building documentation...
call :validate
if errorlevel 1 exit /b 1

REM Clean previous build
if exist "site" (
    rmdir /s /q "site"
    echo [INFO] Cleaned previous build
)

REM Build documentation
mkdocs build --strict
if errorlevel 1 (
    echo [ERROR] Documentation build failed
    exit /b 1
) else (
    echo [SUCCESS] Documentation built successfully
    echo [INFO] Documentation available in: %PROJECT_ROOT%\site\
)
goto :end

:serve
echo [INFO] Starting documentation server...
call :validate
if errorlevel 1 exit /b 1

echo [SUCCESS] Documentation server starting...
echo [INFO] Access documentation at: http://localhost:8000
echo [INFO] Press Ctrl+C to stop the server
echo.
mkdocs serve --dev-addr=0.0.0.0:8000
goto :end

:deploy
echo [INFO] Deploying documentation to GitHub Pages...
call :validate
if errorlevel 1 exit /b 1

call :build
if errorlevel 1 exit /b 1

REM Check if this is a git repository
if not exist ".git" (
    echo [ERROR] Not a git repository. Cannot deploy to GitHub Pages.
    exit /b 1
)

REM Deploy to GitHub Pages
mkdocs gh-deploy --force
if errorlevel 1 (
    echo [ERROR] GitHub Pages deployment failed
    exit /b 1
) else (
    echo [SUCCESS] Documentation deployed to GitHub Pages
)
goto :end

:clean
echo [INFO] Cleaning build artifacts...

REM Remove build directory
if exist "site" (
    rmdir /s /q "site"
    echo [INFO] Removed site\ directory
)

REM Remove Python cache (Windows equivalent)
for /d /r . %%d in (__pycache__) do @if exist "%%d" rmdir /s /q "%%d"
del /s /q *.pyc >nul 2>&1

echo [SUCCESS] Build artifacts cleaned
goto :end

:help
echo GOAD-Blue Documentation Builder
echo.
echo Usage: %~nx0 [COMMAND]
echo.
echo Commands:
echo   install     Install documentation dependencies
echo   build       Build static documentation
echo   serve       Serve documentation locally (default)
echo   deploy      Deploy to GitHub Pages
echo   validate    Validate MkDocs configuration
echo   clean       Clean build artifacts
echo   help        Show this help message
echo.
echo Examples:
echo   %~nx0                    # Serve documentation locally
echo   %~nx0 build             # Build static documentation
echo   %~nx0 deploy            # Deploy to GitHub Pages
goto :end

:end
if "%COMMAND%"=="serve" pause
endlocal
