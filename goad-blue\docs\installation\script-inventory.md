# GOAD-Blue Installation Script Inventory

Complete inventory of all installation scripts, their dependencies, and usage instructions.

## 📁 Script Directory Structure

```
goad-blue/
├── vagrant/
│   ├── Vagrantfile                    # Main VM configuration
│   ├── config.yml                     # VM definitions and settings
│   └── scripts/
│       ├── common/                    # Shared installation scripts
│       │   ├── update-system.sh       # Base system configuration
│       │   ├── install-docker.sh      # Docker installation
│       │   └── disable-windows-defender.ps1  # Windows security disable
│       ├── splunk/                    # Splunk SIEM scripts
│       │   └── install-splunk.sh      # Splunk Enterprise installation
│       ├── flare-vm/                  # FLARE-VM scripts
│       │   ├── configure-windows.ps1  # Windows analysis configuration
│       │   ├── install-chocolatey.ps1 # Package manager installation
│       │   └── install-flare-vm.ps1   # FLARE-VM deployment
│       ├── velociraptor/              # Velociraptor EDR scripts
│       │   └── install-velociraptor.sh # Velociraptor server setup
│       ├── misp/                      # MISP threat intelligence scripts
│       │   └── install-misp.sh        # MISP platform installation
│       ├── malcolm/                   # Malcolm network analysis scripts
│       │   └── install-malcolm.sh     # Malcolm deployment
│       ├── security-onion/            # Security Onion scripts
│       │   └── install-security-onion.sh # SO platform setup
│       ├── elastic/                   # Elasticsearch scripts
│       │   ├── install-elasticsearch.sh # Elasticsearch installation
│       │   ├── install-kibana.sh      # Kibana installation
│       │   └── install-logstash.sh    # Logstash installation
│       ├── pfsense/                   # pfSense firewall scripts
│       │   └── configure-pfsense.sh   # pfSense configuration
│       └── management/                # Management server scripts
│           ├── install-tools.sh       # Management tools installation
│           └── configure-ansible.sh   # Ansible configuration
├── ansible/                           # Ansible automation
│   ├── goad-blue-site.yml            # Main deployment playbook
│   ├── goad-blue-siem.yml            # SIEM deployment
│   ├── goad-blue-security-onion.yml  # Security Onion deployment
│   ├── goad-blue-malcolm.yml         # Malcolm deployment
│   ├── goad-blue-velociraptor.yml    # Velociraptor deployment
│   ├── goad-blue-misp.yml            # MISP deployment
│   ├── goad-blue-flare-vm.yml        # FLARE-VM deployment
│   ├── tasks/                         # Ansible task files
│   ├── templates/                     # Configuration templates
│   └── vars/                          # Variable definitions
└── scripts/                           # Training and utility scripts
    ├── training/                      # Training scenarios
    ├── utilities/                     # Utility scripts
    └── validation/                    # Validation scripts
```

## 🔧 Common Scripts

### **1. System Update Script**
**File:** `vagrant/scripts/common/update-system.sh`
**Purpose:** Base system configuration for all Linux VMs
**Dependencies:** Ubuntu 22.04 LTS, apt package manager
**Runtime:** ~5-10 minutes

```bash
# Features:
✅ System package updates
✅ Essential tool installation  
✅ User account creation (goad-blue)
✅ Directory structure setup
✅ Logging configuration
✅ Security hardening
✅ SSH configuration
✅ Firewall setup (UFW)
✅ Python environment setup
✅ Log rotation configuration

# Usage:
sudo ./update-system.sh

# Post-execution:
- Creates /opt/goad-blue/ directory structure
- Adds goad-blue user with sudo privileges
- Configures logging to /var/log/goad-blue/
- Sets up basic security policies
```

### **2. Docker Installation Script**
**File:** `vagrant/scripts/common/install-docker.sh`
**Purpose:** Docker Engine and Compose installation
**Dependencies:** curl, apt-transport-https, ca-certificates
**Runtime:** ~10-15 minutes

```bash
# Features:
✅ Docker CE installation
✅ Docker Compose installation
✅ User group configuration
✅ Security settings
✅ Monitoring scripts
✅ Cleanup utilities
✅ Log rotation setup
✅ UFW firewall rules

# Usage:
sudo ./install-docker.sh

# Post-execution:
- Docker service running and enabled
- Users added to docker group
- Monitoring scripts in /opt/goad-blue/scripts/
- Security configurations applied
```

### **3. Windows Defender Disable Script**
**File:** `vagrant/scripts/common/disable-windows-defender.ps1`
**Purpose:** Disable Windows security for malware analysis
**Dependencies:** PowerShell 5.0+, Administrator privileges
**Runtime:** ~2-5 minutes

```powershell
# Features:
✅ Real-time protection disable
✅ Registry modifications
✅ Service disabling
✅ Exclusion creation
✅ UAC disabling
✅ SmartScreen disable
✅ Windows Update disable
✅ Error reporting disable

# Usage:
PowerShell -ExecutionPolicy Bypass -File disable-windows-defender.ps1

# Post-execution:
- Windows Defender completely disabled
- Analysis directories excluded
- System configured for malware analysis
- Reboot recommended for full effect
```

## 🛡️ Security Component Scripts

### **4. Splunk Installation Script**
**File:** `vagrant/scripts/splunk/install-splunk.sh`
**Purpose:** Splunk Enterprise deployment
**Dependencies:** Ubuntu 22.04, 8GB+ RAM, Java 11
**Runtime:** ~20-30 minutes

```bash
# Features:
✅ Splunk Enterprise 9.1.2 installation
✅ User and directory creation
✅ SSL certificate generation
✅ GOAD-Blue index configuration
✅ Input configuration (TCP/UDP/HEC)
✅ Systemd service setup
✅ Firewall configuration
✅ Monitoring scripts

# Usage:
sudo SPLUNK_ADMIN_PASSWORD="YourPassword" ./install-splunk.sh

# Post-execution:
- Splunk Web: https://IP:8000
- Management: https://IP:8089
- Forwarder: tcp://IP:9997
- HEC: https://IP:8088
- Admin credentials: admin/YourPassword
```

### **5. Velociraptor Installation Script**
**File:** `vagrant/scripts/velociraptor/install-velociraptor.sh`
**Purpose:** Velociraptor EDR server setup
**Dependencies:** Ubuntu 22.04, 4GB+ RAM, SSL support
**Runtime:** ~10-15 minutes

```bash
# Features:
✅ Velociraptor 0.7.0 binary installation
✅ SSL certificate generation
✅ Server configuration
✅ Systemd service creation
✅ GOAD-Blue artifacts
✅ Admin user creation
✅ Client deployment scripts
✅ Monitoring utilities

# Usage:
sudo ./install-velociraptor.sh

# Post-execution:
- GUI: https://IP:8889 (admin/admin)
- API: https://IP:8001
- Frontend: https://IP:8000
- Client configs available
```

### **6. Security Onion Installation Script**
**File:** `vagrant/scripts/security-onion/install-security-onion.sh`
**Purpose:** Security Onion platform deployment
**Dependencies:** Ubuntu 22.04, 16GB+ RAM, multiple NICs
**Runtime:** ~60-90 minutes

```bash
# Features:
✅ Security Onion 2.3.240 installation
✅ Network interface configuration
✅ Suricata IDS/IPS setup
✅ Zeek network analysis
✅ Elasticsearch configuration
✅ Kibana dashboard setup
✅ GOAD network monitoring
✅ Alert forwarding to SIEM

# Usage:
sudo ./install-security-onion.sh

# Post-execution:
- Web Interface: https://IP
- Kibana: https://IP/kibana
- Elasticsearch: http://IP:9200
- Network monitoring active
```

## 🔍 Analysis Component Scripts

### **7. FLARE-VM Installation Script**
**File:** `vagrant/scripts/flare-vm/install-flare-vm.ps1`
**Purpose:** FLARE-VM malware analysis environment
**Dependencies:** Windows 10/11, 8GB+ RAM, Internet connectivity
**Runtime:** ~2-4 hours

```powershell
# Features:
✅ FLARE-VM tool suite installation
✅ Analysis environment setup
✅ Python package installation
✅ Desktop shortcuts creation
✅ Analysis user configuration
✅ Directory structure creation
✅ Tool verification
✅ Custom analysis scripts

# Usage:
PowerShell -ExecutionPolicy Bypass -File install-flare-vm.ps1

# Post-execution:
- Analysis User: analyst/analyst
- Tools: C:\Tools\
- Samples: C:\MalwareAnalysis\Samples\
- Scripts: C:\MalwareAnalysis\Scripts\
- 100+ analysis tools installed
```

### **8. Malcolm Installation Script**
**File:** `vagrant/scripts/malcolm/install-malcolm.sh`
**Purpose:** Malcolm network analysis platform
**Dependencies:** Ubuntu 22.04, Docker, 12GB+ RAM
**Runtime:** ~30-45 minutes

```bash
# Features:
✅ Malcolm 6.5.0 deployment
✅ Docker Compose configuration
✅ SSL certificate generation
✅ PCAP processing setup
✅ Zeek and Suricata integration
✅ Kibana dashboard import
✅ File extraction configuration
✅ SIEM integration

# Usage:
sudo ./install-malcolm.sh

# Post-execution:
- Web Interface: https://IP
- Kibana: https://IP/kibana
- Upload: https://IP/upload
- PCAP processing active
```

## 🧠 Threat Intelligence Scripts

### **9. MISP Installation Script**
**File:** `vagrant/scripts/misp/install-misp.sh`
**Purpose:** MISP threat intelligence platform
**Dependencies:** Ubuntu 22.04, PHP 8.1+, MySQL, Apache
**Runtime:** ~45-60 minutes

```bash
# Features:
✅ MISP 2.4.170 installation
✅ Database configuration
✅ Web server setup
✅ Background workers
✅ Feed synchronization
✅ Taxonomy import
✅ API configuration
✅ SIEM integration

# Usage:
sudo ./install-misp.sh

# Post-execution:
- Web Interface: https://IP
- API: https://IP/attributes/restSearch
- Admin user created
- Feeds configured
- Ready for threat intelligence
```

## 🔧 Infrastructure Scripts

### **10. Management Tools Installation**
**File:** `vagrant/scripts/management/install-tools.sh`
**Purpose:** Management server with automation tools
**Dependencies:** Ubuntu 22.04, Internet connectivity
**Runtime:** ~30-45 minutes

```bash
# Features:
✅ Ansible and collections
✅ Terraform and Packer
✅ Docker and Kubernetes tools
✅ Security testing tools
✅ Monitoring utilities
✅ Custom GOAD-Blue CLI
✅ SSH key management
✅ Backup utilities

# Usage:
sudo ./install-tools.sh

# Post-execution:
- GOAD-Blue CLI: goad-blue --help
- Ansible ready for automation
- All management tools installed
- SSH keys generated
```

## 📋 Script Dependencies Matrix

| Script | OS | RAM | CPU | Disk | Network | External Deps |
|--------|----|----|-----|------|---------|---------------|
| **update-system.sh** | Ubuntu 22.04 | 1GB | 1 | 10GB | Internet | apt packages |
| **install-docker.sh** | Ubuntu 22.04 | 2GB | 1 | 20GB | Internet | Docker repo |
| **disable-windows-defender.ps1** | Windows 10+ | 2GB | 1 | 5GB | None | PowerShell 5+ |
| **install-splunk.sh** | Ubuntu 22.04 | 8GB | 4 | 100GB | Internet | Splunk download |
| **install-velociraptor.sh** | Ubuntu 22.04 | 4GB | 2 | 50GB | Internet | GitHub releases |
| **install-security-onion.sh** | Ubuntu 22.04 | 16GB | 4 | 200GB | Internet | SO repository |
| **install-flare-vm.ps1** | Windows 10+ | 8GB | 4 | 100GB | Internet | FLARE repo |
| **install-malcolm.sh** | Ubuntu 22.04 | 12GB | 4 | 100GB | Internet | Docker images |
| **install-misp.sh** | Ubuntu 22.04 | 6GB | 2 | 50GB | Internet | MISP repo |
| **install-tools.sh** | Ubuntu 22.04 | 4GB | 2 | 50GB | Internet | Multiple repos |

## 🚀 Execution Order

### **Recommended Installation Sequence:**
1. **Management Server** (`install-tools.sh`) - Central control
2. **pfSense** (`configure-pfsense.sh`) - Network infrastructure  
3. **Splunk/Elasticsearch** (`install-splunk.sh`) - Core SIEM
4. **Velociraptor** (`install-velociraptor.sh`) - Endpoint detection
5. **MISP** (`install-misp.sh`) - Threat intelligence
6. **Security Onion** (`install-security-onion.sh`) - Network monitoring
7. **Malcolm** (`install-malcolm.sh`) - Network analysis
8. **FLARE-VM** (`install-flare-vm.ps1`) - Malware analysis

### **Parallel Installation Groups:**
- **Group 1**: Management, pfSense (infrastructure)
- **Group 2**: Splunk, Velociraptor, MISP (core security)
- **Group 3**: Security Onion, Malcolm (monitoring)
- **Group 4**: FLARE-VM (analysis)

## ✅ Validation Scripts

### **System Validation**
```bash
# File: scripts/validation/validate-system.sh
./validate-system.sh
# Checks: CPU, RAM, disk, virtualization support
```

### **Network Validation**
```bash
# File: scripts/validation/validate-network.sh
./validate-network.sh  
# Checks: Connectivity, ports, services
```

### **Service Validation**
```bash
# File: scripts/validation/validate-services.sh
./validate-services.sh
# Checks: Service status, health endpoints
```

---

!!! tip "Script Usage Tips"
    - Run scripts with appropriate privileges (sudo for Linux, Administrator for Windows)
    - Monitor system resources during installation
    - Check logs in `/var/log/goad-blue/` for troubleshooting
    - Use validation scripts to verify successful installation
    - Keep VM snapshots before major installations

!!! warning "Common Issues"
    - Insufficient system resources (RAM/CPU/Disk)
    - Network connectivity problems
    - Missing dependencies
    - Permission issues
    - Firewall blocking required ports

!!! info "Customization"
    - Scripts can be customized via environment variables
    - Configuration files in `ansible/vars/` for advanced settings
    - Templates in `ansible/templates/` for service configurations
    - Custom artifacts and rules can be added post-installation
