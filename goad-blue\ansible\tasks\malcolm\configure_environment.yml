---
# Configure <PERSON> environment

- name: Create <PERSON> user
  user:
    name: malcolm
    system: yes
    shell: /bin/bash
    home: /home/<USER>
    create_home: yes

- name: Add <PERSON> user to docker group
  user:
    name: malcolm
    groups: docker
    append: yes

- name: Create <PERSON> directories
  file:
    path: "{{ item }}"
    state: directory
    owner: malcolm
    group: malcolm
    mode: '0755'
  loop:
    - "{{ malcolm.install_path }}"
    - "{{ malcolm.install_path }}/config"
    - "{{ malcolm.install_path }}/logs"
    - "{{ malcolm.install_path }}/pcap"
    - "{{ malcolm.install_path }}/suricata-logs"
    - "{{ malcolm.install_path }}/zeek-logs"
    - "{{ malcolm.install_path }}/filebeat"
    - "{{ malcolm.install_path }}/logstash"
    - "{{ malcolm.install_path }}/elasticsearch"
    - "{{ malcolm.install_path }}/kibana"

- name: Set system parameters for <PERSON>
  sysctl:
    name: "{{ item.name }}"
    value: "{{ item.value }}"
    state: present
    reload: yes
  loop:
    - name: vm.max_map_count
      value: "262144"
    - name: fs.file-max
      value: "65536"
    - name: net.core.somaxconn
      value: "65535"
    - name: net.core.netdev_max_backlog
      value: "5000"

- name: Configure Malcolm environment file
  template:
    src: malcolm.env.j2
    dest: "{{ malcolm.install_path }}/.env"
    owner: malcolm
    group: malcolm
    mode: '0644'

- name: Generate Malcolm authentication credentials
  shell: |
    openssl rand -base64 32
  register: malcolm_auth_secret
  changed_when: false

- name: Create Malcolm authentication file
  template:
    src: auth.env.j2
    dest: "{{ malcolm.install_path }}/auth.env"
    owner: malcolm
    group: malcolm
    mode: '0600'

- name: Configure Malcolm docker-compose override
  template:
    src: docker-compose.override.yml.j2
    dest: "{{ malcolm.install_path }}/docker-compose.override.yml"
    owner: malcolm
    group: malcolm
    mode: '0644'

- name: Create Malcolm SSL certificates directory
  file:
    path: "{{ malcolm.install_path }}/nginx/certs"
    state: directory
    owner: malcolm
    group: malcolm
    mode: '0755'

- name: Generate Malcolm SSL certificate
  shell: |
    cd {{ malcolm.install_path }}/nginx/certs
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
      -keyout malcolm.key \
      -out malcolm.crt \
      -subj "/C=US/ST=Lab/L=Lab/O=GOAD-Blue/CN=malcolm.goad-blue.local"
    chown malcolm:malcolm malcolm.key malcolm.crt
    chmod 600 malcolm.key
    chmod 644 malcolm.crt
  args:
    creates: "{{ malcolm.install_path }}/nginx/certs/malcolm.crt"

- name: Configure Malcolm Nginx
  template:
    src: nginx.conf.j2
    dest: "{{ malcolm.install_path }}/nginx/nginx.conf"
    owner: malcolm
    group: malcolm
    mode: '0644'

- name: Configure Malcolm Logstash pipeline
  template:
    src: logstash-malcolm.conf.j2
    dest: "{{ malcolm.install_path }}/logstash/pipelines/malcolm.conf"
    owner: malcolm
    group: malcolm
    mode: '0644'

- name: Configure Malcolm Filebeat
  template:
    src: filebeat-malcolm.yml.j2
    dest: "{{ malcolm.install_path }}/filebeat/filebeat.yml"
    owner: malcolm
    group: malcolm
    mode: '0644'

- name: Create Malcolm data directories with proper permissions
  file:
    path: "{{ item }}"
    state: directory
    owner: "1000"
    group: "1000"
    mode: '0755'
  loop:
    - "{{ malcolm.install_path }}/elasticsearch/data"
    - "{{ malcolm.install_path }}/elasticsearch/logs"
    - "{{ malcolm.install_path }}/kibana/data"

- name: Configure Malcolm Elasticsearch
  template:
    src: elasticsearch-malcolm.yml.j2
    dest: "{{ malcolm.install_path }}/elasticsearch/elasticsearch.yml"
    owner: malcolm
    group: malcolm
    mode: '0644'

- name: Configure Malcolm Kibana
  template:
    src: kibana-malcolm.yml.j2
    dest: "{{ malcolm.install_path }}/kibana/kibana.yml"
    owner: malcolm
    group: malcolm
    mode: '0644'

- name: Set Malcolm file ownership
  file:
    path: "{{ malcolm.install_path }}"
    owner: malcolm
    group: malcolm
    recurse: yes

- name: Create Malcolm systemd service
  template:
    src: malcolm.service.j2
    dest: /etc/systemd/system/malcolm.service
    mode: '0644'
  notify:
    - reload systemd

- name: Create Malcolm log rotation configuration
  template:
    src: malcolm-logrotate.j2
    dest: /etc/logrotate.d/malcolm
    mode: '0644'
