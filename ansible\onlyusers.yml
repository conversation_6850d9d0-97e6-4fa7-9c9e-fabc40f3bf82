---
# Load datas
- import_playbook: data.yml
  vars:
    data_path: "../ad/{{domain_name}}/data/"
  tags: 'data'

# set AD datas ==================================================================================================
- name: DCs AD data configuration
  hosts: dc01,dc02
  roles:
    - { role: 'onlyusers', tags: 'onlyusers' }
  vars:
    hostname: "{{lab.hosts[dict_key].hostname}}"
    domain: "{{lab.hosts[dict_key].domain}}"
    domain_username: "{{domain}}\\{{admin_user}}"
    domain_password: "{{lab.domains[domain].domain_password}}"
    domain_server: "{{lab.hosts[dict_key].hostname}}.{{domain}}"
    ad_users: "{{lab.domains[lab.hosts[dict_key].domain].users}}"
    ad_ou: "{{lab.domains[lab.hosts[dict_key].domain].organisation_units}}"
    ad_groups: "{{lab.domains[lab.hosts[dict_key].domain].groups}}"
