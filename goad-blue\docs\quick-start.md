# Quick Start Guide

Get GOAD-<PERSON> up and running in under 30 minutes with this streamlined quick start guide.

## 🚀 Prerequisites Check

Before starting, ensure you have:

- [ ] **Existing GOAD installation** (recommended but not required)
- [ ] **32GB RAM minimum** (64GB recommended for full deployment)
- [ ] **500GB free disk space**
- [ ] **Virtualization platform** (VMware Workstation/ESXi, VirtualBox, or cloud account)
- [ ] **Administrative privileges** on your system

## ⚡ 5-Minute Setup

### Step 1: Download GOAD-Blue

```bash
# If you have an existing GOAD installation
cd /path/to/your/GOAD
git clone https://github.com/your-org/goad-blue.git goad-blue

# Or standalone installation
git clone https://github.com/your-org/goad-blue.git
cd goad-blue
```

### Step 2: Quick Install

=== "Interactive Installer (Recommended)"
    ```bash
    # Run the interactive installer
    ./scripts/goad-blue-installer.sh
    ```
    
    The installer will:
    
    1. ✅ Check dependencies
    2. 🔍 Detect existing GOAD
    3. ⚙️ Guide component selection
    4. 🏗️ Build and deploy infrastructure
    5. 🔗 Integrate with GOAD

=== "Python CLI"
    ```bash
    # Quick deployment with default components
    python3 goad-blue.py -t install -s splunk -c security_onion,velociraptor,misp
    
    # Interactive configuration
    python3 goad-blue.py --interactive
    ```

=== "Windows"
    ```cmd
    REM Use the Windows launcher
    goad-blue.bat --interactive
    ```

### Step 3: Verify Installation

```bash
# Check component status
python3 goad-blue.py -t status

# Test GOAD integration (if applicable)
python3 goad-blue.py --integrate-goad
```

## 🎯 Quick Configuration

### Minimal Configuration

For a basic setup with essential components:

```yaml
# goad-blue-config.yml
goad_blue:
  name: "quick-start-lab"
  provider: "vmware"  # or virtualbox, aws, azure

siem:
  type: "splunk"
  enabled: true

components:
  security_onion:
    enabled: true
  velociraptor:
    enabled: true
  misp:
    enabled: false
  flare_vm:
    enabled: false
```

### Full Configuration

For a complete training environment:

```yaml
# goad-blue-config.yml
goad_blue:
  name: "full-training-lab"
  provider: "vmware"

siem:
  type: "splunk"
  enabled: true

components:
  security_onion:
    enabled: true
  malcolm:
    enabled: false
  velociraptor:
    enabled: true
  misp:
    enabled: true
  flare_vm:
    enabled: true
```

## 🔧 Component Selection Guide

Choose components based on your use case:

### 🎓 **Training Environment**
```bash
# Essential training components
python3 goad-blue.py -c security_onion,velociraptor,misp
```

**Includes:**
- Security Onion for network monitoring
- Velociraptor for endpoint visibility
- MISP for threat intelligence

### 🔬 **Research Environment**
```bash
# Full research stack
python3 goad-blue.py -c security_onion,malcolm,velociraptor,misp,flare_vm
```

**Includes:**
- All monitoring components
- Malcolm for network forensics
- FLARE-VM for malware analysis

### 💼 **Enterprise Demo**
```bash
# Enterprise-focused components
python3 goad-blue.py -s elastic -c security_onion,velociraptor
```

**Includes:**
- Elastic Stack (ELK) for scalability
- Security Onion for comprehensive monitoring
- Velociraptor for endpoint management

## 🌐 Platform-Specific Quick Start

=== "VMware Workstation"
    ```bash
    # No additional configuration needed
    ./scripts/goad-blue-installer.sh
    ```
    
    **Requirements:**
    - VMware Workstation Pro 16+
    - 32GB RAM minimum
    - 500GB free space

=== "AWS"
    ```bash
    # Configure AWS credentials first
    aws configure
    
    # Deploy to AWS
    python3 goad-blue.py -t install -p aws
    ```
    
    **Requirements:**
    - AWS CLI configured
    - Appropriate IAM permissions
    - VPC with internet gateway

=== "Azure"
    ```bash
    # Login to Azure
    az login
    
    # Deploy to Azure
    python3 goad-blue.py -t install -p azure
    ```
    
    **Requirements:**
    - Azure CLI installed
    - Valid Azure subscription
    - Resource group permissions

=== "VirtualBox"
    ```bash
    # VirtualBox deployment
    python3 goad-blue.py -t install -p virtualbox
    ```
    
    **Requirements:**
    - VirtualBox 6.1+
    - Extension Pack installed
    - Sufficient host resources

## 🎮 First Steps After Installation

### 1. Access Web Interfaces

Once installation completes, access your components:

| Component | URL | Default Credentials |
|-----------|-----|-------------------|
| **Splunk** | https://**************:8000 | admin / ChangeMePlease123! |
| **Security Onion** | https://************** | admin / ChangeMePlease123! |
| **Velociraptor** | https://**************:8889 | admin / ChangeMePlease123! |
| **MISP** | https://************** | <EMAIL> / admin |

!!! warning "Security Notice"
    Change default passwords immediately after first login!

### 2. Verify Data Flow

Check that logs are flowing from GOAD to your SIEM:

=== "Splunk"
    ```spl
    # Search for GOAD events
    index=goad_blue_windows earliest=-1h
    | stats count by host
    ```

=== "Elastic"
    ```json
    # Kibana query
    {
      "query": {
        "bool": {
          "must": [
            {"range": {"@timestamp": {"gte": "now-1h"}}},
            {"wildcard": {"host.name": "*goad*"}}
          ]
        }
      }
    }
    ```

### 3. Run Your First Detection

Test the environment with a simple attack simulation:

```bash
# Simulate Kerberoasting attack
python3 goad-blue.py simulate_attack kerberoasting

# Check for detections in SIEM
# Look for Event ID 4769 with RC4 encryption
```

## 🎯 Quick Training Scenarios

### Scenario 1: Basic Log Analysis (15 minutes)

1. **Generate Activity:**
   - Log into GOAD domain controller
   - Create a new user account
   - Assign administrative privileges

2. **Detect in SIEM:**
   - Search for Event ID 4720 (user creation)
   - Search for Event ID 4728 (group membership change)
   - Correlate events by user and time

3. **Investigate:**
   - Review user creation details
   - Check privilege escalation
   - Document findings

### Scenario 2: Network Monitoring (20 minutes)

1. **Generate Traffic:**
   - Perform network reconnaissance from GOAD
   - Use tools like `nmap` or `ping`
   - Access multiple systems

2. **Monitor in Security Onion:**
   - Review Suricata alerts
   - Analyze Zeek connection logs
   - Identify scanning patterns

3. **Correlate:**
   - Match network activity to host logs
   - Identify source and targets
   - Assess threat level

### Scenario 3: Endpoint Investigation (25 minutes)

1. **Trigger Activity:**
   - Execute PowerShell commands on GOAD
   - Download and run scripts
   - Access sensitive files

2. **Investigate with Velociraptor:**
   - Review process execution
   - Check file access logs
   - Analyze PowerShell activity

3. **Response:**
   - Collect artifacts
   - Document timeline
   - Recommend containment

## 🔧 Troubleshooting Quick Fixes

### Common Issues

**Installation Fails:**
```bash
# Check dependencies
python3 goad-blue.py --check-deps

# Verify permissions
sudo chown -R $USER:$USER goad-blue/
```

**No Data in SIEM:**
```bash
# Check agent status
python3 goad-blue.py -t status

# Restart log forwarding
ansible-playbook -i inventory/ restart-agents.yml
```

**Network Connectivity Issues:**
```bash
# Test network connectivity
ping **************  # SIEM
ping **************  # Velociraptor

# Check firewall rules
sudo ufw status
```

**GOAD Integration Problems:**
```bash
# Re-run integration
python3 goad-blue.py --integrate-goad --force

# Check GOAD status
cd ../  # Go to GOAD directory
python3 goad.py status
```

## 📚 Next Steps

After completing the quick start:

1. **🔧 [Configuration](configuration/)** - Customize your environment
2. **🎓 [Training Scenarios](training/)** - Explore detailed exercises
3. **🔍 [Components](components/)** - Deep dive into each component
4. **🛠️ [Operations](operations/)** - Learn day-to-day management

## 🆘 Getting Help

If you encounter issues:

- **📖 Documentation:** Check the [Troubleshooting](troubleshooting/) section
- **💬 Community:** Join our Discord/Slack community
- **🐛 Issues:** Report bugs on GitHub
- **📧 Support:** Contact support for enterprise users

---

!!! success "Congratulations!"
    You now have a working GOAD-Blue environment! Start with the training scenarios or explore the individual components to begin your blue team journey.

!!! tip "Pro Tip"
    Save your configuration file and document any customizations for easy redeployment and sharing with your team.
