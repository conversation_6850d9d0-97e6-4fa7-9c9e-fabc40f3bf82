# VMware vSphere infrastructure for GOAD-Blue
terraform {
  required_providers {
    vsphere = {
      source  = "hashicorp/vsphere"
      version = "~> 2.0"
    }
  }
}

# Data sources
data "vsphere_datacenter" "dc" {
  name = var.datacenter_name
}

data "vsphere_datastore" "datastore" {
  name          = var.datastore_name
  datacenter_id = data.vsphere_datacenter.dc.id
}

data "vsphere_compute_cluster" "cluster" {
  name          = var.cluster_name
  datacenter_id = data.vsphere_datacenter.dc.id
}

data "vsphere_network" "network" {
  name          = var.network_name
  datacenter_id = data.vsphere_datacenter.dc.id
}

# Resource pool for GOAD-Blue
resource "vsphere_resource_pool" "goad_blue_pool" {
  name                    = "${var.lab_name}-pool"
  parent_resource_pool_id = data.vsphere_compute_cluster.cluster.resource_pool_id
}

# Folder for GOAD-Blue VMs
resource "vsphere_folder" "goad_blue_folder" {
  path          = var.lab_name
  type          = "vm"
  datacenter_id = data.vsphere_datacenter.dc.id
}

# SIEM VM (Splunk or Elastic)
resource "vsphere_virtual_machine" "siem" {
  count = var.siem_enabled ? 1 : 0
  
  name             = "${var.lab_name}-${var.siem_type}-server"
  resource_pool_id = vsphere_resource_pool.goad_blue_pool.id
  datastore_id     = data.vsphere_datastore.datastore.id
  folder           = vsphere_folder.goad_blue_folder.path
  
  num_cpus = 4
  memory   = 8192
  guest_id = "ubuntu64Guest"
  
  network_interface {
    network_id = data.vsphere_network.network.id
  }
  
  disk {
    label = "disk0"
    size  = 100
  }
  
  clone {
    template_uuid = var.siem_template_uuid
  }
  
  vapp {
    properties = {
      "guestinfo.hostname" = "${var.lab_name}-${var.siem_type}-server"
      "guestinfo.ipaddress" = cidrhost(var.siem_subnet, 10)
      "guestinfo.netmask" = cidrnetmask(var.siem_subnet)
      "guestinfo.gateway" = cidrhost(var.siem_subnet, 1)
    }
  }
}

# Security Onion VM
resource "vsphere_virtual_machine" "security_onion" {
  count = var.security_onion_enabled ? 1 : 0
  
  name             = "${var.lab_name}-security-onion"
  resource_pool_id = vsphere_resource_pool.goad_blue_pool.id
  datastore_id     = data.vsphere_datastore.datastore.id
  folder           = vsphere_folder.goad_blue_folder.path
  
  num_cpus = 8
  memory   = 16384
  guest_id = "centos7_64Guest"
  
  network_interface {
    network_id = data.vsphere_network.network.id
  }
  
  disk {
    label = "disk0"
    size  = 200
  }
  
  clone {
    template_uuid = var.security_onion_template_uuid
  }
  
  vapp {
    properties = {
      "guestinfo.hostname" = "${var.lab_name}-security-onion"
      "guestinfo.ipaddress" = cidrhost(var.monitoring_subnet, 10)
      "guestinfo.netmask" = cidrnetmask(var.monitoring_subnet)
      "guestinfo.gateway" = cidrhost(var.monitoring_subnet, 1)
    }
  }
}

# Malcolm VM
resource "vsphere_virtual_machine" "malcolm" {
  count = var.malcolm_enabled ? 1 : 0
  
  name             = "${var.lab_name}-malcolm"
  resource_pool_id = vsphere_resource_pool.goad_blue_pool.id
  datastore_id     = data.vsphere_datastore.datastore.id
  folder           = vsphere_folder.goad_blue_folder.path
  
  num_cpus = 4
  memory   = 8192
  guest_id = "ubuntu64Guest"
  
  network_interface {
    network_id = data.vsphere_network.network.id
  }
  
  disk {
    label = "disk0"
    size  = 100
  }
  
  clone {
    template_uuid = var.malcolm_template_uuid
  }
  
  vapp {
    properties = {
      "guestinfo.hostname" = "${var.lab_name}-malcolm"
      "guestinfo.ipaddress" = cidrhost(var.monitoring_subnet, 11)
      "guestinfo.netmask" = cidrnetmask(var.monitoring_subnet)
      "guestinfo.gateway" = cidrhost(var.monitoring_subnet, 1)
    }
  }
}

# Velociraptor Server VM
resource "vsphere_virtual_machine" "velociraptor" {
  count = var.velociraptor_enabled ? 1 : 0
  
  name             = "${var.lab_name}-velociraptor"
  resource_pool_id = vsphere_resource_pool.goad_blue_pool.id
  datastore_id     = data.vsphere_datastore.datastore.id
  folder           = vsphere_folder.goad_blue_folder.path
  
  num_cpus = 2
  memory   = 4096
  guest_id = "ubuntu64Guest"
  
  network_interface {
    network_id = data.vsphere_network.network.id
  }
  
  disk {
    label = "disk0"
    size  = 50
  }
  
  clone {
    template_uuid = var.velociraptor_template_uuid
  }
  
  vapp {
    properties = {
      "guestinfo.hostname" = "${var.lab_name}-velociraptor"
      "guestinfo.ipaddress" = cidrhost(var.monitoring_subnet, 20)
      "guestinfo.netmask" = cidrnetmask(var.monitoring_subnet)
      "guestinfo.gateway" = cidrhost(var.monitoring_subnet, 1)
    }
  }
}

# MISP VM
resource "vsphere_virtual_machine" "misp" {
  count = var.misp_enabled ? 1 : 0
  
  name             = "${var.lab_name}-misp"
  resource_pool_id = vsphere_resource_pool.goad_blue_pool.id
  datastore_id     = data.vsphere_datastore.datastore.id
  folder           = vsphere_folder.goad_blue_folder.path
  
  num_cpus = 2
  memory   = 4096
  guest_id = "ubuntu64Guest"
  
  network_interface {
    network_id = data.vsphere_network.network.id
  }
  
  disk {
    label = "disk0"
    size  = 50
  }
  
  clone {
    template_uuid = var.misp_template_uuid
  }
  
  vapp {
    properties = {
      "guestinfo.hostname" = "${var.lab_name}-misp"
      "guestinfo.ipaddress" = cidrhost(var.analysis_subnet, 30)
      "guestinfo.netmask" = cidrnetmask(var.analysis_subnet)
      "guestinfo.gateway" = cidrhost(var.analysis_subnet, 1)
    }
  }
}

# FLARE-VM
resource "vsphere_virtual_machine" "flare_vm" {
  count = var.flare_vm_enabled ? 1 : 0
  
  name             = "${var.lab_name}-flare-vm"
  resource_pool_id = vsphere_resource_pool.goad_blue_pool.id
  datastore_id     = data.vsphere_datastore.datastore.id
  folder           = vsphere_folder.goad_blue_folder.path
  
  num_cpus = 4
  memory   = 8192
  guest_id = "windows9_64Guest"
  
  network_interface {
    network_id = data.vsphere_network.network.id
  }
  
  disk {
    label = "disk0"
    size  = 100
  }
  
  clone {
    template_uuid = var.flare_vm_template_uuid
  }
  
  vapp {
    properties = {
      "guestinfo.hostname" = "${var.lab_name}-flare-vm"
      "guestinfo.ipaddress" = cidrhost(var.analysis_subnet, 40)
      "guestinfo.netmask" = cidrnetmask(var.analysis_subnet)
      "guestinfo.gateway" = cidrhost(var.analysis_subnet, 1)
    }
  }
}
