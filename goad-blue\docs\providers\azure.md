# Azure Deployment Guide

This guide covers deploying GOAD-Blue on Microsoft Azure using Infrastructure as Code with Terraform and configuration management with Ansible.

## 🏗️ Azure Architecture

```mermaid
graph TB
    subgraph "🌐 Internet"
        INTERNET[🌍 Internet<br/>Public Access]
    end
    
    subgraph "☁️ Azure Subscription"
        subgraph "🏢 Resource Group"
            RG[📦 goad-blue-rg<br/>East US]
        end
        
        subgraph "🌐 Virtual Network (10.0.0.0/16)"
            VNET[🌐 goad-blue-vnet<br/>10.0.0.0/16]
            
            subgraph "🔒 Public Subnet"
                PUB_SUB[📡 Public Subnet<br/>********/24]
                
                LB[⚖️ Azure Load Balancer<br/>Standard SKU]
                PIP[🌐 Public IP<br/>Static Assignment]
                AGW[🚪 Application Gateway<br/>WAF Enabled]
            end
            
            subgraph "🔐 Private Subnet"
                PRIV_SUB[🏠 Private Subnet<br/>*********/24]
                
                SPLUNK[📊 Splunk Enterprise<br/>Standard_D4s_v3<br/>**********]
                SO_MGR[🧅 Security Onion Manager<br/>Standard_D8s_v3<br/>**********]
                SO_SENSOR[📡 Security Onion Sensor<br/>Standard_D4s_v3<br/>**********]
                VELO[🦖 Velociraptor Server<br/>Standard_B2s<br/>**********]
                MISP[🧠 MISP Server<br/>Standard_B2s<br/>**********]
            end
            
            subgraph "🗄️ Database Subnet"
                DB_SUB[💾 Database Subnet<br/>*********/24]
                
                POSTGRES[🗃️ Azure Database<br/>PostgreSQL Flexible<br/>Zone Redundant]
            end
        end
        
        subgraph "🔍 Monitoring & Security"
            MONITOR[📊 Azure Monitor<br/>Metrics & Logs]
            SENTINEL[🛡️ Azure Sentinel<br/>SIEM Integration]
            DEFENDER[🛡️ Defender for Cloud<br/>Security Posture]
            KEYVAULT[🔐 Key Vault<br/>Secrets Management]
        end
        
        subgraph "💾 Storage"
            STORAGE[💾 Storage Account<br/>Standard_LRS]
            BACKUP[🔄 Recovery Services<br/>VM Backup]
        end
    end
    
    subgraph "🔒 Network Security Groups"
        NSG_PUB[🛡️ Public NSG<br/>HTTP/HTTPS from Internet]
        NSG_PRIV[🛡️ Private NSG<br/>Internal Communication]
        NSG_DB[🛡️ Database NSG<br/>PostgreSQL from App Tier]
    end
    
    %% Connections
    INTERNET --> PIP
    PIP --> AGW
    AGW --> LB
    LB --> SPLUNK
    LB --> VELO
    
    SPLUNK --> POSTGRES
    MISP --> POSTGRES
    
    SPLUNK --> MONITOR
    SO_MGR --> MONITOR
    VELO --> SENTINEL
    
    SPLUNK --> STORAGE
    SO_MGR --> BACKUP
    
    classDef public fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef private fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef database fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef security fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef monitoring fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef storage fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    
    class PUB_SUB,LB,PIP,AGW public
    class PRIV_SUB,SPLUNK,SO_MGR,SO_SENSOR,VELO,MISP private
    class DB_SUB,POSTGRES database
    class NSG_PUB,NSG_PRIV,NSG_DB,DEFENDER,KEYVAULT security
    class MONITOR,SENTINEL monitoring
    class STORAGE,BACKUP storage
```

## 📋 Prerequisites

### **Azure Account Setup**

1. **Azure Subscription**: Active Azure subscription
2. **Service Principal**: For Terraform authentication
3. **Azure CLI**: Installed and configured
4. **Terraform**: Version 1.0+ with Azure provider
5. **Ansible**: Version 4.0+ with Azure collection

### **Azure CLI Installation and Setup**

```bash
# Install Azure CLI (Ubuntu/Debian)
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

# Install Azure CLI (CentOS/RHEL)
sudo rpm --import https://packages.microsoft.com/keys/microsoft.asc
sudo dnf install -y https://packages.microsoft.com/config/rhel/8/packages-microsoft-prod.rpm
sudo dnf install azure-cli

# Login to Azure
az login

# Set default subscription
az account set --subscription "Your Subscription Name"

# Verify login
az account show
```

### **Service Principal Creation**

```bash
# Create service principal for Terraform
az ad sp create-for-rbac \
  --name "goad-blue-terraform" \
  --role="Contributor" \
  --scopes="/subscriptions/$(az account show --query id -o tsv)"

# Output will include:
# {
#   "appId": "********-1234-1234-1234-********9012",
#   "displayName": "goad-blue-terraform",
#   "password": "your-password",
#   "tenant": "********-4321-4321-4321-2109********"
# }

# Set environment variables
export ARM_CLIENT_ID="********-1234-1234-1234-********9012"
export ARM_CLIENT_SECRET="your-password"
export ARM_SUBSCRIPTION_ID="$(az account show --query id -o tsv)"
export ARM_TENANT_ID="********-4321-4321-4321-2109********"
```

## 🚀 Deployment Process

### **1. Infrastructure Deployment**

#### **Terraform Configuration**

```hcl
# terraform/providers/azure/main.tf
terraform {
  required_version = ">= 1.0"
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
    azuread = {
      source  = "hashicorp/azuread"
      version = "~> 2.0"
    }
  }
  
  backend "azurerm" {
    resource_group_name  = "goad-blue-terraform-rg"
    storage_account_name = "goadblueterraformstate"
    container_name       = "tfstate"
    key                  = "azure.terraform.tfstate"
  }
}

provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
    
    virtual_machine {
      delete_os_disk_on_deletion = true
    }
  }
}

# Resource Group
resource "azurerm_resource_group" "main" {
  name     = "${var.name_prefix}-rg"
  location = var.location
  
  tags = var.tags
}

# Virtual Network
module "network" {
  source = "../../modules/azure/network"
  
  name_prefix         = var.name_prefix
  location           = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  
  vnet_cidr          = var.vnet_cidr
  public_subnet_cidr = var.public_subnet_cidr
  private_subnet_cidr = var.private_subnet_cidr
  database_subnet_cidr = var.database_subnet_cidr
  
  tags = var.tags
}

# Network Security Groups
module "security_groups" {
  source = "../../modules/azure/security"
  
  name_prefix         = var.name_prefix
  location           = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  
  allowed_source_addresses = var.allowed_source_addresses
  
  tags = var.tags
}

# Application Gateway
module "application_gateway" {
  source = "../../modules/azure/application_gateway"
  
  name_prefix         = var.name_prefix
  location           = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  
  subnet_id = module.network.public_subnet_id
  
  ssl_certificate_data     = var.ssl_certificate_data
  ssl_certificate_password = var.ssl_certificate_password
  
  tags = var.tags
}

# Virtual Machines
module "virtual_machines" {
  source = "../../modules/azure/virtual_machines"
  
  name_prefix         = var.name_prefix
  location           = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  
  subnet_id = module.network.private_subnet_id
  
  network_security_group_id = module.security_groups.private_nsg_id
  
  virtual_machines = var.virtual_machines
  
  admin_username = var.admin_username
  admin_password = var.admin_password
  ssh_public_key = var.ssh_public_key
  
  tags = var.tags
}

# PostgreSQL Database
module "postgresql" {
  source = "../../modules/azure/postgresql"
  
  name_prefix         = var.name_prefix
  location           = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  
  subnet_id = module.network.database_subnet_id
  
  database_version = var.database_version
  sku_name        = var.database_sku_name
  
  administrator_login    = var.database_admin_username
  administrator_password = var.database_admin_password
  
  backup_retention_days = var.backup_retention_days
  geo_redundant_backup  = var.geo_redundant_backup
  
  tags = var.tags
}

# Storage Account
resource "azurerm_storage_account" "main" {
  name                     = "${replace(var.name_prefix, "-", "")}storage"
  resource_group_name      = azurerm_resource_group.main.name
  location                = azurerm_resource_group.main.location
  account_tier             = "Standard"
  account_replication_type = "LRS"
  
  blob_properties {
    versioning_enabled = true
    
    delete_retention_policy {
      days = 30
    }
  }
  
  tags = var.tags
}

# Key Vault
resource "azurerm_key_vault" "main" {
  name                = "${var.name_prefix}-kv"
  location           = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  tenant_id          = data.azurerm_client_config.current.tenant_id
  
  sku_name = "standard"
  
  access_policy {
    tenant_id = data.azurerm_client_config.current.tenant_id
    object_id = data.azurerm_client_config.current.object_id
    
    secret_permissions = [
      "Get", "List", "Set", "Delete", "Recover", "Backup", "Restore"
    ]
  }
  
  tags = var.tags
}

# Log Analytics Workspace
resource "azurerm_log_analytics_workspace" "main" {
  name                = "${var.name_prefix}-law"
  location           = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  sku                = "PerGB2018"
  retention_in_days  = 30
  
  tags = var.tags
}

# Azure Monitor
module "monitoring" {
  source = "../../modules/azure/monitoring"
  
  name_prefix         = var.name_prefix
  location           = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  
  log_analytics_workspace_id = azurerm_log_analytics_workspace.main.id
  
  virtual_machine_ids = module.virtual_machines.virtual_machine_ids
  
  tags = var.tags
}
```

#### **Variables Configuration**

```hcl
# terraform/providers/azure/variables.tf
variable "name_prefix" {
  description = "Prefix for all resource names"
  type        = string
  default     = "goad-blue"
}

variable "location" {
  description = "Azure region"
  type        = string
  default     = "East US"
}

variable "vnet_cidr" {
  description = "CIDR block for VNet"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_cidr" {
  description = "CIDR block for public subnet"
  type        = string
  default     = "********/24"
}

variable "private_subnet_cidr" {
  description = "CIDR block for private subnet"
  type        = string
  default     = "*********/24"
}

variable "database_subnet_cidr" {
  description = "CIDR block for database subnet"
  type        = string
  default     = "*********/24"
}

variable "virtual_machines" {
  description = "Virtual machine configurations"
  type = map(object({
    vm_size     = string
    image_sku   = string
    disk_size   = number
    disk_type   = string
  }))
  default = {
    splunk_enterprise = {
      vm_size   = "Standard_D4s_v3"
      image_sku = "20_04-lts-gen2"
      disk_size = 500
      disk_type = "Premium_LRS"
    }
    security_onion_manager = {
      vm_size   = "Standard_D8s_v3"
      image_sku = "20_04-lts-gen2"
      disk_size = 1000
      disk_type = "Premium_LRS"
    }
    security_onion_sensor = {
      vm_size   = "Standard_D4s_v3"
      image_sku = "20_04-lts-gen2"
      disk_size = 500
      disk_type = "Premium_LRS"
    }
    velociraptor_server = {
      vm_size   = "Standard_B2s"
      image_sku = "20_04-lts-gen2"
      disk_size = 200
      disk_type = "Standard_LRS"
    }
    misp_server = {
      vm_size   = "Standard_B2s"
      image_sku = "20_04-lts-gen2"
      disk_size = 200
      disk_type = "Standard_LRS"
    }
  }
}

variable "admin_username" {
  description = "Admin username for VMs"
  type        = string
  default     = "azureuser"
}

variable "admin_password" {
  description = "Admin password for VMs"
  type        = string
  sensitive   = true
}

variable "ssh_public_key" {
  description = "SSH public key for VM access"
  type        = string
}

variable "database_admin_username" {
  description = "PostgreSQL admin username"
  type        = string
  default     = "goadblueadmin"
}

variable "database_admin_password" {
  description = "PostgreSQL admin password"
  type        = string
  sensitive   = true
}

variable "allowed_source_addresses" {
  description = "Source IP addresses allowed to access the environment"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default = {
    Project     = "GOAD-Blue"
    Environment = "production"
    ManagedBy   = "Terraform"
  }
}
```

#### **Deployment Commands**

```bash
# Navigate to Azure provider directory
cd terraform/providers/azure

# Initialize Terraform
terraform init

# Create terraform.tfvars file
cat > terraform.tfvars << EOF
name_prefix = "goad-blue-prod"
location = "East US"

vnet_cidr = "10.0.0.0/16"
public_subnet_cidr = "********/24"
private_subnet_cidr = "*********/24"
database_subnet_cidr = "*********/24"

admin_username = "azureuser"
admin_password = "ComplexPassword123!"
ssh_public_key = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQ..."

database_admin_username = "goadblueadmin"
database_admin_password = "DatabasePassword123!"

allowed_source_addresses = ["***********/24"]  # Your office IP range

tags = {
  Project = "GOAD-Blue"
  Environment = "production"
  Owner = "Security-Team"
  CostCenter = "IT-Security"
}
EOF

# Plan deployment
terraform plan -var-file="terraform.tfvars"

# Apply deployment
terraform apply -var-file="terraform.tfvars"
```

### **2. Custom Image Creation with Packer**

#### **Azure Image Builder**

```json
{
  "variables": {
    "client_id": "{{env `ARM_CLIENT_ID`}}",
    "client_secret": "{{env `ARM_CLIENT_SECRET`}}",
    "subscription_id": "{{env `ARM_SUBSCRIPTION_ID`}}",
    "tenant_id": "{{env `ARM_TENANT_ID`}}",
    "resource_group": "goad-blue-images-rg",
    "location": "East US"
  },
  "builders": [
    {
      "type": "azure-arm",
      "client_id": "{{user `client_id`}}",
      "client_secret": "{{user `client_secret`}}",
      "subscription_id": "{{user `subscription_id`}}",
      "tenant_id": "{{user `tenant_id`}}",
      
      "managed_image_resource_group_name": "{{user `resource_group`}}",
      "managed_image_name": "goad-blue-splunk-{{timestamp}}",
      
      "os_type": "Linux",
      "image_publisher": "Canonical",
      "image_offer": "0001-com-ubuntu-server-focal",
      "image_sku": "20_04-lts-gen2",
      
      "location": "{{user `location`}}",
      "vm_size": "Standard_D2s_v3"
    }
  ],
  "provisioners": [
    {
      "type": "shell",
      "inline": [
        "sudo apt-get update",
        "sudo apt-get upgrade -y",
        "sudo apt-get install -y curl wget unzip"
      ]
    },
    {
      "type": "file",
      "source": "../../files/splunk/",
      "destination": "/tmp/splunk-files/"
    },
    {
      "type": "shell",
      "script": "../../scripts/azure/install-splunk.sh"
    },
    {
      "type": "shell",
      "script": "../../scripts/azure/configure-azure-monitor.sh"
    },
    {
      "type": "shell",
      "inline": [
        "sudo waagent -deprovision+user -force"
      ]
    }
  ]
}
```

#### **Build Azure Images**

```bash
# Create resource group for images
az group create --name goad-blue-images-rg --location "East US"

# Build Splunk image
cd packer/templates/azure
packer build -var-file="variables.json" goad-blue-splunk.json

# Build other component images
packer build -var-file="variables.json" goad-blue-security-onion.json
packer build -var-file="variables.json" goad-blue-velociraptor.json
packer build -var-file="variables.json" goad-blue-misp.json
```

### **3. Configuration with Ansible**

#### **Azure Dynamic Inventory**

```yaml
# ansible/inventory/azure_rm.yml
plugin: azure.azcollection.azure_rm
include_vm_resource_groups:
  - goad-blue-prod-rg

auth_source: auto

keyed_groups:
  - key: tags.Component
    prefix: component
  - key: tags.Environment
    prefix: env
  - key: location
    prefix: location

hostnames:
  - private_ipv4_addresses
  - name

compose:
  ansible_host: private_ipv4_addresses[0]
  ansible_user: azureuser
  component: tags.Component
  environment: tags.Environment
```

#### **Azure-Specific Playbook**

```yaml
# ansible/playbooks/azure/site.yml
---
- name: Configure Azure GOAD-Blue Infrastructure
  hosts: localhost
  gather_facts: false
  tasks:
    - name: Display deployment information
      debug:
        msg: |
          Configuring GOAD-Blue on Azure
          Location: {{ azure_location }}
          Environment: {{ environment }}

- name: Configure common Azure settings
  hosts: all
  become: true
  roles:
    - common
    - azure-monitor-agent
    - azure-security
  tags:
    - common
    - azure

- name: Configure Splunk Enterprise
  hosts: component_splunk
  become: true
  roles:
    - splunk-enterprise
    - azure-splunk-integration
  tags:
    - splunk

- name: Configure Security Onion
  hosts: component_security_onion
  become: true
  roles:
    - security-onion
    - azure-security-onion-integration
  tags:
    - security-onion

- name: Configure Azure Sentinel integration
  hosts: all
  become: true
  roles:
    - azure-sentinel-integration
  tags:
    - sentinel
```

#### **Run Ansible Configuration**

```bash
# Install Azure collection
ansible-galaxy collection install azure.azcollection

# Install Azure SDK
pip install azure-cli azure-identity azure-mgmt-compute azure-mgmt-network

# Run site playbook
ansible-playbook -i inventory/azure_rm.yml playbooks/azure/site.yml

# Run specific components
ansible-playbook -i inventory/azure_rm.yml playbooks/azure/site.yml --tags splunk
```

## 🔧 Azure-Specific Features

### **Azure Monitor Integration**

```bash
# Install Azure Monitor Agent
wget https://aka.ms/azcmagent -O ~/install_linux_azcmagent.sh
bash ~/install_linux_azcmagent.sh

# Configure data collection rules
az monitor data-collection rule create \
  --resource-group goad-blue-prod-rg \
  --location "East US" \
  --name "goad-blue-dcr" \
  --rule-file data-collection-rule.json
```

### **Azure Sentinel Integration**

```bash
# Enable Azure Sentinel
az sentinel workspace create \
  --resource-group goad-blue-prod-rg \
  --workspace-name goad-blue-law

# Install Sentinel connectors
az sentinel data-connector create \
  --resource-group goad-blue-prod-rg \
  --workspace-name goad-blue-law \
  --connector-id "AzureSecurityCenter"
```

### **Azure Backup Configuration**

```bash
# Create Recovery Services Vault
az backup vault create \
  --resource-group goad-blue-prod-rg \
  --name goad-blue-vault \
  --location "East US"

# Enable backup for VMs
az backup protection enable-for-vm \
  --resource-group goad-blue-prod-rg \
  --vault-name goad-blue-vault \
  --vm goad-blue-splunk \
  --policy-name DefaultPolicy
```

### **Cost Management**

```bash
# Set up budget alerts
az consumption budget create \
  --resource-group goad-blue-prod-rg \
  --budget-name "goad-blue-monthly-budget" \
  --amount 1000 \
  --time-grain Monthly \
  --start-date "2024-01-01T00:00:00Z" \
  --end-date "2024-12-31T23:59:59Z"

# Get cost analysis
az consumption usage list \
  --start-date "2024-01-01" \
  --end-date "2024-01-31"
```

---

!!! success "Azure Deployment Complete"
    Your GOAD-Blue environment is now deployed on Azure with enterprise-grade security, monitoring, and compliance capabilities.

!!! tip "Azure Best Practices"
    - Use Azure Policy for governance and compliance
    - Enable Azure Security Center for security recommendations
    - Implement Azure AD integration for identity management
    - Use Azure DevOps for CI/CD pipelines
    - Monitor costs with Azure Cost Management

!!! warning "Security Considerations"
    - Enable Azure AD Multi-Factor Authentication
    - Use Azure Key Vault for secrets management
    - Implement Network Security Groups properly
    - Enable Azure Defender for all services
    - Regular security assessments with Azure Security Center
