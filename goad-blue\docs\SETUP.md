# GOAD-Blue Documentation Setup

## Quick Start

### Option 1: Minimal Setup (Recommended)
```bash
# Install minimal requirements (most reliable)
pip install -r requirements-docs-minimal.txt

# Serve documentation
mkdocs serve
```

### Option 2: Full Setup
```bash
# Try full requirements (some plugins may fail)
pip install -r requirements-docs.txt

# If that fails, use minimal setup
pip install -r requirements-docs-minimal.txt

# Serve documentation
mkdocs serve
```

### Option 3: Using Build Scripts
```bash
# Linux/macOS
./scripts/build-docs.sh install
./scripts/build-docs.sh serve

# Windows
scripts\build-docs.bat install
scripts\build-docs.bat serve
```

## What's Included

### Minimal Setup Includes:
- ✅ MkDocs with Material theme
- ✅ Mermaid diagrams (built into Material theme)
- ✅ Code highlighting and syntax support
- ✅ Admonitions and content tabs
- ✅ Search functionality
- ✅ All architecture diagrams working

### Full Setup Adds:
- 📅 Git revision dates
- 🗜️ HTML minification
- 🏷️ Tag support
- 📄 Additional plugins (if available)

## Troubleshooting

### Common Issues

**Plugin Installation Fails:**
```bash
# Use minimal setup instead
pip install mkdocs mkdocs-material pymdown-extensions
mkdocs serve
```

**MkDocs Command Not Found:**
```bash
# Make sure pip installed to correct location
python -m mkdocs serve
```

**Permission Errors:**
```bash
# Use user installation
pip install --user -r requirements-docs-minimal.txt
```

### Verification

Check that everything works:
```bash
# Validate configuration
mkdocs config-check

# Build documentation
mkdocs build

# Serve locally
mkdocs serve
```

Access documentation at: http://localhost:8000

## Features Working

All major features work with the minimal setup:

- 📊 **Architecture Diagrams** - All Mermaid diagrams render correctly
- 🎨 **Custom Styling** - GOAD-Blue theme and colors
- 🔍 **Search** - Full-text search functionality
- 📱 **Responsive Design** - Works on all devices
- 🌙 **Dark/Light Mode** - Theme switching
- 📋 **Code Blocks** - Syntax highlighting
- ⚠️ **Admonitions** - Info, warning, tip boxes
- 🔗 **Navigation** - Full site navigation

## Building for Production

```bash
# Build static site
mkdocs build

# Output will be in site/ directory
# Deploy site/ directory to your web server
```

## GitHub Pages Deployment

```bash
# Deploy to GitHub Pages
mkdocs gh-deploy --force
```

This will create/update the `gh-pages` branch with the built documentation.

## Development Workflow

1. **Edit Documentation**: Modify `.md` files in `docs/` directory
2. **Preview Changes**: Run `mkdocs serve` for live reload
3. **Test Build**: Run `mkdocs build` to ensure no errors
4. **Deploy**: Use `mkdocs gh-deploy` for GitHub Pages

## Need Help?

- Check [MkDocs Documentation](https://www.mkdocs.org/)
- Review [Material Theme Docs](https://squidfunk.github.io/mkdocs-material/)
- Create an issue in the GOAD-Blue repository
