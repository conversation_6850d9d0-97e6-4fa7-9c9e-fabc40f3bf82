- name: Install winlogbeat
  import_tasks: winlogbeat.yml

- name: Set winlogbeat config file
  win_copy:
    src: winlogbeat.yml
    dest: C:\ProgramData\chocolatey\lib\winlogbeat\tools\

# sysmon install (https://github.com/NVISOsecurity/ansible-sysmon)
- name: Create directory
  win_file:
    path: "{{ sysmon_install_location }}"
    state: directory
  register: result

- name: Get sysmon zip
  win_copy:
    src: "{{ sysmon_download_file }}{{ file_ext }}"
    dest: "{{ sysmon_install_location }}/{{ sysmon_download_file }}{{ file_ext }}"

- name: Unzip sysmon
  win_unzip:
    src: "{{ sysmon_install_location }}/{{ sysmon_download_file }}{{ file_ext }}"
    dest: "{{ sysmon_install_location }}"

- name: Copy sysmon config
  win_copy:
    src: sysmonconfig-export.xml
    dest: c:\sysmon\sysmonconfig-export.xml

# RUN sysmon
- name: check sysmon service
  win_service:
    name: sysmon64
  register: result
  failed_when: result is not defined
  ignore_errors: yes

- name: Run sysmon
  win_command: "{{ sysmon_install_location }}\\sysmon64.exe -accepteula -i {{ sysmon_install_location }}\\sysmonconfig-export.xml"
  args:
    chdir: "{{ sysmon_install_location }}"
  when: result.state is not defined or result.name is not defined

# RUN winlogbeat
- name: check winlogbeat service
  win_service:
    name: winlogbeat
  register: resultwlb
  failed_when: resultwlb is not defined
  ignore_errors: yes

- name: Reboot before launch setup
  win_reboot:
    reboot_timeout: 600
    post_reboot_delay: 100
  when: resultwlb.state is defined and resultwlb.state != 'running'

- name: Run winlogbeat setup
  win_command: "winlogbeat setup -e"
  args:
    chdir: "{{ winlogbeat_service.install_path_64 }}\\winlogbeat-{{ winlogbeat_service.version }}-windows-x86_64\\"
  when: resultwlb.state is defined and resultwlb.state != 'running'

# RUN winlogbeat
- name: check winlogbeat service
  win_service:
    name: winlogbeat
    start_mode: auto
    state: started
  when: resultwlb.state is defined and resultwlb.state != 'running'