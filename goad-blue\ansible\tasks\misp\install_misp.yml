---
# Install MISP application

- name: Create MISP directory
  file:
    path: /var/www/MISP
    state: directory
    owner: misp
    group: www-data
    mode: '0755'

- name: Clone MISP repository
  git:
    repo: "{{ misp.repository_url | default('https://github.com/MISP/MISP.git') }}"
    dest: /var/www/MISP
    version: "{{ misp.version | default('v2.4.170') }}"
    force: yes
  become_user: misp

- name: Initialize and update git submodules
  shell: |
    cd /var/www/MISP
    git submodule update --init --recursive
    git submodule foreach --recursive git config core.filemode false
  become_user: misp

- name: Set MISP directory permissions
  file:
    path: /var/www/MISP
    owner: misp
    group: www-data
    recurse: yes
    mode: '0755'

- name: Create MISP writable directories
  file:
    path: "{{ item }}"
    state: directory
    owner: www-data
    group: www-data
    mode: '0755'
  loop:
    - /var/www/MISP/app/files
    - /var/www/MISP/app/files/scripts/tmp
    - /var/www/MISP/app/Plugin/CakeResque/tmp
    - /var/www/MISP/app/tmp
    - /var/www/MISP/app/webroot/img/orgs
    - /var/www/MISP/app/webroot/img/custom

- name: Install PHP dependencies with Composer
  composer:
    command: install
    working_dir: /var/www/MISP/app
    no_dev: no
  become_user: misp
  environment:
    COMPOSER_ALLOW_SUPERUSER: "1"

- name: Copy MISP configuration files
  copy:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    owner: misp
    group: www-data
    mode: '0644'
    remote_src: yes
  loop:
    - src: /var/www/MISP/app/Config/bootstrap.default.php
      dest: /var/www/MISP/app/Config/bootstrap.php
    - src: /var/www/MISP/app/Config/database.default.php
      dest: /var/www/MISP/app/Config/database.php
    - src: /var/www/MISP/app/Config/core.default.php
      dest: /var/www/MISP/app/Config/core.php
    - src: /var/www/MISP/app/Config/config.default.php
      dest: /var/www/MISP/app/Config/config.php

- name: Generate MISP security salt
  shell: |
    cd /var/www/MISP
    openssl rand -base64 32
  register: misp_security_salt
  changed_when: false

- name: Configure MISP core settings
  lineinfile:
    path: /var/www/MISP/app/Config/core.php
    regexp: "{{ item.regexp }}"
    line: "{{ item.line }}"
    backup: yes
  loop:
    - regexp: "Configure::write\\('Security.salt'"
      line: "Configure::write('Security.salt', '{{ misp_security_salt.stdout }}');"
    - regexp: "Configure::write\\('Security.cipherSeed'"
      line: "Configure::write('Security.cipherSeed', '{{ (misp_security_salt.stdout | hash('sha256'))[:29] }}');"

- name: Install PyMISP
  pip:
    name: pymisp
    state: present
    executable: pip3

- name: Install MISP modules dependencies
  pip:
    name:
      - dnspython
      - requests
      - configparser
      - tornado
      - redis
      - base58
      - pyzmq
      - python-magic
      - lief
      - pydeep2
      - python-socketio
      - flask-socketio
    state: present
    executable: pip3

- name: Clone MISP modules
  git:
    repo: https://github.com/MISP/misp-modules.git
    dest: /var/www/MISP/misp-modules
    version: main
  become_user: misp

- name: Install MISP modules
  pip:
    requirements: /var/www/MISP/misp-modules/REQUIREMENTS
    state: present
    executable: pip3

- name: Install additional MISP modules dependencies
  pip:
    name:
      - maec
      - mixbox
      - cybox
      - python-stix
      - stix2-patterns
      - stix2
      - taxii2-client
      - pymisp
    state: present
    executable: pip3

- name: Create MISP modules systemd service
  template:
    src: misp-modules.service.j2
    dest: /etc/systemd/system/misp-modules.service
    mode: '0644'
  notify:
    - reload systemd
    - restart misp-modules

- name: Install MISP galaxy
  git:
    repo: https://github.com/MISP/misp-galaxy.git
    dest: /var/www/MISP/app/files/misp-galaxy
    version: main
  become_user: misp

- name: Install MISP taxonomies
  git:
    repo: https://github.com/MISP/misp-taxonomies.git
    dest: /var/www/MISP/app/files/taxonomies
    version: main
  become_user: misp

- name: Install MISP warning lists
  git:
    repo: https://github.com/MISP/misp-warninglists.git
    dest: /var/www/MISP/app/files/warninglists
    version: main
  become_user: misp

- name: Install MISP objects
  git:
    repo: https://github.com/MISP/misp-objects.git
    dest: /var/www/MISP/app/files/misp-objects
    version: main
  become_user: misp

- name: Set final MISP permissions
  file:
    path: /var/www/MISP
    owner: misp
    group: www-data
    recurse: yes

- name: Set specific directory permissions
  file:
    path: "{{ item }}"
    owner: www-data
    group: www-data
    mode: '0755'
    recurse: yes
  loop:
    - /var/www/MISP/app/files
    - /var/www/MISP/app/tmp
    - /var/www/MISP/app/webroot/img

- name: Create MISP log directory
  file:
    path: /var/log/misp
    state: directory
    owner: www-data
    group: www-data
    mode: '0755'

- name: Create MISP workers systemd service
  template:
    src: misp-workers.service.j2
    dest: /etc/systemd/system/misp-workers.service
    mode: '0644'
  notify:
    - reload systemd
    - restart misp-workers
