# Elasticsearch Configuration for GOAD-Blue
# Generated by Ansible

# Cluster configuration
cluster.name: {{ elasticsearch.cluster.name }}
node.name: {{ elasticsearch.node.name }}
node.roles: {{ elasticsearch.node.roles | to_json }}

# Network configuration
network.host: {{ elasticsearch.network.host }}
http.port: {{ elasticsearch.network.port }}
transport.port: 9300

# Discovery configuration
discovery.seed_hosts: ["127.0.0.1", "[::1]"]
cluster.initial_master_nodes: {{ elasticsearch.cluster.initial_master_nodes | to_json }}

# Path configuration
path.data: {{ elasticsearch.data_path }}
path.logs: {{ elasticsearch.logs_path }}

# Memory configuration
bootstrap.memory_lock: true

# Security configuration
{% if elasticsearch.security.enabled | default(false) %}
xpack.security.enabled: true
xpack.security.transport.ssl.enabled: true
xpack.security.transport.ssl.verification_mode: certificate
xpack.security.transport.ssl.keystore.path: elastic-certificates.p12
xpack.security.transport.ssl.truststore.path: elastic-certificates.p12
xpack.security.http.ssl.enabled: {{ elasticsearch.security.ssl_enabled | default(false) }}
xpack.security.http.ssl.keystore.path: elastic-certificates.p12
{% else %}
xpack.security.enabled: false
{% endif %}

# Monitoring configuration
xpack.monitoring.collection.enabled: true

# Index configuration
action.auto_create_index: +goad-blue-*,+.monitoring-*,+.watches,+.triggered_watches,+.watcher-history-*,+.ml-*

# Performance tuning
indices.memory.index_buffer_size: 10%
indices.memory.min_index_buffer_size: 48mb

# Thread pool configuration
thread_pool:
  write:
    size: {{ ansible_processor_vcpus }}
    queue_size: 200
  search:
    size: {{ (ansible_processor_vcpus * 1.5) | int }}
    queue_size: 1000

# Circuit breaker configuration
indices.breaker.total.limit: 70%
indices.breaker.request.limit: 40%
indices.breaker.fielddata.limit: 40%

# Logging configuration
logger.org.elasticsearch.deprecation: warn
