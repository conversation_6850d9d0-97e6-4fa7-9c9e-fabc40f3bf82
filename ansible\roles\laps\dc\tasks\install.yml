---
- name: "Create Laps OU if not exist"
  win_dsc:
    resource_name: ADOrganizationalUnit
    name: "{{ laps_path.split(',')[0].split('=')[1] }}"
    path:  "{{ ','.join(laps_path.split(',')[1:]) }}"

- name: Install LAPS Package on Servers
  ansible.windows.win_package:
    arguments: "ADDLOCAL=Management.PS,Management.ADMX ALLUSERS=1 /qn"
    path: https://download.microsoft.com/download/C/7/A/C7AAD914-A8A6-4904-88A1-29E657445D03/LAPS.x64.msi
    state: present
    creates_path: "%ProgramFiles%\\LAPS"
  register: pri_laps_install
  until: pri_laps_install is success
  retries: 3  # Try 3 times just in case it failed to download the URL
  delay: 1

- name: Reboot After Installing LAPS on Servers
  ansible.windows.win_reboot:
    reboot_timeout: 900
    post_reboot_delay: 100
  when: pri_laps_install.reboot_required

- name: Configure Password Properties
  win_ad_object:
    name: ms-Mcs-AdmPwd
    attributes:
      adminDescription: LAPS Password Attribute
      lDAPDisplayName: ms-Mcs-AdmPwd
      adminDisplayName: ms-Mcs-AdmPwd
      attributeId: 1.2.840.113556.1.8000.2554.50051.45980.28112.18903.35903.6685103.1224907.2.1
      attributeSyntax: '*******'  # String(IAS)
      omSyntax: 19  # String(Printable)
      isSingleValued: True
      systemOnly: False
      isMemberOfPartialAttributeSet: False
      searchFlags: 904  # RO,NV,CF,PR - http://www.frickelsoft.net/blog/?p=151
      showInAdvancedViewOnly: False
    context: schema
    type: attribute
    update_schema: True
  # privileges required to update the schema attributes
  register: passwordprop
  until: "passwordprop is not failed"
  retries: 3
  delay: 120
  become: yes
  become_method: runas
  become_user: SYSTEM

- name: Configure Password Expiry Time
  win_ad_object:
    name: ms-Mcs-AdmPwdExpirationTime
    attributes:
      adminDescription: LAPS Password Expiration Time Attribute
      lDAPDisplayName: ms-Mcs-AdmPwdExpirationTime
      adminDisplayName: ms-Mcs-AdmPwdExpirationTime
      attributeId: 1.2.840.113556.1.8000.2554.50051.45980.28112.18903.35903.6685103.1224907.2.2
      attributeSyntax: '********'  # LargeInteger
      omSyntax: 65  # LargeInteger
      isSingleValued: True
      systemOnly: False
      isMemberOfPartialAttributeSet: False
      searchFlags: 0
      showInAdvancedViewOnly: False
    context: schema
    type: attribute
    update_schema: True
  register: password_expire_time
  until: "password_expire_time is not failed"
  retries: 3
  delay: 120
  become: yes
  become_method: runas
  become_user: SYSTEM

- name: Add LAPS attributes to the Computer Attribute
  win_ad_object:
    name: Computer
    may_contain:
    - ms-Mcs-AdmPwd
    - ms-Mcs-AdmPwdExpirationTime
    context: schema
    update_schema: True
  register: add_laps_attribute
  until: "add_laps_attribute is not failed"
  retries: 3
  delay: 120
  become: yes
  become_method: runas
  become_user: SYSTEM

- name: "Apply DACL to OU Containers"
  win_ad_dacl:
    path: "{{laps_path}}"
    state: present
    aces:
    - rights:
      - ReadProperty
      - WriteProperty
      inheritance_type: Descendents
      inherited_object_type: Computer
      object_type: ms-Mcs-AdmPwdExpirationTime
      access: allow
      account: S-1-5-10  # NT AUTHORITY\SELF
    - rights: WriteProperty
      inheritance_type: Descendents
      inherited_object_type: Computer
      object_type: ms-Mcs-AdmPwd
      access: allow
      account: S-1-5-10

- name: Create LAPS GPO
  win_gpo:
    name: '{{ opt_laps_gpo_name }}'
    description: Setup by Ansible for LAPS
    state: present
  register: pri_laps_gpo

- name: Add LAPS extension to GPO
  win_ad_object:
    name: '{{ pri_laps_gpo.path }}'
    attributes:
      # [Registry:Admin Tool][AdmPwd:Admin Tool]
      gPCMachineExtensionNames: "[{35378EAC-683F-11D2-A89A-00C04FBBCFA2}{D02B1F72-3407-48AE-BA88-E8213C6761F1}]\
        [{D76B9641-3288-4F75-942D-087DE603E3EA}{D02B1F72-3407-48AE-BA88-E8213C6761F1}]"

- name: Configure Password Policy Settings on GPO
  win_gpo_reg:
    gpo: '{{ opt_laps_gpo_name }}'
    name: '{{ item.name }}'
    path: 'HKLM\Software\Policies\Microsoft Services\AdmPwd'
    state: present
    type: dword
    value: '{{ item.value }}'
  with_items:
  - name: PasswordComplexity
    value: 4
  - name: PasswordLength
    value: 14
  - name: PasswordAgeDays
    value: 30

- name: Configure Expiration Protection on GPO
  win_gpo_reg:
    gpo: '{{ opt_laps_gpo_name }}'
    name: PwdExpirationProtectionEnabled
    path: 'HKLM\Software\Policies\Microsoft Services\AdmPwd'
    state: present
    type: dword
    value: 1

- name: Remove Configuration for Expiration Protection on GPO
  win_gpo_reg:
    gpo: '{{ opt_laps_gpo_name }}'
    name: PwdExpirationProtectionEnabled
    path: 'HKLM\Software\Policies\Microsoft Services\AdmPwd'
    state: absent

- name: Configure Custom Admin Username Policy on GPO
  win_gpo_reg:
    gpo: '{{ opt_laps_gpo_name }}'
    name: AdminAccountName
    path: 'HKLM\Software\Policies\Microsoft Services\AdmPwd'
    state: present
    type: string

- name: Enable the GPO
  win_gpo_reg:
    gpo: '{{ opt_laps_gpo_name }}'
    name: AdmPwdEnabled
    path: 'HKLM\Software\Policies\Microsoft Services\AdmPwd'
    state: present
    type: dword
    value: 1

- name: Create Comment File for GPO
  ansible.windows.win_copy:
    src: ../files/comment.cmtx
    dest: C:\Windows\SYSVOL\domain\Policies\{{ '{' }}{{ pri_laps_gpo.id }}{{ '}' }}\Machine\comment.cmtx

- name: Ensure GPO is Linked
  win_gpo_link:
    name: '{{ opt_laps_gpo_name }}'
    target: '{{laps_path}}'
    state: present
    enforced: True
    enabled: True