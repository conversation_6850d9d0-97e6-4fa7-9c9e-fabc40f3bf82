# Installation Problems

This guide covers common issues encountered during GOAD-Blue installation and deployment.

## 🔧 Pre-Installation Issues

### **System Requirements Not Met**

**Problem:** Installation fails due to insufficient system resources.

**Symptoms:**
- Out of memory errors during installation
- Disk space warnings
- CPU timeout errors
- Virtualization not available

**Solutions:**

```bash
# Check system requirements
echo "=== System Requirements Check ==="
echo "CPU Cores: $(nproc)"
echo "Total RAM: $(free -h | grep Mem | awk '{print $2}')"
echo "Available Disk: $(df -h / | tail -1 | awk '{print $4}')"
echo "Virtualization: $(grep -E '(vmx|svm)' /proc/cpuinfo | wc -l)"

# Minimum requirements check
if [ $(nproc) -lt 8 ]; then
    echo "WARNING: Less than 8 CPU cores detected"
fi

if [ $(free -m | grep Mem | awk '{print $2}') -lt 32768 ]; then
    echo "WARNING: Less than 32GB RAM detected"
fi
```

**Resource Optimization:**

```yaml
# config/minimal-deployment.yml
deployment:
  mode: "minimal"
  components:
    siem:
      memory: "8GB"
      cpu: "4"
    monitoring:
      memory: "4GB"
      cpu: "2"
    endpoints:
      count: 3  # Reduced from default 5
```

### **Virtualization Platform Issues**

**Problem:** Virtualization platform not properly configured.

**VMware Issues:**
```bash
# Check VMware services
sudo systemctl status vmware
sudo systemctl status vmware-workstation-server

# Restart VMware services
sudo systemctl restart vmware
sudo /etc/init.d/vmware restart

# Verify VMware modules
sudo vmware-modconfig --console --install-all
```

**VirtualBox Issues:**
```bash
# Check VirtualBox installation
VBoxManage --version

# Verify kernel modules
sudo modprobe vboxdrv
sudo modprobe vboxnetflt
sudo modprobe vboxnetadp

# Reinstall VirtualBox modules
sudo /sbin/vboxconfig
```

**Proxmox Issues:**
```bash
# Check Proxmox VE status
systemctl status pve-cluster
systemctl status pvedaemon
systemctl status pveproxy

# Verify storage
pvesm status
```

## 📦 Dependency Installation Issues

### **Python Dependencies**

**Problem:** Python package installation failures.

**Common Errors:**
- `pip install` fails with permission errors
- Package conflicts
- Outdated pip version
- Missing system libraries

**Solutions:**

```bash
# Update pip and setuptools
python3 -m pip install --upgrade pip setuptools wheel

# Use virtual environment (recommended)
python3 -m venv goad-blue-env
source goad-blue-env/bin/activate
pip install -r requirements.txt

# Install with user flag (alternative)
pip install --user -r requirements.txt

# Fix permission issues
sudo chown -R $USER:$USER ~/.local/lib/python3.*/site-packages/
```

**System Dependencies:**

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y python3-dev python3-pip python3-venv \
    build-essential libssl-dev libffi-dev \
    git curl wget unzip

# CentOS/RHEL
sudo yum groupinstall -y "Development Tools"
sudo yum install -y python3-devel python3-pip \
    openssl-devel libffi-devel \
    git curl wget unzip

# Arch Linux
sudo pacman -S python python-pip python-virtualenv \
    base-devel openssl libffi \
    git curl wget unzip
```

### **Ansible Installation Issues**

**Problem:** Ansible installation or configuration problems.

**Solutions:**

```bash
# Install Ansible via pip (recommended)
pip install ansible ansible-core

# Install Ansible collections
ansible-galaxy collection install community.general
ansible-galaxy collection install ansible.posix
ansible-galaxy collection install community.windows

# Verify Ansible installation
ansible --version
ansible-config dump --only-changed

# Test Ansible connectivity
ansible localhost -m ping
```

**Ansible Configuration:**

```ini
# ansible.cfg
[defaults]
host_key_checking = False
timeout = 30
gathering = smart
fact_caching = memory

[ssh_connection]
ssh_args = -o ControlMaster=auto -o ControlPersist=60s
pipelining = True
```

## 🏗️ Infrastructure Deployment Issues

### **Packer Build Failures**

**Problem:** Packer fails to build VM images.

**Common Issues:**

```bash
# Check Packer version compatibility
packer version

# Validate Packer templates
packer validate templates/ubuntu-20.04.json
packer validate templates/windows-server-2019.json

# Build with debug output
PACKER_LOG=1 packer build -debug templates/ubuntu-20.04.json
```

**VMware Packer Issues:**

```json
{
  "builders": [{
    "type": "vmware-iso",
    "vmx_data": {
      "memsize": "4096",
      "numvcpus": "2",
      "virtualHW.version": "16"
    },
    "vmx_remove_ethernet_interfaces": true,
    "skip_compaction": false
  }]
}
```

**VirtualBox Packer Issues:**

```json
{
  "builders": [{
    "type": "virtualbox-iso",
    "vboxmanage": [
      ["modifyvm", "{{.Name}}", "--memory", "4096"],
      ["modifyvm", "{{.Name}}", "--cpus", "2"],
      ["modifyvm", "{{.Name}}", "--vram", "128"]
    ],
    "guest_additions_mode": "upload"
  }]
}
```

### **Terraform Deployment Issues**

**Problem:** Terraform fails to deploy infrastructure.

**Diagnostic Commands:**

```bash
# Initialize Terraform
terraform init

# Validate configuration
terraform validate

# Plan deployment
terraform plan -out=tfplan

# Apply with detailed logging
TF_LOG=DEBUG terraform apply tfplan
```

**Common Terraform Fixes:**

```bash
# Fix state lock issues
terraform force-unlock <lock-id>

# Refresh state
terraform refresh

# Import existing resources
terraform import module.goad_blue.resource_type.name resource_id

# Destroy and recreate
terraform destroy -target=module.problematic_resource
terraform apply
```

## 🔌 Network Configuration Issues

### **Network Isolation Problems**

**Problem:** VMs can access external networks when they shouldn't.

**Solutions:**

```bash
# Check network configuration
ip route show
iptables -L -n

# Verify VM network settings
# VMware:
vmrun listNetworkAdapters /path/to/vm.vmx

# VirtualBox:
VBoxManage showvminfo "VM Name" | grep NIC
```

**Network Isolation Script:**

```bash
#!/bin/bash
# Ensure proper network isolation

# Create isolated network
sudo ip link add name goad-isolated type bridge
sudo ip addr add *************/24 dev goad-isolated
sudo ip link set goad-isolated up

# Block external access
sudo iptables -I FORWARD -i goad-isolated -o eth0 -j DROP
sudo iptables -I FORWARD -i eth0 -o goad-isolated -j DROP

# Allow internal communication
sudo iptables -I FORWARD -i goad-isolated -o goad-isolated -j ACCEPT
```

### **DNS Resolution Issues**

**Problem:** VMs cannot resolve domain names properly.

**Solutions:**

```bash
# Configure internal DNS
echo "nameserver **************" > /etc/resolv.conf

# Test DNS resolution
nslookup sevenkingdoms.local **************
dig @************** sevenkingdoms.local

# Configure DNS forwarding
# In /etc/bind/named.conf.options:
forwarders {
    *******;
    *******;
};
```

## 🔐 Security Configuration Issues

### **Certificate Problems**

**Problem:** SSL/TLS certificate errors during installation.

**Solutions:**

```bash
# Generate self-signed certificates
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# Create CA and signed certificates
# Generate CA
openssl genrsa -out ca-key.pem 4096
openssl req -new -x509 -days 365 -key ca-key.pem -out ca.pem

# Generate server certificate
openssl genrsa -out server-key.pem 4096
openssl req -subj "/CN=goad-blue.local" -new -key server-key.pem -out server.csr
openssl x509 -req -days 365 -in server.csr -CA ca.pem -CAkey ca-key.pem -out server-cert.pem
```

### **Firewall Configuration**

**Problem:** Services cannot communicate due to firewall rules.

**Solutions:**

```bash
# Ubuntu/Debian UFW
sudo ufw allow 22/tcp      # SSH
sudo ufw allow 443/tcp     # HTTPS
sudo ufw allow 8000/tcp    # Splunk Web
sudo ufw allow 8089/tcp    # Splunk Management
sudo ufw allow 9997/tcp    # Splunk Indexing

# CentOS/RHEL Firewalld
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --permanent --add-port=8089/tcp
sudo firewall-cmd --permanent --add-port=9997/tcp
sudo firewall-cmd --reload

# Check open ports
sudo netstat -tlnp
sudo ss -tlnp
```

## 🔄 Installation Recovery

### **Clean Installation**

**Problem:** Installation is corrupted and needs to be restarted.

**Complete Cleanup:**

```bash
#!/bin/bash
# Complete GOAD-Blue cleanup script

echo "WARNING: This will remove all GOAD-Blue components!"
read -p "Are you sure? (yes/no): " confirm

if [ "$confirm" = "yes" ]; then
    # Stop all services
    python3 goad-blue.py stop_all
    
    # Remove VMs
    python3 goad-blue.py cleanup_vms
    
    # Remove Docker containers
    docker stop $(docker ps -aq --filter "label=goad-blue")
    docker rm $(docker ps -aq --filter "label=goad-blue")
    
    # Remove data directories
    sudo rm -rf /opt/goad-blue
    sudo rm -rf /var/log/goad-blue
    sudo rm -rf ~/.goad-blue
    
    # Remove virtual environment
    rm -rf goad-blue-env
    
    echo "Cleanup complete. Ready for fresh installation."
fi
```

### **Partial Recovery**

**Problem:** Only specific components need to be reinstalled.

**Component-Specific Cleanup:**

```bash
# Reinstall SIEM components only
python3 goad-blue.py uninstall --component siem
python3 goad-blue.py install --component siem

# Reinstall monitoring components
python3 goad-blue.py uninstall --component monitoring
python3 goad-blue.py install --component monitoring

# Redeploy agents only
python3 goad-blue.py redeploy_agents
```

## 📋 Installation Validation

### **Post-Installation Checks**

```bash
#!/bin/bash
# Comprehensive installation validation

echo "=== GOAD-Blue Installation Validation ==="

# Check core services
services=("splunk" "elasticsearch" "suricata" "velociraptor")
for service in "${services[@]}"; do
    if systemctl is-active --quiet $service; then
        echo "✓ $service is running"
    else
        echo "✗ $service is not running"
    fi
done

# Check network connectivity
endpoints=("**************:8000" "**************:8889" "**************2:443")
for endpoint in "${endpoints[@]}"; do
    if timeout 5 bash -c "</dev/tcp/${endpoint/:/ }"; then
        echo "✓ $endpoint is reachable"
    else
        echo "✗ $endpoint is not reachable"
    fi
done

# Check data ingestion
python3 goad-blue.py test_data_flow

# Generate test events
python3 goad-blue.py generate_test_events

echo "Validation complete. Check output for any issues."
```

---

!!! tip "Installation Best Practices"
    - Always use virtual environments for Python dependencies
    - Verify system requirements before starting installation
    - Keep installation logs for troubleshooting
    - Test network connectivity before deploying components
    - Use minimal deployments for resource-constrained environments

!!! warning "Common Pitfalls"
    - Don't skip system requirement checks
    - Ensure virtualization is properly enabled
    - Verify network isolation is working correctly
    - Check firewall rules after installation
    - Monitor disk space during installation

!!! info "Getting Help"
    - Check [Common Issues](common-issues.md) for quick fixes
    - Review [Component Issues](components.md) for specific problems
    - Join the community forum for installation support
    - Contact support for complex installation issues
