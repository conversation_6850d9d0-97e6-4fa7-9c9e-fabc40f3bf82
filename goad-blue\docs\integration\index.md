# GOAD Integration Guide

GOAD-Blue seamlessly integrates with existing GOAD (Game of Active Directory) environments to provide comprehensive blue team monitoring and detection capabilities for red team exercises.

## 🎯 Integration Overview

### **What is GOAD Integration?**

GOAD integration transforms your existing GOAD lab into a fully monitored environment where:
- **Red team activities** are automatically detected and logged
- **Blue team analysts** can practice real-time threat hunting
- **Security tools** provide comprehensive visibility into attack techniques
- **Training scenarios** simulate realistic SOC operations

```mermaid
graph TB
    subgraph "🎮 GOAD Environment (Existing)"
        DC1[🏰 Kingslanding<br/>sevenkingdoms.local<br/>*************]
        DC2[❄️ Winterfell<br/>north.sevenkingdoms.local<br/>*************]
        DC3[🐉 Meereen<br/>essos.local<br/>*************]
        SRV1[⚔️ Castelblack<br/>File Server<br/>*************]
        SRV2[🏛️ Braavos<br/>Web Server<br/>*************]
        WS1[🌹 Tyrell<br/>Workstation<br/>*************]
    end
    
    subgraph "🔗 Integration Layer"
        DISCOVERY[🔍 Auto-Discovery<br/>VM Detection & Mapping]
        AGENTS[🤖 Agent Deployment<br/>Monitoring Installation]
        NETWORK[🌐 Network Monitoring<br/>Traffic Analysis]
        LOGS[📋 Log Collection<br/>Event Forwarding]
    end
    
    subgraph "🛡️ GOAD-Blue Monitoring"
        SIEM[📊 SIEM Platform<br/>Splunk/Elastic<br/>**************]
        SO[🧅 Security Onion<br/>Network Monitoring<br/>192.168.100.70]
        VELO[🦖 Velociraptor<br/>Endpoint Visibility<br/>192.168.100.85]
        MISP[🧠 MISP<br/>Threat Intelligence<br/>192.168.100.30]
    end
    
    subgraph "👤 Blue Team Operations"
        ANALYST[👨‍💻 SOC Analyst<br/>Investigation & Response]
        HUNTER[🎯 Threat Hunter<br/>Proactive Detection]
        RESPONDER[🚨 Incident Responder<br/>Containment & Recovery]
    end
    
    %% GOAD to Integration
    DC1 --> DISCOVERY
    DC2 --> DISCOVERY
    DC3 --> DISCOVERY
    SRV1 --> DISCOVERY
    SRV2 --> DISCOVERY
    WS1 --> DISCOVERY
    
    %% Integration to Monitoring
    DISCOVERY --> AGENTS
    AGENTS --> NETWORK
    NETWORK --> LOGS
    
    %% Monitoring Data Flow
    LOGS --> SIEM
    NETWORK --> SO
    AGENTS --> VELO
    SIEM --> MISP
    
    %% Blue Team Access
    SIEM --> ANALYST
    SO --> HUNTER
    VELO --> RESPONDER
    MISP --> ANALYST
    
    classDef goad fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef integration fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef monitoring fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef blueteam fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class DC1,DC2,DC3,SRV1,SRV2,WS1 goad
    class DISCOVERY,AGENTS,NETWORK,LOGS integration
    class SIEM,SO,VELO,MISP monitoring
    class ANALYST,HUNTER,RESPONDER blueteam
```

## 🔍 Auto-Discovery Process

### **GOAD Environment Detection**

GOAD-Blue automatically discovers existing GOAD installations through multiple methods:

#### **1. Network Scanning**
```bash
# Scan for GOAD VMs on common networks
python3 goad-blue.py discover_goad --networks "************/24,10.0.0.0/24"

# Scan specific IP ranges
python3 goad-blue.py discover_goad --range "*************-*************"

# Auto-detect from virtualization platform
python3 goad-blue.py discover_goad --platform vmware --auto-scan
```

#### **2. DNS Resolution**
```bash
# Discover via DNS queries
nslookup sevenkingdoms.local
nslookup north.sevenkingdoms.local
nslookup essos.local

# Automated DNS discovery
python3 goad-blue.py discover_goad --dns-domains "sevenkingdoms.local,essos.local"
```

#### **3. Virtualization Platform Integration**
```bash
# VMware vSphere discovery
python3 goad-blue.py discover_goad --vmware-host "vcenter.local" --vmware-user "admin"

# Proxmox discovery
python3 goad-blue.py discover_goad --proxmox-host "*************" --proxmox-user "root@pam"

# VirtualBox discovery
python3 goad-blue.py discover_goad --virtualbox --local
```

### **Discovery Output Example**

```json
{
  "goad_environment": {
    "version": "v3.0",
    "provider": "vmware",
    "domains": [
      {
        "name": "sevenkingdoms.local",
        "domain_controllers": [
          {
            "hostname": "kingslanding",
            "ip": "*************",
            "os": "Windows Server 2019",
            "roles": ["DC", "DNS", "DHCP"]
          }
        ]
      },
      {
        "name": "north.sevenkingdoms.local",
        "domain_controllers": [
          {
            "hostname": "winterfell",
            "ip": "*************",
            "os": "Windows Server 2019",
            "roles": ["DC", "DNS"]
          }
        ]
      },
      {
        "name": "essos.local",
        "domain_controllers": [
          {
            "hostname": "meereen",
            "ip": "*************",
            "os": "Windows Server 2019",
            "roles": ["DC", "DNS"]
          }
        ]
      }
    ],
    "member_servers": [
      {
        "hostname": "castelblack",
        "ip": "*************",
        "domain": "north.sevenkingdoms.local",
        "os": "Windows Server 2019",
        "services": ["File Server", "Print Server"]
      },
      {
        "hostname": "braavos",
        "ip": "*************",
        "domain": "essos.local",
        "os": "Windows Server 2019",
        "services": ["IIS", "MSSQL"]
      }
    ],
    "workstations": [
      {
        "hostname": "tyrell",
        "ip": "*************",
        "domain": "sevenkingdoms.local",
        "os": "Windows 10",
        "users": ["margaery.tyrell", "olenna.tyrell"]
      }
    ]
  }
}
```

## 🤖 Agent Deployment

### **Monitoring Agent Installation**

GOAD-Blue deploys lightweight monitoring agents to all GOAD systems:

#### **Windows Systems (Domain Controllers, Servers, Workstations)**

**Sysmon Installation:**
```powershell
# Download and install Sysmon
Invoke-WebRequest -Uri "https://download.sysinternals.com/files/Sysmon.zip" -OutFile "C:\temp\Sysmon.zip"
Expand-Archive -Path "C:\temp\Sysmon.zip" -DestinationPath "C:\temp\Sysmon"

# Install with GOAD-Blue configuration
C:\temp\Sysmon\Sysmon64.exe -accepteula -i C:\goad-blue\configs\sysmon-config.xml

# Verify installation
Get-WinEvent -LogName "Microsoft-Windows-Sysmon/Operational" -MaxEvents 5
```

**Splunk Universal Forwarder:**
```powershell
# Download Splunk Universal Forwarder
$splunkUrl = "https://download.splunk.com/products/universalforwarder/releases/9.1.2/windows/splunkforwarder-9.1.2-b6b9c8185839-x64-release.msi"
Invoke-WebRequest -Uri $splunkUrl -OutFile "C:\temp\splunkforwarder.msi"

# Install Universal Forwarder
msiexec /i C:\temp\splunkforwarder.msi RECEIVING_INDEXER="**************:9997" WINEVENTLOG_APP_ENABLE=1 WINEVENTLOG_SEC_ENABLE=1 WINEVENTLOG_SYS_ENABLE=1 /quiet

# Configure inputs
$inputsConf = @"
[WinEventLog://Application]
disabled = false
index = goad_blue_windows

[WinEventLog://Security]
disabled = false
index = goad_blue_windows

[WinEventLog://System]
disabled = false
index = goad_blue_windows

[WinEventLog://Microsoft-Windows-Sysmon/Operational]
disabled = false
index = goad_blue_windows
renderXml = true
"@

$inputsConf | Out-File -FilePath "C:\Program Files\SplunkUniversalForwarder\etc\system\local\inputs.conf"

# Start service
Start-Service SplunkForwarder
```

**Velociraptor Agent:**
```powershell
# Download Velociraptor client
Invoke-WebRequest -Uri "https://github.com/Velocidex/velociraptor/releases/download/v0.7.0/velociraptor-v0.7.0-windows-amd64.exe" -OutFile "C:\temp\velociraptor.exe"

# Install as service with GOAD-Blue server configuration
C:\temp\velociraptor.exe --config C:\goad-blue\configs\velociraptor-client.yaml service install

# Start service
Start-Service Velociraptor
```

#### **Linux Systems (if any)**

**Elastic Beats Installation:**
```bash
# Install Filebeat
curl -L -O https://artifacts.elastic.co/downloads/beats/filebeat/filebeat-8.10.0-linux-x86_64.tar.gz
tar xzvf filebeat-8.10.0-linux-x86_64.tar.gz
sudo mv filebeat-8.10.0-linux-x86_64 /opt/filebeat

# Configure for GOAD-Blue
sudo tee /opt/filebeat/filebeat.yml > /dev/null <<EOF
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/*.log
    - /var/log/auth.log
    - /var/log/syslog

output.elasticsearch:
  hosts: ["**************:9200"]
  index: "goad-blue-linux-%{+yyyy.MM.dd}"

setup.template.name: "goad-blue-linux"
setup.template.pattern: "goad-blue-linux-*"
EOF

# Start Filebeat
sudo systemctl enable filebeat
sudo systemctl start filebeat
```

### **Automated Deployment Script**

```bash
#!/bin/bash
# scripts/deploy-goad-agents.sh

GOAD_BLUE_SERVER="**************"
GOAD_NETWORK="************/24"
DOMAIN_ADMIN_USER="sevenkingdoms\\administrator"
DOMAIN_ADMIN_PASS="Password123!"

deploy_windows_agents() {
    local target_ip=$1
    local hostname=$2
    
    echo "Deploying agents to $hostname ($target_ip)..."
    
    # Copy deployment scripts
    smbclient //$target_ip/C$ -U "$DOMAIN_ADMIN_USER%$DOMAIN_ADMIN_PASS" -c "
        put scripts/install-sysmon.ps1 temp\\install-sysmon.ps1
        put scripts/install-splunk-uf.ps1 temp\\install-splunk-uf.ps1
        put scripts/install-velociraptor.ps1 temp\\install-velociraptor.ps1
        put configs/sysmon-config.xml temp\\sysmon-config.xml
        put configs/velociraptor-client.yaml temp\\velociraptor-client.yaml
    "
    
    # Execute installation via WinRM
    winrs -r:$target_ip -u:"$DOMAIN_ADMIN_USER" -p:"$DOMAIN_ADMIN_PASS" "
        powershell.exe -ExecutionPolicy Bypass -File C:\\temp\\install-sysmon.ps1
        powershell.exe -ExecutionPolicy Bypass -File C:\\temp\\install-splunk-uf.ps1
        powershell.exe -ExecutionPolicy Bypass -File C:\\temp\\install-velociraptor.ps1
    "
    
    echo "Agent deployment completed for $hostname"
}

# Discover GOAD systems
GOAD_SYSTEMS=$(python3 goad-blue.py discover_goad --network $GOAD_NETWORK --format json | jq -r '.systems[] | "\(.ip) \(.hostname)"')

# Deploy agents to each system
while read -r ip hostname; do
    deploy_windows_agents "$ip" "$hostname"
done <<< "$GOAD_SYSTEMS"

echo "All agents deployed successfully"
```

## 🌐 Network Integration

### **Network Connectivity Setup**

#### **Bridge Configuration (VMware)**
```bash
# Create additional network adapter for GOAD-Blue monitoring
# Add vmnet2 for monitoring traffic

# Configure VMware virtual networks
sudo vmware-netcfg

# Add custom network for monitoring
vmnet2: Host-only network (*************/24)
vmnet8: NAT network (************/24) - GOAD existing
```

#### **Routing Configuration**
```bash
# Enable routing between GOAD and GOAD-Blue networks
sudo sysctl net.ipv4.ip_forward=1
echo 'net.ipv4.ip_forward=1' | sudo tee -a /etc/sysctl.conf

# Add static routes
sudo ip route add ************/24 via *************
sudo ip route add *************/24 via ************

# Make routes persistent
echo "************/24 via *************" | sudo tee -a /etc/network/interfaces
echo "*************/24 via ************" | sudo tee -a /etc/network/interfaces
```

#### **Firewall Configuration**
```bash
# Allow communication between networks
sudo ufw allow from ************/24 to *************/24
sudo ufw allow from *************/24 to ************/24

# Allow specific services
sudo ufw allow 9997/tcp  # Splunk forwarder
sudo ufw allow 8089/tcp  # Splunk management
sudo ufw allow 8000/tcp  # Velociraptor
sudo ufw allow 5985/tcp  # WinRM HTTP
sudo ufw allow 5986/tcp  # WinRM HTTPS
```

### **Traffic Mirroring Setup**

#### **VMware vSphere Port Mirroring**
```bash
# Configure port mirroring for Security Onion
# Create distributed port group for monitoring

# PowerCLI commands
Connect-VIServer -Server vcenter.local -User administrator -Password password

# Create monitoring port group
$vds = Get-VDSwitch -Name "DSwitch"
New-VDPortgroup -VDSwitch $vds -Name "GOAD-Monitoring" -VlanId 100

# Configure port mirroring
$spec = New-Object VMware.Vim.VMwareDVSPortSetting
$spec.UplinkTeamingPolicy = New-Object VMware.Vim.VmwareUplinkPortTeamingPolicy
$spec.UplinkTeamingPolicy.Policy = "loadbalance_srcid"

# Enable promiscuous mode for monitoring
$securityPolicy = New-Object VMware.Vim.DVSSecurityPolicy
$securityPolicy.AllowPromiscuous = $true
$securityPolicy.ForgedTransmits = $true
$securityPolicy.MacChanges = $true
```

#### **Proxmox Traffic Mirroring**
```bash
# Configure bridge for monitoring
auto vmbr-monitor
iface vmbr-monitor inet manual
    bridge-ports none
    bridge-stp off
    bridge-fd 0

# Enable promiscuous mode
ip link set vmbr-monitor promisc on

# Configure SPAN port equivalent
# Add monitoring interface to Security Onion VM
qm set 101 -net1 virtio,bridge=vmbr-monitor
```

## 📊 Data Flow Configuration

### **SIEM Integration**

#### **Splunk Configuration**
```conf
# indexes.conf
[goad_blue_windows]
homePath = $SPLUNK_DB/goad_blue_windows/db
coldPath = $SPLUNK_DB/goad_blue_windows/colddb
thawedPath = $SPLUNK_DB/goad_blue_windows/thaweddb
maxDataSize = 500MB
maxHotBuckets = 3
maxWarmDBCount = 20

[goad_blue_network]
homePath = $SPLUNK_DB/goad_blue_network/db
coldPath = $SPLUNK_DB/goad_blue_network/colddb
thawedPath = $SPLUNK_DB/goad_blue_network/thaweddb
maxDataSize = 1GB
maxHotBuckets = 5
maxWarmDBCount = 30

# inputs.conf
[splunktcp:9997]
disabled = false
index = goad_blue_windows

[udp:514]
disabled = false
index = goad_blue_network
sourcetype = syslog

# props.conf
[WinEventLog:Security]
SHOULD_LINEMERGE = false
TRUNCATE = 0
KV_MODE = xml
category = Windows Event Logs
description = Windows Security Event Log
pulldown_type = 1

[WinEventLog:Microsoft-Windows-Sysmon/Operational]
SHOULD_LINEMERGE = false
TRUNCATE = 0
KV_MODE = xml
category = Sysmon
description = Sysmon Event Log
pulldown_type = 1
```

#### **Elastic Stack Configuration**
```yaml
# logstash.conf
input {
  beats {
    port => 5044
  }
  
  tcp {
    port => 514
    type => "syslog"
  }
}

filter {
  if [agent][type] == "winlogbeat" {
    mutate {
      add_field => { "index_name" => "goad-blue-windows" }
    }
  }
  
  if [type] == "syslog" {
    mutate {
      add_field => { "index_name" => "goad-blue-network" }
    }
  }
  
  # Parse Windows Event Logs
  if [winlog][event_id] {
    mutate {
      add_field => { "event_code" => "%{[winlog][event_id]}" }
    }
  }
}

output {
  elasticsearch {
    hosts => ["localhost:9200"]
    index => "%{index_name}-%{+YYYY.MM.dd}"
  }
}
```

### **Security Onion Integration**

```bash
# Configure Security Onion for GOAD monitoring
sudo so-setup

# Configure Suricata rules for GOAD
sudo tee -a /opt/so/saltstack/local/salt/suricata/rules/goad-blue.rules > /dev/null <<EOF
# GOAD-Blue Custom Rules
alert tcp any any -> ************/24 88 (msg:"GOAD Kerberos Authentication"; sid:1000001; rev:1;)
alert tcp any any -> ************/24 389 (msg:"GOAD LDAP Query"; sid:1000002; rev:1;)
alert tcp any any -> ************/24 445 (msg:"GOAD SMB Activity"; sid:1000003; rev:1;)
alert tcp any any -> ************/24 3389 (msg:"GOAD RDP Connection"; sid:1000004; rev:1;)
EOF

# Update rules
sudo so-rule-update

# Configure Zeek for GOAD monitoring
sudo tee -a /opt/so/saltstack/local/salt/zeek/policy/goad-blue.zeek > /dev/null <<EOF
# GOAD-Blue monitoring script
@load base/protocols/conn
@load base/protocols/dns
@load base/protocols/http
@load base/protocols/ssl

# Monitor GOAD network
const goad_networks: set[subnet] = {
    ************/24,
};

event connection_established(c: connection) {
    if (c$id$orig_h in goad_networks || c$id$resp_h in goad_networks) {
        print fmt("GOAD Connection: %s -> %s:%s", c$id$orig_h, c$id$resp_h, c$id$resp_p);
    }
}
EOF

# Restart Zeek
sudo so-zeek-restart
```

## 🚀 Quick Integration

### **Automated Integration Script**

```bash
# Clone GOAD-Blue repository
git clone https://github.com/your-org/goad-blue.git
cd goad-blue

# Run automated integration
./scripts/integrate-goad.sh --domain-password "Password123!"

# Or with custom configuration
./scripts/integrate-goad.sh \
  --goad-network "10.0.0.0/24" \
  --domain-admin "mydomain\\admin" \
  --domain-password "MyPassword123!" \
  --splunk-indexer "**************"
```

### **Manual Integration Steps**

1. **Network Configuration** - Set up routing and firewall rules
2. **Agent Deployment** - Install monitoring agents on GOAD systems
3. **Data Flow Setup** - Configure log forwarding and processing
4. **Validation** - Test integration and data flow

## 📋 Integration Checklist

- [ ] GOAD environment is running and accessible
- [ ] GOAD-Blue platform is deployed and operational
- [ ] Network connectivity between environments established
- [ ] Domain administrator credentials available
- [ ] Monitoring agents deployed to all GOAD systems
- [ ] Data flow configured and validated
- [ ] Security monitoring rules activated
- [ ] Integration testing completed

---

!!! info "Integration Resources"
    For detailed integration procedures, see:

    - [Agent Deployment](agents.md) - Deploy monitoring agents to GOAD systems
    - [Network Configuration](network.md) - Configure network connectivity and routing
    - [Data Flow Setup](data-flow.md) - Configure log forwarding and processing
    - [Troubleshooting Integration](troubleshooting.md) - Resolve common integration issues

!!! tip "Best Practices"
    - Test integration in a development environment first
    - Document all network changes and configurations
    - Monitor resource usage during initial deployment
    - Validate data flow before production use
    - Use the automated integration script for consistent deployment

!!! warning "Security Considerations"
    - Use dedicated service accounts for agent deployment
    - Implement least privilege access principles
    - Monitor for any performance impact on GOAD systems
    - Ensure proper network segmentation and access controls
    - Regularly update monitoring agents and configurations
