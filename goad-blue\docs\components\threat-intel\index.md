# Threat Intelligence

Threat Intelligence is a critical component of GOAD-Blue, providing actionable intelligence about current and emerging threats to enhance detection, prevention, and response capabilities across the GOAD environment.

## 🎯 Overview

GOAD-Blue's threat intelligence capabilities enable security teams to collect, analyze, and operationalize threat data to improve security posture and incident response effectiveness.

```mermaid
graph TB
    subgraph "🧠 Threat Intelligence Architecture"
        COLLECTION[📡 Collection<br/>Multi-source Ingestion<br/>Automated Feeds]
        PROCESSING[⚙️ Processing<br/>Normalization<br/>Enrichment]
        ANALYSIS[🔍 Analysis<br/>Correlation<br/>Attribution]
        DISSEMINATION[📤 Dissemination<br/>Tool Integration<br/>Actionable Intelligence]
    end
    
    subgraph "📥 Intelligence Sources"
        COMMERCIAL[💼 Commercial Feeds<br/>Threat Intelligence Platforms<br/>Vendor Reports]
        OPEN_SOURCE[🌐 Open Source<br/>Public Feeds<br/>Research Reports]
        INTERNAL[🏠 Internal Sources<br/>Incident Analysis<br/>Malware Analysis]
        COMMUNITY[🤝 Community Sharing<br/>ISACs<br/>Peer Organizations]
    end
    
    subgraph "🔧 Processing Capabilities"
        NORMALIZATION[📋 Data Normalization<br/>STIX/TAXII<br/>Standard Formats]
        ENRICHMENT[📈 Enrichment<br/>Context Addition<br/>Relationship Mapping]
        VALIDATION[✅ Validation<br/>Quality Assessment<br/>Confidence Scoring]
        DEDUPLICATION[🔄 Deduplication<br/>Duplicate Removal<br/>Data Optimization]
    end
    
    subgraph "📊 Analysis Functions"
        CORRELATION[🔗 Correlation<br/>Pattern Recognition<br/>Campaign Tracking]
        ATTRIBUTION[🎯 Attribution<br/>Actor Profiling<br/>TTP Analysis]
        PREDICTION[🔮 Predictive Analysis<br/>Trend Identification<br/>Risk Assessment]
        CONTEXTUALIZATION[📚 Contextualization<br/>Environmental Relevance<br/>Priority Scoring]
    end
    
    subgraph "🎮 GOAD Integration"
        GOAD_INCIDENTS[🚨 GOAD Incidents<br/>Attack Analysis<br/>IOC Generation]
        GOAD_HUNTING[🔍 Threat Hunting<br/>Proactive Detection<br/>IOC Deployment]
        GOAD_RESPONSE[🛡️ Incident Response<br/>Attribution Support<br/>Countermeasures]
    end
    
    COMMERCIAL --> COLLECTION
    OPEN_SOURCE --> COLLECTION
    INTERNAL --> COLLECTION
    COMMUNITY --> COLLECTION
    
    COLLECTION --> PROCESSING
    PROCESSING --> NORMALIZATION
    PROCESSING --> ENRICHMENT
    PROCESSING --> VALIDATION
    PROCESSING --> DEDUPLICATION
    
    NORMALIZATION --> ANALYSIS
    ENRICHMENT --> ANALYSIS
    VALIDATION --> CORRELATION
    DEDUPLICATION --> ATTRIBUTION
    
    ANALYSIS --> CORRELATION
    ANALYSIS --> ATTRIBUTION
    ANALYSIS --> PREDICTION
    ANALYSIS --> CONTEXTUALIZATION
    
    CORRELATION --> DISSEMINATION
    ATTRIBUTION --> DISSEMINATION
    PREDICTION --> DISSEMINATION
    CONTEXTUALIZATION --> DISSEMINATION
    
    DISSEMINATION --> GOAD_INCIDENTS
    DISSEMINATION --> GOAD_HUNTING
    DISSEMINATION --> GOAD_RESPONSE
    
    classDef intelligence fill:#9c27b0,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef sources fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef processing fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef analysis fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef goad fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class COLLECTION,PROCESSING,ANALYSIS,DISSEMINATION intelligence
    class COMMERCIAL,OPEN_SOURCE,INTERNAL,COMMUNITY sources
    class NORMALIZATION,ENRICHMENT,VALIDATION,DEDUPLICATION processing
    class CORRELATION,ATTRIBUTION,PREDICTION,CONTEXTUALIZATION analysis
    class GOAD_INCIDENTS,GOAD_HUNTING,GOAD_RESPONSE goad
```

## 🔧 Core Components

### **[MISP](misp.md)**
Malware Information Sharing Platform serving as the central threat intelligence hub for GOAD-Blue, providing comprehensive IOC management and sharing capabilities.

**Key Features:**
- **IOC Management** - Centralized indicator storage and correlation
- **Event Tracking** - Incident and campaign management
- **Feed Integration** - Automated threat feed consumption
- **Community Sharing** - Collaborative intelligence sharing
- **API Integration** - Seamless tool integration

### **Threat Intelligence Feeds**
Automated collection and processing of threat intelligence from multiple sources to provide comprehensive coverage of the threat landscape.

**Feed Categories:**
- **Commercial Feeds** - Premium threat intelligence services
- **Open Source Feeds** - Public threat intelligence sources
- **Government Feeds** - Official threat advisories and alerts
- **Community Feeds** - Peer organization intelligence sharing

### **Intelligence Analysis Platform**
Advanced analytics capabilities for processing and analyzing threat intelligence to generate actionable insights.

**Analysis Capabilities:**
- **Pattern Recognition** - Automated threat pattern identification
- **Campaign Tracking** - Multi-stage attack campaign analysis
- **Actor Attribution** - Threat actor profiling and tracking
- **Predictive Analytics** - Emerging threat prediction

## 📊 Intelligence Collection

### **Automated Feed Management**

```python
# Threat intelligence feed management system
import requests
import json
import hashlib
from datetime import datetime, timedelta
import xml.etree.ElementTree as ET

class ThreatIntelligenceManager:
    def __init__(self, config):
        self.config = config
        self.feeds = []
        self.indicators = []
        
    def add_feed(self, feed_config):
        """Add new threat intelligence feed"""
        feed = {
            'name': feed_config['name'],
            'url': feed_config['url'],
            'format': feed_config.get('format', 'json'),
            'frequency': feed_config.get('frequency', 'daily'),
            'enabled': feed_config.get('enabled', True),
            'last_update': None,
            'api_key': feed_config.get('api_key'),
            'headers': feed_config.get('headers', {})
        }
        self.feeds.append(feed)
        
    def fetch_feed_data(self, feed):
        """Fetch data from threat intelligence feed"""
        try:
            headers = feed['headers'].copy()
            if feed.get('api_key'):
                headers['Authorization'] = f"Bearer {feed['api_key']}"
                
            response = requests.get(feed['url'], headers=headers, timeout=30)
            response.raise_for_status()
            
            if feed['format'] == 'json':
                return response.json()
            elif feed['format'] == 'xml':
                return ET.fromstring(response.text)
            elif feed['format'] == 'csv':
                return response.text.split('\n')
            else:
                return response.text
                
        except Exception as e:
            print(f"Error fetching feed {feed['name']}: {e}")
            return None
    
    def process_indicators(self, data, feed_name):
        """Process indicators from feed data"""
        indicators = []
        
        # Process based on feed format and structure
        if isinstance(data, dict):
            # JSON format processing
            if 'indicators' in data:
                for indicator in data['indicators']:
                    processed = self.normalize_indicator(indicator, feed_name)
                    if processed:
                        indicators.append(processed)
        
        return indicators
    
    def normalize_indicator(self, raw_indicator, source):
        """Normalize indicator to standard format"""
        try:
            indicator = {
                'value': raw_indicator.get('value', ''),
                'type': raw_indicator.get('type', ''),
                'confidence': raw_indicator.get('confidence', 50),
                'first_seen': raw_indicator.get('first_seen', datetime.now().isoformat()),
                'last_seen': raw_indicator.get('last_seen', datetime.now().isoformat()),
                'source': source,
                'tags': raw_indicator.get('tags', []),
                'description': raw_indicator.get('description', ''),
                'tlp': raw_indicator.get('tlp', 'white')
            }
            
            # Validate indicator
            if self.validate_indicator(indicator):
                return indicator
            else:
                return None
                
        except Exception as e:
            print(f"Error normalizing indicator: {e}")
            return None
    
    def validate_indicator(self, indicator):
        """Validate indicator quality and format"""
        # Check required fields
        if not indicator.get('value') or not indicator.get('type'):
            return False
            
        # Check confidence score
        if not (0 <= indicator.get('confidence', 0) <= 100):
            return False
            
        # Type-specific validation
        if indicator['type'] == 'ip':
            return self.validate_ip(indicator['value'])
        elif indicator['type'] == 'domain':
            return self.validate_domain(indicator['value'])
        elif indicator['type'] in ['md5', 'sha1', 'sha256']:
            return self.validate_hash(indicator['value'], indicator['type'])
            
        return True
    
    def validate_ip(self, ip):
        """Validate IP address format"""
        import ipaddress
        try:
            ipaddress.ip_address(ip)
            return True
        except:
            return False
    
    def validate_domain(self, domain):
        """Validate domain name format"""
        import re
        pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        return re.match(pattern, domain) is not None
    
    def validate_hash(self, hash_value, hash_type):
        """Validate hash format"""
        hash_lengths = {'md5': 32, 'sha1': 40, 'sha256': 64}
        expected_length = hash_lengths.get(hash_type)
        
        if not expected_length:
            return False
            
        return len(hash_value) == expected_length and all(c in '0123456789abcdefABCDEF' for c in hash_value)
    
    def update_all_feeds(self):
        """Update all enabled feeds"""
        for feed in self.feeds:
            if feed['enabled']:
                print(f"Updating feed: {feed['name']}")
                data = self.fetch_feed_data(feed)
                
                if data:
                    indicators = self.process_indicators(data, feed['name'])
                    self.indicators.extend(indicators)
                    feed['last_update'] = datetime.now().isoformat()
                    print(f"Added {len(indicators)} indicators from {feed['name']}")
    
    def export_indicators(self, format='json'):
        """Export indicators in specified format"""
        if format == 'json':
            return json.dumps(self.indicators, indent=2)
        elif format == 'csv':
            import csv
            import io
            
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=['value', 'type', 'confidence', 'source', 'description'])
            writer.writeheader()
            writer.writerows(self.indicators)
            return output.getvalue()
        elif format == 'stix':
            return self.export_stix()
    
    def export_stix(self):
        """Export indicators in STIX format"""
        # Simplified STIX export
        stix_bundle = {
            "type": "bundle",
            "id": f"bundle--{hashlib.uuid4()}",
            "spec_version": "2.1",
            "objects": []
        }
        
        for indicator in self.indicators:
            stix_indicator = {
                "type": "indicator",
                "id": f"indicator--{hashlib.uuid4()}",
                "created": indicator['first_seen'],
                "modified": indicator['last_seen'],
                "pattern": f"[{indicator['type']}:value = '{indicator['value']}']",
                "labels": ["malicious-activity"],
                "confidence": indicator['confidence']
            }
            stix_bundle["objects"].append(stix_indicator)
        
        return json.dumps(stix_bundle, indent=2)

# Example usage
config = {
    'misp_url': 'https://misp.goad-blue.local',
    'api_key': 'your-api-key'
}

tim = ThreatIntelligenceManager(config)

# Add threat intelligence feeds
tim.add_feed({
    'name': 'Emerging Threats',
    'url': 'https://rules.emergingthreats.net/blockrules/compromised-ips.txt',
    'format': 'csv'
})

tim.add_feed({
    'name': 'Abuse.ch URLhaus',
    'url': 'https://urlhaus.abuse.ch/downloads/json/',
    'format': 'json'
})

# Update feeds and process indicators
tim.update_all_feeds()

# Export indicators
json_export = tim.export_indicators('json')
stix_export = tim.export_indicators('stix')
```

## 🔍 Intelligence Analysis

### **Threat Actor Profiling**

```python
# Threat actor profiling and attribution system
class ThreatActorProfiler:
    def __init__(self):
        self.actors = {}
        self.campaigns = {}
        self.ttps = {}
        
    def create_actor_profile(self, actor_data):
        """Create comprehensive threat actor profile"""
        profile = {
            'name': actor_data['name'],
            'aliases': actor_data.get('aliases', []),
            'attribution_confidence': actor_data.get('confidence', 'low'),
            'motivation': actor_data.get('motivation', 'unknown'),
            'sophistication': actor_data.get('sophistication', 'unknown'),
            'origin': actor_data.get('origin', 'unknown'),
            'first_observed': actor_data.get('first_observed'),
            'last_observed': actor_data.get('last_observed'),
            'targets': actor_data.get('targets', []),
            'ttps': actor_data.get('ttps', []),
            'tools': actor_data.get('tools', []),
            'infrastructure': actor_data.get('infrastructure', []),
            'campaigns': actor_data.get('campaigns', [])
        }
        
        self.actors[actor_data['name']] = profile
        return profile
    
    def analyze_campaign(self, campaign_data):
        """Analyze threat campaign for attribution"""
        analysis = {
            'campaign_id': campaign_data['id'],
            'timeline': self.build_timeline(campaign_data),
            'infrastructure_analysis': self.analyze_infrastructure(campaign_data),
            'ttp_analysis': self.analyze_ttps(campaign_data),
            'tool_analysis': self.analyze_tools(campaign_data),
            'attribution_assessment': self.assess_attribution(campaign_data)
        }
        
        return analysis
    
    def build_timeline(self, campaign_data):
        """Build campaign timeline"""
        events = []
        
        for event in campaign_data.get('events', []):
            events.append({
                'timestamp': event['timestamp'],
                'event_type': event['type'],
                'description': event['description'],
                'confidence': event.get('confidence', 'medium')
            })
        
        return sorted(events, key=lambda x: x['timestamp'])
    
    def analyze_infrastructure(self, campaign_data):
        """Analyze campaign infrastructure patterns"""
        infrastructure = {
            'domains': [],
            'ip_addresses': [],
            'certificates': [],
            'patterns': []
        }
        
        # Extract infrastructure indicators
        for indicator in campaign_data.get('indicators', []):
            if indicator['type'] == 'domain':
                infrastructure['domains'].append(indicator['value'])
            elif indicator['type'] == 'ip':
                infrastructure['ip_addresses'].append(indicator['value'])
        
        # Identify patterns
        infrastructure['patterns'] = self.identify_infrastructure_patterns(infrastructure)
        
        return infrastructure
    
    def identify_infrastructure_patterns(self, infrastructure):
        """Identify patterns in threat actor infrastructure"""
        patterns = []
        
        # Domain generation algorithm patterns
        domains = infrastructure['domains']
        if len(domains) > 5:
            # Check for DGA patterns
            if self.detect_dga_pattern(domains):
                patterns.append({
                    'type': 'DGA',
                    'description': 'Domain Generation Algorithm detected',
                    'confidence': 'high'
                })
        
        # IP address patterns
        ips = infrastructure['ip_addresses']
        if len(ips) > 3:
            # Check for subnet clustering
            subnets = self.analyze_ip_clustering(ips)
            if subnets:
                patterns.append({
                    'type': 'IP_CLUSTERING',
                    'description': f'IP addresses clustered in subnets: {subnets}',
                    'confidence': 'medium'
                })
        
        return patterns
    
    def detect_dga_pattern(self, domains):
        """Detect domain generation algorithm patterns"""
        # Simplified DGA detection
        import re
        
        # Check for random-looking domains
        random_pattern = re.compile(r'^[a-z]{8,20}\.(com|net|org)$')
        random_domains = [d for d in domains if random_pattern.match(d)]
        
        return len(random_domains) > len(domains) * 0.7
    
    def analyze_ip_clustering(self, ips):
        """Analyze IP address clustering patterns"""
        import ipaddress
        
        subnets = {}
        for ip in ips:
            try:
                network = ipaddress.ip_network(f"{ip}/24", strict=False)
                subnet = str(network)
                if subnet not in subnets:
                    subnets[subnet] = []
                subnets[subnet].append(ip)
            except:
                continue
        
        # Return subnets with multiple IPs
        return {k: v for k, v in subnets.items() if len(v) > 1}
```

## 📈 Intelligence Metrics

### **Key Performance Indicators**

```yaml
# Threat intelligence KPIs for GOAD-Blue
threat_intelligence_kpis:
  collection_metrics:
    - feed_availability_percentage
    - indicator_ingestion_rate
    - data_freshness_score
    - source_diversity_index
    
  quality_metrics:
    - false_positive_rate
    - indicator_confidence_average
    - attribution_accuracy
    - timeliness_score
    
  utilization_metrics:
    - detection_hit_rate
    - hunting_success_rate
    - response_time_improvement
    - analyst_productivity_gain
    
  operational_metrics:
    - feed_processing_time
    - system_availability
    - storage_efficiency
    - api_response_time
```

---

!!! tip "Threat Intelligence Best Practices"
    - Maintain diverse intelligence sources for comprehensive coverage
    - Implement quality controls and validation processes
    - Focus on actionable intelligence relevant to your environment
    - Regularly assess and tune intelligence feeds
    - Integrate intelligence into all security operations

!!! warning "Data Quality Considerations"
    Threat intelligence quality varies significantly between sources. Implement proper validation, confidence scoring, and false positive management.

!!! info "Standards and Frameworks"
    - STIX/TAXII for standardized intelligence sharing
    - MITRE ATT&CK for TTP classification
    - TLP (Traffic Light Protocol) for sharing guidelines
    - Diamond Model for threat analysis
