# Integration Problems

This guide covers troubleshooting GOAD-Blue integration with existing GOAD environments, agent deployment, and data flow issues.

## 🔍 GOAD Discovery Issues

### **Cannot Find GOAD Installation**

**Problem:** GOAD-<PERSON> cannot automatically discover existing GOAD environment.

**Symptoms:**
- "GOAD installation not found" error
- Discovery process fails
- Empty GOAD workspace detected

**Diagnostic Commands:**
```bash
# Check GOAD installation
ls -la /path/to/goad/
cat /path/to/goad/workspace/goad.yml

# Verify GOAD status
cd /path/to/goad
python3 goad.py status

# Check GOAD-Blue discovery
python3 goad-blue.py --discover-goad --verbose
```

**Solutions:**
```bash
# Manually specify GOAD path
python3 goad-blue.py --goad-path /path/to/goad

# Update GOAD-Blue configuration
cat > config/goad-integration.yml << EOF
goad:
  installation_path: "/path/to/goad"
  workspace: "/path/to/goad/workspace"
  provider: "vmware"  # or virtualbox, proxmox
  domain: "sevenkingdoms.local"
EOF

# Force rediscovery
python3 goad-blue.py --rediscover-goad --force
```

### **GOAD Version Compatibility**

**Problem:** GOAD version incompatible with GOAD-Blue.

**Version Check:**
```bash
# Check GOAD version
cd /path/to/goad
git describe --tags
git log --oneline -1

# Check GOAD-Blue compatibility
python3 goad-blue.py --check-compatibility
```

**Solutions:**
```bash
# Update GOAD to compatible version
cd /path/to/goad
git fetch --tags
git checkout v2.0.0  # or latest compatible version

# Update GOAD-Blue
cd /path/to/goad-blue
git pull origin main
pip install -r requirements.txt --upgrade
```

## 🔗 Network Integration Issues

### **Cannot Reach GOAD VMs**

**Problem:** GOAD-Blue cannot connect to GOAD virtual machines.

**Network Troubleshooting:**
```bash
# Test connectivity to GOAD VMs
ping *************   # Domain Controller
ping *************   # Member Server
ping *************   # Workstation

# Check routing
ip route show | grep 192.168.56
traceroute *************

# Verify network interfaces
ip addr show
```

**Network Configuration Fixes:**
```bash
# Add route to GOAD network
sudo ip route add ************/24 via *************

# Configure bridge networking
sudo brctl addbr goad-bridge
sudo brctl addif goad-bridge eth1
sudo ip addr add *************0/24 dev goad-bridge
sudo ip link set goad-bridge up

# VMware network configuration
vmrun listNetworkAdapters
vmrun setNetworkAdapter /path/to/vm.vmx 0 bridged eth0
```

### **DNS Resolution Problems**

**Problem:** Cannot resolve GOAD domain names.

**DNS Configuration:**
```bash
# Test DNS resolution
nslookup sevenkingdoms.local *************
dig @************* sevenkingdoms.local

# Configure DNS forwarding
echo "nameserver *************" >> /etc/resolv.conf
echo "search sevenkingdoms.local" >> /etc/resolv.conf

# Test domain resolution
nslookup winterfell.sevenkingdoms.local
nslookup kingslanding.sevenkingdoms.local
```

**DNS Server Configuration:**
```bash
# Configure BIND for GOAD integration
cat > /etc/bind/named.conf.local << EOF
zone "sevenkingdoms.local" {
    type forward;
    forwarders { *************; };
};
EOF

sudo systemctl restart bind9
```

## 🤖 Agent Deployment Issues

### **SSH/WinRM Connection Failures**

**Problem:** Cannot connect to GOAD VMs for agent deployment.

**SSH Troubleshooting:**
```bash
# Test SSH connectivity
ssh -v vagrant@*************
ssh -i ~/.ssh/goad_key vagrant@*************

# Check SSH configuration
cat ~/.ssh/config
ls -la ~/.ssh/

# Generate new SSH keys if needed
ssh-keygen -t rsa -b 4096 -f ~/.ssh/goad_key
ssh-copy-id -i ~/.ssh/goad_key vagrant@*************
```

**WinRM Troubleshooting:**
```bash
# Test WinRM connectivity
python3 -c "
import winrm
session = winrm.Session('*************', auth=('vagrant', 'vagrant'))
result = session.run_cmd('ipconfig')
print(result.std_out.decode())
"

# Configure WinRM on Windows VMs
# Run on Windows VM:
winrm quickconfig -y
winrm set winrm/config/service/auth '@{Basic="true"}'
winrm set winrm/config/service '@{AllowUnencrypted="true"}'
```

**Credential Management:**
```yaml
# credentials.yml
goad_credentials:
  linux:
    username: "vagrant"
    password: "vagrant"
    ssh_key: "~/.ssh/goad_key"
  
  windows:
    username: "vagrant"
    password: "vagrant"
    auth_method: "basic"
```

### **Ansible Deployment Failures**

**Problem:** Ansible playbooks fail during agent deployment.

**Ansible Troubleshooting:**
```bash
# Test Ansible connectivity
ansible all -i inventory/goad_hosts.yml -m ping

# Run with verbose output
ansible-playbook -i inventory/goad_hosts.yml deploy-agents.yml -vvv

# Check Ansible inventory
cat inventory/goad_hosts.yml
ansible-inventory -i inventory/goad_hosts.yml --list
```

**Inventory Configuration:**
```yaml
# inventory/goad_hosts.yml
all:
  children:
    windows:
      hosts:
        winterfell:
          ansible_host: *************
          ansible_user: vagrant
          ansible_password: vagrant
          ansible_connection: winrm
          ansible_winrm_transport: basic
          ansible_winrm_server_cert_validation: ignore
    
    linux:
      hosts:
        kingslanding:
          ansible_host: *************
          ansible_user: vagrant
          ansible_ssh_private_key_file: ~/.ssh/goad_key
```

### **Agent Installation Failures**

**Problem:** Monitoring agents fail to install on GOAD VMs.

**Windows Agent Issues:**
```powershell
# Check Windows agent installation
Get-Service | Where-Object {$_.Name -like "*velociraptor*"}
Get-Process | Where-Object {$_.Name -like "*splunk*"}

# Manual agent installation
# Download agent
Invoke-WebRequest -Uri "https://**************:8000/downloads/velociraptor.exe" -OutFile "C:\temp\velociraptor.exe"

# Install as service
C:\temp\velociraptor.exe service install --config C:\temp\client.config.yaml
```

**Linux Agent Issues:**
```bash
# Check Linux agent status
systemctl status velociraptor-client
systemctl status splunkforwarder

# Manual agent installation
wget https://**************:8000/downloads/velociraptor-linux
chmod +x velociraptor-linux
sudo ./velociraptor-linux service install --config /etc/velociraptor/client.config.yaml
```

## 📊 Data Flow Issues

### **No Logs from GOAD VMs**

**Problem:** GOAD VM logs not appearing in SIEM.

**Log Flow Verification:**
```bash
# Check log generation on GOAD VMs
# Windows:
Get-WinEvent -LogName Security -MaxEvents 10
Get-WinEvent -LogName System -MaxEvents 10

# Linux:
tail -f /var/log/syslog
tail -f /var/log/auth.log

# Check forwarder status
# Windows:
& "C:\Program Files\SplunkUniversalForwarder\bin\splunk.exe" list forward-server

# Linux:
sudo /opt/splunkforwarder/bin/splunk list forward-server
```

**Forwarder Configuration:**
```conf
# inputs.conf for Windows
[WinEventLog://Security]
index = goad_blue_windows
disabled = false

[WinEventLog://System]
index = goad_blue_windows
disabled = false

[WinEventLog://Application]
index = goad_blue_windows
disabled = false

# inputs.conf for Linux
[monitor:///var/log/syslog]
index = goad_blue_linux
sourcetype = syslog

[monitor:///var/log/auth.log]
index = goad_blue_linux
sourcetype = linux_secure
```

### **Network Traffic Not Captured**

**Problem:** Network traffic from GOAD not being monitored.

**Traffic Capture Setup:**
```bash
# Configure network monitoring
# Set up SPAN port or network TAP

# VMware: Configure port mirroring
vmrun setNetworkAdapter /path/to/sensor.vmx 1 custom vmnet8

# Check traffic capture
sudo tcpdump -i eth1 host *************
sudo tcpdump -i eth1 net ************/24

# Verify Suricata is seeing traffic
sudo tail -f /var/log/suricata/eve.json | grep 192.168.56
```

**Network Monitoring Configuration:**
```yaml
# suricata.yaml
af-packet:
  - interface: eth1
    cluster-id: 99
    cluster-type: cluster_flow
    defrag: yes

vars:
  address-groups:
    HOME_NET: "[************/24,*************/24]"
    EXTERNAL_NET: "!$HOME_NET"
```

## 🔧 Integration Recovery

### **Reset Integration**

**Problem:** Integration is corrupted and needs to be reset.

**Complete Reset:**
```bash
#!/bin/bash
# Reset GOAD-Blue integration

echo "Resetting GOAD-Blue integration..."

# Stop all services
python3 goad-blue.py stop_all

# Remove agents from GOAD VMs
ansible-playbook -i inventory/goad_hosts.yml remove-agents.yml

# Clear integration data
rm -rf data/integration/
rm -rf logs/integration/

# Rediscover GOAD environment
python3 goad-blue.py --rediscover-goad --force

# Redeploy agents
python3 goad-blue.py deploy_agents

echo "Integration reset complete"
```

### **Partial Recovery**

**Problem:** Only specific integration components need fixing.

**Component-Specific Recovery:**
```bash
# Redeploy agents only
python3 goad-blue.py redeploy_agents --target windows
python3 goad-blue.py redeploy_agents --target linux

# Reset network monitoring
python3 goad-blue.py reset_network_monitoring

# Reconfigure data forwarding
python3 goad-blue.py reconfigure_forwarding
```

## 📋 Integration Validation

### **Comprehensive Integration Test**

```bash
#!/bin/bash
# Comprehensive integration validation

echo "=== GOAD-Blue Integration Validation ==="

# Test GOAD connectivity
echo "Testing GOAD VM connectivity..."
for ip in ************* ************* *************; do
    if ping -c 1 $ip > /dev/null 2>&1; then
        echo "✓ $ip reachable"
    else
        echo "✗ $ip unreachable"
    fi
done

# Test agent connectivity
echo "Testing agent connectivity..."
python3 goad-blue.py test_agents

# Test data flow
echo "Testing data flow..."
python3 goad-blue.py test_data_flow

# Generate test events
echo "Generating test events..."
python3 goad-blue.py generate_test_events --target goad

# Verify events in SIEM
echo "Verifying events in SIEM..."
sleep 30
python3 goad-blue.py verify_events --source goad

echo "Integration validation complete"
```

### **Monitoring Integration Health**

```python
#!/usr/bin/env python3
# Integration health monitoring

import subprocess
import json
import time
from datetime import datetime

def check_goad_connectivity():
    """Check connectivity to GOAD VMs"""
    goad_ips = ['*************', '*************', '*************']
    results = {}
    
    for ip in goad_ips:
        try:
            result = subprocess.run(['ping', '-c', '1', ip], 
                                  capture_output=True, timeout=5)
            results[ip] = result.returncode == 0
        except subprocess.TimeoutExpired:
            results[ip] = False
    
    return results

def check_agent_status():
    """Check status of deployed agents"""
    try:
        result = subprocess.run(['python3', 'goad-blue.py', 'check_agents'], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

def check_data_flow():
    """Check if data is flowing from GOAD to SIEM"""
    try:
        result = subprocess.run(['python3', 'goad-blue.py', 'test_data_flow'], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

def main():
    health_data = {
        'timestamp': datetime.now().isoformat(),
        'goad_connectivity': check_goad_connectivity(),
        'agent_status': check_agent_status(),
        'data_flow': check_data_flow()
    }
    
    # Calculate overall health
    connectivity_ok = all(health_data['goad_connectivity'].values())
    overall_health = connectivity_ok and health_data['agent_status'] and health_data['data_flow']
    
    health_data['overall_health'] = overall_health
    
    # Save results
    with open('/var/log/goad-blue-integration-health.json', 'w') as f:
        json.dump(health_data, f, indent=2)
    
    # Print summary
    status = "✓ Healthy" if overall_health else "✗ Unhealthy"
    print(f"Integration Status: {status}")
    
    return 0 if overall_health else 1

if __name__ == "__main__":
    exit(main())
```

---

!!! tip "Integration Best Practices"
    - Always verify GOAD is running before integration
    - Test network connectivity before deploying agents
    - Use consistent credentials across all GOAD VMs
    - Monitor integration health continuously
    - Keep integration logs for troubleshooting

!!! warning "Common Integration Pitfalls"
    - Network isolation can block agent communication
    - Firewall rules may prevent data flow
    - Credential changes can break agent connectivity
    - GOAD updates may require re-integration
    - Time synchronization issues can affect log correlation

!!! info "Advanced Integration"
    - Consider using configuration management for large deployments
    - Implement automated agent health monitoring
    - Use centralized credential management
    - Set up integration monitoring and alerting
    - Document custom integration procedures
