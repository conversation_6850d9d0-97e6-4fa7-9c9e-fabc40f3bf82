# AWS Configuration

This guide covers GOAD-Blue deployment and configuration on Amazon Web Services (AWS), including VPC setup, EC2 instances, and security configurations.

## ☁️ AWS Architecture Overview

GOAD-Blue on AWS leverages cloud-native services for scalability, reliability, and cost optimization.

```mermaid
graph TB
    subgraph "🌐 AWS Global Infrastructure"
        REGION[🌍 AWS Region<br/>us-east-1<br/>Primary Region]
        AZ1[📍 Availability Zone 1a<br/>Primary AZ<br/>Main Components]
        AZ2[📍 Availability Zone 1b<br/>Secondary AZ<br/>HA Components]
    end
    
    subgraph "🔒 VPC & Networking"
        VPC[🏠 GOAD-Blue VPC<br/>10.0.0.0/16<br/>Isolated Network]
        IGW[🌐 Internet Gateway<br/>Internet Access<br/>NAT Gateway]
        
        PUB_SUBNET[🌐 Public Subnet<br/>********/24<br/>Load Balancers]
        PRIV_SUBNET[🔒 Private Subnet<br/>********/24<br/>Application Tier]
        DB_SUBNET[🗄️ Database Subnet<br/>10.0.3.0/24<br/>Data Tier]
    end
    
    subgraph "🖥️ Compute Resources"
        ALB[⚖️ Application Load Balancer<br/>Traffic Distribution<br/>SSL Termination]
        SPLUNK[📊 Splunk Cluster<br/>c5.2xlarge<br/>Auto Scaling]
        SO[🧅 Security Onion<br/>c5.4xlarge<br/>Network Monitoring]
        VELO[🦖 Velociraptor<br/>t3.large<br/>Endpoint Detection]
    end
    
    subgraph "🗄️ Storage & Data"
        EBS[💾 EBS Volumes<br/>gp3 & io2<br/>Encrypted Storage]
        S3[🪣 S3 Buckets<br/>Log Archive<br/>Backup Storage]
        RDS[🗄️ RDS Database<br/>PostgreSQL<br/>Multi-AZ]
    end
    
    subgraph "🔍 Monitoring & Security"
        CLOUDWATCH[📊 CloudWatch<br/>Metrics & Logs<br/>Alerting]
        CLOUDTRAIL[🔍 CloudTrail<br/>API Logging<br/>Audit Trail]
        GUARDDUTY[🛡️ GuardDuty<br/>Threat Detection<br/>ML-based Security]
    end
    
    REGION --> AZ1
    REGION --> AZ2
    
    VPC --> IGW
    VPC --> PUB_SUBNET
    VPC --> PRIV_SUBNET
    VPC --> DB_SUBNET
    
    PUB_SUBNET --> ALB
    PRIV_SUBNET --> SPLUNK
    PRIV_SUBNET --> SO
    PRIV_SUBNET --> VELO
    
    SPLUNK --> EBS
    SO --> EBS
    VELO --> EBS
    
    SPLUNK --> S3
    SO --> S3
    
    SPLUNK --> RDS
    VELO --> RDS
    
    SPLUNK --> CLOUDWATCH
    SO --> CLOUDWATCH
    VELO --> CLOUDWATCH
    
    classDef aws fill:#ff9900,stroke:#232f3e,stroke-width:2px,color:#fff
    classDef network fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef compute fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef storage fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef monitoring fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class REGION,AZ1,AZ2 aws
    class VPC,IGW,PUB_SUBNET,PRIV_SUBNET,DB_SUBNET network
    class ALB,SPLUNK,SO,VELO compute
    class EBS,S3,RDS storage
    class CLOUDWATCH,CLOUDTRAIL,GUARDDUTY monitoring
```

## 🏗️ VPC and Network Configuration

### **VPC Setup**

```yaml
# AWS VPC configuration
aws_vpc:
  # VPC settings
  vpc:
    cidr_block: "10.0.0.0/16"
    enable_dns_hostnames: true
    enable_dns_support: true
    
    tags:
      Name: "GOAD-Blue-VPC"
      Environment: "production"
      Project: "GOAD-Blue"
      
  # Internet Gateway
  internet_gateway:
    tags:
      Name: "GOAD-Blue-IGW"
      
  # NAT Gateway
  nat_gateway:
    allocation_id: "eipalloc-12345678"
    subnet_id: "subnet-public-1a"
    
    tags:
      Name: "GOAD-Blue-NAT"
      
  # Subnets
  subnets:
    # Public subnets
    public_1a:
      cidr_block: "********/24"
      availability_zone: "us-east-1a"
      map_public_ip_on_launch: true
      
      tags:
        Name: "GOAD-Blue-Public-1a"
        Type: "Public"
        
    public_1b:
      cidr_block: "********/24"
      availability_zone: "us-east-1b"
      map_public_ip_on_launch: true
      
      tags:
        Name: "GOAD-Blue-Public-1b"
        Type: "Public"
        
    # Private subnets
    private_1a:
      cidr_block: "*********/24"
      availability_zone: "us-east-1a"
      
      tags:
        Name: "GOAD-Blue-Private-1a"
        Type: "Private"
        
    private_1b:
      cidr_block: "*********/24"
      availability_zone: "us-east-1b"
      
      tags:
        Name: "GOAD-Blue-Private-1b"
        Type: "Private"
        
    # Database subnets
    database_1a:
      cidr_block: "*********/24"
      availability_zone: "us-east-1a"
      
      tags:
        Name: "GOAD-Blue-Database-1a"
        Type: "Database"
        
    database_1b:
      cidr_block: "*********/24"
      availability_zone: "us-east-1b"
      
      tags:
        Name: "GOAD-Blue-Database-1b"
        Type: "Database"
```

### **Security Groups**

```yaml
# Security Groups configuration
security_groups:
  # Web tier security group
  web_tier:
    name: "goad-blue-web-sg"
    description: "Security group for web tier"
    
    ingress_rules:
      - protocol: "tcp"
        from_port: 443
        to_port: 443
        cidr_blocks: ["0.0.0.0/0"]
        description: "HTTPS from anywhere"
        
      - protocol: "tcp"
        from_port: 80
        to_port: 80
        cidr_blocks: ["0.0.0.0/0"]
        description: "HTTP from anywhere"
        
    egress_rules:
      - protocol: "-1"
        from_port: 0
        to_port: 0
        cidr_blocks: ["0.0.0.0/0"]
        description: "All outbound traffic"
        
  # Application tier security group
  app_tier:
    name: "goad-blue-app-sg"
    description: "Security group for application tier"
    
    ingress_rules:
      - protocol: "tcp"
        from_port: 8000
        to_port: 8000
        source_security_group_id: "sg-web-tier"
        description: "Splunk Web from web tier"
        
      - protocol: "tcp"
        from_port: 8089
        to_port: 8089
        source_security_group_id: "sg-web-tier"
        description: "Splunk Management from web tier"
        
      - protocol: "tcp"
        from_port: 22
        to_port: 22
        source_security_group_id: "sg-bastion"
        description: "SSH from bastion"
        
  # Database tier security group
  db_tier:
    name: "goad-blue-db-sg"
    description: "Security group for database tier"
    
    ingress_rules:
      - protocol: "tcp"
        from_port: 5432
        to_port: 5432
        source_security_group_id: "sg-app-tier"
        description: "PostgreSQL from app tier"
        
  # Bastion host security group
  bastion:
    name: "goad-blue-bastion-sg"
    description: "Security group for bastion host"
    
    ingress_rules:
      - protocol: "tcp"
        from_port: 22
        to_port: 22
        cidr_blocks: ["YOUR_IP/32"]
        description: "SSH from admin IP"
```

## 🖥️ EC2 Instance Configuration

### **Instance Specifications**

```yaml
# EC2 instance configurations
ec2_instances:
  # Splunk Search Head
  splunk_search_head:
    instance_type: "c5.2xlarge"
    ami_id: "ami-0abcdef**********"  # Ubuntu 20.04 LTS
    key_name: "goad-blue-keypair"
    subnet_id: "subnet-private-1a"
    security_group_ids: ["sg-app-tier"]
    
    # Storage configuration
    block_device_mappings:
      - device_name: "/dev/sda1"
        ebs:
          volume_type: "gp3"
          volume_size: 50
          encrypted: true
          delete_on_termination: true
          
      - device_name: "/dev/sdf"
        ebs:
          volume_type: "gp3"
          volume_size: 100
          encrypted: true
          delete_on_termination: false
          
    # User data script
    user_data: |
      #!/bin/bash
      apt-get update
      apt-get install -y awscli
      
      # Mount additional EBS volume
      mkfs.ext4 /dev/xvdf
      mkdir -p /opt/splunk
      mount /dev/xvdf /opt/splunk
      echo '/dev/xvdf /opt/splunk ext4 defaults 0 0' >> /etc/fstab
      
    tags:
      Name: "GOAD-Blue-Splunk-SH"
      Component: "Splunk"
      Role: "SearchHead"
      
  # Splunk Indexer
  splunk_indexer:
    instance_type: "c5.4xlarge"
    ami_id: "ami-0abcdef**********"
    key_name: "goad-blue-keypair"
    subnet_id: "subnet-private-1a"
    security_group_ids: ["sg-app-tier"]
    
    # Storage configuration
    block_device_mappings:
      - device_name: "/dev/sda1"
        ebs:
          volume_type: "gp3"
          volume_size: 50
          encrypted: true
          
      - device_name: "/dev/sdf"
        ebs:
          volume_type: "io2"
          volume_size: 500
          iops: 3000
          encrypted: true
          
    tags:
      Name: "GOAD-Blue-Splunk-IDX"
      Component: "Splunk"
      Role: "Indexer"
      
  # Security Onion Manager
  security_onion_manager:
    instance_type: "c5.4xlarge"
    ami_id: "ami-0abcdef**********"
    key_name: "goad-blue-keypair"
    subnet_id: "subnet-private-1a"
    security_group_ids: ["sg-app-tier"]
    
    # Enhanced networking
    ena_support: true
    sriov_net_support: "simple"
    
    # Storage configuration
    block_device_mappings:
      - device_name: "/dev/sda1"
        ebs:
          volume_type: "gp3"
          volume_size: 100
          encrypted: true
          
      - device_name: "/dev/sdf"
        ebs:
          volume_type: "gp3"
          volume_size: 1000
          encrypted: true
          
    tags:
      Name: "GOAD-Blue-SO-Manager"
      Component: "SecurityOnion"
      Role: "Manager"
      
  # Velociraptor Server
  velociraptor:
    instance_type: "t3.large"
    ami_id: "ami-0abcdef**********"
    key_name: "goad-blue-keypair"
    subnet_id: "subnet-private-1a"
    security_group_ids: ["sg-app-tier"]
    
    # Storage configuration
    block_device_mappings:
      - device_name: "/dev/sda1"
        ebs:
          volume_type: "gp3"
          volume_size: 100
          encrypted: true
          
    tags:
      Name: "GOAD-Blue-Velociraptor"
      Component: "Velociraptor"
      Role: "Server"
```

### **Auto Scaling Configuration**

```yaml
# Auto Scaling configuration
auto_scaling:
  # Launch template
  launch_template:
    name: "goad-blue-splunk-indexer-template"
    image_id: "ami-0abcdef**********"
    instance_type: "c5.2xlarge"
    key_name: "goad-blue-keypair"
    security_group_ids: ["sg-app-tier"]
    
    # IAM instance profile
    iam_instance_profile:
      name: "GOAD-Blue-EC2-Profile"
      
    # User data
    user_data_base64: "IyEvYmluL2Jhc2gKYXB0LWdldCB1cGRhdGU="
    
    # Block device mappings
    block_device_mappings:
      - device_name: "/dev/sda1"
        ebs:
          volume_size: 50
          volume_type: "gp3"
          encrypted: true
          
  # Auto Scaling Group
  auto_scaling_group:
    name: "goad-blue-splunk-indexer-asg"
    min_size: 2
    max_size: 6
    desired_capacity: 3
    
    # Subnets
    vpc_zone_identifiers:
      - "subnet-private-1a"
      - "subnet-private-1b"
      
    # Health check
    health_check_type: "EC2"
    health_check_grace_period: 300
    
    # Tags
    tags:
      - key: "Name"
        value: "GOAD-Blue-Splunk-Indexer-ASG"
        propagate_at_launch: true
        
  # Scaling policies
  scaling_policies:
    scale_up:
      name: "goad-blue-scale-up"
      scaling_adjustment: 1
      adjustment_type: "ChangeInCapacity"
      cooldown: 300
      
    scale_down:
      name: "goad-blue-scale-down"
      scaling_adjustment: -1
      adjustment_type: "ChangeInCapacity"
      cooldown: 300
```

## 🗄️ RDS Database Configuration

### **PostgreSQL for MISP and Velociraptor**

```yaml
# RDS configuration
rds:
  # DB Subnet Group
  db_subnet_group:
    name: "goad-blue-db-subnet-group"
    subnet_ids:
      - "subnet-database-1a"
      - "subnet-database-1b"
      
    tags:
      Name: "GOAD-Blue-DB-Subnet-Group"
      
  # DB Parameter Group
  db_parameter_group:
    name: "goad-blue-postgres-params"
    family: "postgres13"
    
    parameters:
      - name: "shared_preload_libraries"
        value: "pg_stat_statements"
        
      - name: "log_statement"
        value: "all"
        
  # RDS Instance
  db_instance:
    identifier: "goad-blue-postgres"
    engine: "postgres"
    engine_version: "13.7"
    instance_class: "db.t3.medium"
    
    # Storage
    allocated_storage: 100
    max_allocated_storage: 1000
    storage_type: "gp2"
    storage_encrypted: true
    
    # Database settings
    db_name: "goadblue"
    username: "goadblue_admin"
    password: "${DB_PASSWORD}"
    port: 5432
    
    # Network settings
    db_subnet_group_name: "goad-blue-db-subnet-group"
    vpc_security_group_ids: ["sg-db-tier"]
    publicly_accessible: false
    
    # Backup settings
    backup_retention_period: 7
    backup_window: "03:00-04:00"
    maintenance_window: "sun:04:00-sun:05:00"
    
    # Monitoring
    monitoring_interval: 60
    monitoring_role_arn: "arn:aws:iam::**********12:role/rds-monitoring-role"
    performance_insights_enabled: true
    
    # Multi-AZ
    multi_az: true
    
    tags:
      Name: "GOAD-Blue-PostgreSQL"
      Environment: "production"
```

## 🪣 S3 Storage Configuration

### **S3 Buckets for Logs and Backups**

```yaml
# S3 configuration
s3:
  # Log archive bucket
  log_archive_bucket:
    bucket_name: "goad-blue-log-archive-${random_id}"
    
    # Versioning
    versioning:
      enabled: true
      
    # Encryption
    server_side_encryption_configuration:
      rule:
        apply_server_side_encryption_by_default:
          sse_algorithm: "AES256"
          
    # Lifecycle policy
    lifecycle_configuration:
      rules:
        - id: "log_lifecycle"
          status: "Enabled"
          
          transitions:
            - days: 30
              storage_class: "STANDARD_IA"
              
            - days: 90
              storage_class: "GLACIER"
              
            - days: 365
              storage_class: "DEEP_ARCHIVE"
              
          expiration:
            days: 2555  # 7 years
            
    # Public access block
    public_access_block:
      block_public_acls: true
      block_public_policy: true
      ignore_public_acls: true
      restrict_public_buckets: true
      
  # Backup bucket
  backup_bucket:
    bucket_name: "goad-blue-backups-${random_id}"
    
    # Cross-region replication
    replication_configuration:
      role: "arn:aws:iam::**********12:role/replication-role"
      
      rules:
        - id: "backup_replication"
          status: "Enabled"
          
          destination:
            bucket: "arn:aws:s3:::goad-blue-backups-replica-${random_id}"
            storage_class: "STANDARD_IA"
```

## 📊 CloudWatch Monitoring

### **Monitoring and Alerting**

```yaml
# CloudWatch configuration
cloudwatch:
  # Log Groups
  log_groups:
    - name: "/aws/ec2/goad-blue/splunk"
      retention_in_days: 90
      
    - name: "/aws/ec2/goad-blue/security-onion"
      retention_in_days: 30
      
    - name: "/aws/ec2/goad-blue/velociraptor"
      retention_in_days: 30
      
  # Custom Metrics
  custom_metrics:
    - metric_name: "SplunkIndexingRate"
      namespace: "GOAD-Blue/Splunk"
      dimensions:
        - name: "InstanceId"
          value: "${instance_id}"
          
    - metric_name: "SecurityOnionAlerts"
      namespace: "GOAD-Blue/SecurityOnion"
      
  # Alarms
  alarms:
    # High CPU alarm
    high_cpu:
      alarm_name: "GOAD-Blue-High-CPU"
      comparison_operator: "GreaterThanThreshold"
      evaluation_periods: 2
      metric_name: "CPUUtilization"
      namespace: "AWS/EC2"
      period: 300
      statistic: "Average"
      threshold: 80
      
      dimensions:
        InstanceId: "${instance_id}"
        
      alarm_actions:
        - "arn:aws:sns:us-east-1:**********12:goad-blue-alerts"
        
    # Low disk space alarm
    low_disk_space:
      alarm_name: "GOAD-Blue-Low-Disk-Space"
      comparison_operator: "LessThanThreshold"
      evaluation_periods: 1
      metric_name: "DiskSpaceUtilization"
      namespace: "CWAgent"
      period: 300
      statistic: "Average"
      threshold: 20
      
  # SNS Topics
  sns_topics:
    alerts:
      name: "goad-blue-alerts"
      
      subscriptions:
        - protocol: "email"
          endpoint: "<EMAIL>"
          
        - protocol: "sms"
          endpoint: "+**********"
```

## 🔧 Terraform Configuration

### **Main Terraform Configuration**

```hcl
# terraform/providers/aws/main.tf

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  
  backend "s3" {
    bucket = "goad-blue-terraform-state"
    key    = "aws/terraform.tfstate"
    region = "us-east-1"
  }
}

provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = "GOAD-Blue"
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# VPC
resource "aws_vpc" "goad_blue" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  tags = {
    Name = "GOAD-Blue-VPC"
  }
}

# Internet Gateway
resource "aws_internet_gateway" "goad_blue" {
  vpc_id = aws_vpc.goad_blue.id
  
  tags = {
    Name = "GOAD-Blue-IGW"
  }
}

# Public Subnets
resource "aws_subnet" "public" {
  count = length(var.public_subnet_cidrs)
  
  vpc_id                  = aws_vpc.goad_blue.id
  cidr_block              = var.public_subnet_cidrs[count.index]
  availability_zone       = var.availability_zones[count.index]
  map_public_ip_on_launch = true
  
  tags = {
    Name = "GOAD-Blue-Public-${count.index + 1}"
    Type = "Public"
  }
}

# Private Subnets
resource "aws_subnet" "private" {
  count = length(var.private_subnet_cidrs)
  
  vpc_id            = aws_vpc.goad_blue.id
  cidr_block        = var.private_subnet_cidrs[count.index]
  availability_zone = var.availability_zones[count.index]
  
  tags = {
    Name = "GOAD-Blue-Private-${count.index + 1}"
    Type = "Private"
  }
}

# Splunk Search Head
resource "aws_instance" "splunk_search_head" {
  ami                    = var.ubuntu_ami_id
  instance_type          = var.splunk_instance_type
  key_name              = var.key_pair_name
  subnet_id             = aws_subnet.private[0].id
  vpc_security_group_ids = [aws_security_group.app_tier.id]
  
  root_block_device {
    volume_type = "gp3"
    volume_size = 50
    encrypted   = true
  }
  
  ebs_block_device {
    device_name = "/dev/sdf"
    volume_type = "gp3"
    volume_size = 100
    encrypted   = true
  }
  
  user_data = base64encode(templatefile("${path.module}/user_data/splunk_search_head.sh", {
    region = var.aws_region
  }))
  
  tags = {
    Name      = "GOAD-Blue-Splunk-SH"
    Component = "Splunk"
    Role      = "SearchHead"
  }
}
```

---

!!! tip "AWS Best Practices"
    - Use IAM roles instead of access keys for EC2 instances
    - Enable CloudTrail for audit logging
    - Use encrypted EBS volumes and S3 buckets
    - Implement least privilege security groups
    - Use Auto Scaling for high availability

!!! warning "Cost Management"
    Monitor AWS costs closely. Use Reserved Instances or Savings Plans for predictable workloads. Consider using Spot Instances for non-critical components.

!!! info "Security Considerations"
    - Enable GuardDuty for threat detection
    - Use AWS Config for compliance monitoring
    - Implement VPC Flow Logs for network monitoring
    - Use AWS Systems Manager for patch management
