#!/bin/bash
# GOAD Integration Automation Script
# This script automates the integration of GOAD-Blue with existing GOAD environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Default configuration
GOAD_NETWORK="************/24"
GOAD_BLUE_NETWORK="*************/24"
DOMAIN_ADMIN="sevenkingdoms\\administrator"
DOMAIN_PASSWORD=""
SPLUNK_INDEXER="**************"
VELOCIRAPTOR_SERVER="**************"
ELASTICSEARCH_HOST="**************"

# Integration options
DEPLOY_AGENTS=true
CONFIGURE_NETWORK=true
SETUP_DATA_FLOW=true
VALIDATE_INTEGRATION=true

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    cat << EOF
GOAD Integration Automation Script

Usage: $0 [OPTIONS]

Options:
    --goad-network CIDR         GOAD network CIDR (default: ************/24)
    --goad-blue-network CIDR    GOAD-Blue network CIDR (default: *************/24)
    --domain-admin USER         Domain administrator username (default: sevenkingdoms\\administrator)
    --domain-password PASS      Domain administrator password (required)
    --splunk-indexer IP         Splunk indexer IP (default: **************)
    --velociraptor-server IP    Velociraptor server IP (default: **************)
    --elasticsearch-host IP     Elasticsearch host IP (default: **************)
    --skip-agents              Skip agent deployment
    --skip-network             Skip network configuration
    --skip-data-flow           Skip data flow setup
    --skip-validation          Skip integration validation
    --help                     Show this help message

Examples:
    $0 --domain-password "Password123!"
    $0 --domain-password "Password123!" --skip-network
    $0 --goad-network "10.0.0.0/24" --domain-password "MyPassword"

EOF
}

discover_goad_environment() {
    log_info "Discovering GOAD environment..."
    
    # Create discovery results file
    local discovery_file="/tmp/goad_discovery.json"
    
    # Scan for GOAD systems
    log_info "Scanning network $GOAD_NETWORK for GOAD systems..."
    
    # Extract network base for scanning
    local network_base=$(echo $GOAD_NETWORK | cut -d'/' -f1 | cut -d'.' -f1-3)
    
    # Common GOAD IP addresses
    local goad_ips=(
        "${network_base}.10"  # Kingslanding
        "${network_base}.11"  # Winterfell
        "${network_base}.12"  # Meereen
        "${network_base}.22"  # Castelblack
        "${network_base}.23"  # Braavos
        "${network_base}.30"  # Tyrell
    )
    
    local discovered_systems=()
    
    for ip in "${goad_ips[@]}"; do
        if ping -c 2 -W 2 "$ip" > /dev/null 2>&1; then
            log_success "Found GOAD system at $ip"
            discovered_systems+=("$ip")
        fi
    done
    
    if [ ${#discovered_systems[@]} -eq 0 ]; then
        log_error "No GOAD systems discovered. Please verify GOAD is running and network connectivity."
        exit 1
    fi
    
    # Create discovery JSON
    cat > "$discovery_file" << EOF
{
  "goad_environment": {
    "network": "$GOAD_NETWORK",
    "discovered_systems": [
$(printf '      "%s",\n' "${discovered_systems[@]}" | sed '$ s/,$//')
    ],
    "discovery_time": "$(date -Iseconds)"
  }
}
EOF
    
    log_success "Discovered ${#discovered_systems[@]} GOAD systems"
    echo "$discovery_file"
}

configure_network_integration() {
    if [ "$CONFIGURE_NETWORK" != "true" ]; then
        log_info "Skipping network configuration"
        return 0
    fi
    
    log_info "Configuring network integration..."
    
    # Enable IP forwarding
    log_info "Enabling IP forwarding..."
    sudo sysctl net.ipv4.ip_forward=1
    echo 'net.ipv4.ip_forward=1' | sudo tee -a /etc/sysctl.conf > /dev/null
    
    # Extract network bases
    local goad_base=$(echo $GOAD_NETWORK | cut -d'/' -f1 | cut -d'.' -f1-3)
    local gb_base=$(echo $GOAD_BLUE_NETWORK | cut -d'/' -f1 | cut -d'.' -f1-3)
    
    # Configure routing
    log_info "Configuring routing between networks..."
    
    # Add routes (these may fail if already exist, that's OK)
    sudo ip route add $GOAD_NETWORK via ${gb_base}.1 2>/dev/null || true
    sudo ip route add $GOAD_BLUE_NETWORK via ${goad_base}.1 2>/dev/null || true
    
    # Configure firewall
    log_info "Configuring firewall rules..."
    
    # Allow communication between networks
    sudo ufw allow from $GOAD_NETWORK to $GOAD_BLUE_NETWORK 2>/dev/null || true
    sudo ufw allow from $GOAD_BLUE_NETWORK to $GOAD_NETWORK 2>/dev/null || true
    
    # Allow specific services
    sudo ufw allow 9997/tcp comment "Splunk Forwarder" 2>/dev/null || true
    sudo ufw allow 8089/tcp comment "Splunk Management" 2>/dev/null || true
    sudo ufw allow 5985/tcp comment "WinRM HTTP" 2>/dev/null || true
    sudo ufw allow 5986/tcp comment "WinRM HTTPS" 2>/dev/null || true
    
    log_success "Network integration configured"
}

deploy_monitoring_agents() {
    if [ "$DEPLOY_AGENTS" != "true" ]; then
        log_info "Skipping agent deployment"
        return 0
    fi
    
    log_info "Deploying monitoring agents to GOAD systems..."
    
    # Discover GOAD systems
    local discovery_file=$(discover_goad_environment)
    local goad_systems=($(jq -r '.goad_environment.discovered_systems[]' "$discovery_file"))
    
    for system_ip in "${goad_systems[@]}"; do
        log_info "Deploying agents to $system_ip..."
        
        # Test WinRM connectivity
        if ! nc -zv "$system_ip" 5985 > /dev/null 2>&1; then
            log_warning "WinRM not accessible on $system_ip, skipping..."
            continue
        fi
        
        # Deploy agents via WinRM
        deploy_agents_to_system "$system_ip"
    done
    
    log_success "Agent deployment completed"
}

deploy_agents_to_system() {
    local target_ip=$1
    
    log_info "Deploying agents to $target_ip..."
    
    # Create PowerShell script for agent deployment
    local deploy_script="/tmp/deploy_agents_${target_ip//\./_}.ps1"
    
    cat > "$deploy_script" << 'EOF'
param(
    [string]$SplunkIndexer,
    [string]$VelociraptorServer,
    [string]$ElasticsearchHost
)

# Create temp directory
if (!(Test-Path "C:\temp")) { 
    New-Item -Path "C:\temp" -ItemType Directory -Force 
}

Write-Host "Installing Sysmon..."
try {
    # Download Sysmon
    Invoke-WebRequest -Uri "https://download.sysinternals.com/files/Sysmon.zip" -OutFile "C:\temp\Sysmon.zip"
    Expand-Archive -Path "C:\temp\Sysmon.zip" -DestinationPath "C:\temp\Sysmon" -Force
    
    # Install Sysmon with basic config
    & "C:\temp\Sysmon\Sysmon64.exe" -accepteula -i
    Write-Host "Sysmon installed successfully"
} catch {
    Write-Warning "Sysmon installation failed: $($_.Exception.Message)"
}

Write-Host "Installing Splunk Universal Forwarder..."
try {
    # Download Splunk UF
    $splunkUrl = "https://download.splunk.com/products/universalforwarder/releases/9.1.2/windows/splunkforwarder-9.1.2-b6b9c8185839-x64-release.msi"
    Invoke-WebRequest -Uri $splunkUrl -OutFile "C:\temp\splunkforwarder.msi"
    
    # Install Splunk UF
    $arguments = @(
        "/i", "C:\temp\splunkforwarder.msi",
        "RECEIVING_INDEXER=$SplunkIndexer:9997",
        "WINEVENTLOG_APP_ENABLE=1",
        "WINEVENTLOG_SEC_ENABLE=1",
        "WINEVENTLOG_SYS_ENABLE=1",
        "AGREETOLICENSE=Yes",
        "/quiet"
    )
    
    Start-Process msiexec -ArgumentList $arguments -Wait
    
    # Configure inputs
    $inputsConf = @"
[WinEventLog://Application]
disabled = false
index = goad_blue_windows

[WinEventLog://Security]
disabled = false
index = goad_blue_windows

[WinEventLog://System]
disabled = false
index = goad_blue_windows

[WinEventLog://Microsoft-Windows-Sysmon/Operational]
disabled = false
index = goad_blue_windows
renderXml = true
"@
    
    $inputsConf | Out-File -FilePath "C:\Program Files\SplunkUniversalForwarder\etc\system\local\inputs.conf" -Encoding UTF8
    
    # Start service
    Start-Service SplunkForwarder
    Set-Service SplunkForwarder -StartupType Automatic
    
    Write-Host "Splunk Universal Forwarder installed successfully"
} catch {
    Write-Warning "Splunk UF installation failed: $($_.Exception.Message)"
}

Write-Host "Agent deployment completed on $env:COMPUTERNAME"
EOF
    
    # Execute deployment script via WinRM
    winrs -r:"$target_ip" -u:"$DOMAIN_ADMIN" -p:"$DOMAIN_PASSWORD" \
        "powershell.exe -ExecutionPolicy Bypass -File - -SplunkIndexer $SPLUNK_INDEXER -VelociraptorServer $VELOCIRAPTOR_SERVER -ElasticsearchHost $ELASTICSEARCH_HOST" < "$deploy_script"
    
    # Clean up
    rm -f "$deploy_script"
    
    log_success "Agents deployed to $target_ip"
}

setup_data_flow() {
    if [ "$SETUP_DATA_FLOW" != "true" ]; then
        log_info "Skipping data flow setup"
        return 0
    fi
    
    log_info "Setting up data flow configuration..."
    
    # Configure Splunk indexes
    log_info "Configuring Splunk indexes..."
    configure_splunk_indexes
    
    # Configure Elasticsearch templates
    log_info "Configuring Elasticsearch templates..."
    configure_elasticsearch_templates
    
    # Setup Security Onion rules
    log_info "Configuring Security Onion rules..."
    configure_security_onion_rules
    
    log_success "Data flow configuration completed"
}

configure_splunk_indexes() {
    # This would typically use Splunk REST API
    log_info "Splunk index configuration (manual step required)"
    log_warning "Please manually configure Splunk indexes using the web interface"
}

configure_elasticsearch_templates() {
    # Configure Elasticsearch index template
    local template_file="/tmp/goad_blue_template.json"
    
    cat > "$template_file" << 'EOF'
{
  "index_patterns": ["goad-blue-*"],
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "refresh_interval": "30s"
  },
  "mappings": {
    "properties": {
      "@timestamp": {"type": "date"},
      "event": {
        "properties": {
          "action": {"type": "keyword"},
          "category": {"type": "keyword"}
        }
      },
      "host": {
        "properties": {
          "name": {"type": "keyword"},
          "ip": {"type": "ip"}
        }
      }
    }
  }
}
EOF
    
    # Apply template
    curl -X PUT "$ELASTICSEARCH_HOST:9200/_index_template/goad-blue-template" \
        -H "Content-Type: application/json" \
        -d @"$template_file" > /dev/null 2>&1 || log_warning "Failed to configure Elasticsearch template"
    
    rm -f "$template_file"
}

configure_security_onion_rules() {
    log_info "Security Onion rule configuration (manual step required)"
    log_warning "Please manually configure Security Onion rules for GOAD monitoring"
}

validate_integration() {
    if [ "$VALIDATE_INTEGRATION" != "true" ]; then
        log_info "Skipping integration validation"
        return 0
    fi
    
    log_info "Validating GOAD-Blue integration..."
    
    # Test network connectivity
    log_info "Testing network connectivity..."
    test_network_connectivity
    
    # Test agent status
    log_info "Testing agent status..."
    test_agent_status
    
    # Test data flow
    log_info "Testing data flow..."
    test_data_flow
    
    log_success "Integration validation completed"
}

test_network_connectivity() {
    local goad_base=$(echo $GOAD_NETWORK | cut -d'/' -f1 | cut -d'.' -f1-3)
    local gb_base=$(echo $GOAD_BLUE_NETWORK | cut -d'/' -f1 | cut -d'.' -f1-3)
    
    # Test GOAD systems
    ping -c 2 "${goad_base}.10" > /dev/null 2>&1 && log_success "✓ GOAD DC reachable" || log_warning "✗ GOAD DC unreachable"
    
    # Test GOAD-Blue systems
    ping -c 2 "$SPLUNK_INDEXER" > /dev/null 2>&1 && log_success "✓ Splunk reachable" || log_warning "✗ Splunk unreachable"
    
    # Test service ports
    nc -zv "$SPLUNK_INDEXER" 8000 > /dev/null 2>&1 && log_success "✓ Splunk Web accessible" || log_warning "✗ Splunk Web inaccessible"
    nc -zv "$SPLUNK_INDEXER" 9997 > /dev/null 2>&1 && log_success "✓ Splunk Forwarder port open" || log_warning "✗ Splunk Forwarder port closed"
}

test_agent_status() {
    log_info "Agent status testing requires manual verification"
    log_warning "Please check agent status using: python3 goad-blue.py check_agents"
}

test_data_flow() {
    log_info "Data flow testing requires manual verification"
    log_warning "Please check data flow using: python3 goad-blue.py test_data_flow"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --goad-network)
            GOAD_NETWORK="$2"
            shift 2
            ;;
        --goad-blue-network)
            GOAD_BLUE_NETWORK="$2"
            shift 2
            ;;
        --domain-admin)
            DOMAIN_ADMIN="$2"
            shift 2
            ;;
        --domain-password)
            DOMAIN_PASSWORD="$2"
            shift 2
            ;;
        --splunk-indexer)
            SPLUNK_INDEXER="$2"
            shift 2
            ;;
        --velociraptor-server)
            VELOCIRAPTOR_SERVER="$2"
            shift 2
            ;;
        --elasticsearch-host)
            ELASTICSEARCH_HOST="$2"
            shift 2
            ;;
        --skip-agents)
            DEPLOY_AGENTS=false
            shift
            ;;
        --skip-network)
            CONFIGURE_NETWORK=false
            shift
            ;;
        --skip-data-flow)
            SETUP_DATA_FLOW=false
            shift
            ;;
        --skip-validation)
            VALIDATE_INTEGRATION=false
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [ -z "$DOMAIN_PASSWORD" ]; then
    log_error "Domain password is required"
    show_usage
    exit 1
fi

# Main execution
main() {
    log_info "Starting GOAD-Blue integration..."
    log_info "GOAD Network: $GOAD_NETWORK"
    log_info "GOAD-Blue Network: $GOAD_BLUE_NETWORK"
    log_info "Domain Admin: $DOMAIN_ADMIN"
    echo ""
    
    configure_network_integration
    deploy_monitoring_agents
    setup_data_flow
    validate_integration
    
    log_success "GOAD-Blue integration completed successfully!"
    echo ""
    log_info "Next steps:"
    log_info "1. Verify agent status: python3 goad-blue.py check_agents"
    log_info "2. Test data flow: python3 goad-blue.py test_data_flow"
    log_info "3. Access Splunk: https://$SPLUNK_INDEXER:8000"
    log_info "4. Access Velociraptor: https://$VELOCIRAPTOR_SERVER:8889"
}

# Run main function
main "$@"
