# pfSense Configuration for GOAD Integration

This guide covers configuring pfSense firewall rules and network settings for GOAD-Blue integration with existing GOAD environments.

## 🎯 Overview

pfSense serves as the network gateway and firewall for GOAD-Blue, providing network segmentation, traffic filtering, and monitoring capabilities. Proper configuration is essential for secure GOAD integration.

```mermaid
graph TB
    subgraph "🌐 Network Architecture"
        INTERNET[🌍 Internet<br/>WAN Interface<br/>External Access]
        PFSENSE[🛡️ pfSense Firewall<br/>***********<br/>Gateway & Security]
        
        subgraph "🏢 GOAD-Blue Networks"
            MGMT[🔧 Management<br/>***********/24<br/>Admin Access]
            SIEM[📊 SIEM<br/>*************/26<br/>Log Analysis]
            MONITOR[👁️ Monitoring<br/>192.168.100.64/26<br/>Network Security]
            ANALYSIS[🔬 Analysis<br/>*************92/26<br/>Threat Analysis]
        end
        
        subgraph "⚔️ GOAD Environment"
            GOAD_NET[🏰 GOAD Network<br/>************/24<br/>Target Environment]
            DC[🏛️ Domain Controller<br/>************0]
            SERVERS[🖥️ Member Servers<br/>************1-20]
            WORKSTATIONS[💻 Workstations<br/>192.168.56.21-30]
        end
    end
    
    INTERNET --> PFSENSE
    PFSENSE --> MGMT
    PFSENSE --> SIEM
    PFSENSE --> MONITOR
    PFSENSE --> ANALYSIS
    PFSENSE --> GOAD_NET
    
    SIEM -.->|Log Collection| DC
    SIEM -.->|Log Collection| SERVERS
    SIEM -.->|Log Collection| WORKSTATIONS
    MONITOR -.->|Traffic Monitoring| GOAD_NET
    
    classDef internet fill:#ff6b6b,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef firewall fill:#4ecdc4,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef goadblue fill:#45b7d1,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef goad fill:#f9ca24,stroke:#ffffff,stroke-width:2px,color:#fff
    
    class INTERNET internet
    class PFSENSE firewall
    class MGMT,SIEM,MONITOR,ANALYSIS goadblue
    class GOAD_NET,DC,SERVERS,WORKSTATIONS goad
```

## 🔧 Initial pfSense Setup

### **Basic Configuration**

```bash
# Access pfSense web interface
# Default: https://***********
# Username: admin
# Password: pfsense (change immediately)

# Initial setup wizard configuration:
# 1. General Information
# 2. Time Server Information  
# 3. Configure WAN Interface
# 4. Configure LAN Interface
# 5. Set Admin Password
# 6. Reload Configuration
```

### **Interface Configuration**

```yaml
# pfSense interface configuration for GOAD-Blue
interfaces:
  wan:
    description: "WAN - Internet Access"
    type: "dhcp"  # or static based on your setup
    block_rfc1918: false  # Allow private networks if needed
    
  lan:
    description: "LAN - Management Network"
    ipv4_address: "***********"
    subnet_mask: "24"
    dhcp_enabled: true
    dhcp_range: "***********00-*************"
    
  opt1:
    description: "GOAD-Blue SIEM"
    ipv4_address: "*************"
    subnet_mask: "24"
    dhcp_enabled: false
    
  opt2:
    description: "GOAD Network"
    ipv4_address: "************"
    subnet_mask: "24"
    dhcp_enabled: false
    
  opt3:
    description: "DMZ - Monitoring"
    ipv4_address: "*************"
    subnet_mask: "24"
    dhcp_enabled: false
```

## 🛡️ Firewall Rules Configuration

### **Management Network Rules (LAN)**

```bash
# Navigate to: Firewall > Rules > LAN

# Rule 1: Allow SSH to pfSense
Action: Pass
Interface: LAN
Address Family: IPv4
Protocol: TCP
Source: LAN net
Destination: LAN address
Destination Port Range: 22 (SSH)
Description: Allow SSH to pfSense from management network

# Rule 2: Allow HTTPS to pfSense
Action: Pass
Interface: LAN
Address Family: IPv4
Protocol: TCP
Source: LAN net
Destination: LAN address
Destination Port Range: 443 (HTTPS)
Description: Allow web interface access

# Rule 3: Allow management access to GOAD-Blue
Action: Pass
Interface: LAN
Address Family: IPv4
Protocol: Any
Source: LAN net
Destination: *************/24
Description: Allow management access to GOAD-Blue networks

# Rule 4: Allow management access to GOAD
Action: Pass
Interface: LAN
Address Family: IPv4
Protocol: Any
Source: LAN net
Destination: ************/24
Description: Allow management access to GOAD environment
```

### **GOAD-Blue SIEM Network Rules (OPT1)**

```bash
# Navigate to: Firewall > Rules > OPT1

# Rule 1: Allow log forwarding from GOAD
Action: Pass
Interface: OPT1
Address Family: IPv4
Protocol: TCP
Source: ************/24
Destination: *************/26
Destination Port Range: 9997 (Splunk)
Description: Allow Splunk log forwarding from GOAD

# Rule 2: Allow syslog from GOAD
Action: Pass
Interface: OPT1
Address Family: IPv4
Protocol: UDP
Source: ************/24
Destination: *************/26
Destination Port Range: 514 (Syslog)
Description: Allow syslog forwarding from GOAD

# Rule 3: Allow Beats/Logstash
Action: Pass
Interface: OPT1
Address Family: IPv4
Protocol: TCP
Source: ************/24
Destination: *************/26
Destination Port Range: 5044 (Beats)
Description: Allow Elastic Beats from GOAD

# Rule 4: Allow web access from management
Action: Pass
Interface: OPT1
Address Family: IPv4
Protocol: TCP
Source: ***********/24
Destination: *************/26
Destination Port Range: 8000,5601,443 (Web interfaces)
Description: Allow web access to SIEM platforms

# Rule 5: Allow DNS
Action: Pass
Interface: OPT1
Address Family: IPv4
Protocol: UDP
Source: *************/24
Destination: Any
Destination Port Range: 53 (DNS)
Description: Allow DNS queries

# Rule 6: Allow NTP
Action: Pass
Interface: OPT1
Address Family: IPv4
Protocol: UDP
Source: *************/24
Destination: Any
Destination Port Range: 123 (NTP)
Description: Allow time synchronization

# Rule 7: Block all other traffic
Action: Block
Interface: OPT1
Address Family: IPv4
Protocol: Any
Source: Any
Destination: Any
Description: Default deny rule
```

### **GOAD Network Rules (OPT2)**

```bash
# Navigate to: Firewall > Rules > OPT2

# Rule 1: Allow agent communication to GOAD-Blue
Action: Pass
Interface: OPT2
Address Family: IPv4
Protocol: TCP
Source: ************/24
Destination: **************
Destination Port Range: 8000,8889 (Velociraptor)
Description: Allow Velociraptor agent communication

# Rule 2: Allow WinRM for agent deployment
Action: Pass
Interface: OPT2
Address Family: IPv4
Protocol: TCP
Source: *************/24
Destination: ************/24
Destination Port Range: 5985,5986 (WinRM)
Description: Allow WinRM for agent deployment

# Rule 3: Allow SSH for Linux agents
Action: Pass
Interface: OPT2
Address Family: IPv4
Protocol: TCP
Source: *************/24
Destination: ************/24
Destination Port Range: 22 (SSH)
Description: Allow SSH for Linux agent deployment

# Rule 4: Allow RDP for management
Action: Pass
Interface: OPT2
Address Family: IPv4
Protocol: TCP
Source: ***********/24
Destination: ************/24
Destination Port Range: 3389 (RDP)
Description: Allow RDP access from management

# Rule 5: Allow ICMP for monitoring
Action: Pass
Interface: OPT2
Address Family: IPv4
Protocol: ICMP
Source: *************/24
Destination: ************/24
Description: Allow ICMP for network monitoring

# Rule 6: Block internet access from GOAD
Action: Block
Interface: OPT2
Address Family: IPv4
Protocol: Any
Source: ************/24
Destination: !RFC1918
Description: Block internet access from GOAD (isolation)
```

## 🔀 NAT Configuration

### **Outbound NAT Rules**

```bash
# Navigate to: Firewall > NAT > Outbound
# Mode: Manual Outbound NAT rule generation

# Rule 1: Management network internet access
Interface: WAN
Address Family: IPv4
Protocol: Any
Source: ***********/24
Destination: Any
Translation Address: Interface Address
Description: Management network internet access

# Rule 2: GOAD-Blue internet access (limited)
Interface: WAN
Address Family: IPv4
Protocol: TCP/UDP
Source: *************/24
Destination: Any
Destination Port Range: 80,443,53,123 (HTTP,HTTPS,DNS,NTP)
Translation Address: Interface Address
Description: Limited internet access for GOAD-Blue

# Rule 3: Block GOAD internet access
# (No NAT rule = no internet access for GOAD network)
```

### **Port Forwarding (if needed)**

```bash
# Navigate to: Firewall > NAT > Port Forward

# Example: External access to Splunk (if required)
Interface: WAN
Protocol: TCP
Destination Port Range: 8000
Redirect Target IP: *************0
Redirect Target Port: 8000
Description: External Splunk access (use with caution)
Filter Rule Association: Add associated filter rule
```

## 🌐 VLAN Configuration

### **VLAN Setup**

```bash
# Navigate to: Interfaces > Assignments > VLANs

# VLAN 100: GOAD-Blue SIEM
Parent Interface: em1 (or your physical interface)
VLAN Tag: 100
Description: GOAD-Blue SIEM Network

# VLAN 101: GOAD-Blue Monitoring  
Parent Interface: em1
VLAN Tag: 101
Description: GOAD-Blue Monitoring Network

# VLAN 56: GOAD Environment
Parent Interface: em1
VLAN Tag: 56
Description: GOAD Target Environment

# VLAN 200: DMZ
Parent Interface: em1
VLAN Tag: 200
Description: DMZ for external services
```

### **Interface Assignment**

```bash
# Navigate to: Interfaces > Assignments

# Assign VLANs to interfaces:
OPT1: VLAN 100 (GOAD-Blue SIEM)
OPT2: VLAN 56 (GOAD)
OPT3: VLAN 101 (GOAD-Blue Monitoring)
OPT4: VLAN 200 (DMZ)
```

## 📊 Traffic Monitoring Configuration

### **Traffic Shaping**

```bash
# Navigate to: Firewall > Traffic Shaper

# Create traffic shaping rules for GOAD-Blue:

# High Priority: Management traffic
Queue: Management_High
Bandwidth: 50% of available
Priority: 7
Description: Management and critical services

# Medium Priority: Log forwarding
Queue: Logs_Medium  
Bandwidth: 30% of available
Priority: 4
Description: Log forwarding and SIEM traffic

# Low Priority: General traffic
Queue: General_Low
Bandwidth: 20% of available
Priority: 1
Description: General network traffic
```

### **Packet Capture**

```bash
# Navigate to: Diagnostics > Packet Capture

# Configure packet capture for monitoring:
Interface: OPT2 (GOAD Network)
Host Address: ************/24
Protocol: Any
Packet Length: 1500
Count: 1000
Description: GOAD network traffic capture

# Save captures to: /tmp/goad_traffic_capture.pcap
```

## 🔍 Monitoring and Logging

### **System Logs Configuration**

```bash
# Navigate to: Status > System Logs > Settings

# Configure logging:
Forward logs to remote syslog server: Enabled
Remote Syslog Server: *************0:514
Remote Syslog Contents: Everything

# Log rotation:
Log file size: 500KB
Log files to keep: 10

# Enable detailed logging:
Firewall Log Detail: Enabled
DHCP Log Detail: Enabled
```

### **pfSense Integration with GOAD-Blue SIEM**

```bash
# Configure pfSense to send logs to Splunk

# 1. Install Splunk Universal Forwarder on pfSense (if possible)
# 2. Configure syslog forwarding to GOAD-Blue SIEM
# 3. Set up log parsing in Splunk

# Splunk configuration for pfSense logs:
# inputs.conf
[udp://514]
index = goad_blue_network
sourcetype = pfsense:filterlog

# props.conf
[pfsense:filterlog]
SHOULD_LINEMERGE = false
TIME_PREFIX = ^
TIME_FORMAT = %b %d %H:%M:%S
MAX_TIMESTAMP_LOOKAHEAD = 32
```

## 🚨 Security Hardening

### **Security Settings**

```bash
# Navigate to: System > Advanced > Admin Access

# Secure web interface:
Protocol: HTTPS
SSL Certificate: Generate or import valid certificate
Session Timeout: 30 minutes
Anti-lockout: Enabled

# SSH Configuration:
Secure Shell Server: Enabled (if needed)
SSH Key Only: Enabled
Root Login: Disabled

# Navigate to: System > Advanced > Networking

# Security options:
IP Do-Not-Fragment: Clear
IP Random ID: Enabled
Source Routing: Disabled
Accept ICMP Redirects: Disabled
```

### **Intrusion Detection**

```bash
# Navigate to: Services > Intrusion Detection

# Enable Suricata IDS/IPS:
Interface: WAN, OPT1, OPT2
Detection Mode: IDS (or IPS for blocking)
Rule Categories: Enable relevant categories
Update Frequency: Daily

# Custom rules for GOAD monitoring:
alert tcp ************/24 any -> *************/24 any (msg:"GOAD to SIEM traffic"; sid:1000001;)
alert tcp any any -> ************/24 22 (msg:"SSH to GOAD network"; sid:1000002;)
```

## 🔧 Automation Scripts

### **pfSense Configuration Script**

```bash
#!/bin/bash
# pfSense configuration automation script

PFSENSE_HOST="***********"
PFSENSE_USER="admin"
PFSENSE_PASS="your_password"

# Function to add firewall rule via API
add_firewall_rule() {
    local interface=$1
    local action=$2
    local protocol=$3
    local source=$4
    local destination=$5
    local port=$6
    local description=$7
    
    curl -k -X POST \
        -u "${PFSENSE_USER}:${PFSENSE_PASS}" \
        -H "Content-Type: application/json" \
        -d "{
            \"interface\": \"${interface}\",
            \"action\": \"${action}\",
            \"protocol\": \"${protocol}\",
            \"source\": \"${source}\",
            \"destination\": \"${destination}\",
            \"destination_port\": \"${port}\",
            \"description\": \"${description}\"
        }" \
        "https://${PFSENSE_HOST}/api/v1/firewall/rule"
}

# Add GOAD-Blue integration rules
echo "Configuring pfSense for GOAD-Blue integration..."

# Allow log forwarding from GOAD to SIEM
add_firewall_rule "opt1" "pass" "tcp" "************/24" "*************/26" "9997" "Splunk log forwarding"

# Allow agent communication
add_firewall_rule "opt2" "pass" "tcp" "************/24" "**************" "8000,8889" "Velociraptor agents"

# Allow management access
add_firewall_rule "lan" "pass" "any" "***********/24" "*************/24" "any" "Management access"

echo "pfSense configuration completed"
```

---

!!! tip "pfSense Configuration Best Practices"
    - Always backup configuration before making changes
    - Test rules in a lab environment first
    - Use descriptive names for all rules and interfaces
    - Implement least privilege access principles
    - Monitor firewall logs regularly for anomalies

!!! warning "Security Considerations"
    - Never expose GOAD environment directly to the internet
    - Regularly update pfSense and security rules
    - Monitor for unauthorized configuration changes
    - Implement strong authentication for pfSense access
    - Review and audit firewall rules periodically

!!! info "Integration Notes"
    - pfSense logs can be valuable for network security monitoring
    - Consider using pfSense packages like ntopng for additional monitoring
    - Coordinate firewall rules with GOAD-Blue security policies
    - Document all custom configurations for team reference
    - Test connectivity after each configuration change

## 🔄 Advanced Configuration

### **High Availability Setup**

```yaml
# pfSense HA configuration for production environments
ha_configuration:
  primary_node:
    hostname: "pfsense-primary"
    ip_address: "***********"
    interfaces:
      wan: "em0"
      lan: "em1"
      sync: "em2"  # Dedicated sync interface

  secondary_node:
    hostname: "pfsense-secondary"
    ip_address: "***********"
    interfaces:
      wan: "em0"
      lan: "em1"
      sync: "em2"

  carp_settings:
    # Virtual IP addresses
    wan_vip: "***********0"
    lan_vip: "***********1"

    # CARP passwords (change these!)
    wan_password: "secure_wan_password"
    lan_password: "secure_lan_password"

  sync_settings:
    sync_interface: "em2"
    sync_peer_ip: "********"  # Secondary node sync IP
    synchronize_states: true
    synchronize_config: true
    synchronize_nat: true
    synchronize_rules: true
```

### **Load Balancing Configuration**

```bash
# Navigate to: Services > Load Balancer

# Configure load balancing for GOAD-Blue SIEM services

# Pool 1: Splunk Search Heads
Name: splunk_search_heads
Mode: Load Balance
Port: 8000
Monitor: HTTP
Servers:
  - *************0:8000 (Primary)
  - *************1:8000 (Secondary)

# Pool 2: Elasticsearch Cluster
Name: elasticsearch_cluster
Mode: Load Balance
Port: 9200
Monitor: HTTP
Servers:
  - **************:9200
  - **************:9200
  - **************:9200

# Virtual Server Configuration
Name: goad_blue_siem
IP Address: *************
Port: 8000
Pool: splunk_search_heads
```

### **VPN Configuration for Remote Access**

```bash
# Navigate to: VPN > OpenVPN > Servers

# GOAD-Blue Remote Access VPN
Server Mode: Remote Access (SSL/TLS)
Protocol: UDP
Interface: WAN
Local Port: 1194

# Cryptographic Settings
TLS Authentication: Enabled
Peer Certificate Authority: GOAD-Blue-CA
Server Certificate: GOAD-Blue-Server
DH Parameter Length: 2048
Encryption Algorithm: AES-256-CBC
Auth Digest Algorithm: SHA256

# Tunnel Settings
IPv4 Tunnel Network: ********/24
IPv4 Local Network: ***********/24,*************/24,************/24
Concurrent Connections: 10

# Client Settings
Dynamic IP: Enabled
DNS Default Domain: goad-blue.local
DNS Servers: ***********,*******
```

## 🔧 Troubleshooting Common Issues

### **Connectivity Problems**

```bash
# Diagnostic commands for pfSense troubleshooting

# 1. Check interface status
# Navigate to: Status > Interfaces
# Verify all interfaces are up and have correct IP addresses

# 2. Test routing
# Navigate to: Diagnostics > Ping
# Test connectivity between networks:
ping -c 4 ************0  # GOAD DC
ping -c 4 *************0 # Splunk server

# 3. Check firewall logs
# Navigate to: Status > System Logs > Firewall
# Look for blocked traffic that should be allowed

# 4. Verify NAT rules
# Navigate to: Diagnostics > States
# Check active connections and NAT translations

# 5. Test DNS resolution
# Navigate to: Diagnostics > DNS Lookup
# Verify DNS resolution is working
```

### **Performance Optimization**

```bash
# Navigate to: System > Advanced > Networking

# Optimize for GOAD-Blue traffic:
Hardware Checksum Offloading: Enabled (if supported)
Hardware TCP Segmentation Offloading: Enabled (if supported)
Hardware Large Receive Offloading: Enabled (if supported)

# Firewall optimization:
Firewall Optimization: Conservative (for stability)
Firewall Maximum States: 100000 (increase if needed)
Firewall Maximum Table Entries: 200000

# Memory optimization:
# Navigate to: System > Advanced > Miscellaneous
Use RAM Disks: Enabled (for /tmp and /var)
RAM Disk Size: 256MB
```

### **Log Analysis and Monitoring**

```bash
# pfSense log analysis for GOAD-Blue integration

# 1. Monitor firewall blocks
grep "block" /var/log/filter.log | tail -20

# 2. Check DHCP assignments
grep "DHCPACK" /var/log/dhcpd.log | tail -10

# 3. Monitor VPN connections
grep "openvpn" /var/log/openvpn.log | tail -15

# 4. Check system performance
top -d 1
vmstat 5
iostat 5

# 5. Network interface statistics
netstat -i
pfctl -s info
```

## 📊 Monitoring Integration

### **SNMP Configuration**

```bash
# Navigate to: Services > SNMP

# Enable SNMP for monitoring integration
SNMP Daemon: Enabled
Polling Port: 161
System Location: GOAD-Blue Lab
System Contact: <EMAIL>

# Community Strings
Community: goadblue_readonly
Network: *************/24

# Modules to Load:
- netgraph
- pf
- hostres
- bridge
- ucd
```

### **Grafana Dashboard Integration**

```json
{
  "dashboard": {
    "title": "pfSense GOAD-Blue Monitoring",
    "panels": [
      {
        "title": "Interface Traffic",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(pfsense_interface_bytes_received[5m])",
            "legendFormat": "{{interface}} RX"
          },
          {
            "expr": "rate(pfsense_interface_bytes_transmitted[5m])",
            "legendFormat": "{{interface}} TX"
          }
        ]
      },
      {
        "title": "Firewall States",
        "type": "stat",
        "targets": [
          {
            "expr": "pfsense_firewall_states_current",
            "legendFormat": "Current States"
          }
        ]
      },
      {
        "title": "Top Blocked IPs",
        "type": "table",
        "targets": [
          {
            "expr": "topk(10, rate(pfsense_firewall_blocks_total[1h]))",
            "format": "table"
          }
        ]
      }
    ]
  }
}
```

## 🔐 Security Hardening Checklist

### **Essential Security Configurations**

```yaml
security_checklist:
  authentication:
    - change_default_passwords: true
    - enable_two_factor_auth: true
    - disable_unused_accounts: true
    - set_session_timeout: "30_minutes"

  network_security:
    - disable_unused_services: true
    - enable_anti_spoofing: true
    - configure_bogon_networks: true
    - enable_syn_flood_protection: true

  access_control:
    - restrict_admin_access: "management_network_only"
    - disable_ssh_root_login: true
    - enable_ssh_key_auth: true
    - configure_fail2ban: true

  monitoring:
    - enable_detailed_logging: true
    - configure_log_rotation: true
    - setup_log_forwarding: true
    - enable_intrusion_detection: true

  updates:
    - enable_auto_updates: false  # Manual control preferred
    - schedule_regular_updates: "monthly"
    - backup_before_updates: true
    - test_updates_in_lab: true
```

### **Backup and Recovery**

```bash
# Automated backup script for pfSense configuration

#!/bin/bash
# pfSense backup automation

PFSENSE_HOST="***********"
BACKUP_DIR="/opt/goad-blue/backups/pfsense"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Download configuration backup
curl -k -u "admin:password" \
    "https://$PFSENSE_HOST/diag_backup.php?download=download&donotbackuprrd=yes" \
    -o "$BACKUP_DIR/pfsense_config_$DATE.xml"

# Verify backup
if [ -f "$BACKUP_DIR/pfsense_config_$DATE.xml" ]; then
    echo "Backup successful: pfsense_config_$DATE.xml"

    # Keep only last 30 backups
    find "$BACKUP_DIR" -name "pfsense_config_*.xml" -mtime +30 -delete
else
    echo "Backup failed!"
    exit 1
fi

# Optional: Upload to remote storage
# rsync -av "$BACKUP_DIR/" user@backup-server:/backups/pfsense/
```

## 📚 Configuration Templates

### **GOAD-Blue Specific Rules Template**

```xml
<!-- pfSense configuration template for GOAD-Blue -->
<pfsense>
  <filter>
    <!-- GOAD to SIEM log forwarding -->
    <rule>
      <type>pass</type>
      <interface>opt1</interface>
      <ipprotocol>inet</ipprotocol>
      <protocol>tcp</protocol>
      <source>
        <network>************/24</network>
      </source>
      <destination>
        <network>*************/26</network>
        <port>9997</port>
      </destination>
      <descr>Allow Splunk log forwarding from GOAD</descr>
    </rule>

    <!-- Velociraptor agent communication -->
    <rule>
      <type>pass</type>
      <interface>opt2</interface>
      <ipprotocol>inet</ipprotocol>
      <protocol>tcp</protocol>
      <source>
        <network>************/24</network>
      </source>
      <destination>
        <address>**************</address>
        <port>8000</port>
      </destination>
      <descr>Allow Velociraptor agent communication</descr>
    </rule>

    <!-- Block GOAD internet access -->
    <rule>
      <type>block</type>
      <interface>opt2</interface>
      <ipprotocol>inet</ipprotocol>
      <protocol>any</protocol>
      <source>
        <network>************/24</network>
      </source>
      <destination>
        <any/>
      </destination>
      <descr>Block internet access from GOAD (isolation)</descr>
    </rule>
  </filter>
</pfsense>
```

---

!!! tip "Advanced pfSense Tips"
    - Use aliases for frequently referenced IP addresses and ports
    - Implement traffic shaping to prioritize critical GOAD-Blue traffic
    - Set up automated configuration backups
    - Monitor pfSense performance metrics in your SIEM
    - Use pfSense packages like ntopng for enhanced network visibility

!!! warning "Production Considerations"
    - Always test configuration changes in a lab environment first
    - Implement high availability for critical production deployments
    - Regularly review and audit firewall rules
    - Monitor for configuration drift and unauthorized changes
    - Maintain detailed documentation of all custom configurations

!!! info "Integration Best Practices"
    - Coordinate pfSense rules with GOAD-Blue security policies
    - Use consistent naming conventions across all network components
    - Implement centralized logging for all network security events
    - Regular security assessments of firewall configurations
    - Train team members on pfSense administration and troubleshooting
