# ws01

- Extension name : `ws01`
- Description : Add a Windows 10 workstation to the lab GOAD or GOAD Light in the domain sevenkingdoms.local
- Compatibility  : GOAD / GOAD-Light /Goad-Mini 
- Providers : virtualbox/azure/vmware/aws/ludus/proxmox

- Add a machine  : {{lab_name}}-WS01 (casterlyrock.sevenkingdoms.local)  (ip_range.31)

!!! warning "rearm"
    The vm is not armed by default (90 days trials), connect to the vm with vagrant/vagrant and run as admin `slmgr -rearm` to rearm the box. (need a restart)

!!! warning "aws"
    AWS doesn't got any windows 10 so for aws the vm is a windows server 2019

## Lab info
- Lab infos:
    - hostname: casterlyrock 
    - Users:
        - Administrators :
            - tywin.lannister
            - jaime.lannister
        - RDP Users:
            - Lannister group

- Features :
    - run_as_ppl
    - powershell restricted
    - asr rules :
        - block lsass stealing
        - block PSExec and WMI

## Prerequisites

- GOAD or GOAD-Light installation

- On ludus prepare template :
```
ludus templates add -d win10-21h1-x64-enterprise
ludus templates build
```

## Installation

- select your instance
```
load <instance_id>
```

- install the ws01 extension
```
install_extension ws01
```

## thanks

- asr rules implementation : https://github.com/zuesdevil (https://github.com/Orange-Cyberdefense/GOAD/pull/172)