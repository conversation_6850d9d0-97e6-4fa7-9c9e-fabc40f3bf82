#!/bin/bash
# Velociraptor installation script for GOAD-Blue

set -e

echo "=== Installing Velociraptor for GOAD-Blue ==="

# Configuration variables
VELOCIRAPTOR_VERSION="0.7.0"
VELOCIRAPTOR_USER="velociraptor"
VELOCIRAPTOR_HOME="/opt/velociraptor"
VELOCIRAPTOR_CONFIG="/etc/velociraptor"
VELOCIRAPTOR_LOGS="/var/log/velociraptor"

# Create velociraptor user
echo "Creating Velociraptor user..."
if ! id "$VELOCIRAPTOR_USER" &>/dev/null; then
    useradd -r -m -s /bin/bash -d $VELOCIRAPTOR_HOME $VELOCIRAPTOR_USER
fi

# Create directories
echo "Creating Velociraptor directories..."
mkdir -p $VELOCIRAPTOR_HOME/{datastore,filestore,logs,clients}
mkdir -p $VELOCIRAPTOR_CONFIG
mkdir -p $VELOCIRAPTOR_LOGS
mkdir -p /usr/local/bin

# Download Velociraptor binary
echo "Downloading Velociraptor ${VELOCIRAPTOR_VERSION}..."
cd /tmp
VELOCIRAPTOR_URL="https://github.com/Velocidex/velociraptor/releases/download/v${VELOCIRAPTOR_VERSION}/velociraptor-v${VELOCIRAPTOR_VERSION}-linux-amd64"

if [ ! -f "velociraptor-${VELOCIRAPTOR_VERSION}" ]; then
    wget -O "velociraptor-${VELOCIRAPTOR_VERSION}" "$VELOCIRAPTOR_URL" || {
        echo "Failed to download Velociraptor. Please check the URL or download manually."
        exit 1
    }
fi

# Install Velociraptor binary
echo "Installing Velociraptor binary..."
cp "velociraptor-${VELOCIRAPTOR_VERSION}" /usr/local/bin/velociraptor
chmod +x /usr/local/bin/velociraptor
chown root:root /usr/local/bin/velociraptor

# Generate Velociraptor configuration
echo "Generating Velociraptor configuration..."
cat > $VELOCIRAPTOR_CONFIG/server.config.yaml << EOF
# Velociraptor Server Configuration for GOAD-Blue
version:
  name: velociraptor
  version: "${VELOCIRAPTOR_VERSION}"
  commit: goad-blue
  build_time: "$(date -u +%Y-%m-%dT%H:%M:%SZ)"

Client:
  server_urls:
    - https://$(hostname -I | awk '{print $1}'):8000/
  ca_certificate: |
$(cat $VELOCIRAPTOR_CONFIG/ca.pem | sed 's/^/    /')
  nonce: goad-blue-nonce
  writeback_darwin: /usr/local/var/lib/velociraptor/
  writeback_linux: /etc/velociraptor/
  writeback_windows: C:\\\\Program Files\\\\Velociraptor\\\\
  max_poll: 60
  max_poll_std: 30

API:
  bind_address: 0.0.0.0
  bind_port: 8001
  bind_scheme: tcp
  pinned_gw_name: GOAD-Blue-GW

GUI:
  bind_address: 0.0.0.0
  bind_port: 8889
  gw_certificate: |
$(cat $VELOCIRAPTOR_CONFIG/server.pem | sed 's/^/    /')
  gw_private_key: |
$(cat $VELOCIRAPTOR_CONFIG/server.key | sed 's/^/    /')
  internal_cidr:
    - 127.0.0.1/32
    - ***********/16
    - 10.0.0.0/8
    - **********/12
  authenticator:
    type: Basic

Frontend:
  bind_address: 0.0.0.0
  bind_port: 8000
  certificate: |
$(cat $VELOCIRAPTOR_CONFIG/server.pem | sed 's/^/    /')
  private_key: |
$(cat $VELOCIRAPTOR_CONFIG/server.key | sed 's/^/    /')
  dyn_dns: {}

Datastore:
  implementation: FileBaseDataStore
  location: $VELOCIRAPTOR_HOME/datastore
  filestore_directory: $VELOCIRAPTOR_HOME/filestore

Logging:
  output_directory: $VELOCIRAPTOR_LOGS
  separate_logs_per_component: true
  rotation_time: 604800
  max_age: 31536000

Monitoring:
  bind_address: 127.0.0.1
  bind_port: 8003

autocert_domain: velociraptor.goad-blue.local
autocert_cert_cache: $VELOCIRAPTOR_HOME/acme_cache

defaults:
  hunt_expiry_hours: 168
  notebook_cell_timeout_min: 10

server_type: linux
EOF

# Generate SSL certificates
echo "Generating SSL certificates..."
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout $VELOCIRAPTOR_CONFIG/server.key \
    -out $VELOCIRAPTOR_CONFIG/server.pem \
    -subj "/C=US/ST=Lab/L=Lab/O=GOAD-Blue/CN=velociraptor.goad-blue.local" \
    -extensions v3_req \
    -config <(cat /etc/ssl/openssl.cnf <(printf "\n[v3_req]\nsubjectAltName=DNS:velociraptor.goad-blue.local,DNS:localhost,IP:$(hostname -I | awk '{print $1}'),IP:127.0.0.1"))

# Generate CA certificate
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout $VELOCIRAPTOR_CONFIG/ca.key \
    -out $VELOCIRAPTOR_CONFIG/ca.pem \
    -subj "/C=US/ST=Lab/L=Lab/O=GOAD-Blue/CN=GOAD-Blue-CA"

# Set permissions
chown -R $VELOCIRAPTOR_USER:$VELOCIRAPTOR_USER $VELOCIRAPTOR_HOME
chown -R $VELOCIRAPTOR_USER:$VELOCIRAPTOR_USER $VELOCIRAPTOR_CONFIG
chown -R $VELOCIRAPTOR_USER:$VELOCIRAPTOR_USER $VELOCIRAPTOR_LOGS
chmod 600 $VELOCIRAPTOR_CONFIG/*.key
chmod 644 $VELOCIRAPTOR_CONFIG/*.pem

# Create systemd service
echo "Creating Velociraptor systemd service..."
cat > /etc/systemd/system/velociraptor.service << EOF
[Unit]
Description=Velociraptor Server
After=network.target

[Service]
Type=simple
User=$VELOCIRAPTOR_USER
Group=$VELOCIRAPTOR_USER
ExecStart=/usr/local/bin/velociraptor --config $VELOCIRAPTOR_CONFIG/server.config.yaml frontend -v
WorkingDirectory=$VELOCIRAPTOR_HOME
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=velociraptor

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$VELOCIRAPTOR_HOME $VELOCIRAPTOR_LOGS $VELOCIRAPTOR_CONFIG

[Install]
WantedBy=multi-user.target
EOF

# Configure UFW for Velociraptor
echo "Configuring firewall for Velociraptor..."
ufw allow 8000/tcp   # Frontend
ufw allow 8001/tcp   # API
ufw allow 8889/tcp   # GUI
ufw allow 8003/tcp   # Monitoring

# Start and enable Velociraptor service
echo "Starting Velociraptor service..."
systemctl daemon-reload
systemctl enable velociraptor
systemctl start velociraptor

# Wait for Velociraptor to be ready
echo "Waiting for Velociraptor to be ready..."
timeout=120
counter=0
while ! curl -k https://localhost:8889 >/dev/null 2>&1; do
    if [ $counter -ge $timeout ]; then
        echo "Timeout waiting for Velociraptor to start"
        exit 1
    fi
    echo "Waiting for Velociraptor... ($counter/$timeout)"
    sleep 5
    counter=$((counter + 5))
done

# Create initial admin user
echo "Creating initial admin user..."
sudo -u $VELOCIRAPTOR_USER /usr/local/bin/velociraptor --config $VELOCIRAPTOR_CONFIG/server.config.yaml user add admin --role administrator --password admin

# Create GOAD-Blue artifacts
echo "Creating GOAD-Blue artifacts..."
mkdir -p $VELOCIRAPTOR_HOME/artifacts

# Windows Event Log Collection Artifact
cat > $VELOCIRAPTOR_HOME/artifacts/GOAD.Windows.EventLogs.yaml << 'EOF'
name: GOAD.Windows.EventLogs
description: Collect Windows Event Logs for GOAD-Blue analysis
type: CLIENT

parameters:
  - name: EventLogs
    default: |
      - System
      - Security
      - Application
      - Microsoft-Windows-Sysmon/Operational
      - Microsoft-Windows-PowerShell/Operational
      - Microsoft-Windows-WinRM/Operational

sources:
  - precondition:
      SELECT OS From info() where OS = 'windows'
    
    query: |
      SELECT * FROM foreach(
        row={
          SELECT * FROM parse_csv(filename=EventLogs, accessor="data")
        },
        query={
          SELECT EventTime, Computer, Channel, EventID, EventRecordID,
                 EventData, UserData, Message
          FROM watch_evtx(filename=expand(path='C:/Windows/System32/winevt/Logs/' + _value + '.evtx'))
          WHERE EventTime > now() - 3600
        })
EOF

# Process Monitoring Artifact
cat > $VELOCIRAPTOR_HOME/artifacts/GOAD.Windows.ProcessMonitoring.yaml << 'EOF'
name: GOAD.Windows.ProcessMonitoring
description: Monitor process creation for GOAD-Blue
type: CLIENT_EVENT

sources:
  - precondition:
      SELECT OS From info() where OS = 'windows'
    
    query: |
      SELECT timestamp(epoch=Timestamp) AS EventTime,
             Pid, Ppid, Name, CommandLine, Username
      FROM watch_monitoring(category="ProcessCreation")
EOF

# Set artifact permissions
chown -R $VELOCIRAPTOR_USER:$VELOCIRAPTOR_USER $VELOCIRAPTOR_HOME/artifacts

# Create Velociraptor monitoring script
cat > /opt/goad-blue/scripts/velociraptor-monitor.sh << 'EOF'
#!/bin/bash
# Velociraptor monitoring script for GOAD-Blue

echo "=== Velociraptor System Status ==="
systemctl status velociraptor --no-pager
echo ""

echo "=== Velociraptor Process Information ==="
ps aux | grep velociraptor | grep -v grep
echo ""

echo "=== Velociraptor Disk Usage ==="
du -sh /opt/velociraptor/
echo ""

echo "=== Active Clients ==="
curl -k -s https://localhost:8889/api/v1/GetClients | jq '.items | length' 2>/dev/null || echo "API not accessible"
echo ""

echo "=== Recent Hunts ==="
curl -k -s https://localhost:8889/api/v1/ListHunts | jq '.items[0:5] | .[] | {hunt_id, description, state}' 2>/dev/null || echo "API not accessible"
echo ""
EOF

chmod +x /opt/goad-blue/scripts/velociraptor-monitor.sh

# Create client deployment script
cat > /opt/goad-blue/scripts/deploy-velociraptor-client.sh << 'EOF'
#!/bin/bash
# Deploy Velociraptor client to GOAD systems

VELOCIRAPTOR_SERVER="**************"
CLIENT_CONFIG="/opt/velociraptor/client.config.yaml"

echo "=== Deploying Velociraptor Client ==="

# Generate client configuration
sudo -u velociraptor /usr/local/bin/velociraptor --config /etc/velociraptor/server.config.yaml config client > $CLIENT_CONFIG

echo "Client configuration generated: $CLIENT_CONFIG"
echo "Deploy this configuration to GOAD systems"
echo "Windows: Copy to C:\\Program Files\\Velociraptor\\client.config.yaml"
echo "Linux: Copy to /etc/velociraptor/client.config.yaml"
EOF

chmod +x /opt/goad-blue/scripts/deploy-velociraptor-client.sh

# Create completion marker
touch /opt/goad-blue/.velociraptor-installed

echo "=== Velociraptor Installation Completed ==="
echo "Web Interface: https://$(hostname -I | awk '{print $1}'):8889"
echo "Username: admin"
echo "Password: admin"
echo "API Endpoint: https://$(hostname -I | awk '{print $1}'):8001"
echo "Client Endpoint: https://$(hostname -I | awk '{print $1}'):8000"
echo ""
echo "To monitor Velociraptor: /opt/goad-blue/scripts/velociraptor-monitor.sh"
echo "To deploy clients: /opt/goad-blue/scripts/deploy-velociraptor-client.sh"
