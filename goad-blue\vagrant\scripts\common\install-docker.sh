#!/bin/bash
# Docker installation script for GOAD-Blue VMs

set -e

echo "=== Installing Docker for GOAD-Blue ==="

# Remove old Docker versions
echo "Removing old Docker versions..."
apt-get remove -y docker docker-engine docker.io containerd runc || true

# Update package index
apt-get update -y

# Install packages to allow apt to use a repository over HTTPS
apt-get install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release

# Add Docker's official GPG key
echo "Adding Docker GPG key..."
mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# Set up the repository
echo "Setting up Docker repository..."
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

# Update package index again
apt-get update -y

# Install Docker Engine
echo "Installing Docker Engine..."
apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# Install Docker Compose (standalone)
echo "Installing Docker Compose..."
DOCKER_COMPOSE_VERSION="2.21.0"
curl -L "https://github.com/docker/compose/releases/download/v${DOCKER_COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Create docker-compose symlink
ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose

# Start and enable Docker
echo "Starting Docker service..."
systemctl start docker
systemctl enable docker

# Add users to docker group
echo "Adding users to docker group..."
usermod -aG docker vagrant || true
usermod -aG docker goad-blue || true

# Configure Docker daemon
echo "Configuring Docker daemon..."
mkdir -p /etc/docker
cat > /etc/docker/daemon.json << 'EOF'
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "live-restore": true,
  "userland-proxy": false,
  "experimental": false,
  "metrics-addr": "0.0.0.0:9323",
  "default-address-pools": [
    {
      "base": "**********/12",
      "size": 24
    }
  ]
}
EOF

# Restart Docker to apply configuration
systemctl restart docker

# Configure Docker log rotation
cat > /etc/logrotate.d/docker << 'EOF'
/var/lib/docker/containers/*/*.log {
    rotate 7
    daily
    compress
    size=1M
    missingok
    delaycompress
    copytruncate
}
EOF

# Install Docker security tools
echo "Installing Docker security tools..."
pip3 install docker-compose

# Create Docker monitoring script
cat > /opt/goad-blue/scripts/docker-monitor.sh << 'EOF'
#!/bin/bash
# Docker monitoring script for GOAD-Blue

echo "=== Docker System Information ==="
echo "Docker Version: $(docker --version)"
echo "Docker Compose Version: $(docker-compose --version)"
echo ""

echo "=== Running Containers ==="
docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
echo ""

echo "=== Docker System Usage ==="
docker system df
echo ""

echo "=== Docker Network List ==="
docker network ls
echo ""

echo "=== Docker Volume List ==="
docker volume ls
echo ""
EOF

chmod +x /opt/goad-blue/scripts/docker-monitor.sh

# Create Docker cleanup script
cat > /opt/goad-blue/scripts/docker-cleanup.sh << 'EOF'
#!/bin/bash
# Docker cleanup script for GOAD-Blue

echo "=== Docker Cleanup Started ==="

# Remove stopped containers
echo "Removing stopped containers..."
docker container prune -f

# Remove unused images
echo "Removing unused images..."
docker image prune -f

# Remove unused volumes
echo "Removing unused volumes..."
docker volume prune -f

# Remove unused networks
echo "Removing unused networks..."
docker network prune -f

# Remove build cache
echo "Removing build cache..."
docker builder prune -f

echo "=== Docker Cleanup Completed ==="
docker system df
EOF

chmod +x /opt/goad-blue/scripts/docker-cleanup.sh

# Configure UFW for Docker
echo "Configuring UFW for Docker..."
ufw allow 2376/tcp  # Docker daemon
ufw allow 2377/tcp  # Docker swarm
ufw allow 7946/tcp  # Docker swarm
ufw allow 7946/udp  # Docker swarm
ufw allow 4789/udp  # Docker overlay networks

# Test Docker installation
echo "Testing Docker installation..."
docker run --rm hello-world

# Verify Docker Compose
echo "Testing Docker Compose..."
docker-compose --version

# Create completion marker
touch /opt/goad-blue/.docker-installed

echo "=== Docker Installation Completed ==="
echo "Docker Version: $(docker --version)"
echo "Docker Compose Version: $(docker-compose --version)"
echo "Users in docker group: $(getent group docker | cut -d: -f4)"
