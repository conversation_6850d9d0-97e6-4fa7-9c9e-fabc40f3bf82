/* GOAD-Blue Custom Styles */

/* Brand Colors */
:root {
  --goad-blue-primary: #1976d2;
  --goad-blue-secondary: #42a5f5;
  --goad-blue-accent: #64b5f6;
  --goad-blue-dark: #0d47a1;
  --goad-blue-light: #e3f2fd;
  --goad-blue-success: #4caf50;
  --goad-blue-warning: #ff9800;
  --goad-blue-error: #f44336;
  --goad-blue-info: #2196f3;
}

/* Custom Header Styling */
.md-header {
  background: linear-gradient(135deg, var(--goad-blue-primary), var(--goad-blue-secondary));
}

.md-header__title {
  font-weight: 600;
  color: white;
}

/* Logo Styling */
.md-header__button.md-logo img,
.md-header__button.md-logo svg {
  height: 2rem;
  width: auto;
}

/* Navigation Styling */
.md-tabs__link {
  font-weight: 500;
}

.md-tabs__link--active {
  color: var(--goad-blue-accent) !important;
}

/* Content Area Enhancements */
.md-content__inner {
  padding-bottom: 2rem;
}

/* Custom Admonition Styles */
.md-typeset .admonition.goad-blue {
  border-color: var(--goad-blue-primary);
}

.md-typeset .admonition.goad-blue > .admonition-title {
  background-color: var(--goad-blue-light);
  border-color: var(--goad-blue-primary);
}

.md-typeset .admonition.goad-blue > .admonition-title::before {
  background-color: var(--goad-blue-primary);
  mask-image: var(--md-admonition-icon--note);
}

/* Code Block Enhancements */
.md-typeset .highlight {
  border-radius: 0.5rem;
  overflow: hidden;
}

.md-typeset .highlight .filename {
  background: var(--goad-blue-dark);
  color: white;
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  margin: 0;
  border-radius: 0.5rem 0.5rem 0 0;
}

/* Table Styling */
.md-typeset table:not([class]) {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.md-typeset table:not([class]) th {
  background: var(--goad-blue-primary);
  color: white;
  font-weight: 600;
}

.md-typeset table:not([class]) tr:nth-child(even) {
  background: var(--goad-blue-light);
}

/* Button Styling */
.md-button {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.md-button--primary {
  background: var(--goad-blue-primary);
  border-color: var(--goad-blue-primary);
}

.md-button--primary:hover {
  background: var(--goad-blue-dark);
  border-color: var(--goad-blue-dark);
  transform: translateY(-1px);
}

/* Badge Styling */
.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge--primary {
  background: var(--goad-blue-primary);
  color: white;
}

.badge--success {
  background: var(--goad-blue-success);
  color: white;
}

.badge--warning {
  background: var(--goad-blue-warning);
  color: white;
}

.badge--error {
  background: var(--goad-blue-error);
  color: white;
}

/* Component Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  margin: 0.25rem 0;
}

.status-indicator--online {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid var(--goad-blue-success);
  color: var(--goad-blue-success);
}

.status-indicator--offline {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid var(--goad-blue-error);
  color: var(--goad-blue-error);
}

.status-indicator--warning {
  background: rgba(255, 152, 0, 0.1);
  border: 1px solid var(--goad-blue-warning);
  color: var(--goad-blue-warning);
}

.status-indicator::before {
  content: "";
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background: currentColor;
}

/* Architecture Diagrams */
.architecture-diagram {
  background: white;
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Command Line Examples */
.command-example {
  background: #1e1e1e;
  color: #d4d4d4;
  padding: 1rem;
  border-radius: 0.5rem;
  font-family: 'Roboto Mono', monospace;
  margin: 1rem 0;
  position: relative;
}

.command-example::before {
  content: "$ ";
  color: var(--goad-blue-accent);
  font-weight: bold;
}

.command-example .comment {
  color: #6a9955;
}

.command-example .string {
  color: #ce9178;
}

.command-example .keyword {
  color: #569cd6;
}

/* Network Diagram Styling */
.network-segment {
  background: var(--goad-blue-light);
  border: 2px solid var(--goad-blue-primary);
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 0.5rem;
  text-align: center;
}

.network-segment h4 {
  color: var(--goad-blue-dark);
  margin-bottom: 0.5rem;
}

/* Progress Indicators */
.progress-bar {
  width: 100%;
  height: 0.5rem;
  background: #e0e0e0;
  border-radius: 0.25rem;
  overflow: hidden;
  margin: 0.5rem 0;
}

.progress-bar__fill {
  height: 100%;
  background: linear-gradient(90deg, var(--goad-blue-primary), var(--goad-blue-secondary));
  transition: width 0.3s ease;
}

/* Installation Steps */
.installation-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin: 1.5rem 0;
  padding: 1rem;
  border-radius: 0.5rem;
  background: var(--goad-blue-light);
}

.installation-step__number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: var(--goad-blue-primary);
  color: white;
  border-radius: 50%;
  font-weight: bold;
  flex-shrink: 0;
}

.installation-step__content h4 {
  margin-top: 0;
  color: var(--goad-blue-dark);
}

/* Component Cards */
.component-card {
  background: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin: 1rem 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid var(--goad-blue-primary);
}

.component-card h3 {
  color: var(--goad-blue-dark);
  margin-top: 0;
}

.component-card__features {
  list-style: none;
  padding: 0;
}

.component-card__features li {
  padding: 0.25rem 0;
  position: relative;
  padding-left: 1.5rem;
}

.component-card__features li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--goad-blue-success);
  font-weight: bold;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .installation-step {
    flex-direction: column;
    text-align: center;
  }
  
  .installation-step__number {
    align-self: center;
  }
  
  .component-card {
    margin: 0.5rem 0;
    padding: 1rem;
  }
}

/* Dark Mode Adjustments */
[data-md-color-scheme="slate"] {
  --goad-blue-light: rgba(25, 118, 210, 0.1);
}

[data-md-color-scheme="slate"] .component-card {
  background: var(--md-default-bg-color);
  border-color: var(--goad-blue-primary);
}

[data-md-color-scheme="slate"] .network-segment {
  background: rgba(25, 118, 210, 0.1);
  border-color: var(--goad-blue-primary);
}

[data-md-color-scheme="slate"] .installation-step {
  background: rgba(25, 118, 210, 0.1);
}

/* Print Styles */
@media print {
  .md-header,
  .md-tabs,
  .md-sidebar,
  .md-footer {
    display: none !important;
  }
  
  .md-content {
    margin: 0 !important;
  }
  
  .component-card,
  .installation-step {
    break-inside: avoid;
  }
}
