#jinja2: newline_sequence:"\r\n"
;SQL Server Configuration File
[OPTIONS]
IACCEPTSQLSERVERLICENSETERMS="True"
ACTION="Install"
ENU="True"
QUIET="True"
QUIETSIMPLE="False"
UpdateEnabled="False"
ERRORREPORTING="False"
USEMICROSOFTUPDATE="False"
FEATURES=SQLENGINE,FULLTEXT
UpdateSource="MU"
HELP="False"
INDICATEPROGRESS="False"
; X86="False"
INSTALLSHAREDDIR="C:\Program Files\Microsoft SQL Server"
INSTALLSHAREDWOWDIR="C:\Program Files (x86)\Microsoft SQL Server"
INSTANCENAME="{{ sql_instance_name }}"
SQMREPORTING="False"
INSTANCEID="{{ sql_instance_name }}"
; RSINSTALLMODE="DefaultNativeMode"
INSTANCEDIR="C:\Program Files\Microsoft SQL Server"
AGTSVCACCOUNT="NT AUTHORITY\NETWORK SERVICE"
AGTSVCSTARTUPTYPE="Automatic"
; COMMFABRICPORT="0"
; COMMFABRICNETWORKLEVEL="0"
; COMMFABRICENCRYPTION="0"
; MATRIXCMBRICKCOMMPORT="0"
SQLSVCSTARTUPTYPE="Automatic"
FILESTREAMLEVEL="0"
ENABLERANU="False"
SQLCOLLATION="SQL_Latin1_General_CP1_CI_AS"
SQLSVCACCOUNT="{{ SQLSVCACCOUNT }}"
{% if SQLSVCPASSWORD != "" %}
SQLSVCPASSWORD="{{ SQLSVCPASSWORD }}"
{% endif %}
SAPWD="{{sa_password}}"
SQLSYSADMINACCOUNTS="{{ SQLYSADMIN }}"
;ADDCURRENTUSERASSQLADMIN="True"
ADDCURRENTUSERASSQLADMIN="False"
TCPENABLED="1"
NPENABLED="0"
BROWSERSVCSTARTUPTYPE="Disabled"
; RSSVCSTARTUPTYPE="manual"
; FTSVCACCOUNT="NT Service\MSSQLFDLauncher"