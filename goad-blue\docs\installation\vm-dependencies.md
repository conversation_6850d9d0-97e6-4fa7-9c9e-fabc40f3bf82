# VM Dependencies and Installation Scripts

This document provides a comprehensive overview of all VM dependencies, installation scripts, and provisioning requirements for GOAD-Blue.

## 🎯 Overview

GOAD-Blue uses a multi-VM architecture with automated provisioning through Vagrant, Ansible, and custom installation scripts. Each component has specific dependencies and installation procedures.

## 📋 VM Architecture Matrix

| Component | OS | Memory | CPU | Disk | Network | Dependencies |
|-----------|----|---------|----|------|---------|--------------|
| **Splunk Server** | Ubuntu 22.04 | 8GB | 4 cores | 100GB | SIEM | Java, Splunk Enterprise |
| **Security Onion** | Ubuntu 22.04 | 16GB | 4 cores | 200GB | Monitor | Docker, SO Platform |
| **Malcolm** | Ubuntu 22.04 | 12GB | 4 cores | 100GB | Monitor | Docker, <PERSON> |
| **Velociraptor** | Ubuntu 22.04 | 4GB | 2 cores | 50GB | SIEM | Go Runtime, Velociraptor |
| **MISP** | Ubuntu 22.04 | 6GB | 2 cores | 50GB | SIEM | PHP, MySQL, Apache |
| **FLARE-VM** | Windows 10 | 8GB | 4 cores | 100GB | Analysis | .NET, FLARE Tools |
| **Elasticsearch** | Ubuntu 22.04 | 8GB | 4 cores | 100GB | SIEM | Java, Elastic Stack |
| **pfSense** | FreeBSD | 2GB | 2 cores | 20GB | All | pfSense CE |
| **Management** | Ubuntu 22.04 | 4GB | 2 cores | 50GB | Mgmt | Ansible, Tools |

## 🔧 Host System Requirements

### **Minimum Requirements:**
- **CPU**: 16 cores (32 threads recommended)
- **Memory**: 64GB RAM (128GB recommended)
- **Storage**: 1TB SSD (2TB recommended)
- **Network**: Gigabit Ethernet
- **Virtualization**: Intel VT-x/AMD-V enabled

### **Software Prerequisites:**
```bash
# Virtualization Platform (choose one)
VirtualBox 7.0+
VMware Workstation 17.0+
VMware ESXi 7.0+
Proxmox VE 7.0+

# Provisioning Tools
Vagrant 2.3.0+
Ansible 6.0.0+
Packer 1.9.0+
Terraform 1.5.0+

# Container Platform
Docker 24.0.0+
Docker Compose 2.20.0+

# Development Tools
Git 2.30.0+
Python 3.8+
PowerShell 7.0+ (for Windows VMs)
```

## 📦 Installation Scripts Overview

### **Common Scripts (`vagrant/scripts/common/`)**

#### **1. System Update Script**
```bash
# File: update-system.sh
# Purpose: Base system configuration for all Linux VMs
# Dependencies: apt/yum package managers
# Features:
- System package updates
- Essential tool installation
- User account creation
- Directory structure setup
- Logging configuration
- Security hardening
```

#### **2. Docker Installation**
```bash
# File: install-docker.sh
# Purpose: Docker Engine and Compose installation
# Dependencies: curl, apt-transport-https
# Features:
- Docker CE installation
- Docker Compose installation
- User group configuration
- Security settings
- Monitoring scripts
```

#### **3. Windows Defender Disable**
```powershell
# File: disable-windows-defender.ps1
# Purpose: Disable Windows security for malware analysis
# Dependencies: PowerShell 5.0+
# Features:
- Real-time protection disable
- Registry modifications
- Service disabling
- Exclusion creation
- UAC disabling
```

### **Component-Specific Scripts**

#### **Splunk Server (`vagrant/scripts/splunk/`)**

**Installation Script: `install-splunk.sh`**
```bash
# Dependencies:
- Ubuntu 22.04 LTS
- 8GB+ RAM
- Java 11 (auto-installed)
- 100GB+ storage

# Installation Process:
1. Download Splunk Enterprise 9.1.2
2. Create splunk user and directories
3. Configure system limits
4. Generate SSL certificates
5. Configure indexes and inputs
6. Set up systemd service
7. Create monitoring scripts

# Post-Installation:
- Web UI: https://IP:8000
- Management: https://IP:8089
- Forwarder: tcp://IP:9997
- HEC: https://IP:8088
```

**Configuration Files:**
- `/opt/splunk/etc/system/local/server.conf`
- `/opt/splunk/etc/system/local/web.conf`
- `/opt/splunk/etc/system/local/inputs.conf`
- `/opt/splunk/etc/system/local/indexes.conf`

#### **FLARE-VM (`vagrant/scripts/flare-vm/`)**

**Installation Script: `install-flare-vm.ps1`**
```powershell
# Dependencies:
- Windows 10/11 Enterprise
- 8GB+ RAM
- PowerShell 5.0+
- Internet connectivity

# Installation Process:
1. Disable Windows Defender
2. Configure Windows for analysis
3. Install Chocolatey
4. Download FLARE-VM script
5. Run FLARE-VM installation
6. Install Python packages
7. Create analysis environment

# Tools Installed:
- Disassemblers: IDA Free, Ghidra, x64dbg
- Hex Editors: HxD, 010 Editor
- Network: Wireshark, Fiddler
- System: Process Monitor, Autoruns
- Analysis: YARA, Volatility, FLOSS
```

**Analysis Environment:**
- Analysis User: `analyst/analyst`
- Sample Directory: `C:\MalwareAnalysis\Samples`
- Tools Directory: `C:\Tools`
- Scripts Directory: `C:\MalwareAnalysis\Scripts`

#### **Velociraptor (`vagrant/scripts/velociraptor/`)**

**Installation Script: `install-velociraptor.sh`**
```bash
# Dependencies:
- Ubuntu 22.04 LTS
- 4GB+ RAM
- SSL certificates
- Network connectivity

# Installation Process:
1. Download Velociraptor binary
2. Create user and directories
3. Generate SSL certificates
4. Configure server settings
5. Create systemd service
6. Set up artifacts
7. Create admin user

# Endpoints:
- GUI: https://IP:8889
- API: https://IP:8001
- Frontend: https://IP:8000
```

**Artifacts Created:**
- `GOAD.Windows.EventLogs.yaml`
- `GOAD.Windows.ProcessMonitoring.yaml`
- Custom GOAD-Blue artifacts

#### **Security Onion (`vagrant/scripts/security-onion/`)**

**Installation Script: `install-security-onion.sh`**
```bash
# Dependencies:
- Ubuntu 22.04 LTS
- 16GB+ RAM
- Multiple network interfaces
- 200GB+ storage

# Installation Process:
1. Download Security Onion ISO
2. Configure network interfaces
3. Run SO setup wizard
4. Configure Suricata rules
5. Set up Zeek scripts
6. Configure Elasticsearch
7. Import Kibana dashboards

# Services:
- Elasticsearch: :9200
- Kibana: :5601
- Suricata: IDS/IPS
- Zeek: Network analysis
```

#### **Malcolm (`vagrant/scripts/malcolm/`)**

**Installation Script: `install-malcolm.sh`**
```bash
# Dependencies:
- Ubuntu 22.04 LTS
- Docker and Docker Compose
- 12GB+ RAM
- 100GB+ storage

# Installation Process:
1. Clone Malcolm repository
2. Configure environment
3. Generate SSL certificates
4. Configure Docker Compose
5. Start Malcolm services
6. Import dashboards
7. Configure PCAP processing

# Services:
- Web Interface: https://IP
- Kibana: https://IP/kibana
- Upload: https://IP/upload
```

#### **MISP (`vagrant/scripts/misp/`)**

**Installation Script: `install-misp.sh`**
```bash
# Dependencies:
- Ubuntu 22.04 LTS
- PHP 8.1+
- MySQL/MariaDB
- Apache/Nginx
- Redis

# Installation Process:
1. Install system dependencies
2. Configure database
3. Clone MISP repository
4. Install PHP dependencies
5. Configure web server
6. Set up background workers
7. Import feeds and taxonomies

# Features:
- Web Interface: https://IP
- API: https://IP/attributes/restSearch
- Feeds: Auto-sync enabled
- Modules: Enrichment and export
```

#### **Management Server (`vagrant/scripts/management/`)**

**Installation Script: `install-tools.sh`**
```bash
# Dependencies:
- Ubuntu 22.04 LTS
- Internet connectivity
- 4GB+ RAM

# Tools Installed:
- Ansible and collections
- Terraform and Packer
- Docker and Kubernetes tools
- Security testing tools
- Monitoring utilities
- Custom GOAD-Blue CLI

# Management Features:
- Central configuration
- Automated deployment
- Monitoring dashboards
- Backup and restore
- SSH key management
```

## 🔗 Dependency Chain

### **Installation Order:**
1. **Management Server** - Central control
2. **pfSense** - Network infrastructure
3. **Core SIEM** - Splunk/Elasticsearch
4. **Security Monitoring** - Security Onion, Malcolm
5. **Threat Intelligence** - MISP
6. **Endpoint Detection** - Velociraptor
7. **Analysis Environment** - FLARE-VM

### **Network Dependencies:**
```mermaid
graph TB
    subgraph "Management Network (***********/24)"
        MGMT[Management Server<br/>************]
        PFS[pfSense<br/>***********]
    end
    
    subgraph "SIEM Network (192.168.100.0/26)"
        SPL[Splunk<br/>**************]
        VEL[Velociraptor<br/>**************]
        MISP[MISP<br/>192.168.100.40]
        ELK[Elasticsearch<br/>192.168.100.60]
    end
    
    subgraph "Monitor Network (192.168.100.64/26)"
        SO[Security Onion<br/>192.168.100.20]
        MAL[Malcolm<br/>192.168.100.30]
    end
    
    subgraph "Analysis Network (***************/26)"
        FLARE[FLARE-VM<br/>**************]
    end
    
    subgraph "GOAD Network (************/24)"
        GOAD[GOAD Environment<br/>************/24]
    end
    
    MGMT --> PFS
    PFS --> SPL
    PFS --> VEL
    PFS --> MISP
    PFS --> ELK
    PFS --> SO
    PFS --> MAL
    PFS --> FLARE
    PFS --> GOAD
    
    SPL -.->|Logs| SO
    SPL -.->|Logs| MAL
    VEL -.->|Agents| GOAD
    MISP -.->|Intel| SPL
    SO -.->|Alerts| SPL
    MAL -.->|PCAP| SPL
```

## 📋 Validation Scripts

### **System Validation**
```bash
# File: validate-system.sh
# Purpose: Verify system requirements
#!/bin/bash
echo "=== System Validation ==="
echo "CPU Cores: $(nproc)"
echo "Memory: $(free -h | grep Mem | awk '{print $2}')"
echo "Disk Space: $(df -h / | tail -1 | awk '{print $4}')"
echo "Virtualization: $(grep -E '(vmx|svm)' /proc/cpuinfo | wc -l)"
```

### **Network Validation**
```bash
# File: validate-network.sh
# Purpose: Verify network connectivity
#!/bin/bash
echo "=== Network Validation ==="
ping -c 1 ************** && echo "Splunk: OK" || echo "Splunk: FAIL"
ping -c 1 ************** && echo "Velociraptor: OK" || echo "Velociraptor: FAIL"
curl -k https://**************:8000 && echo "Splunk Web: OK" || echo "Splunk Web: FAIL"
```

### **Service Validation**
```bash
# File: validate-services.sh
# Purpose: Verify all services are running
#!/bin/bash
echo "=== Service Validation ==="
systemctl is-active splunk && echo "Splunk: Running" || echo "Splunk: Stopped"
systemctl is-active velociraptor && echo "Velociraptor: Running" || echo "Velociraptor: Stopped"
docker ps | grep malcolm && echo "Malcolm: Running" || echo "Malcolm: Stopped"
```

## 🚀 Quick Deployment

### **Complete Environment:**
```bash
cd goad-blue/vagrant
vagrant up
```

### **Selective Deployment:**
```bash
# Core SIEM only
vagrant up splunk-server velociraptor misp

# Analysis environment
vagrant up flare-vm malcolm

# Monitoring stack
vagrant up security-onion malcolm
```

### **Validation:**
```bash
# Run validation scripts
./scripts/validate-system.sh
./scripts/validate-network.sh
./scripts/validate-services.sh
```

---

!!! tip "Deployment Tips"
    - Deploy VMs in order of dependency
    - Allow 2-4 hours for complete deployment
    - Monitor system resources during deployment
    - Validate each component before proceeding
    - Keep VM snapshots for quick recovery

!!! warning "Resource Requirements"
    - Ensure adequate system resources
    - Monitor disk space during installation
    - Some components require internet connectivity
    - FLARE-VM installation may take several hours
    - Security Onion requires significant resources

!!! info "Troubleshooting"
    - Check logs in `/var/log/goad-blue/`
    - Use validation scripts to identify issues
    - Restart services if needed
    - Consult component-specific documentation
    - Use VM snapshots for rollback if needed
