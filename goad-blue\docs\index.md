# GOAD-Blue Documentation

<div align="center">
  <h1>🛡️ GOAD-<PERSON> 🛡️</h1>
  <p><strong>Full-Spectrum Cybersecurity Training Platform</strong></p>
  <p>Blue Team Enhancement for GOAD (Game of Active Directory)</p>
</div>

---

## Welcome to GOAD-Blue

GOAD-Blue is a comprehensive cybersecurity training and testing platform that extends the original [GOAD (Game of Active Directory)](https://github.com/Orange-Cyberdefense/GOAD) project with defensive capabilities. While GOAD focuses on red team Active Directory attack simulation, GOAD-Blue integrates a complete blue team stack to create a full-spectrum environment for both offensive and defensive security operations.

## 🎯 What is GOAD-Blue?

GOAD-<PERSON> transforms your existing GOAD environment into a professional-grade cybersecurity training platform by adding:

- **🔍 Centralized SIEM** - Splunk Enterprise or Elastic Stack for log analysis and correlation
- **🌐 Network Monitoring** - Security Onion and Malcolm for traffic analysis and threat detection
- **💻 Endpoint Visibility** - Velociraptor and Sysmon for comprehensive endpoint monitoring
- **🧠 Threat Intelligence** - MISP platform for IOC management and threat correlation
- **🔬 Malware Analysis** - FLARE-VM environment for safe malware analysis

## 🏗️ Architecture Overview

```mermaid
graph TB
    subgraph "Red Team Environment (GOAD)"
        DC1[Domain Controller 1<br/>kingslanding.sevenkingdoms.local]
        DC2[Domain Controller 2<br/>winterfell.north.sevenkingdoms.local]
        DC3[Domain Controller 3<br/>meereen.essos.local]
        SRV1[Server 1<br/>castelblack]
        SRV2[Server 2<br/>braavos]
    end
    
    subgraph "Blue Team Stack (GOAD-Blue)"
        SIEM[SIEM Platform<br/>Splunk/Elastic]
        SO[Security Onion<br/>Suricata + Zeek + Wazuh]
        VELO[Velociraptor<br/>Endpoint Visibility]
        MISP[MISP<br/>Threat Intelligence]
        FLARE[FLARE-VM<br/>Malware Analysis]
    end
    
    DC1 --> SIEM
    DC2 --> SIEM
    DC3 --> SIEM
    SRV1 --> SIEM
    SRV2 --> SIEM
    
    SO --> SIEM
    VELO --> SIEM
    MISP <--> SIEM
```

## 🚀 Key Features

### **Modular Architecture**
Choose only the components you need for your specific training or testing requirements.

### **Multi-Platform Support**
Deploy on VMware, VirtualBox, AWS, Azure, or Proxmox with consistent functionality.

### **Infrastructure as Code**
Automated deployment using Packer, Terraform, and Ansible for reproducible environments.

### **Seamless GOAD Integration**
Automatically discovers and integrates with existing GOAD installations without modification.

### **Professional Training**
Real-world scenarios using industry-standard tools and techniques.

## 🎓 Use Cases

### **Red Team vs Blue Team Exercises**
- Red team executes attacks using GOAD
- Blue team detects and responds using GOAD-Blue
- Realistic attack-defense scenarios

### **SOC Analyst Training**
- Hands-on SIEM operation
- Alert triage and investigation
- Incident response workflows

### **Threat Hunting Workshops**
- Advanced detection techniques
- Behavioral analysis
- Custom rule development

### **Security Tool Evaluation**
- Test detection capabilities
- Compare tool effectiveness
- Validate security controls

## 📊 Network Layout

| Subnet | CIDR | Purpose | Components |
|--------|------|---------|------------|
| **Management** | `192.168.100.0/26` | SIEM & Central Services | Splunk/ELK, MISP |
| **Monitoring** | `192.168.100.64/26` | Security Tools | Security Onion, Malcolm, Velociraptor |
| **Red Team** | `192.168.100.128/26` | GOAD Environment | Domain Controllers, Servers |
| **Analysis** | `192.168.100.192/26` | Malware Analysis | FLARE-VM, Isolated Analysis |

## 🚀 Quick Start

!!! success "New to GOAD-Blue?"
    **[Start Here: First Steps Guide →](first-steps.md)**

    Complete beginner-friendly guide that gets you up and running in 5 minutes!

### Three Ways to Get Started

=== "🏠 Home Lab (Easiest)"
    **Perfect for learning and personal development**

    ```bash
    git clone https://github.com/your-org/goad-blue.git
    cd goad-blue
    vagrant up  # Uses VirtualBox
    ```

    - **Requirements**: 32GB RAM, VirtualBox
    - **Time**: ~30 minutes
    - **Cost**: Free

=== "☁️ Cloud (Scalable)"
    **Best for teams and advanced scenarios**

    ```bash
    cd terraform/providers/aws
    terraform apply -var-file="production.tfvars"
    ```

    - **Requirements**: AWS/Azure/GCP account
    - **Time**: ~15 minutes
    - **Cost**: Pay-per-use

=== "🏢 Enterprise (Production)"
    **For corporate training and large deployments**

    ```bash
    cd terraform/providers/vmware
    terraform apply -var-file="enterprise.tfvars"
    ```

    - **Requirements**: VMware vSphere/Proxmox
    - **Time**: ~20 minutes
    - **Cost**: Infrastructure only

## 📚 Documentation Structure

This documentation is organized into several main sections:

- **[Getting Started](getting-started/)** - Installation and initial setup
- **[Configuration](configuration/)** - Detailed configuration options
- **[Components](components/)** - Individual component documentation
- **[GOAD Integration](integration/)** - Integration with existing GOAD
- **[Training & Use Cases](training/)** - Educational scenarios and exercises
- **[Deployment](deployment/)** - Infrastructure as Code details
- **[Operations](operations/)** - Day-to-day management and maintenance
- **[Troubleshooting](troubleshooting/)** - Common issues and solutions

## 🤝 Community

GOAD-Blue is an open-source project that welcomes contributions from the cybersecurity community:

- **GitHub Repository** - Source code, issues, and discussions
- **Documentation** - Community-driven documentation improvements
- **Training Materials** - Shared scenarios and exercises
- **Tool Integrations** - Additional component integrations

## 🔗 Related Projects

- **[GOAD](https://github.com/Orange-Cyberdefense/GOAD)** - The original Game of Active Directory project
- **[Security Onion](https://securityonionsolutions.com/)** - Network security monitoring platform
- **[MISP](https://www.misp-project.org/)** - Threat intelligence sharing platform
- **[Velociraptor](https://www.velocidex.com/)** - Endpoint visibility and DFIR platform

## 📄 License

GOAD-Blue is released under the GPL-3.0 License. See the [License](reference/license.md) page for details.

## 🙏 Acknowledgments

Special thanks to:
- **Orange Cyberdefense** for the original GOAD project
- **Security Onion Solutions** for the Security Onion platform
- **The MISP Project** for the threat intelligence platform
- **Rapid7 Velociraptor** for the endpoint visibility platform
- **FireEye/Mandiant** for the FLARE-VM toolkit

---

!!! tip "Ready to Get Started?"
    **[🚀 Follow the First Steps Guide](first-steps.md)** - Get up and running in 5 minutes!

    Or explore the detailed [Getting Started](getting-started/) section for comprehensive setup instructions.

!!! info "Need Help?"
    Check out our [FAQ](reference/faq.md) or visit the [Troubleshooting](troubleshooting/) section for common issues and solutions.
