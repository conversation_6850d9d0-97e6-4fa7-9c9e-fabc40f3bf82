#!/bin/bash
# Common system update script for GOAD-Blue VMs

set -e

echo "=== GOAD-Blue System Update Started ==="
echo "Timestamp: $(date)"
echo "Hostname: $(hostname)"
echo "OS: $(lsb_release -d | cut -f2)"

# Update package lists
echo "Updating package lists..."
export DEBIAN_FRONTEND=noninteractive
apt-get update -y

# Upgrade system packages
echo "Upgrading system packages..."
apt-get upgrade -y

# Install essential packages
echo "Installing essential packages..."
apt-get install -y \
    curl \
    wget \
    git \
    vim \
    htop \
    net-tools \
    unzip \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    build-essential \
    python3 \
    python3-pip \
    python3-venv \
    jq \
    tree \
    rsync \
    ntp \
    chrony

# Configure timezone
echo "Configuring timezone..."
timedatectl set-timezone UTC

# Configure NTP
echo "Configuring NTP..."
systemctl enable ntp
systemctl start ntp

# Configure SSH
echo "Configuring SSH..."
sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config
systemctl restart ssh

# Create goad-blue user
echo "Creating goad-blue user..."
if ! id "goad-blue" &>/dev/null; then
    useradd -m -s /bin/bash -G sudo goad-blue
    echo "goad-blue:goad-blue" | chpasswd
    echo "goad-blue ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers.d/goad-blue
fi

# Create directories
echo "Creating GOAD-Blue directories..."
mkdir -p /opt/goad-blue/{logs,config,scripts,data}
mkdir -p /var/log/goad-blue
chown -R goad-blue:goad-blue /opt/goad-blue
chown -R goad-blue:goad-blue /var/log/goad-blue

# Configure logging
echo "Configuring logging..."
cat > /etc/rsyslog.d/50-goad-blue.conf << 'EOF'
# GOAD-Blue logging configuration
local0.*    /var/log/goad-blue/goad-blue.log
& stop
EOF

systemctl restart rsyslog

# Install Python packages
echo "Installing Python packages..."
pip3 install --upgrade pip
pip3 install \
    ansible \
    requests \
    pyyaml \
    jinja2 \
    netaddr \
    dnspython

# Configure firewall
echo "Configuring UFW firewall..."
ufw --force enable
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow from ***********/16

# Set up log rotation
echo "Configuring log rotation..."
cat > /etc/logrotate.d/goad-blue << 'EOF'
/var/log/goad-blue/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 goad-blue goad-blue
    postrotate
        systemctl reload rsyslog > /dev/null 2>&1 || true
    endscript
}
EOF

# Clean up
echo "Cleaning up..."
apt-get autoremove -y
apt-get autoclean

# Create system info script
cat > /opt/goad-blue/scripts/system-info.sh << 'EOF'
#!/bin/bash
echo "=== GOAD-Blue System Information ==="
echo "Hostname: $(hostname)"
echo "IP Address: $(hostname -I | awk '{print $1}')"
echo "OS: $(lsb_release -d | cut -f2)"
echo "Kernel: $(uname -r)"
echo "Uptime: $(uptime -p)"
echo "Memory: $(free -h | grep Mem | awk '{print $3 "/" $2}')"
echo "Disk: $(df -h / | tail -1 | awk '{print $3 "/" $2 " (" $5 " used)"}')"
echo "Load: $(uptime | awk -F'load average:' '{print $2}')"
echo "==================================="
EOF

chmod +x /opt/goad-blue/scripts/system-info.sh

# Create GOAD-Blue environment file
cat > /etc/environment << 'EOF'
PATH="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin"
GOAD_BLUE_HOME="/opt/goad-blue"
GOAD_BLUE_LOGS="/var/log/goad-blue"
PYTHONPATH="/opt/goad-blue/scripts:$PYTHONPATH"
EOF

# Set hostname in /etc/hosts
echo "********* $(hostname)" >> /etc/hosts

# Create completion marker
touch /opt/goad-blue/.system-updated

echo "=== GOAD-Blue System Update Completed ==="
echo "Timestamp: $(date)"
/opt/goad-blue/scripts/system-info.sh
