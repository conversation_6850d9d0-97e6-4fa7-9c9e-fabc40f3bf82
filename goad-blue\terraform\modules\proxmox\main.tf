# Proxmox Terraform Module for GOAD-Blue
# This module creates GOAD-Blue infrastructure on Proxmox VE

terraform {
  required_version = ">= 1.0"
  required_providers {
    proxmox = {
      source  = "telmate/proxmox"
      version = "~> 2.9"
    }
  }
}

# Data sources for existing Proxmox resources
data "proxmox_template" "ubuntu_template" {
  count = var.ubuntu_template_name != "" ? 1 : 0
  name  = var.ubuntu_template_name
}

# Splunk Enterprise VM
resource "proxmox_vm_qemu" "splunk_enterprise" {
  count = var.deploy_splunk ? 1 : 0
  
  name        = "${var.name_prefix}-splunk"
  target_node = var.target_node
  vmid        = var.vm_ids.splunk
  
  # VM Configuration
  cores    = var.vm_configs.splunk.cores
  memory   = var.vm_configs.splunk.memory
  sockets  = 1
  cpu      = "host"
  numa     = true
  hotplug  = "network,disk,usb"
  
  # Boot configuration
  boot     = "order=scsi0"
  agent    = 1
  os_type  = "cloud-init"
  
  # Clone from template if specified
  clone = var.ubuntu_template_name != "" ? var.ubuntu_template_name : null
  
  # Cloud-init configuration
  ciuser     = var.vm_user
  cipassword = var.vm_password
  sshkeys    = var.ssh_public_key
  ipconfig0  = "ip=${var.network_config.splunk.ip}/${var.network_config.splunk.cidr},gw=${var.network_config.gateway}"
  nameserver = var.network_config.dns_servers
  
  # Primary disk
  disk {
    slot     = 0
    type     = "scsi"
    storage  = var.storage_config.primary_storage
    size     = var.vm_configs.splunk.disk_size
    format   = var.storage_config.disk_format
    ssd      = var.storage_config.ssd
    discard  = "on"
  }
  
  # Additional data disk for Splunk
  disk {
    slot     = 1
    type     = "scsi"
    storage  = var.storage_config.data_storage
    size     = var.vm_configs.splunk.data_disk_size
    format   = var.storage_config.disk_format
    ssd      = var.storage_config.ssd
    discard  = "on"
  }
  
  # GOAD-Blue network interface
  network {
    model  = "virtio"
    bridge = var.network_config.goad_blue_bridge
    tag    = var.network_config.goad_blue_vlan
  }
  
  # Internet access interface
  network {
    model  = "virtio"
    bridge = var.network_config.internet_bridge
  }
  
  # Lifecycle management
  lifecycle {
    ignore_changes = [
      network,
      disk,
    ]
  }
  
  # Tags for organization
  tags = join(",", [var.common_tags.project, var.common_tags.environment, "splunk"])
}

# Security Onion Manager VM
resource "proxmox_vm_qemu" "security_onion_manager" {
  count = var.deploy_security_onion ? 1 : 0
  
  name        = "${var.name_prefix}-so-manager"
  target_node = var.target_node
  vmid        = var.vm_ids.security_onion_manager
  
  # VM Configuration
  cores    = var.vm_configs.security_onion_manager.cores
  memory   = var.vm_configs.security_onion_manager.memory
  sockets  = 1
  cpu      = "host"
  numa     = true
  hotplug  = "network,disk,usb"
  
  # Boot configuration
  boot     = "order=scsi0"
  agent    = 1
  os_type  = "cloud-init"
  
  # Clone from template if specified
  clone = var.ubuntu_template_name != "" ? var.ubuntu_template_name : null
  
  # Cloud-init configuration
  ciuser     = var.vm_user
  cipassword = var.vm_password
  sshkeys    = var.ssh_public_key
  ipconfig0  = "ip=${var.network_config.security_onion_manager.ip}/${var.network_config.security_onion_manager.cidr},gw=${var.network_config.gateway}"
  ipconfig1  = "ip=${var.network_config.security_onion_manager.monitor_ip}/${var.network_config.goad_cidr}"
  nameserver = var.network_config.dns_servers
  
  # Primary disk
  disk {
    slot     = 0
    type     = "scsi"
    storage  = var.storage_config.primary_storage
    size     = var.vm_configs.security_onion_manager.disk_size
    format   = var.storage_config.disk_format
    ssd      = var.storage_config.ssd
    discard  = "on"
  }
  
  # GOAD-Blue management network
  network {
    model  = "virtio"
    bridge = var.network_config.goad_blue_bridge
    tag    = var.network_config.goad_blue_vlan
  }
  
  # GOAD monitoring network
  network {
    model  = "virtio"
    bridge = var.network_config.goad_bridge
    tag    = var.network_config.goad_vlan
  }
  
  # Internet access interface
  network {
    model  = "virtio"
    bridge = var.network_config.internet_bridge
  }
  
  # Tags for organization
  tags = join(",", [var.common_tags.project, var.common_tags.environment, "security-onion"])
}

# Security Onion Sensor VM
resource "proxmox_vm_qemu" "security_onion_sensor" {
  count = var.deploy_security_onion ? var.security_onion_sensor_count : 0
  
  name        = "${var.name_prefix}-so-sensor-${count.index + 1}"
  target_node = var.target_node
  vmid        = var.vm_ids.security_onion_sensor_base + count.index
  
  # VM Configuration
  cores    = var.vm_configs.security_onion_sensor.cores
  memory   = var.vm_configs.security_onion_sensor.memory
  sockets  = 1
  cpu      = "host"
  numa     = true
  hotplug  = "network,disk,usb"
  
  # Boot configuration
  boot     = "order=scsi0"
  agent    = 1
  os_type  = "cloud-init"
  
  # Clone from template if specified
  clone = var.ubuntu_template_name != "" ? var.ubuntu_template_name : null
  
  # Cloud-init configuration
  ciuser     = var.vm_user
  cipassword = var.vm_password
  sshkeys    = var.ssh_public_key
  ipconfig0  = "ip=${cidrhost(var.network_config.goad_blue_cidr, 71 + count.index)}/${var.network_config.goad_blue_cidr_suffix},gw=${var.network_config.gateway}"
  ipconfig1  = "ip=${cidrhost(var.network_config.goad_cidr, 71 + count.index)}/${var.network_config.goad_cidr_suffix}"
  nameserver = var.network_config.dns_servers
  
  # Primary disk
  disk {
    slot     = 0
    type     = "scsi"
    storage  = var.storage_config.primary_storage
    size     = var.vm_configs.security_onion_sensor.disk_size
    format   = var.storage_config.disk_format
    ssd      = var.storage_config.ssd
    discard  = "on"
  }
  
  # GOAD-Blue management network
  network {
    model  = "virtio"
    bridge = var.network_config.goad_blue_bridge
    tag    = var.network_config.goad_blue_vlan
  }
  
  # GOAD monitoring network (promiscuous mode)
  network {
    model  = "virtio"
    bridge = var.network_config.goad_bridge
    tag    = var.network_config.goad_vlan
  }
  
  # Internet access interface
  network {
    model  = "virtio"
    bridge = var.network_config.internet_bridge
  }
  
  # Tags for organization
  tags = join(",", [var.common_tags.project, var.common_tags.environment, "security-onion-sensor"])
}

# Velociraptor Server VM
resource "proxmox_vm_qemu" "velociraptor_server" {
  count = var.deploy_velociraptor ? 1 : 0
  
  name        = "${var.name_prefix}-velociraptor"
  target_node = var.target_node
  vmid        = var.vm_ids.velociraptor
  
  # VM Configuration
  cores    = var.vm_configs.velociraptor.cores
  memory   = var.vm_configs.velociraptor.memory
  sockets  = 1
  cpu      = "host"
  numa     = true
  
  # Boot configuration
  boot     = "order=scsi0"
  agent    = 1
  os_type  = "cloud-init"
  
  # Clone from template if specified
  clone = var.ubuntu_template_name != "" ? var.ubuntu_template_name : null
  
  # Cloud-init configuration
  ciuser     = var.vm_user
  cipassword = var.vm_password
  sshkeys    = var.ssh_public_key
  ipconfig0  = "ip=${var.network_config.velociraptor.ip}/${var.network_config.velociraptor.cidr},gw=${var.network_config.gateway}"
  nameserver = var.network_config.dns_servers
  
  # Primary disk
  disk {
    slot     = 0
    type     = "scsi"
    storage  = var.storage_config.primary_storage
    size     = var.vm_configs.velociraptor.disk_size
    format   = var.storage_config.disk_format
    ssd      = var.storage_config.ssd
    discard  = "on"
  }
  
  # GOAD-Blue network interface
  network {
    model  = "virtio"
    bridge = var.network_config.goad_blue_bridge
    tag    = var.network_config.goad_blue_vlan
  }
  
  # Internet access interface
  network {
    model  = "virtio"
    bridge = var.network_config.internet_bridge
  }
  
  # Tags for organization
  tags = join(",", [var.common_tags.project, var.common_tags.environment, "velociraptor"])
}

# MISP Server VM
resource "proxmox_vm_qemu" "misp_server" {
  count = var.deploy_misp ? 1 : 0
  
  name        = "${var.name_prefix}-misp"
  target_node = var.target_node
  vmid        = var.vm_ids.misp
  
  # VM Configuration
  cores    = var.vm_configs.misp.cores
  memory   = var.vm_configs.misp.memory
  sockets  = 1
  cpu      = "host"
  numa     = true
  
  # Boot configuration
  boot     = "order=scsi0"
  agent    = 1
  os_type  = "cloud-init"
  
  # Clone from template if specified
  clone = var.ubuntu_template_name != "" ? var.ubuntu_template_name : null
  
  # Cloud-init configuration
  ciuser     = var.vm_user
  cipassword = var.vm_password
  sshkeys    = var.ssh_public_key
  ipconfig0  = "ip=${var.network_config.misp.ip}/${var.network_config.misp.cidr},gw=${var.network_config.gateway}"
  nameserver = var.network_config.dns_servers
  
  # Primary disk
  disk {
    slot     = 0
    type     = "scsi"
    storage  = var.storage_config.primary_storage
    size     = var.vm_configs.misp.disk_size
    format   = var.storage_config.disk_format
    ssd      = var.storage_config.ssd
    discard  = "on"
  }
  
  # GOAD-Blue network interface
  network {
    model  = "virtio"
    bridge = var.network_config.goad_blue_bridge
    tag    = var.network_config.goad_blue_vlan
  }
  
  # Internet access interface
  network {
    model  = "virtio"
    bridge = var.network_config.internet_bridge
  }
  
  # Tags for organization
  tags = join(",", [var.common_tags.project, var.common_tags.environment, "misp"])
}
