# Configuration Guide

This section covers all aspects of configuring GOAD-Blue, from initial setup to advanced customization and optimization.

## 📋 Configuration Overview

GOAD-Blue uses a hierarchical configuration system that allows for flexible deployment scenarios while maintaining consistency and ease of management.

### **Configuration Hierarchy**

```mermaid
graph TB
    subgraph "🔧 Configuration Layers"
        DEFAULTS[📄 Default Configuration<br/>Built-in defaults]
        GLOBAL[🌍 Global Configuration<br/>goad-blue-config.yml]
        COMPONENT[🔧 Component Configuration<br/>Component-specific files]
        RUNTIME[⚡ Runtime Configuration<br/>CLI parameters & environment]
    end
    
    DEFAULTS --> GLOBAL
    GLOBAL --> COMPONENT
    COMPONENT --> RUNTIME
    
    classDef config fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    class DEFAULTS,GLOBAL,COMPONENT,RUNTIME config
```

### **Configuration Files Structure**

```
goad-blue/
├── goad-blue-config.yml          # Main configuration file
├── config/
│   ├── defaults/                 # Default configurations
│   │   ├── siem.yml
│   │   ├── monitoring.yml
│   │   └── endpoint.yml
│   ├── components/               # Component-specific configs
│   │   ├── splunk/
│   │   ├── security-onion/
│   │   └── velociraptor/
│   └── environments/             # Environment-specific configs
│       ├── development.yml
│       ├── testing.yml
│       └── production.yml
```

## ⚙️ Main Configuration File

### **Basic Configuration Template**

```yaml
# goad-blue-config.yml
goad_blue:
  name: "my-goad-blue-lab"
  version: "1.0.0"
  environment: "development"  # development, testing, production
  provider: "vmware"          # vmware, virtualbox, aws, azure, gcp
  
# SIEM Configuration
siem:
  type: "splunk"              # splunk or elastic
  enabled: true
  version: "9.1.2"
  license_type: "enterprise"  # free, enterprise
  
# Component Selection
components:
  security_onion:
    enabled: true
    version: "2.4.60"
    deployment_type: "standalone"  # standalone, distributed
    
  malcolm:
    enabled: false
    version: "6.5.0"
    
  velociraptor:
    enabled: true
    version: "0.7.0"
    
  misp:
    enabled: true
    version: "2.4.170"
    
  flare_vm:
    enabled: false
    version: "latest"

# Network Configuration
network:
  base_cidr: "*************/24"
  subnets:
    management: "*************/26"     # .1-.62
    monitoring: "**************/26"    # .65-.126
    production: "*************28/26"   # .129-.190
    analysis: "*************92/26"     # .193-.254
  
  dns:
    primary: "8.8.8.8"
    secondary: "8.8.4.4"
    
  firewall:
    enabled: true
    default_policy: "deny"

# GOAD Integration
integration:
  goad_enabled: true
  auto_discover: true
  goad_path: "../"
  goad_network: "************/24"
  
# Security Settings
security:
  ssl_enabled: true
  generate_certificates: true
  change_default_passwords: true
  enable_audit_logging: true
  
# Performance Settings
performance:
  log_retention_days: 90
  index_optimization: true
  resource_limits:
    cpu_limit: "80%"
    memory_limit: "80%"
    disk_limit: "85%"

# Backup Configuration
backup:
  enabled: true
  schedule: "daily"
  retention_days: 30
  destination: "/backup/goad-blue"
  
# Monitoring and Alerting
monitoring:
  health_checks: true
  performance_monitoring: true
  alert_email: "<EMAIL>"
  slack_webhook: ""
```

## 🎯 Component Configuration

### **SIEM Configuration**

#### **Splunk Configuration**
```yaml
# config/components/splunk/splunk.yml
splunk:
  installation:
    version: "9.1.2"
    license_file: "/opt/splunk/etc/licenses/enterprise.lic"
    admin_password: "ChangeMePlease123!"
    
  server:
    hostname: "goad-blue-splunk"
    web_port: 8000
    management_port: 8089
    receiving_port: 9997
    
  indexes:
    - name: "goad_blue_windows"
      max_data_size: "500MB"
      max_hot_buckets: 3
      retention_days: 90
      
    - name: "goad_blue_network"
      max_data_size: "1GB"
      max_hot_buckets: 5
      retention_days: 30
      
  apps:
    - "Splunk_Security_Essentials"
    - "Splunk_ML_Toolkit"
    - "GOAD_Blue_Security"
    
  forwarders:
    universal_forwarder:
      version: "9.1.2"
      deployment_server: "goad-blue-splunk:8089"
```

#### **Elastic Stack Configuration**
```yaml
# config/components/elastic/elastic.yml
elasticsearch:
  version: "8.10.0"
  cluster_name: "goad-blue-cluster"
  node_name: "goad-blue-node-1"
  
  network:
    host: "0.0.0.0"
    port: 9200
    
  indices:
    - name: "goad-blue-windows"
      shards: 3
      replicas: 1
      retention: "90d"
      
    - name: "goad-blue-network"
      shards: 5
      replicas: 1
      retention: "30d"

kibana:
  version: "8.10.0"
  server:
    port: 5601
    host: "0.0.0.0"
    
logstash:
  version: "8.10.0"
  pipeline:
    workers: 4
    batch_size: 125
```

### **Network Monitoring Configuration**

#### **Security Onion Configuration**
```yaml
# config/components/security-onion/so.yml
security_onion:
  version: "2.4.60"
  
  manager:
    hostname: "goad-blue-so-manager"
    ip_address: "**************"
    
  sensors:
    - hostname: "goad-blue-so-sensor1"
      ip_address: "**************"
      interface: "eth1"
      
    - hostname: "goad-blue-so-sensor2"
      ip_address: "**************"
      interface: "eth1"
      
  suricata:
    rules:
      - "emerging-threats"
      - "etpro"
      - "custom-goad-blue"
    rule_update_frequency: "daily"
    
  zeek:
    scripts:
      - "base/protocols"
      - "policy/protocols"
      - "custom/goad-blue"
```

### **Endpoint Configuration**

#### **Velociraptor Configuration**
```yaml
# config/components/velociraptor/velo.yml
velociraptor:
  version: "0.7.0"
  
  server:
    hostname: "goad-blue-velociraptor"
    frontend_port: 8000
    gui_port: 8889
    
  client:
    poll_frequency: 60
    max_poll_frequency: 600
    
  artifacts:
    - "Windows.Events.EventLogs"
    - "Windows.System.Pslist"
    - "Windows.Network.Netstat"
    - "Custom.GOAD.ProcessMonitoring"
    
  hunts:
    max_concurrent: 10
    default_timeout: 3600
```

## 🌐 Network Configuration

### **Subnet Planning**

```yaml
# Detailed network configuration
network:
  base_cidr: "*************/24"
  
  subnets:
    management:
      cidr: "*************/26"
      gateway: "*************"
      dhcp_range: "*************0-**************"
      static_assignments:
        splunk: "*************0"
        elastic: "*************1"
        misp: "**************"
        
    monitoring:
      cidr: "**************/26"
      gateway: "**************"
      static_assignments:
        so_manager: "**************"
        so_sensor1: "**************"
        so_sensor2: "**************"
        malcolm: "**************"
        velociraptor: "**************"
        
    production:
      cidr: "*************28/26"
      gateway: "*************29"
      description: "GOAD environment integration"
      
    analysis:
      cidr: "*************92/26"
      gateway: "*************93"
      isolated: true
      static_assignments:
        flare_vm: "***************"
        sandbox: "***************"
```

### **Firewall Rules**

```yaml
# Firewall configuration
firewall:
  enabled: true
  default_policy: "deny"
  
  rules:
    # Management access
    - name: "ssh_access"
      action: "allow"
      protocol: "tcp"
      port: 22
      source: "*************/26"
      
    # SIEM access
    - name: "splunk_web"
      action: "allow"
      protocol: "tcp"
      port: 8000
      source: "*************/24"
      
    # Log forwarding
    - name: "splunk_forwarder"
      action: "allow"
      protocol: "tcp"
      port: 9997
      source: "*************/24"
      destination: "*************0"
      
    # GOAD integration
    - name: "goad_integration"
      action: "allow"
      protocol: "any"
      source: "************/24"
      destination: "*************/24"
```

## 🔒 Security Configuration

### **SSL/TLS Configuration**

```yaml
# SSL certificate configuration
ssl:
  enabled: true
  generate_self_signed: true
  
  certificate_authority:
    country: "US"
    state: "State"
    locality: "City"
    organization: "GOAD-Blue Lab"
    organizational_unit: "Security Team"
    
  certificates:
    - hostname: "goad-blue-splunk"
      san: ["splunk.goad-blue.local", "*************0"]
      
    - hostname: "goad-blue-so-manager"
      san: ["so.goad-blue.local", "**************"]
```

### **Authentication Configuration**

```yaml
# Authentication settings
authentication:
  local_auth: true
  
  ldap:
    enabled: false
    server: "ldap://dc.goad-blue.local"
    base_dn: "dc=goad-blue,dc=local"
    bind_dn: "cn=ldap_user,ou=users,dc=goad-blue,dc=local"
    
  saml:
    enabled: false
    idp_url: "https://idp.example.com/saml"
    
  multi_factor:
    enabled: false
    provider: "totp"  # totp, duo, okta
```

## 📊 Performance Configuration

### **Resource Limits**

```yaml
# Performance and resource configuration
performance:
  global_limits:
    cpu_limit: "80%"
    memory_limit: "80%"
    disk_limit: "85%"
    
  component_limits:
    splunk:
      cpu_cores: 4
      memory_gb: 8
      disk_gb: 100
      
    security_onion:
      cpu_cores: 8
      memory_gb: 16
      disk_gb: 200
      
  optimization:
    log_compression: true
    index_optimization: true
    cache_size: "2GB"
```

### **Data Retention**

```yaml
# Data retention policies
retention:
  default_days: 90
  
  by_index:
    goad_blue_windows: 90
    goad_blue_network: 30
    goad_blue_endpoint: 60
    goad_blue_threat_intel: 365
    
  archival:
    enabled: true
    storage_type: "s3"  # s3, nfs, local
    compression: true
```

## 🔧 Environment-Specific Configuration

### **Development Environment**

```yaml
# config/environments/development.yml
environment: "development"

# Reduced resource requirements
performance:
  component_limits:
    splunk:
      cpu_cores: 2
      memory_gb: 4
      disk_gb: 50

# Shorter retention
retention:
  default_days: 7
  
# Relaxed security
security:
  ssl_enabled: false
  generate_certificates: false
```

### **Production Environment**

```yaml
# config/environments/production.yml
environment: "production"

# High availability
high_availability:
  enabled: true
  
# Enhanced security
security:
  ssl_enabled: true
  multi_factor_auth: true
  audit_logging: true
  
# Extended retention
retention:
  default_days: 365
  
# Performance optimization
performance:
  optimization:
    aggressive_caching: true
    parallel_processing: true
```

---

!!! info "Configuration Sections"
    For detailed configuration of specific areas, see:
    
    - [Network Configuration](network.md)
    - [Security Configuration](security.md)
    - [Performance Tuning](performance.md)
    - [Integration Settings](integration.md)

!!! tip "Configuration Validation"
    Use `python3 goad-blue.py --validate-config` to check your configuration for errors before deployment.

!!! warning "Sensitive Data"
    Never commit passwords or API keys to version control. Use environment variables or secure vaults for sensitive configuration data.
