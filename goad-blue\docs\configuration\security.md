# Security Configuration

This guide covers comprehensive security configuration for GOAD-Blue, including authentication, encryption, access controls, and compliance settings.

## 🔒 Security Architecture Overview

GOAD-Blue implements defense-in-depth security principles with multiple layers of protection.

```mermaid
graph TB
    subgraph "🌐 Perimeter Security"
        FIREWALL[🔥 Firewall<br/>Network Filtering<br/>Intrusion Prevention]
        VPN[🔐 VPN Gateway<br/>Secure Remote Access<br/>Multi-Factor Auth]
        WAF[🛡️ Web Application Firewall<br/>HTTP/HTTPS Protection<br/>Attack Prevention]
    end
    
    subgraph "🔐 Identity & Access"
        AUTH[👤 Authentication<br/>LDAP/SAML/OAuth<br/>Multi-Factor Auth]
        AUTHZ[🔑 Authorization<br/>Role-Based Access<br/>Least Privilege]
        SSO[🎫 Single Sign-On<br/>Centralized Auth<br/>Session Management]
    end
    
    subgraph "🔒 Data Protection"
        ENCRYPT[🔐 Encryption<br/>Data at Rest<br/>Data in Transit]
        PKI[📜 PKI Infrastructure<br/>Certificate Management<br/>Key Rotation]
        SECRETS[🗝️ Secrets Management<br/>Credential Vault<br/>API Keys]
    end
    
    subgraph "📊 Monitoring & Compliance"
        AUDIT[📋 Audit Logging<br/>Activity Tracking<br/>Compliance Reports]
        SIEM_SEC[🔍 Security Monitoring<br/>Threat Detection<br/>Incident Response]
        COMPLIANCE[📜 Compliance<br/>Regulatory Standards<br/>Policy Enforcement]
    end
    
    FIREWALL --> AUTH
    VPN --> AUTH
    WAF --> AUTH
    
    AUTH --> AUTHZ
    AUTHZ --> SSO
    
    SSO --> ENCRYPT
    ENCRYPT --> PKI
    PKI --> SECRETS
    
    SECRETS --> AUDIT
    AUDIT --> SIEM_SEC
    SIEM_SEC --> COMPLIANCE
    
    classDef perimeter fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef identity fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef data fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef monitoring fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class FIREWALL,VPN,WAF perimeter
    class AUTH,AUTHZ,SSO identity
    class ENCRYPT,PKI,SECRETS data
    class AUDIT,SIEM_SEC,COMPLIANCE monitoring
```

## 🔐 Authentication Configuration

### **Multi-Factor Authentication**

```yaml
# Multi-factor authentication configuration
authentication:
  # Primary authentication method
  primary_method: "ldap"  # local, ldap, saml, oauth
  
  # Multi-factor authentication
  mfa:
    enabled: true
    required_for_roles: ["admin", "security_admin"]
    optional_for_roles: ["analyst", "user"]
    
    # MFA methods
    methods:
      totp:
        enabled: true
        issuer: "GOAD-Blue"
        algorithm: "SHA1"
        digits: 6
        period: 30
        
      sms:
        enabled: true
        provider: "twilio"
        api_key: "${SMS_API_KEY}"
        from_number: "+**********"
        
      email:
        enabled: true
        smtp_server: "smtp.company.com"
        from_address: "<EMAIL>"
        
      hardware_token:
        enabled: false
        supported_types: ["yubikey", "rsa_securid"]
        
    # MFA policies
    policies:
      backup_codes: 10
      remember_device_days: 30
      max_failed_attempts: 3
      lockout_duration_minutes: 15
```

### **LDAP/Active Directory Integration**

```yaml
# LDAP authentication configuration
ldap:
  # Server configuration
  servers:
    primary:
      host: "ldap://dc1.company.com"
      port: 389
      use_ssl: false
      use_tls: true
      
    secondary:
      host: "ldap://dc2.company.com"
      port: 389
      use_ssl: false
      use_tls: true
      
  # Bind configuration
  bind:
    dn: "CN=goad-blue-service,OU=Service Accounts,DC=company,DC=com"
    password: "${LDAP_BIND_PASSWORD}"
    
  # Search configuration
  search:
    base_dn: "DC=company,DC=com"
    user_base: "OU=Users,DC=company,DC=com"
    group_base: "OU=Groups,DC=company,DC=com"
    
    # User search filter
    user_filter: "(&(objectClass=user)(sAMAccountName={username})(!(userAccountControl:1.2.840.113556.1.4.803:=2)))"
    
    # Group search filter
    group_filter: "(&(objectClass=group)(member={user_dn}))"
    
  # Attribute mapping
  attributes:
    username: "sAMAccountName"
    email: "mail"
    first_name: "givenName"
    last_name: "sn"
    display_name: "displayName"
    groups: "memberOf"
    
  # Connection settings
  connection:
    timeout: 30
    pool_size: 10
    retry_attempts: 3
    retry_delay: 5
```

### **SAML SSO Configuration**

```yaml
# SAML single sign-on configuration
saml:
  # Service Provider (SP) settings
  sp:
    entity_id: "https://goad-blue.company.com"
    assertion_consumer_service:
      url: "https://goad-blue.company.com/auth/saml/acs"
      binding: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
      
    single_logout_service:
      url: "https://goad-blue.company.com/auth/saml/sls"
      binding: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
      
    # SP certificate and key
    x509cert: "${SAML_SP_CERT}"
    private_key: "${SAML_SP_KEY}"
    
  # Identity Provider (IdP) settings
  idp:
    entity_id: "https://company.okta.com"
    single_sign_on_service:
      url: "https://company.okta.com/app/goadblue/sso/saml"
      binding: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
      
    single_logout_service:
      url: "https://company.okta.com/app/goadblue/slo/saml"
      binding: "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
      
    # IdP certificate
    x509cert: |
      -----BEGIN CERTIFICATE-----
      MIICertificateDataHere...
      -----END CERTIFICATE-----
      
  # Attribute mapping
  attribute_mapping:
    email: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
    first_name: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname"
    last_name: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname"
    groups: "http://schemas.microsoft.com/ws/2008/06/identity/claims/groups"
    
  # Security settings
  security:
    name_id_encrypted: false
    authn_requests_signed: true
    logout_requests_signed: true
    logout_responses_signed: true
    sign_metadata: true
    want_assertions_signed: true
    want_name_id: true
    want_assertions_encrypted: false
    signature_algorithm: "http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"
```

## 🔑 Authorization and Access Control

### **Role-Based Access Control (RBAC)**

```yaml
# Role-based access control configuration
rbac:
  # Role definitions
  roles:
    # System administrator role
    system_admin:
      description: "Full system administration access"
      permissions:
        - "system:*"
        - "users:*"
        - "config:*"
        - "components:*"
        
    # Security administrator role
    security_admin:
      description: "Security-focused administration"
      permissions:
        - "security:*"
        - "audit:read"
        - "components:security:*"
        - "users:security:*"
        
    # SOC analyst role
    soc_analyst:
      description: "Security operations center analyst"
      permissions:
        - "splunk:search"
        - "splunk:dashboards:read"
        - "security_onion:alerts:read"
        - "velociraptor:hunts:read"
        - "misp:events:read"
        
    # Threat hunter role
    threat_hunter:
      description: "Advanced threat hunting capabilities"
      inherits: ["soc_analyst"]
      permissions:
        - "splunk:searches:create"
        - "splunk:dashboards:create"
        - "velociraptor:hunts:create"
        - "velociraptor:artifacts:read"
        
    # Incident responder role
    incident_responder:
      description: "Incident response capabilities"
      inherits: ["threat_hunter"]
      permissions:
        - "velociraptor:collections:create"
        - "flare_vm:access"
        - "misp:events:create"
        - "quarantine:execute"
        
    # Security researcher role
    security_researcher:
      description: "Security research and analysis"
      permissions:
        - "flare_vm:*"
        - "jupyter:*"
        - "misp:*"
        - "sandbox:*"
        
    # Read-only user role
    readonly_user:
      description: "Read-only access to dashboards"
      permissions:
        - "splunk:dashboards:read"
        - "security_onion:dashboards:read"
        - "kibana:dashboards:read"
        
  # Group to role mapping
  group_mapping:
    "CN=GOAD-Blue-Admins,OU=Groups,DC=company,DC=com": ["system_admin"]
    "CN=Security-Admins,OU=Groups,DC=company,DC=com": ["security_admin"]
    "CN=SOC-Analysts,OU=Groups,DC=company,DC=com": ["soc_analyst"]
    "CN=Threat-Hunters,OU=Groups,DC=company,DC=com": ["threat_hunter"]
    "CN=Incident-Responders,OU=Groups,DC=company,DC=com": ["incident_responder"]
    "CN=Security-Researchers,OU=Groups,DC=company,DC=com": ["security_researcher"]
    "CN=Security-Team,OU=Groups,DC=company,DC=com": ["readonly_user"]
```

### **API Access Control**

```yaml
# API access control configuration
api_security:
  # API authentication methods
  authentication:
    methods: ["api_key", "jwt", "oauth2"]
    
    # API key configuration
    api_key:
      header_name: "X-API-Key"
      query_param: "api_key"
      key_length: 32
      expiration_days: 90
      
    # JWT configuration
    jwt:
      algorithm: "RS256"
      expiration_minutes: 60
      refresh_token_expiration_days: 30
      issuer: "goad-blue.company.com"
      
    # OAuth2 configuration
    oauth2:
      authorization_server: "https://oauth.company.com"
      client_id: "${OAUTH2_CLIENT_ID}"
      client_secret: "${OAUTH2_CLIENT_SECRET}"
      scopes: ["goad-blue:read", "goad-blue:write", "goad-blue:admin"]
      
  # Rate limiting
  rate_limiting:
    enabled: true
    default_limit: "100/hour"
    
    # Per-endpoint limits
    endpoint_limits:
      "/api/v1/search": "50/hour"
      "/api/v1/scenarios": "20/hour"
      "/api/v1/admin/*": "10/hour"
      
    # Per-role limits
    role_limits:
      "readonly_user": "50/hour"
      "soc_analyst": "200/hour"
      "threat_hunter": "500/hour"
      "system_admin": "unlimited"
      
  # API permissions
  permissions:
    endpoints:
      "GET /api/v1/status": ["*"]
      "GET /api/v1/components": ["soc_analyst", "threat_hunter", "system_admin"]
      "POST /api/v1/components/*/restart": ["system_admin"]
      "GET /api/v1/scenarios": ["soc_analyst", "threat_hunter"]
      "POST /api/v1/scenarios/*/start": ["threat_hunter", "system_admin"]
```

## 🔒 Encryption and PKI

### **SSL/TLS Configuration**

```yaml
# SSL/TLS configuration
ssl_tls:
  # Global SSL settings
  enabled: true
  min_version: "TLSv1.2"
  max_version: "TLSv1.3"
  
  # Cipher suites (secure configurations)
  cipher_suites:
    - "ECDHE-ECDSA-AES256-GCM-SHA384"
    - "ECDHE-RSA-AES256-GCM-SHA384"
    - "ECDHE-ECDSA-CHACHA20-POLY1305"
    - "ECDHE-RSA-CHACHA20-POLY1305"
    - "ECDHE-ECDSA-AES128-GCM-SHA256"
    - "ECDHE-RSA-AES128-GCM-SHA256"
    
  # Certificate configuration
  certificates:
    # Main certificate for web interfaces
    web_interface:
      cert_file: "/etc/ssl/certs/goad-blue.crt"
      key_file: "/etc/ssl/private/goad-blue.key"
      ca_file: "/etc/ssl/certs/ca.crt"
      
    # Component-specific certificates
    splunk:
      cert_file: "/opt/splunk/etc/auth/server.pem"
      key_file: "/opt/splunk/etc/auth/server.key"
      ca_file: "/opt/splunk/etc/auth/ca.pem"
      
    security_onion:
      cert_file: "/etc/ssl/certs/so.crt"
      key_file: "/etc/ssl/private/so.key"
      
  # HSTS configuration
  hsts:
    enabled: true
    max_age: 31536000  # 1 year
    include_subdomains: true
    preload: true
```

### **Certificate Management**

```yaml
# PKI and certificate management
pki:
  # Certificate Authority configuration
  ca:
    # Root CA
    root_ca:
      common_name: "GOAD-Blue Root CA"
      organization: "GOAD-Blue Lab"
      country: "US"
      validity_days: 3650
      key_size: 4096
      
    # Intermediate CA
    intermediate_ca:
      common_name: "GOAD-Blue Intermediate CA"
      organization: "GOAD-Blue Lab"
      country: "US"
      validity_days: 1825
      key_size: 2048
      
  # Certificate templates
  templates:
    # Web server certificate template
    web_server:
      key_usage: ["digital_signature", "key_encipherment"]
      extended_key_usage: ["server_auth"]
      validity_days: 365
      
    # Client certificate template
    client:
      key_usage: ["digital_signature"]
      extended_key_usage: ["client_auth"]
      validity_days: 365
      
  # Automatic certificate renewal
  auto_renewal:
    enabled: true
    renewal_threshold_days: 30
    notification_email: "<EMAIL>"
    
  # Certificate revocation
  crl:
    enabled: true
    update_interval_hours: 24
    distribution_points:
      - "http://crl.goad-blue.local/root.crl"
      - "http://crl.goad-blue.local/intermediate.crl"
```

### **Data Encryption**

```yaml
# Data encryption configuration
encryption:
  # Encryption at rest
  at_rest:
    # Database encryption
    databases:
      splunk:
        enabled: true
        algorithm: "AES-256-GCM"
        key_rotation_days: 90
        
      elasticsearch:
        enabled: true
        algorithm: "AES-256-GCM"
        
    # File system encryption
    filesystems:
      - mount_point: "/opt/splunk/var"
        algorithm: "AES-256-XTS"
        
      - mount_point: "/nsm"
        algorithm: "AES-256-XTS"
        
  # Encryption in transit
  in_transit:
    # Log forwarding encryption
    log_forwarding:
      splunk_forwarders:
        ssl_enabled: true
        ssl_version: "TLSv1.2"
        cipher_suite: "ECDHE-RSA-AES256-GCM-SHA384"
        
      syslog:
        tls_enabled: true
        ca_cert: "/etc/ssl/certs/ca.crt"
        
    # API communication
    api_communication:
      force_https: true
      certificate_validation: true
      
  # Key management
  key_management:
    # Key derivation
    kdf: "PBKDF2"
    iterations: 100000
    salt_length: 32
    
    # Key storage
    storage: "vault"  # vault, hsm, file
    vault_address: "https://vault.company.com"
    vault_token: "${VAULT_TOKEN}"
```

## 📋 Audit and Compliance

### **Audit Logging Configuration**

```yaml
# Audit logging configuration
audit:
  # Global audit settings
  enabled: true
  log_level: "info"
  retention_days: 2555  # 7 years for compliance
  
  # Audit events
  events:
    # Authentication events
    authentication:
      - "login_success"
      - "login_failure"
      - "logout"
      - "password_change"
      - "mfa_challenge"
      - "mfa_success"
      - "mfa_failure"
      
    # Authorization events
    authorization:
      - "permission_granted"
      - "permission_denied"
      - "role_assignment"
      - "role_removal"
      
    # System events
    system:
      - "configuration_change"
      - "component_start"
      - "component_stop"
      - "backup_created"
      - "backup_restored"
      
    # Data events
    data:
      - "search_executed"
      - "data_export"
      - "report_generated"
      - "alert_triggered"
      
  # Audit log destinations
  destinations:
    # Local file logging
    file:
      enabled: true
      path: "/var/log/goad-blue/audit.log"
      format: "json"
      rotation: "daily"
      
    # Syslog forwarding
    syslog:
      enabled: true
      server: "siem.company.com"
      port: 514
      protocol: "tcp"
      facility: "local0"
      
    # SIEM integration
    splunk:
      enabled: true
      index: "goad_blue_audit"
      sourcetype: "goad_blue:audit"
      
  # Audit log protection
  protection:
    # Log signing
    signing:
      enabled: true
      algorithm: "RSA-SHA256"
      key_file: "/etc/goad-blue/audit-signing.key"
      
    # Log encryption
    encryption:
      enabled: true
      algorithm: "AES-256-GCM"
      
    # Tamper detection
    tamper_detection:
      enabled: true
      hash_algorithm: "SHA-256"
      verification_interval_hours: 1
```

### **Compliance Configuration**

```yaml
# Compliance configuration
compliance:
  # Regulatory frameworks
  frameworks:
    # SOX compliance
    sox:
      enabled: true
      requirements:
        - "access_controls"
        - "audit_logging"
        - "data_retention"
        - "change_management"
        
    # PCI DSS compliance
    pci_dss:
      enabled: false
      requirements:
        - "network_segmentation"
        - "encryption"
        - "access_controls"
        - "monitoring"
        
    # NIST Cybersecurity Framework
    nist_csf:
      enabled: true
      functions:
        - "identify"
        - "protect"
        - "detect"
        - "respond"
        - "recover"
        
  # Data retention policies
  data_retention:
    # Log retention
    logs:
      security_logs: 2555    # 7 years
      audit_logs: 2555       # 7 years
      system_logs: 365       # 1 year
      application_logs: 90   # 3 months
      
    # Backup retention
    backups:
      daily: 30              # 30 days
      weekly: 52             # 52 weeks
      monthly: 84            # 7 years
      
  # Privacy controls
  privacy:
    # Data classification
    classification:
      enabled: true
      levels: ["public", "internal", "confidential", "restricted"]
      
    # Data masking
    masking:
      enabled: true
      fields: ["ssn", "credit_card", "email"]
      
    # Right to be forgotten
    data_deletion:
      enabled: true
      retention_override: false
```

## 🔧 Security Hardening

### **System Hardening**

```bash
# Security hardening script
#!/bin/bash

# Disable unnecessary services
systemctl disable telnet
systemctl disable ftp
systemctl disable rsh
systemctl disable rlogin

# Configure SSH hardening
cat > /etc/ssh/sshd_config.d/99-goad-blue-hardening.conf << 'EOF'
# SSH Hardening Configuration
Protocol 2
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
PermitEmptyPasswords no
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2
AllowUsers goad-blue-admin
DenyUsers root
EOF

# Configure firewall
ufw --force enable
ufw default deny incoming
ufw default allow outgoing

# Allow SSH from management network only
ufw allow from ***********/24 to any port 22

# Allow web interfaces from management network
ufw allow from ***********/24 to any port 443
ufw allow from ***********/24 to any port 8000

# Configure fail2ban
cat > /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log

[goad-blue-web]
enabled = true
port = 443,8000
filter = goad-blue-web
logpath = /var/log/goad-blue/access.log
EOF

# Set file permissions
chmod 600 /etc/goad-blue/config.yml
chmod 600 /etc/ssl/private/*
chown root:root /etc/goad-blue/config.yml

# Configure log rotation
cat > /etc/logrotate.d/goad-blue << 'EOF'
/var/log/goad-blue/*.log {
    daily
    rotate 365
    compress
    delaycompress
    missingok
    notifempty
    create 640 goad-blue goad-blue
    postrotate
        systemctl reload goad-blue
    endscript
}
EOF
```

---

!!! tip "Security Best Practices"
    - Change all default passwords immediately
    - Enable MFA for all administrative accounts
    - Regularly rotate certificates and API keys
    - Monitor audit logs for suspicious activities
    - Keep all components updated with security patches

!!! warning "Production Security"
    This configuration is for training environments. Production deployments require additional security measures and should be reviewed by security professionals.

!!! info "Compliance Requirements"
    Ensure your security configuration meets your organization's compliance requirements. Consult with legal and compliance teams for specific regulatory needs.
