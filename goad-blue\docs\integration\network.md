# Network Integration

This guide covers the network configuration required to integrate GOAD-Blue with existing GOAD environments across different virtualization platforms.

## 🌐 Network Architecture

```mermaid
graph TB
    subgraph "🖥️ Host System"
        HOST[💻 Host Machine<br/>Physical/Virtual Host]
    end
    
    subgraph "🎮 GOAD Network (Existing)"
        GOAD_NET[🌐 GOAD Network<br/>************/24]
        DC1[🏰 Kingslanding<br/>*************]
        DC2[❄️ Winterfell<br/>************1]
        DC3[🐉 Meereen<br/>************2]
        SRV1[⚔️ Castelblack<br/>*************]
        WS1[🌹 Tyrell<br/>*************]
    end
    
    subgraph "🛡️ GOAD-Blue Network"
        GB_NET[🌐 GOAD-Blue Network<br/>*************/24]
        SPLUNK[📊 Splunk<br/>*************0]
        SO_MGR[🧅 SO Manager<br/>**************]
        SO_SENSOR[📡 SO Sensor<br/>**************]
        VELO[🦖 Velociraptor<br/>**************]
        MISP[🧠 MISP<br/>**************]
    end
    
    subgraph "🔬 Analysis Network (Isolated)"
        ANALYSIS_NET[🌐 Analysis Network<br/>*************/24]
        FLARE[🔥 FLARE-VM<br/>*************0]
        SANDBOX[📦 Sandbox<br/>**************]
    end
    
    subgraph "🔗 Network Bridges/Switches"
        BRIDGE1[🌉 Management Bridge<br/>Host Network Access]
        BRIDGE2[🌉 GOAD Bridge<br/>vmnet8/vmbr1]
        BRIDGE3[🌉 GOAD-Blue Bridge<br/>vmnet2/vmbr2]
        BRIDGE4[🌉 Analysis Bridge<br/>vmnet3/vmbr3]
    end
    
    %% Host connections
    HOST --> BRIDGE1
    HOST --> BRIDGE2
    HOST --> BRIDGE3
    HOST --> BRIDGE4
    
    %% Network connections
    BRIDGE2 --> GOAD_NET
    BRIDGE3 --> GB_NET
    BRIDGE4 --> ANALYSIS_NET
    
    %% VM connections
    GOAD_NET --> DC1
    GOAD_NET --> DC2
    GOAD_NET --> DC3
    GOAD_NET --> SRV1
    GOAD_NET --> WS1
    
    GB_NET --> SPLUNK
    GB_NET --> SO_MGR
    GB_NET --> SO_SENSOR
    GB_NET --> VELO
    GB_NET --> MISP
    
    ANALYSIS_NET --> FLARE
    ANALYSIS_NET --> SANDBOX
    
    %% Cross-network communication
    GOAD_NET -.->|Monitoring Traffic| GB_NET
    GB_NET -.->|Agent Communication| GOAD_NET
    
    classDef host fill:#f9f9f9,stroke:#333,stroke-width:2px
    classDef goad fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef goadblue fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef analysis fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef bridge fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class HOST host
    class GOAD_NET,DC1,DC2,DC3,SRV1,WS1 goad
    class GB_NET,SPLUNK,SO_MGR,SO_SENSOR,VELO,MISP goadblue
    class ANALYSIS_NET,FLARE,SANDBOX analysis
    class BRIDGE1,BRIDGE2,BRIDGE3,BRIDGE4 bridge
```

## 🔧 Platform-Specific Configuration

### **VMware Workstation/Player**

#### **Virtual Network Configuration**

```bash
# Edit VMware virtual networks
sudo vmware-netcfg

# Or manually edit vmnetdhcp.conf and vmnetnat.conf
sudo nano /etc/vmware/vmnetdhcp.conf
sudo nano /etc/vmware/vmnetnat.conf
```

**Network Assignments:**
```bash
# vmnet0: Bridged (Host network access)
# vmnet1: Host-only (***********/24) - Management
# vmnet8: NAT (************/24) - GOAD existing network
# vmnet2: Host-only (*************/24) - GOAD-Blue network
# vmnet3: Host-only (*************/24) - Analysis network (isolated)
```

**VMware Network Configuration Files:**

```ini
# /etc/vmware/networking
VERSION=1,0
answer VNET_1_DHCP yes
answer VNET_1_DHCP_CFG_HASH 12345678901234567890123456789012345678901234567890123456789012345678
answer VNET_1_HOSTONLY_NETMASK *************
answer VNET_1_HOSTONLY_SUBNET ***********
answer VNET_1_VIRTUAL_ADAPTER yes

answer VNET_2_DHCP yes
answer VNET_2_DHCP_CFG_HASH 12345678901234567890123456789012345678901234567890123456789012345678
answer VNET_2_HOSTONLY_NETMASK *************
answer VNET_2_HOSTONLY_SUBNET *************
answer VNET_2_VIRTUAL_ADAPTER yes

answer VNET_3_DHCP no
answer VNET_3_HOSTONLY_NETMASK *************
answer VNET_3_HOSTONLY_SUBNET *************
answer VNET_3_VIRTUAL_ADAPTER no

answer VNET_8_DHCP yes
answer VNET_8_DHCP_CFG_HASH 12345678901234567890123456789012345678901234567890123456789012345678
answer VNET_8_HOSTONLY_NETMASK *************
answer VNET_8_HOSTONLY_SUBNET ************
answer VNET_8_NAT yes
answer VNET_8_VIRTUAL_ADAPTER yes
```

#### **Routing Configuration**

```bash
# Enable IP forwarding on host
sudo sysctl net.ipv4.ip_forward=1
echo 'net.ipv4.ip_forward=1' | sudo tee -a /etc/sysctl.conf

# Add routes between GOAD and GOAD-Blue networks
sudo ip route add ************/24 via ************* dev vmnet2
sudo ip route add *************/24 via ************ dev vmnet8

# Make routes persistent (Ubuntu/Debian)
echo "************/24 via ************* dev vmnet2" | sudo tee -a /etc/network/interfaces
echo "*************/24 via ************ dev vmnet8" | sudo tee -a /etc/network/interfaces

# For CentOS/RHEL, add to /etc/sysconfig/network-scripts/route-vmnet2
echo "************/24 via *************" | sudo tee /etc/sysconfig/network-scripts/route-vmnet2
echo "*************/24 via ************" | sudo tee /etc/sysconfig/network-scripts/route-vmnet8
```

### **VMware vSphere/ESXi**

#### **Distributed Switch Configuration**

```powershell
# PowerCLI script for vSphere network configuration
Connect-VIServer -Server vcenter.local -User <EMAIL>

# Create distributed switch
$vds = New-VDSwitch -Name "GOAD-Blue-DSwitch" -Location (Get-Datacenter)

# Add hosts to distributed switch
$vmhosts = Get-VMHost
Add-VDSwitchVMHost -VDSwitch $vds -VMHost $vmhosts

# Create port groups
New-VDPortgroup -VDSwitch $vds -Name "GOAD-Network" -VlanId 56
New-VDPortgroup -VDSwitch $vds -Name "GOAD-Blue-Network" -VlanId 100
New-VDPortgroup -VDSwitch $vds -Name "Analysis-Network" -VlanId 200
New-VDPortgroup -VDSwitch $vds -Name "Monitoring-SPAN" -VlanId 999

# Configure SPAN port for Security Onion
$spanSpec = New-Object VMware.Vim.VMwareDVSPortSetting
$spanSpec.UplinkTeamingPolicy = New-Object VMware.Vim.VmwareUplinkPortTeamingPolicy
$spanSpec.UplinkTeamingPolicy.Policy = "loadbalance_srcid"

# Enable promiscuous mode for monitoring
$securityPolicy = New-Object VMware.Vim.DVSSecurityPolicy
$securityPolicy.AllowPromiscuous = $true
$securityPolicy.ForgedTransmits = $true
$securityPolicy.MacChanges = $true

$monitoringPG = Get-VDPortgroup -Name "Monitoring-SPAN"
$monitoringPG | Set-VDPortgroup -Policy $securityPolicy
```

#### **NSX-T Integration (Advanced)**

```bash
# NSX-T configuration for micro-segmentation
# Create segments for GOAD-Blue networks

# GOAD segment
nsxt segment create \
  --display-name "GOAD-Segment" \
  --transport-zone-path "/infra/sites/default/enforcement-points/default/transport-zones/overlay-tz" \
  --connectivity-path "/infra/tier-1s/tier1-gw" \
  --subnets "************/24"

# GOAD-Blue segment
nsxt segment create \
  --display-name "GOAD-Blue-Segment" \
  --transport-zone-path "/infra/sites/default/enforcement-points/default/transport-zones/overlay-tz" \
  --connectivity-path "/infra/tier-1s/tier1-gw" \
  --subnets "*************/24"

# Analysis segment (isolated)
nsxt segment create \
  --display-name "Analysis-Segment" \
  --transport-zone-path "/infra/sites/default/enforcement-points/default/transport-zones/overlay-tz" \
  --subnets "*************/24"

# Create firewall rules
nsxt security-policy create \
  --display-name "GOAD-Blue-Policy" \
  --category Application \
  --scope "/infra/domains/default/groups/GOAD-Blue-VMs"
```

### **Proxmox VE**

#### **Bridge Configuration**

```bash
# Edit network configuration
sudo nano /etc/network/interfaces

# Network configuration
auto lo
iface lo inet loopback

# Management interface
iface eno1 inet manual

# Management bridge
auto vmbr0
iface vmbr0 inet static
    address ***********00/24
    gateway ***********
    bridge-ports eno1
    bridge-stp off
    bridge-fd 0

# GOAD network bridge
auto vmbr1
iface vmbr1 inet static
    address ************/24
    bridge-ports none
    bridge-stp off
    bridge-fd 0
    post-up echo 1 > /proc/sys/net/ipv4/ip_forward
    post-up iptables -t nat -A POSTROUTING -s '************/24' -o vmbr0 -j MASQUERADE
    post-down iptables -t nat -D POSTROUTING -s '************/24' -o vmbr0 -j MASQUERADE

# GOAD-Blue network bridge
auto vmbr2
iface vmbr2 inet static
    address *************/24
    bridge-ports none
    bridge-stp off
    bridge-fd 0
    post-up echo 1 > /proc/sys/net/ipv4/ip_forward
    post-up iptables -t nat -A POSTROUTING -s '*************/24' -o vmbr0 -j MASQUERADE
    post-up iptables -A FORWARD -s ************/24 -d *************/24 -j ACCEPT
    post-up iptables -A FORWARD -s *************/24 -d ************/24 -j ACCEPT
    post-down iptables -t nat -D POSTROUTING -s '*************/24' -o vmbr0 -j MASQUERADE
    post-down iptables -D FORWARD -s ************/24 -d *************/24 -j ACCEPT
    post-down iptables -D FORWARD -s *************/24 -d ************/24 -j ACCEPT

# Analysis network bridge (isolated)
auto vmbr3
iface vmbr3 inet static
    address *************/24
    bridge-ports none
    bridge-stp off
    bridge-fd 0
    # No internet access or routing for analysis network

# Apply configuration
sudo systemctl restart networking
```

#### **OVS Bridge Configuration (Alternative)**

```bash
# Install Open vSwitch
sudo apt install openvswitch-switch

# Create OVS bridges
sudo ovs-vsctl add-br ovsbr1
sudo ovs-vsctl add-br ovsbr2
sudo ovs-vsctl add-br ovsbr3

# Configure IP addresses
sudo ip addr add ************/24 dev ovsbr1
sudo ip addr add *************/24 dev ovsbr2
sudo ip addr add *************/24 dev ovsbr3

# Bring interfaces up
sudo ip link set ovsbr1 up
sudo ip link set ovsbr2 up
sudo ip link set ovsbr3 up

# Configure VLANs for traffic isolation
sudo ovs-vsctl set port ovsbr1 tag=56
sudo ovs-vsctl set port ovsbr2 tag=100
sudo ovs-vsctl set port ovsbr3 tag=200

# Enable SPAN port for monitoring
sudo ovs-vsctl -- set Bridge ovsbr1 mirrors=@m \
  -- --id=@ovsbr1 get Port ovsbr1 \
  -- --id=@ovsbr2 get Port ovsbr2 \
  -- --id=@span get Port span-port \
  -- --id=@m create Mirror name=span select-dst-port=@ovsbr1,@ovsbr2 output-port=@span
```

### **VirtualBox**

#### **Host-Only Network Configuration**

```bash
# Create host-only networks
VBoxManage hostonlyif create  # vboxnet0 for management
VBoxManage hostonlyif create  # vboxnet1 for GOAD
VBoxManage hostonlyif create  # vboxnet2 for GOAD-Blue
VBoxManage hostonlyif create  # vboxnet3 for analysis

# Configure IP addresses
VBoxManage hostonlyif ipconfig vboxnet0 --ip *********** --netmask *************
VBoxManage hostonlyif ipconfig vboxnet1 --ip ************ --netmask *************
VBoxManage hostonlyif ipconfig vboxnet2 --ip ************* --netmask *************
VBoxManage hostonlyif ipconfig vboxnet3 --ip ************* --netmask *************

# Enable DHCP for networks
VBoxManage dhcpserver add --netname HostInterfaceNetworking-vboxnet1 \
  --ip ************ --netmask ************* \
  --lowerip *************0 --upperip ************** --enable

VBoxManage dhcpserver add --netname HostInterfaceNetworking-vboxnet2 \
  --ip ************* --netmask ************* \
  --lowerip *************00 --upperip *************** --enable

# Configure routing on host
sudo ip route add ************/24 via ************* dev vboxnet2
sudo ip route add *************/24 via ************ dev vboxnet1
```

## 🔥 Firewall Configuration

### **Host Firewall Rules**

#### **UFW (Ubuntu/Debian)**

```bash
# Enable UFW
sudo ufw enable

# Allow SSH
sudo ufw allow ssh

# Allow communication between GOAD networks
sudo ufw allow from ************/24 to *************/24
sudo ufw allow from *************/24 to ************/24

# Allow GOAD-Blue services
sudo ufw allow 8000/tcp   # Splunk Web
sudo ufw allow 8089/tcp   # Splunk Management
sudo ufw allow 9997/tcp   # Splunk Forwarder
sudo ufw allow 443/tcp    # HTTPS services
sudo ufw allow 8889/tcp   # Velociraptor GUI
sudo ufw allow 5985/tcp   # WinRM HTTP
sudo ufw allow 5986/tcp   # WinRM HTTPS

# Block analysis network from internet
sudo ufw deny from *************/24 to any port 80
sudo ufw deny from *************/24 to any port 443
sudo ufw deny from *************/24 to any port 53

# Allow analysis network internal communication
sudo ufw allow from *************/24 to *************/24
```

#### **iptables (Advanced)**

```bash
#!/bin/bash
# goad-blue-firewall.sh

# Flush existing rules
iptables -F
iptables -t nat -F
iptables -t mangle -F

# Set default policies
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# Allow loopback
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT

# Allow established connections
iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT
iptables -A FORWARD -m state --state ESTABLISHED,RELATED -j ACCEPT

# Allow SSH
iptables -A INPUT -p tcp --dport 22 -j ACCEPT

# Allow GOAD <-> GOAD-Blue communication
iptables -A FORWARD -s ************/24 -d *************/24 -j ACCEPT
iptables -A FORWARD -s *************/24 -d ************/24 -j ACCEPT

# Allow GOAD-Blue services
iptables -A INPUT -p tcp --dport 8000 -s *************/24 -j ACCEPT  # Splunk
iptables -A INPUT -p tcp --dport 8089 -s *************/24 -j ACCEPT  # Splunk Mgmt
iptables -A INPUT -p tcp --dport 9997 -s ************/24 -j ACCEPT   # Forwarders
iptables -A INPUT -p tcp --dport 443 -j ACCEPT                        # HTTPS
iptables -A INPUT -p tcp --dport 8889 -s *************/24 -j ACCEPT  # Velociraptor

# NAT for internet access
iptables -t nat -A POSTROUTING -s ************/24 -o eth0 -j MASQUERADE
iptables -t nat -A POSTROUTING -s *************/24 -o eth0 -j MASQUERADE

# Block analysis network internet access
iptables -A FORWARD -s *************/24 -d 0.0.0.0/0 -j DROP
iptables -A FORWARD -d *************/24 -s 0.0.0.0/0 -j DROP

# Allow analysis network internal communication
iptables -A FORWARD -s *************/24 -d *************/24 -j ACCEPT

# Save rules
iptables-save > /etc/iptables/rules.v4
```

## 📊 Traffic Monitoring Setup

### **Port Mirroring Configuration**

#### **VMware SPAN Port**

```bash
# Configure port mirroring for Security Onion
# Add monitoring interface to Security Onion VM

# PowerCLI script
$vm = Get-VM "goad-blue-security-onion"
$spec = New-Object VMware.Vim.VirtualMachineConfigSpec
$spec.deviceChange = New-Object VMware.Vim.VirtualDeviceConfigSpec[] (1)
$spec.deviceChange[0] = New-Object VMware.Vim.VirtualDeviceConfigSpec
$spec.deviceChange[0].operation = "add"
$spec.deviceChange[0].device = New-Object VMware.Vim.VirtualVmxnet3
$spec.deviceChange[0].device.key = -1
$spec.deviceChange[0].device.backing = New-Object VMware.Vim.VirtualEthernetCardDistributedVirtualPortBackingInfo
$spec.deviceChange[0].device.backing.port = New-Object VMware.Vim.DistributedVirtualSwitchPortConnection
$spec.deviceChange[0].device.backing.port.portgroupKey = (Get-VDPortgroup "Monitoring-SPAN").Key
$spec.deviceChange[0].device.backing.port.switchUuid = (Get-VDSwitch "GOAD-Blue-DSwitch").Uuid

$vm.ExtensionData.ReconfigVM($spec)
```

#### **Linux Bridge Monitoring**

```bash
# Configure bridge monitoring on Linux host
# Create monitoring interface
sudo ip link add name monitor0 type dummy
sudo ip link set monitor0 up

# Mirror traffic to monitoring interface
sudo tc qdisc add dev vmnet8 ingress
sudo tc filter add dev vmnet8 parent ffff: protocol all u32 match u32 0 0 action mirred egress mirror dev monitor0

sudo tc qdisc add dev vmnet2 ingress
sudo tc filter add dev vmnet2 parent ffff: protocol all u32 match u32 0 0 action mirred egress mirror dev monitor0

# Attach monitoring interface to Security Onion VM
# This requires VM configuration to use the monitor0 interface
```

## 🧪 Network Testing and Validation

### **Connectivity Testing Script**

```bash
#!/bin/bash
# test-goad-blue-network.sh

echo "=== GOAD-Blue Network Connectivity Test ==="

# Test basic connectivity
echo "Testing basic connectivity..."
ping -c 2 ************* && echo "✓ GOAD DC reachable" || echo "✗ GOAD DC unreachable"
ping -c 2 *************0 && echo "✓ Splunk reachable" || echo "✗ Splunk unreachable"
ping -c 2 ************** && echo "✓ Security Onion reachable" || echo "✗ Security Onion unreachable"

# Test service ports
echo "Testing service ports..."
nc -zv *************0 8000 && echo "✓ Splunk Web accessible" || echo "✗ Splunk Web inaccessible"
nc -zv *************0 9997 && echo "✓ Splunk Forwarder port open" || echo "✗ Splunk Forwarder port closed"
nc -zv ************** 8889 && echo "✓ Velociraptor GUI accessible" || echo "✗ Velociraptor GUI inaccessible"

# Test cross-network routing
echo "Testing cross-network routing..."
traceroute ************* | grep -q "*************" && echo "✓ GOAD routing via GOAD-Blue" || echo "✗ GOAD routing issue"

# Test DNS resolution
echo "Testing DNS resolution..."
nslookup sevenkingdoms.local ************* && echo "✓ GOAD DNS working" || echo "✗ GOAD DNS issue"

echo "Network connectivity test completed."
```

### **Performance Monitoring**

```bash
# Monitor network performance
iftop -i vmnet8  # Monitor GOAD network traffic
iftop -i vmnet2  # Monitor GOAD-Blue network traffic

# Check bandwidth utilization
vnstat -i vmnet8 -h  # Hourly stats for GOAD network
vnstat -i vmnet2 -h  # Hourly stats for GOAD-Blue network

# Monitor connection counts
ss -tuln | grep -E "(8000|9997|8889|443)"  # Check listening services
netstat -an | grep ESTABLISHED | wc -l    # Count active connections
```

---

!!! success "Network Integration Complete"
    Once network integration is configured and tested, GOAD and GOAD-Blue environments will have seamless connectivity for comprehensive monitoring and blue team operations.

!!! tip "Performance Optimization"
    Monitor network performance during intensive exercises. Consider adjusting MTU sizes and enabling jumbo frames for high-throughput scenarios.

!!! warning "Security Considerations"
    - Ensure proper network segmentation between production and lab networks
    - Implement monitoring for unusual cross-network traffic
    - Regularly audit firewall rules and access controls
    - Keep analysis networks properly isolated from production systems
