# Frequently Asked Questions

## General Questions

### What is GO<PERSON>-<PERSON>?

GOAD-<PERSON> is a comprehensive blue team enhancement for the GOAD (Game of Active Directory) project. It adds defensive capabilities including SIEM, network monitoring, endpoint visibility, threat intelligence, and malware analysis to create a full-spectrum cybersecurity training platform.

### How does <PERSON><PERSON>-<PERSON> relate to the original GOAD project?

GOAD-Blue is designed as a complementary extension that integrates seamlessly with existing GOAD installations. It doesn't modify the original GOAD files but adds blue team capabilities that monitor and analyze the red team activities in GOAD.

### Do I need an existing GOAD installation to use GOAD-Blue?

While GOAD-Blue is designed to integrate with GOAD, it can also function as a standalone blue team training platform. However, the full value is realized when used with GOAD for red vs blue team exercises.

### What's the difference between GOAD and GOAD-Blue?

- **GOAD**: Focuses on red team activities - provides vulnerable Active Directory environments for practicing attack techniques
- **GOAD-Blue**: Focuses on blue team activities - provides defensive tools and monitoring capabilities to detect and respond to attacks

## Installation and Setup

### What are the minimum system requirements?

**Minimum Requirements:**
- 32GB RAM
- 500GB storage
- 8 CPU cores
- Virtualization support

**Recommended Requirements:**
- 64GB RAM
- 1TB SSD storage
- 16 CPU cores
- Dedicated network interface

### Which virtualization platforms are supported?

GOAD-<PERSON> supports:
- VMware Workstation Pro/ESXi
- VirtualBox
- AWS EC2
- Microsoft Azure
- Google Cloud Platform
- Proxmox VE

### How long does installation take?

Installation time varies by platform and components:
- **Quick Start**: 30 minutes (minimal components)
- **Full Installation**: 2-3 hours (all components)
- **Cloud Deployment**: 1-2 hours (depending on region)

### Can I install only specific components?

Yes! GOAD-Blue has a modular architecture. You can select which components to install:
- SIEM (Splunk or Elastic)
- Security Onion
- Malcolm
- Velociraptor
- MISP
- FLARE-VM

## Configuration and Usage

### How do I change default passwords?

Default passwords should be changed immediately after installation:

1. **Splunk**: Login to web interface → Settings → Access controls → Users
2. **Security Onion**: Use `so-user` command on the manager
3. **Velociraptor**: Access admin interface → Server Artifacts → Change password
4. **MISP**: Login to web interface → Administration → Users

### Can I use my own SSL certificates?

Yes, you can replace the default self-signed certificates:

1. Place your certificates in the appropriate directories
2. Update configuration files to reference new certificates
3. Restart services to apply changes

Refer to the [Security Configuration](../configuration/security.md) guide for detailed instructions.

### How do I add custom detection rules?

**For Splunk:**
```spl
# Create saved search in Splunk
index=goad_blue_windows EventCode=4625
| stats count by Account_Name
| where count > 5
```

**For Suricata:**
```
# Add to custom.rules
alert tcp any any -> any any (msg:"Custom Rule"; content:"pattern"; sid:1000001;)
```

**For Velociraptor:**
Create custom artifacts using VQL (Velociraptor Query Language).

### How do I backup my GOAD-Blue environment?

1. **VM Snapshots**: Take snapshots of all VMs
2. **Configuration Backup**: Save configuration files
3. **Data Backup**: Export SIEM data and threat intelligence
4. **Documentation**: Document customizations and changes

## Troubleshooting

### Installation fails with dependency errors

**Common Solutions:**
```bash
# Update package managers
sudo apt update && sudo apt upgrade  # Ubuntu/Debian
sudo yum update                      # CentOS/RHEL

# Install missing dependencies
pip3 install -r requirements.txt

# Check Python version
python3 --version  # Should be 3.8+
```

### No data appears in SIEM

**Troubleshooting Steps:**
1. Check agent status: `python3 goad-blue.py -t status`
2. Verify network connectivity between GOAD and SIEM
3. Check firewall rules on SIEM server
4. Review agent logs for errors
5. Restart log forwarding services

### GOAD integration not working

**Common Issues:**
- GOAD not running or accessible
- Network connectivity problems
- Incorrect GOAD path in configuration
- Permission issues

**Solutions:**
```bash
# Verify GOAD status
cd /path/to/goad && python3 goad.py status

# Re-run integration
python3 goad-blue.py --integrate-goad --force

# Check network connectivity
ping <goad-vm-ip>
```

### Performance issues

**Optimization Tips:**
1. **Increase VM resources** (RAM, CPU)
2. **Use SSD storage** for better I/O performance
3. **Adjust log retention** to reduce storage usage
4. **Tune SIEM indexing** for better performance
5. **Monitor resource usage** and scale accordingly

## Licensing and Legal

### What license is GOAD-Blue released under?

GOAD-Blue is released under the GPL-3.0 License, which allows for free use, modification, and distribution while requiring derivative works to also be open source.

### Can I use GOAD-Blue in commercial training?

Yes, the GPL-3.0 license allows commercial use. However, if you modify GOAD-Blue and distribute it, you must make your modifications available under the same license.

### Are there any export restrictions?

GOAD-Blue contains security tools that may be subject to export regulations in some countries. Check your local laws and regulations before international deployment.

## Training and Education

### What skill level is required to use GOAD-Blue?

**Beginner Level:**
- Basic cybersecurity knowledge
- Command-line familiarity
- Willingness to learn

**Intermediate Level:**
- Security tool experience
- Active Directory understanding
- Basic scripting knowledge

**Advanced Level:**
- Deep security expertise
- Infrastructure automation experience
- Custom development capabilities

### Are there training materials available?

Yes! GOAD-Blue includes:
- **Built-in scenarios** for hands-on practice
- **Documentation** with step-by-step guides
- **Video tutorials** (coming soon)
- **Community resources** and shared scenarios

### Can GOAD-Blue be used for certification preparation?

Absolutely! GOAD-Blue helps prepare for various certifications:
- **GCIH** (GIAC Certified Incident Handler)
- **GCFA** (GIAC Certified Forensic Analyst)
- **GNFA** (GIAC Network Forensic Analyst)
- **GSEC** (GIAC Security Essentials)
- **CompTIA CySA+**
- **CompTIA Security+**

## Community and Support

### How can I contribute to GOAD-Blue?

We welcome contributions:
1. **Code contributions** via GitHub pull requests
2. **Documentation improvements**
3. **Bug reports** and feature requests
4. **Training scenarios** and use cases
5. **Community support** in forums and chat

### Where can I get help?

**Community Support:**
- GitHub Issues for bug reports
- Discord/Slack community chat
- Community forums and discussions

**Professional Support:**
- Enterprise support packages available
- Training and consulting services
- Custom deployment assistance

### How often is GOAD-Blue updated?

- **Major releases**: Every 6 months
- **Minor updates**: Monthly
- **Security patches**: As needed
- **Community contributions**: Ongoing

### Can I request new features?

Yes! Feature requests are welcome:
1. Check existing GitHub issues first
2. Create a detailed feature request
3. Participate in community discussions
4. Consider contributing the feature yourself

## Technical Details

### What data does GOAD-Blue collect?

GOAD-Blue collects:
- **Windows Event Logs** from GOAD VMs
- **Network traffic** metadata and alerts
- **Process execution** data from endpoints
- **Threat intelligence** indicators
- **System performance** metrics

All data stays within your environment - nothing is sent externally.

### How is data stored and retained?

- **SIEM data**: Configurable retention (default: 90 days)
- **Network captures**: Configurable retention (default: 30 days)
- **Threat intelligence**: Persistent storage
- **Configuration data**: Backed up with system

### Can GOAD-Blue scale to enterprise environments?

Yes, GOAD-Blue is designed to scale:
- **Horizontal scaling**: Add more instances
- **Vertical scaling**: Increase VM resources
- **Distributed deployment**: Multiple sites
- **Load balancing**: Distribute processing load

### What APIs are available?

GOAD-Blue provides APIs for:
- **Configuration management**
- **Component status and control**
- **Data export and import**
- **Integration with external tools**
- **Custom automation scripts**

---

!!! question "Still Have Questions?"
    If you can't find the answer you're looking for, please:
    
    - Check our [Troubleshooting Guide](../troubleshooting/)
    - Search the [GitHub Issues](https://github.com/your-org/goad-blue/issues)
    - Join our [Community Discord](https://discord.gg/goad-blue)
    - Contact [Support](mailto:<EMAIL>) for enterprise users
