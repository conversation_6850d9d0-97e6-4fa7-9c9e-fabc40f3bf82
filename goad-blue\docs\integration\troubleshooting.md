# Integration Troubleshooting

This guide provides solutions for common issues encountered when integrating GOAD-Blue with existing GOAD environments.

## 🔍 Common Integration Issues

### **Network Connectivity Problems**

#### **Issue: Cannot reach GOAD systems from GOAD-Blue**

**Symptoms:**
- Ping timeouts to GOAD IP addresses
- Agent deployment failures
- No data flow to SIEM platforms

**Diagnosis:**
```bash
# Test basic connectivity
ping *************  # GOAD DC
ping ************** # GOAD-Blue Splunk

# Check routing
ip route show | grep 192.168.56
ip route show | grep 192.168.100

# Test specific ports
nc -zv ************* 445   # SMB
nc -zv ************* 5985  # WinRM
nc -zv ************** 9997 # Splunk forwarder
```

**Solutions:**

1. **Check Network Bridges/Switches:**
```bash
# VMware
sudo vmware-netcfg
# Verify vmnet8 (GOAD) and vmnet2 (GOAD-Blue) are configured

# Proxmox
ip addr show vmbr1  # GOAD network
ip addr show vmbr2  # GOAD-Blue network
brctl show

# VirtualBox
VBoxManage list hostonlyifs
VBoxManage list dhcpservers
```

2. **Configure Routing:**
```bash
# Add missing routes
sudo ip route add ************/24 via *************
sudo ip route add *************/24 via ************

# Make persistent (Ubuntu/Debian)
echo "************/24 via *************" | sudo tee -a /etc/network/interfaces
```

3. **Check Firewall Rules:**
```bash
# UFW
sudo ufw status
sudo ufw allow from ************/24 to *************/24
sudo ufw allow from *************/24 to ************/24

# iptables
sudo iptables -L -n | grep 192.168
sudo iptables -A FORWARD -s ************/24 -d *************/24 -j ACCEPT
```

#### **Issue: Intermittent connectivity**

**Symptoms:**
- Sporadic connection failures
- Agents going offline randomly
- Inconsistent data flow

**Solutions:**

1. **Check Network Performance:**
```bash
# Monitor network interfaces
iftop -i vmnet8
iftop -i vmnet2

# Check for packet loss
ping -c 100 ************* | grep "packet loss"

# Monitor bandwidth usage
vnstat -i vmnet8 -l
```

2. **Adjust Network Settings:**
```bash
# Increase network buffers
echo 'net.core.rmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
echo 'net.core.wmem_max = 16777216' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### **Agent Deployment Issues**

#### **Issue: PowerShell execution policy blocks agent installation**

**Symptoms:**
- "Execution of scripts is disabled" errors
- Agent installation scripts fail
- PowerShell commands rejected

**Solutions:**

1. **Temporarily bypass execution policy:**
```powershell
# On target GOAD system
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Or run scripts with bypass
powershell.exe -ExecutionPolicy Bypass -File install-agents.ps1
```

2. **Configure execution policy via Group Policy:**
```powershell
# On domain controller
gpupdate /force

# Check current policy
Get-ExecutionPolicy -List
```

#### **Issue: WinRM authentication failures**

**Symptoms:**
- "Access denied" errors during remote execution
- Authentication prompts that fail
- Cannot establish PowerShell sessions

**Solutions:**

1. **Configure WinRM on GOAD systems:**
```powershell
# Enable WinRM
winrm quickconfig -force

# Configure authentication
winrm set winrm/config/service/auth '@{Basic="true"}'
winrm set winrm/config/service '@{AllowUnencrypted="true"}'

# Add trusted hosts
winrm set winrm/config/client '@{TrustedHosts="192.168.100.*"}'
```

2. **Use proper credentials:**
```powershell
# Create credential object
$securePassword = ConvertTo-SecureString "Password123!" -AsPlainText -Force
$credential = New-Object System.Management.Automation.PSCredential("sevenkingdoms\administrator", $securePassword)

# Test connection
Test-WSMan -ComputerName ************* -Credential $credential
```

#### **Issue: Sysmon installation fails**

**Symptoms:**
- Sysmon service not starting
- No Sysmon events in Event Log
- Installation errors

**Solutions:**

1. **Check Sysmon installation:**
```powershell
# Verify Sysmon is installed
Get-Service Sysmon64

# Check Sysmon configuration
sysmon64.exe -c

# Reinstall with verbose logging
sysmon64.exe -accepteula -i -l
```

2. **Validate Sysmon configuration:**
```xml
<!-- Basic sysmon-config.xml -->
<Sysmon schemaversion="4.82">
  <EventFiltering>
    <ProcessCreate onmatch="include">
      <Image condition="contains">powershell</Image>
      <Image condition="contains">cmd</Image>
      <Image condition="contains">wmic</Image>
    </ProcessCreate>
    <NetworkConnect onmatch="include">
      <DestinationPort condition="is">445</DestinationPort>
      <DestinationPort condition="is">3389</DestinationPort>
    </NetworkConnect>
  </EventFiltering>
</Sysmon>
```

### **Data Flow Problems**

#### **Issue: No data appearing in Splunk**

**Symptoms:**
- Empty search results in Splunk
- Universal Forwarder shows as connected but no data
- Index appears empty

**Diagnosis:**
```bash
# Check Splunk forwarder status
/opt/splunkforwarder/bin/splunk status

# Check forwarder logs
tail -f /opt/splunkforwarder/var/log/splunk/splunkd.log

# Test connection to indexer
telnet ************** 9997

# Check index status in Splunk
index=goad_blue_windows | stats count
```

**Solutions:**

1. **Verify Universal Forwarder configuration:**
```ini
# Check inputs.conf
cat /opt/splunkforwarder/etc/system/local/inputs.conf

# Check outputs.conf
cat /opt/splunkforwarder/etc/system/local/outputs.conf

# Restart forwarder
/opt/splunkforwarder/bin/splunk restart
```

2. **Check Windows Event Log permissions:**
```powershell
# Verify SplunkForwarder service account has log access
wevtutil gl Security
wevtutil gl System
wevtutil gl Application

# Grant permissions if needed
wevtutil sl Security /ca:O:BAG:SYD:(A;;0xf0005;;;SY)(A;;0x5;;;BA)(A;;0x1;;;S-1-5-32-573)
```

3. **Validate index configuration:**
```ini
# On Splunk indexer, check indexes.conf
[goad_blue_windows]
homePath = $SPLUNK_DB/goad_blue_windows/db
coldPath = $SPLUNK_DB/goad_blue_windows/colddb
thawedPath = $SPLUNK_DB/goad_blue_windows/thaweddb
```

#### **Issue: Elasticsearch not receiving data**

**Symptoms:**
- No documents in Elasticsearch indices
- Logstash processing errors
- Winlogbeat connection failures

**Diagnosis:**
```bash
# Check Elasticsearch cluster health
curl -X GET "*************1:9200/_cluster/health"

# Check indices
curl -X GET "*************1:9200/_cat/indices/goad-blue-*"

# Check Logstash logs
tail -f /var/log/logstash/logstash-plain.log

# Test Winlogbeat connection
winlogbeat test output -c winlogbeat.yml
```

**Solutions:**

1. **Fix Logstash pipeline issues:**
```bash
# Test Logstash configuration
/usr/share/logstash/bin/logstash --config.test_and_exit -f /etc/logstash/conf.d/

# Check pipeline status
curl -X GET "*************1:9600/_node/stats/pipelines"

# Restart Logstash
sudo systemctl restart logstash
```

2. **Verify Winlogbeat configuration:**
```yaml
# Test configuration
winlogbeat test config -c winlogbeat.yml

# Test output connectivity
winlogbeat test output -c winlogbeat.yml

# Check service status
Get-Service winlogbeat
```

#### **Issue: Velociraptor agents not connecting**

**Symptoms:**
- Agents show as offline in Velociraptor console
- No artifact collection occurring
- Connection timeouts

**Solutions:**

1. **Check Velociraptor server configuration:**
```bash
# Verify server is running
sudo systemctl status velociraptor

# Check server logs
sudo journalctl -u velociraptor -f

# Test server connectivity
curl -k https://**************:8000/
```

2. **Validate client configuration:**
```yaml
# Check client config on GOAD system
type C:\temp\velociraptor-client.yaml

# Test client connectivity
velociraptor.exe --config C:\temp\velociraptor-client.yaml query "SELECT * FROM info()"

# Check client service
Get-Service Velociraptor
```

### **Performance Issues**

#### **Issue: High resource usage on GOAD systems**

**Symptoms:**
- Slow GOAD system performance
- High CPU/memory usage
- Disk space filling up rapidly

**Solutions:**

1. **Optimize agent collection intervals:**
```ini
# Reduce Splunk monitoring frequency
[perfmon://CPU]
interval = 300  # Increase from 30 to 300 seconds

# Limit Sysmon events
<ProcessCreate onmatch="exclude">
  <Image condition="contains">svchost</Image>
  <Image condition="contains">explorer</Image>
</ProcessCreate>
```

2. **Configure log rotation:**
```powershell
# Windows Event Log size limits
wevtutil sl Security /ms:104857600  # 100MB
wevtutil sl System /ms:104857600
wevtutil sl Application /ms:104857600
```

3. **Monitor resource usage:**
```bash
# Check system resources
python3 goad-blue.py monitor_resources --hosts goad_systems

# Generate resource report
python3 goad-blue.py resource_report --period week
```

## 🛠️ Diagnostic Tools

### **Network Diagnostic Script**

```bash
#!/bin/bash
# diagnose_network.sh

echo "=== GOAD-Blue Network Diagnostics ==="

# Test basic connectivity
echo "Testing basic connectivity..."
ping -c 3 ************* && echo "✓ GOAD DC reachable" || echo "✗ GOAD DC unreachable"
ping -c 3 ************** && echo "✓ Splunk reachable" || echo "✗ Splunk unreachable"

# Test service ports
echo "Testing service ports..."
nc -zv ************** 8000 2>&1 | grep -q "succeeded" && echo "✓ Splunk Web" || echo "✗ Splunk Web"
nc -zv ************** 9997 2>&1 | grep -q "succeeded" && echo "✓ Splunk Forwarder" || echo "✗ Splunk Forwarder"
nc -zv ************* 5985 2>&1 | grep -q "succeeded" && echo "✓ WinRM HTTP" || echo "✗ WinRM HTTP"

# Check routing
echo "Checking routing..."
ip route show | grep 192.168.56 && echo "✓ GOAD route exists" || echo "✗ GOAD route missing"
ip route show | grep 192.168.100 && echo "✓ GOAD-Blue route exists" || echo "✗ GOAD-Blue route missing"

# Check firewall
echo "Checking firewall..."
sudo ufw status | grep -q "************/24" && echo "✓ GOAD network allowed" || echo "⚠ GOAD network rules missing"

echo "Network diagnostics completed."
```

### **Agent Status Checker**

```powershell
# check_agents.ps1
param(
    [string[]]$ComputerNames = @("kingslanding", "winterfell", "meereen", "castelblack", "braavos", "tyrell")
)

function Test-AgentStatus {
    param([string]$ComputerName)
    
    Write-Host "Checking $ComputerName..." -ForegroundColor Yellow
    
    try {
        $session = New-PSSession -ComputerName $ComputerName -ErrorAction Stop
        
        $result = Invoke-Command -Session $session -ScriptBlock {
            $status = @{}
            
            # Check Sysmon
            $sysmon = Get-Service -Name "Sysmon64" -ErrorAction SilentlyContinue
            $status.Sysmon = if ($sysmon) { $sysmon.Status } else { "Not Installed" }
            
            # Check Splunk UF
            $splunk = Get-Service -Name "SplunkForwarder" -ErrorAction SilentlyContinue
            $status.SplunkUF = if ($splunk) { $splunk.Status } else { "Not Installed" }
            
            # Check Velociraptor
            $velo = Get-Service -Name "Velociraptor" -ErrorAction SilentlyContinue
            $status.Velociraptor = if ($velo) { $velo.Status } else { "Not Installed" }
            
            # Check recent Sysmon events
            $recentEvents = Get-WinEvent -LogName "Microsoft-Windows-Sysmon/Operational" -MaxEvents 5 -ErrorAction SilentlyContinue
            $status.RecentEvents = if ($recentEvents) { $recentEvents.Count } else { 0 }
            
            return $status
        }
        
        Write-Host "  Sysmon: $($result.Sysmon)" -ForegroundColor $(if($result.Sysmon -eq "Running"){"Green"}else{"Red"})
        Write-Host "  Splunk UF: $($result.SplunkUF)" -ForegroundColor $(if($result.SplunkUF -eq "Running"){"Green"}else{"Red"})
        Write-Host "  Velociraptor: $($result.Velociraptor)" -ForegroundColor $(if($result.Velociraptor -eq "Running"){"Green"}else{"Red"})
        Write-Host "  Recent Events: $($result.RecentEvents)" -ForegroundColor $(if($result.RecentEvents -gt 0){"Green"}else{"Yellow"})
        
        Remove-PSSession $session
        
    } catch {
        Write-Host "  ✗ Connection failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

foreach ($computer in $ComputerNames) {
    Test-AgentStatus -ComputerName $computer
    Write-Host ""
}
```

### **Data Flow Validator**

```python
#!/usr/bin/env python3
# validate_data_flow.py

import requests
import json
import time
from datetime import datetime, timedelta

def validate_splunk_flow():
    """Validate data flow to Splunk"""
    try:
        # Check if Splunk is accessible
        response = requests.get(
            "https://**************:8000",
            verify=False,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✓ Splunk web interface accessible")
            
            # Additional checks would require Splunk SDK
            # For now, just check accessibility
            return True
        else:
            print("✗ Splunk web interface not accessible")
            return False
            
    except Exception as e:
        print(f"✗ Splunk check failed: {e}")
        return False

def validate_elasticsearch_flow():
    """Validate data flow to Elasticsearch"""
    try:
        # Check cluster health
        response = requests.get("http://*************1:9200/_cluster/health")
        
        if response.status_code == 200:
            health = response.json()
            print(f"✓ Elasticsearch cluster: {health['status']}")
            
            # Check for recent data
            response = requests.get("http://*************1:9200/goad-blue-*/_count")
            if response.status_code == 200:
                count = response.json()['count']
                print(f"✓ Total documents: {count}")
                return count > 0
            
        return False
        
    except Exception as e:
        print(f"✗ Elasticsearch check failed: {e}")
        return False

def main():
    print("=== GOAD-Blue Data Flow Validation ===")
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    splunk_ok = validate_splunk_flow()
    elastic_ok = validate_elasticsearch_flow()
    
    print()
    print("=== Summary ===")
    print(f"Splunk: {'OK' if splunk_ok else 'FAILED'}")
    print(f"Elasticsearch: {'OK' if elastic_ok else 'FAILED'}")
    
    overall = splunk_ok and elastic_ok
    print(f"Overall Status: {'HEALTHY' if overall else 'DEGRADED'}")

if __name__ == "__main__":
    main()
```

## 📞 Getting Help

### **Log Locations**

**GOAD-Blue Logs:**
- Splunk: `/opt/splunk/var/log/splunk/`
- Elasticsearch: `/var/log/elasticsearch/`
- Logstash: `/var/log/logstash/`
- Security Onion: `/var/log/suricata/`, `/opt/so/log/`

**GOAD System Logs:**
- Windows Event Logs: Event Viewer
- Sysmon: `Microsoft-Windows-Sysmon/Operational`
- Splunk UF: `C:\Program Files\SplunkUniversalForwarder\var\log\splunk\`
- Velociraptor: Windows Event Log

### **Support Resources**

- **GitHub Issues**: Report bugs and request features
- **Documentation**: Complete troubleshooting guides
- **Community Forum**: Get help from other users
- **Discord**: Real-time support chat

---

!!! tip "Systematic Troubleshooting"
    When encountering issues:
    1. Check network connectivity first
    2. Verify service status on all systems
    3. Review logs for error messages
    4. Test with minimal configuration
    5. Gradually add complexity back

!!! warning "Common Pitfalls"
    - Forgetting to configure routing between networks
    - Using incorrect credentials for domain authentication
    - Not accounting for Windows Firewall on GOAD systems
    - Insufficient disk space for log storage
    - Clock synchronization issues between systems
