{"variables": {"splunk_version": "9.1.2", "splunk_build": "b6b9c8185839", "vm_name": "goad-blue-splunk-server", "iso_url": "https://releases.ubuntu.com/22.04/ubuntu-22.04.3-live-server-amd64.iso", "iso_checksum": "sha256:a4acfda10b18da50e2ec50ccaf860d7f20b389df8765611142305c0e911d16fd"}, "builders": [{"type": "vmware-iso", "vm_name": "{{user `vm_name`}}", "guest_os_type": "ubuntu-64", "iso_url": "{{user `iso_url`}}", "iso_checksum": "{{user `iso_checksum`}}", "ssh_username": "ubuntu", "ssh_password": "ubuntu", "ssh_timeout": "20m", "disk_size": 100000, "memory": 8192, "cpus": 4, "network_adapter_type": "vmxnet3", "sound": false, "usb": false, "boot_wait": "5s", "boot_command": ["<enter><wait><f6><wait><esc><wait>", "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>", "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>", "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>", "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>", "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>", "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>", "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>", "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>", "<bs><bs><bs>", "/install/vmlinuz", " initrd=/install/initrd.gz", " priority=critical", " locale=en_US", " file=/cdrom/preseed/ubuntu-server.seed", " quiet", " ---", "<enter>"]}], "provisioners": [{"type": "shell", "inline": ["sudo apt-get update", "sudo apt-get install -y python3-pip ansible wget curl", "sudo pip3 install ansible-core"]}, {"type": "ansible-local", "playbook_file": "../ansible/goad-blue-splunk-server.yml", "extra_arguments": ["--extra-vars", "splunk_version={{user `splunk_version`}} splunk_build={{user `splunk_build`}}"]}, {"type": "shell", "inline": ["sudo apt-get clean", "sudo rm -rf /var/lib/apt/lists/*", "sudo rm -rf /tmp/*", "sudo rm -rf /var/tmp/*", "history -c"]}], "post-processors": [{"type": "manifest", "output": "manifest.json", "strip_path": true}]}