- name: "Check if administrator folder already exist"
  ansible.windows.win_stat:
    path: "c:\\users\\<USER>\\users\\administrator\\desktop"
  register: administrator_desktop_folder

- name: "Create administrator directory"
  ansible.windows.win_file:
    path: "c:\\users\\<USER>\\users\\administrator\\desktop"
    state: directory
  when: not administrator_desktop_folder.stat.exists

- name: Disable inherited ACE's
  ansible.windows.win_acl_inheritance:
    path: "c:\\users\\<USER>\\users\\administrator to administrators"
  ansible.windows.win_acl:
    path: "c:\\users\\<USER>\\users\\administrator to administrators"
  ansible.windows.win_acl:
    path: "c:\\users\\<USER>\SYSTEM
    rights: Read,<PERSON>rite,<PERSON><PERSON><PERSON>,FullControl,Delete
    type: allow
    state: present
  when: not administrator_folder.stat.exists

- name: "Allow c:\\users\\<USER>\\users\\administrator"
    user: Administrators
    rights: Read,Write,Modify,FullControl,Delete
    type: allow
    state: present
  when: not administrator_folder.stat.exists