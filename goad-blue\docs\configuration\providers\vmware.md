# VMware Configuration

This guide covers GOAD-Blue configuration for VMware environments, including vSphere, Workstation, and Fusion deployments.

## 🏗️ VMware Environment Overview

GOAD-Blue supports multiple VMware platforms with optimized configurations for each environment type.

```mermaid
graph TB
    subgraph "🏢 VMware vSphere"
        VCENTER[📊 vCenter Server<br/>Centralized Management<br/>Resource Allocation]
        ESXI[🖥️ ESXi Hosts<br/>Hypervisor Layer<br/>VM Hosting]
        VSAN[💾 vSAN Storage<br/>Distributed Storage<br/>High Performance]
        NSX[🌐 NSX Networking<br/>Software-Defined Network<br/>Micro-segmentation]
    end
    
    subgraph "🖥️ VMware Workstation"
        WORKSTATION[💻 Workstation Pro<br/>Desktop Virtualization<br/>Development Environment]
        HOST_NETWORKS[🌐 Host Networks<br/>NAT & Host-Only<br/>Custom Networks]
    end
    
    subgraph "🍎 VMware Fusion"
        FUSION[🖥️ Fusion Pro<br/>Mac Virtualization<br/>Development Platform]
        MAC_NETWORKS[🌐 Mac Networks<br/>Shared & Private<br/>Custom Adapters]
    end
    
    subgraph "🛡️ GOAD-Blue Components"
        SPLUNK[📊 Splunk Enterprise<br/>SIEM Platform<br/>Log Analysis]
        SO[🧅 Security Onion<br/>Network Monitoring<br/>IDS/IPS]
        VELO[🦖 Velociraptor<br/>Endpoint Detection<br/>Digital Forensics]
        MISP[🧠 MISP<br/>Threat Intelligence<br/>IOC Management]
    end
    
    VCENTER --> ESXI
    ESXI --> VSAN
    ESXI --> NSX
    
    WORKSTATION --> HOST_NETWORKS
    FUSION --> MAC_NETWORKS
    
    ESXI --> SPLUNK
    ESXI --> SO
    ESXI --> VELO
    ESXI --> MISP
    
    HOST_NETWORKS --> SPLUNK
    MAC_NETWORKS --> SPLUNK
    
    classDef vsphere fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef workstation fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef fusion fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef components fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class VCENTER,ESXI,VSAN,NSX vsphere
    class WORKSTATION,HOST_NETWORKS workstation
    class FUSION,MAC_NETWORKS fusion
    class SPLUNK,SO,VELO,MISP components
```

## 🏢 VMware vSphere Configuration

### **vCenter Server Setup**

```yaml
# VMware vSphere configuration
vmware_vsphere:
  # vCenter connection details
  vcenter:
    hostname: "vcenter.company.com"
    username: "<EMAIL>"
    password: "${VCENTER_PASSWORD}"
    port: 443
    ssl_verify: true
    
  # Datacenter configuration
  datacenter:
    name: "GOAD-Blue-DC"
    location: "Primary Datacenter"
    
  # Cluster configuration
  cluster:
    name: "GOAD-Blue-Cluster"
    drs_enabled: true
    ha_enabled: true
    vsan_enabled: true
    
    # DRS configuration
    drs:
      automation_level: "fullyAutomated"
      migration_threshold: 3
      
    # HA configuration
    ha:
      admission_control_enabled: true
      failover_level: 1
      vm_monitoring: "vmMonitoringOnly"
      
  # ESXi hosts
  hosts:
    - hostname: "esxi01.company.com"
      username: "root"
      password: "${ESXI_PASSWORD}"
      
    - hostname: "esxi02.company.com"
      username: "root"
      password: "${ESXI_PASSWORD}"
      
    - hostname: "esxi03.company.com"
      username: "root"
      password: "${ESXI_PASSWORD}"
```

### **Network Configuration**

```yaml
# vSphere networking configuration
networking:
  # Distributed Switch configuration
  distributed_switch:
    name: "GOAD-Blue-DVS"
    version: "7.0.0"
    mtu: 9000
    
    # Uplinks configuration
    uplinks:
      - name: "Uplink1"
        active: true
        
      - name: "Uplink2"
        active: true
        
    # Port groups
    port_groups:
      # Management network
      - name: "GOAD-Blue-Management"
        vlan_id: 10
        vlan_type: "vlan"
        ports: 128
        
      # SIEM network
      - name: "GOAD-Blue-SIEM"
        vlan_id: 100
        vlan_type: "vlan"
        ports: 64
        
      # Monitoring network
      - name: "GOAD-Blue-Monitoring"
        vlan_id: 101
        vlan_type: "vlan"
        ports: 64
        promiscuous_mode: true
        forged_transmits: true
        mac_changes: true
        
      # GOAD network
      - name: "GOAD-Production"
        vlan_id: 200
        vlan_type: "vlan"
        ports: 128
        
  # NSX configuration (if available)
  nsx:
    enabled: false
    manager: "nsx-manager.company.com"
    username: "admin"
    password: "${NSX_PASSWORD}"
    
    # Logical switches
    logical_switches:
      - name: "GOAD-Blue-Logical"
        transport_zone: "TZ-VLAN"
        replication_mode: "MTEP"
```

### **Storage Configuration**

```yaml
# vSphere storage configuration
storage:
  # vSAN configuration
  vsan:
    enabled: true
    
    # Storage policy
    policies:
      - name: "GOAD-Blue-Performance"
        description: "High performance for SIEM workloads"
        rules:
          - "VSAN.hostFailuresToTolerate=1"
          - "VSAN.stripeWidth=2"
          - "VSAN.forceProvisioning=false"
          
      - name: "GOAD-Blue-Capacity"
        description: "Capacity optimized for logs"
        rules:
          - "VSAN.hostFailuresToTolerate=1"
          - "VSAN.stripeWidth=1"
          - "VSAN.spbmProfileGenerationNumber=1"
          
  # Traditional storage
  datastores:
    - name: "GOAD-Blue-SSD"
      type: "VMFS"
      capacity_gb: 2000
      performance_tier: "high"
      
    - name: "GOAD-Blue-Archive"
      type: "NFS"
      server: "nas.company.com"
      path: "/volume1/goad-blue-archive"
      capacity_gb: 10000
      performance_tier: "archive"
```

### **VM Templates and Deployment**

```yaml
# VM template configuration
vm_templates:
  # Ubuntu template for most components
  ubuntu_template:
    name: "ubuntu-20.04-template"
    guest_os: "ubuntu64Guest"
    cpu_cores: 2
    memory_mb: 4096
    disk_gb: 50
    network: "GOAD-Blue-Management"
    
    # Customization specification
    customization:
      hostname_prefix: "goad-blue"
      domain: "goad-blue.local"
      dns_servers: ["************", "************"]
      timezone: "America/New_York"
      
  # Windows template for FLARE-VM
  windows_template:
    name: "windows-10-template"
    guest_os: "windows9_64Guest"
    cpu_cores: 4
    memory_mb: 8192
    disk_gb: 100
    network: "GOAD-Blue-Analysis"
    
# Component VM specifications
vm_specifications:
  # Splunk VMs
  splunk_search_head:
    template: "ubuntu_template"
    cpu_cores: 4
    memory_mb: 8192
    disk_gb: 100
    network: "GOAD-Blue-SIEM"
    storage_policy: "GOAD-Blue-Performance"
    
  splunk_indexer:
    template: "ubuntu_template"
    cpu_cores: 8
    memory_mb: 16384
    disk_gb: 500
    network: "GOAD-Blue-SIEM"
    storage_policy: "GOAD-Blue-Capacity"
    
  # Security Onion VMs
  security_onion_manager:
    template: "ubuntu_template"
    cpu_cores: 4
    memory_mb: 16384
    disk_gb: 200
    network: "GOAD-Blue-Monitoring"
    storage_policy: "GOAD-Blue-Performance"
    
  security_onion_sensor:
    template: "ubuntu_template"
    cpu_cores: 4
    memory_mb: 8192
    disk_gb: 1000
    networks: ["GOAD-Blue-Monitoring", "GOAD-Production"]
    storage_policy: "GOAD-Blue-Capacity"
    
  # Velociraptor VM
  velociraptor:
    template: "ubuntu_template"
    cpu_cores: 2
    memory_mb: 4096
    disk_gb: 100
    network: "GOAD-Blue-SIEM"
    storage_policy: "GOAD-Blue-Performance"
    
  # MISP VM
  misp:
    template: "ubuntu_template"
    cpu_cores: 2
    memory_mb: 4096
    disk_gb: 50
    network: "GOAD-Blue-SIEM"
    storage_policy: "GOAD-Blue-Performance"
    
  # FLARE-VM
  flare_vm:
    template: "windows_template"
    cpu_cores: 4
    memory_mb: 8192
    disk_gb: 100
    network: "GOAD-Blue-Analysis"
    storage_policy: "GOAD-Blue-Performance"
```

## 🖥️ VMware Workstation Configuration

### **Workstation Pro Setup**

```yaml
# VMware Workstation configuration
vmware_workstation:
  # Installation path
  installation_path: "C:\\Program Files (x86)\\VMware\\VMware Workstation"
  
  # Global preferences
  preferences:
    default_vm_path: "D:\\Virtual Machines\\GOAD-Blue"
    memory_allocation: "automatic"
    cpu_allocation: "automatic"
    
  # Network configuration
  networks:
    # Host-only network for management
    vmnet1:
      type: "host-only"
      subnet: "***********"
      netmask: "*************"
      dhcp_enabled: true
      dhcp_range: "************0-*************"
      
    # Custom network for GOAD-Blue
    vmnet2:
      type: "host-only"
      subnet: "*************"
      netmask: "*************"
      dhcp_enabled: false
      
    # Custom network for GOAD
    vmnet3:
      type: "host-only"
      subnet: "************"
      netmask: "*************"
      dhcp_enabled: true
      dhcp_range: "**************-**************"
      
  # VM configurations
  virtual_machines:
    # Splunk VM
    splunk:
      name: "GOAD-Blue-Splunk"
      guest_os: "ubuntu-64"
      memory_mb: 4096
      cpu_cores: 2
      disk_size_gb: 100
      network_adapter: "vmnet2"
      ip_address: "**************"
      
    # Security Onion VM
    security_onion:
      name: "GOAD-Blue-SecurityOnion"
      guest_os: "ubuntu-64"
      memory_mb: 8192
      cpu_cores: 4
      disk_size_gb: 200
      network_adapters:
        - "vmnet2"  # Management
        - "vmnet3"  # Monitoring
      ip_addresses:
        - "**************"
        - "*************"
        
    # Velociraptor VM
    velociraptor:
      name: "GOAD-Blue-Velociraptor"
      guest_os: "ubuntu-64"
      memory_mb: 2048
      cpu_cores: 2
      disk_size_gb: 50
      network_adapter: "vmnet2"
      ip_address: "**************"
```

### **Workstation Automation Scripts**

```powershell
# PowerShell script for Workstation automation
# Create-GOADBlueWorkstation.ps1

param(
    [string]$VMwarePath = "C:\Program Files (x86)\VMware\VMware Workstation",
    [string]$VMPath = "D:\Virtual Machines\GOAD-Blue"
)

# VMware Workstation executable
$vmrun = "$VMwarePath\vmrun.exe"

# Create VM directory
New-Item -ItemType Directory -Path $VMPath -Force

# Create Splunk VM
& $vmrun create "$VMPath\Splunk\Splunk.vmx" "ubuntu-64" -memory 4096 -disk 100GB

# Configure Splunk VM network
$splunkVMX = @"
ethernet0.present = "TRUE"
ethernet0.connectionType = "custom"
ethernet0.vnet = "vmnet2"
ethernet0.addressType = "static"
guestOS = "ubuntu-64"
memsize = "4096"
numvcpus = "2"
"@

$splunkVMX | Out-File -FilePath "$VMPath\Splunk\Splunk.vmx" -Encoding ASCII

# Start VMs
& $vmrun start "$VMPath\Splunk\Splunk.vmx"

Write-Host "GOAD-Blue Workstation environment created successfully!"
```

## 🍎 VMware Fusion Configuration

### **Fusion Pro Setup**

```yaml
# VMware Fusion configuration
vmware_fusion:
  # Installation path
  installation_path: "/Applications/VMware Fusion.app"
  
  # Global preferences
  preferences:
    default_vm_path: "~/Virtual Machines/GOAD-Blue"
    memory_allocation: "automatic"
    cpu_allocation: "automatic"
    
  # Network configuration
  networks:
    # Private network for GOAD-Blue
    vmnet2:
      type: "private"
      subnet: "*************"
      netmask: "*************"
      
    # Shared network for GOAD
    vmnet8:
      type: "shared"
      subnet: "************"
      netmask: "*************"
      
  # VM configurations
  virtual_machines:
    # Splunk VM
    splunk:
      name: "GOAD-Blue-Splunk"
      guest_os: "ubuntu-64"
      memory_mb: 4096
      cpu_cores: 2
      disk_size_gb: 100
      network: "vmnet2"
      
    # Security Onion VM
    security_onion:
      name: "GOAD-Blue-SecurityOnion"
      guest_os: "ubuntu-64"
      memory_mb: 8192
      cpu_cores: 4
      disk_size_gb: 200
      networks: ["vmnet2", "vmnet8"]
```

## 🔧 Terraform Configuration for VMware

### **vSphere Provider Configuration**

```hcl
# terraform/providers/vmware/main.tf

terraform {
  required_providers {
    vsphere = {
      source  = "hashicorp/vsphere"
      version = "~> 2.0"
    }
  }
}

# Configure the VMware vSphere Provider
provider "vsphere" {
  user                 = var.vsphere_user
  password             = var.vsphere_password
  vsphere_server       = var.vsphere_server
  allow_unverified_ssl = var.allow_unverified_ssl
}

# Data sources
data "vsphere_datacenter" "dc" {
  name = var.datacenter_name
}

data "vsphere_datastore" "datastore" {
  name          = var.datastore_name
  datacenter_id = data.vsphere_datacenter.dc.id
}

data "vsphere_compute_cluster" "cluster" {
  name          = var.cluster_name
  datacenter_id = data.vsphere_datacenter.dc.id
}

data "vsphere_network" "management" {
  name          = var.management_network
  datacenter_id = data.vsphere_datacenter.dc.id
}

data "vsphere_network" "siem" {
  name          = var.siem_network
  datacenter_id = data.vsphere_datacenter.dc.id
}

data "vsphere_virtual_machine" "ubuntu_template" {
  name          = var.ubuntu_template_name
  datacenter_id = data.vsphere_datacenter.dc.id
}

# Splunk Search Head VM
resource "vsphere_virtual_machine" "splunk_search_head" {
  name             = "goad-blue-splunk-sh"
  resource_pool_id = data.vsphere_compute_cluster.cluster.resource_pool_id
  datastore_id     = data.vsphere_datastore.datastore.id
  
  num_cpus = 4
  memory   = 8192
  guest_id = data.vsphere_virtual_machine.ubuntu_template.guest_id
  
  scsi_type = data.vsphere_virtual_machine.ubuntu_template.scsi_type
  
  network_interface {
    network_id   = data.vsphere_network.siem.id
    adapter_type = data.vsphere_virtual_machine.ubuntu_template.network_interface_types[0]
  }
  
  disk {
    label            = "disk0"
    size             = 100
    eagerly_scrub    = data.vsphere_virtual_machine.ubuntu_template.disks.0.eagerly_scrub
    thin_provisioned = data.vsphere_virtual_machine.ubuntu_template.disks.0.thin_provisioned
  }
  
  clone {
    template_uuid = data.vsphere_virtual_machine.ubuntu_template.id
    
    customize {
      linux_options {
        host_name = "splunk-sh"
        domain    = "goad-blue.local"
      }
      
      network_interface {
        ipv4_address = "**************"
        ipv4_netmask = 26
      }
      
      ipv4_gateway = "*************"
      dns_server_list = ["************", "************"]
    }
  }
}

# Security Onion Manager VM
resource "vsphere_virtual_machine" "security_onion_manager" {
  name             = "goad-blue-so-manager"
  resource_pool_id = data.vsphere_compute_cluster.cluster.resource_pool_id
  datastore_id     = data.vsphere_datastore.datastore.id
  
  num_cpus = 4
  memory   = 16384
  guest_id = data.vsphere_virtual_machine.ubuntu_template.guest_id
  
  network_interface {
    network_id = data.vsphere_network.management.id
  }
  
  disk {
    label = "disk0"
    size  = 200
  }
  
  clone {
    template_uuid = data.vsphere_virtual_machine.ubuntu_template.id
    
    customize {
      linux_options {
        host_name = "so-manager"
        domain    = "goad-blue.local"
      }
      
      network_interface {
        ipv4_address = "**************"
        ipv4_netmask = 26
      }
      
      ipv4_gateway = "**************"
    }
  }
}
```

### **Variables Configuration**

```hcl
# terraform/providers/vmware/variables.tf

variable "vsphere_user" {
  description = "vSphere username"
  type        = string
  sensitive   = true
}

variable "vsphere_password" {
  description = "vSphere password"
  type        = string
  sensitive   = true
}

variable "vsphere_server" {
  description = "vSphere server hostname or IP"
  type        = string
}

variable "allow_unverified_ssl" {
  description = "Allow unverified SSL certificates"
  type        = bool
  default     = false
}

variable "datacenter_name" {
  description = "Name of the vSphere datacenter"
  type        = string
  default     = "GOAD-Blue-DC"
}

variable "cluster_name" {
  description = "Name of the vSphere cluster"
  type        = string
  default     = "GOAD-Blue-Cluster"
}

variable "datastore_name" {
  description = "Name of the datastore"
  type        = string
  default     = "GOAD-Blue-SSD"
}

variable "ubuntu_template_name" {
  description = "Name of the Ubuntu VM template"
  type        = string
  default     = "ubuntu-20.04-template"
}

variable "management_network" {
  description = "Management network name"
  type        = string
  default     = "GOAD-Blue-Management"
}

variable "siem_network" {
  description = "SIEM network name"
  type        = string
  default     = "GOAD-Blue-SIEM"
}
```

---

!!! tip "VMware Best Practices"
    - Use VM templates for consistent deployments
    - Configure resource pools for better resource management
    - Enable DRS and HA for production environments
    - Use distributed switches for advanced networking features

!!! warning "Resource Requirements"
    Ensure adequate CPU, memory, and storage resources are available. GOAD-Blue components can be resource-intensive, especially Security Onion and Splunk.

!!! info "Licensing"
    Verify that you have appropriate VMware licenses for your deployment. Some features require specific license levels.
