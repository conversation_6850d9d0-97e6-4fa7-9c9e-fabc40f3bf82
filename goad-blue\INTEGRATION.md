# GOAD-Blue Integration Guide

## Overview

GOAD-Blue is designed as a separate but integrated project that extends the original GOAD (Game of Active Directory) with comprehensive blue team capabilities. This document explains how GOAD-Blue integrates with the existing GOAD project.

## Project Structure

```
GOAD/                           # Original GOAD project
├── goad.py                     # Original GOAD CLI
├── goad/                       # GOAD Python package
├── ansible/                    # GOAD Ansible playbooks
├── ad/                         # GOAD lab configurations
└── ...

goad-blue/                      # GOAD-Blue project (this directory)
├── goad-blue.py               # GOAD-Blue CLI
├── goad-blue.bat              # Windows launcher
├── goad_blue/                 # GOAD-Blue Python package
├── ansible/                   # Blue team Ansible playbooks
├── terraform/                 # Infrastructure as Code
├── packer/                    # VM image templates
├── docs/                      # Documentation
└── scripts/                   # Installation scripts
```

## Integration Architecture

### 1. Seamless Coexistence

GOAD-Blue is designed to:
- **Detect existing GOAD installations** automatically
- **Integrate without modifying** original GOAD files
- **Share network resources** efficiently
- **Maintain separate management** interfaces

### 2. Network Integration

```
┌─────────────────────────────────────────────────────────────┐
│                    GOAD-Blue Network Layout                  │
├─────────────────────────────────────────────────────────────┤
│ Management Subnet (*************/26)                       │
│ ├── Splunk/Elastic SIEM                                     │
│ └── MISP Threat Intelligence                                │
├─────────────────────────────────────────────────────────────┤
│ Monitoring Subnet (192.168.100.64/26)                      │
│ ├── Security Onion                                          │
│ ├── Malcolm                                                 │
│ └── Velociraptor Server                                     │
├─────────────────────────────────────────────────────────────┤
│ Red Team Subnet (192.168.100.128/26)                       │
│ └── GOAD Environment (existing)                             │
├─────────────────────────────────────────────────────────────┤
│ Analysis Subnet (192.168.100.192/26)                       │
│ └── FLARE-VM                                                │
└─────────────────────────────────────────────────────────────┘
```

### 3. Data Flow Integration

```mermaid
graph TB
    subgraph "GOAD Environment"
        DC1[Domain Controller 1]
        DC2[Domain Controller 2]
        SRV1[Server 1]
        SRV2[Server 2]
    end
    
    subgraph "GOAD-Blue Monitoring"
        SIEM[SIEM Platform]
        SO[Security Onion]
        VELO[Velociraptor]
        MISP[MISP]
    end
    
    DC1 --> SIEM
    DC2 --> SIEM
    SRV1 --> SIEM
    SRV2 --> SIEM
    
    DC1 --> SO
    DC2 --> SO
    SRV1 --> SO
    SRV2 --> SO
    
    DC1 --> VELO
    DC2 --> VELO
    SRV1 --> VELO
    SRV2 --> VELO
    
    SIEM <--> MISP
    SO --> SIEM
    VELO --> SIEM
```

## Integration Process

### 1. Automatic Discovery

GOAD-Blue automatically discovers existing GOAD installations:

```python
def discover_goad_instances(self):
    """Discover existing GOAD instances"""
    workspace_path = self.goad_path / "workspace"
    
    for instance_dir in workspace_path.iterdir():
        instance_file = instance_dir / "instance.json"
        if instance_file.exists():
            # Parse GOAD instance configuration
            # Add to integration list
```

### 2. Agent Deployment

GOAD-Blue deploys monitoring agents to existing GOAD VMs:

**Windows Event Log Forwarding:**
- Configures Windows Event Forwarding (WEF)
- Deploys Splunk Universal Forwarders or Beats
- Installs and configures Sysmon

**Endpoint Monitoring:**
- Deploys Velociraptor agents
- Configures PowerShell logging
- Sets up process monitoring

### 3. Network Monitoring

**Traffic Mirroring:**
- Configures network taps or SPAN ports
- Routes GOAD traffic to Security Onion sensors
- Enables Malcolm for packet capture analysis

**DNS Monitoring:**
- Monitors DNS queries from GOAD environment
- Detects DNS tunneling and exfiltration
- Correlates with threat intelligence

### 4. SIEM Integration

**Log Aggregation:**
- Centralizes logs from all GOAD components
- Normalizes data using CIM (Splunk) or ECS (Elastic)
- Creates custom parsing rules for GOAD-specific events

**Correlation Rules:**
- Implements detection rules for common AD attacks
- Correlates events across multiple data sources
- Generates alerts for suspicious activities

## Usage Scenarios

### 1. Red Team vs Blue Team Exercise

**Red Team (GOAD):**
```bash
# Execute attacks using original GOAD
cd /path/to/GOAD
python3 goad.py
# Run attack scenarios
```

**Blue Team (GOAD-Blue):**
```bash
# Monitor and respond using GOAD-Blue
cd /path/to/GOAD/goad-blue
python3 goad-blue.py
# Analyze detections and respond
```

### 2. Training Environment

**Instructor Setup:**
1. Deploy GOAD environment for attack simulation
2. Deploy GOAD-Blue for monitoring and detection
3. Configure scenarios and training materials

**Student Experience:**
- Access both red team (GOAD) and blue team (GOAD-Blue) tools
- Practice attack techniques and detection methods
- Learn incident response procedures

### 3. Security Research

**Researchers can:**
- Test new attack techniques in GOAD
- Evaluate detection capabilities in GOAD-Blue
- Develop new security tools and techniques
- Publish findings and improvements

## Configuration Management

### 1. Separate Configuration Files

GOAD-Blue maintains its own configuration:

```yaml
# goad-blue-config.yml
goad_blue:
  name: "goad-blue-lab"
  provider: "vmware"

integration:
  goad_enabled: true
  auto_discover: true
  goad_path: "../"  # Path to GOAD installation
```

### 2. Shared Resources

Where appropriate, GOAD-Blue can share:
- Network infrastructure
- Storage resources
- Virtualization platforms
- Management interfaces

### 3. Independent Operation

GOAD-Blue can also operate independently:
- Standalone blue team training
- Security tool evaluation
- Compliance testing
- Research environments

## Deployment Options

### 1. Integrated Deployment

Deploy GOAD-Blue alongside existing GOAD:

```bash
# From GOAD project root
git clone <goad-blue-repo> goad-blue
cd goad-blue
./scripts/goad-blue-installer.sh
```

### 2. Separate Deployment

Deploy GOAD-Blue independently:

```bash
# Separate directory
git clone <goad-blue-repo>
cd goad-blue
# Configure for standalone operation
python3 goad-blue.py --configure
```

### 3. Cloud Deployment

Deploy in cloud environments:

```bash
# AWS deployment
python3 goad-blue.py -t install -p aws

# Azure deployment
python3 goad-blue.py -t install -p azure
```

## Maintenance and Updates

### 1. Independent Updates

- GOAD and GOAD-Blue can be updated independently
- Version compatibility is maintained
- Breaking changes are documented

### 2. Shared Dependencies

- Common tools (Ansible, Terraform) are shared
- Python dependencies are isolated
- Virtualization platforms are shared

### 3. Backup and Recovery

- Separate backup procedures for each project
- Shared infrastructure backup considerations
- Recovery procedures documented

## Troubleshooting Integration

### Common Issues

**GOAD Not Detected:**
- Verify GOAD installation path
- Check workspace directory structure
- Ensure proper permissions

**Network Connectivity:**
- Verify subnet configuration
- Check firewall rules
- Test inter-VM communication

**Agent Deployment Failures:**
- Check SSH/WinRM connectivity
- Verify credentials and permissions
- Review Ansible inventory

### Debug Commands

```bash
# Check GOAD integration status
python3 goad-blue.py --integrate-goad --debug

# Validate network connectivity
python3 goad-blue.py test_connectivity

# Check component status
python3 goad-blue.py -t status
```

## Future Enhancements

### Planned Features

1. **Deeper Integration:**
   - Shared attack/defense scenarios
   - Coordinated red/blue team exercises
   - Integrated reporting and analytics

2. **Enhanced Automation:**
   - Automated attack simulation
   - Dynamic response scenarios
   - Machine learning integration

3. **Extended Platform Support:**
   - Additional cloud providers
   - Container-based deployment
   - Kubernetes integration

### Community Contributions

- Integration improvements
- New detection rules
- Additional components
- Documentation enhancements

## Support and Resources

### Documentation
- [Installation Guide](docs/installation.md)
- [Configuration Reference](docs/configuration.md)
- [Use Cases & Scenarios](docs/use-cases.md)

### Community
- GitHub Issues for bug reports
- Discussions for feature requests
- Wiki for community contributions
- Regular community calls

### Professional Support
- Training and consulting services
- Custom deployment assistance
- Enterprise support options
