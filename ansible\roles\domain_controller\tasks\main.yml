# DNSCHANGE
# - name: "disable interface {{nat_adapter}} before join domain"
#   win_shell: netsh interface set interface "{{nat_adapter}}" disable

- name: "disable the registration of the {{nat_adapter}} interface (NAT address) in DNS"
  ansible.windows.win_shell:
    Get-NetAdapter {{nat_adapter}} | Set-DNSClient -RegisterThisConnectionsAddress $False
  when: two_adapters

- name: Ensure that domain exists
  win_domain:
    domain_netbios_name: "{{netbios_name}}"
    dns_domain_name: "{{domain}}"
    safe_mode_password: "{{domain_password}}"
  register: check_domain

- name: Reboot to complete domain creation
  win_reboot:
    reboot_timeout: 900
    post_reboot_delay: 300
  when: check_domain.changed

- name: Ensure the server is a domain controller
  win_domain_controller:
    dns_domain_name: "{{domain}}"
    domain_admin_user: "{{domain_username}}"
    domain_admin_password: "{{domain_password}}"
    safe_mode_password: "{{domain_password}}"
    state: domain_controller
  register: check_domain_controller

- name: Reboot to complete domain controller setup
  win_reboot:
    reboot_timeout: 900
    post_reboot_delay: 100
  when: check_domain_controller.changed

# DNSCHANGE
# - name: "enable interface {{nat_adapter}} after domain joined"
#   win_shell: netsh interface set interface "{{nat_adapter}}" enable
#   register: enable_interface_dc
#   until: "enable_interface_dc is not failed"
#   retries: 3
#   delay: 120

- name: Be sure DNS feature is installed
  win_feature:
    name: DNS
    state: present

- name: Check for xDnsServer Powershell module
  win_psmodule:
    name: xDnsServer
    state: present

- name: enable only the {{domain_adapter}} interface (local) for DNS client requests
  ansible.windows.win_shell: dnscmd . /resetlistenaddresses {{ hostvars[dict_key].ansible_host }}
  when: two_adapters

- name: Configure DNS Forwarders
  win_dsc:
    resource_name: xDnsServerForwarder
    IsSingleInstance: "yes"
    UseRootHint: false
    IPAddresses:
      - "{{dns_server_forwarder}}"

#- name: install nuget
#  win_shell: install-packageprovider -name nuget -force

- name: "Install XactiveDirectory"
  win_psmodule:
    name: ActiveDirectoryDSC
    state: present

- name: Ensure Administrator is part of Enterprise Admins
  win_domain_group_membership:
    name: "Enterprise Admins"
    members:
      - Administrator
    state: present

- name: Ensure Administrator is part of Domain Admins
  win_domain_group_membership:
    name: "Domain Admins"
    members:
      - Administrator
    state: present
