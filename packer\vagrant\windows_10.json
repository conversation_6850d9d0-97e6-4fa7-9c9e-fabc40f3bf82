{"builders": [{"boot_command": "", "boot_wait": "6m", "communicator": "winrm", "cpus": 2, "disk_adapter_type": "lsisas1068", "disk_size": "{{user `disk_size`}}", "disk_type_id": "{{user `disk_type_id`}}", "floppy_files": ["{{user `autounattend`}}", "./scripts/fixnetwork.ps1", "./scripts/disable-screensaver.ps1", "./scripts/disable-winrm.ps1", "./scripts/enable-winrm.ps1", "./scripts/microsoft-updates.bat", "./scripts/win-updates.ps1"], "guest_os_type": "windows9-64", "headless": "{{user `headless`}}", "iso_checksum": "{{user `iso_checksum`}}", "iso_url": "{{user `iso_url`}}", "memory": "{{user `memory`}}", "shutdown_command": "shutdown /s /t 10 /f /d p:4:1 /c \"Packer Shutdown\"", "type": "vmware-iso", "version": "{{user `vmx_version`}}", "vm_name": "{{user `vm_name`}}", "vmx_data": {"RemoteDisplay.vnc.enabled": "false", "RemoteDisplay.vnc.port": "5900"}, "vmx_remove_ethernet_interfaces": true, "vnc_port_max": 5980, "vnc_port_min": 5900, "winrm_password": "vagrant", "winrm_timeout": "{{user `winrm_timeout`}}", "winrm_username": "vagrant"}, {"boot_command": "", "boot_wait": "6m", "communicator": "winrm", "cpus": 2, "disk_size": "{{user `disk_size`}}", "floppy_files": ["{{user `autounattend`}}", "./scripts/fixnetwork.ps1", "./scripts/disable-screensaver.ps1", "./scripts/disable-winrm.ps1", "./scripts/enable-winrm.ps1", "./scripts/microsoft-updates.bat", "./scripts/win-updates.ps1"], "guest_additions_mode": "disable", "guest_os_type": "Windows10_64", "headless": "{{user `headless`}}", "iso_checksum": "{{user `iso_checksum`}}", "iso_url": "{{user `iso_url`}}", "memory": "{{user `memory`}}", "shutdown_command": "shutdown /s /t 10 /f /d p:4:1 /c \"Packer Shutdown\"", "type": "virtualbox-iso", "vm_name": "{{user `vm_name`}}", "winrm_password": "vagrant", "winrm_timeout": "{{user `winrm_timeout`}}", "winrm_username": "vagrant"}], "post-processors": [{"keep_input_artifact": false, "output": "windows_10_{{.Provider}}.box", "type": "vagrant", "vagrantfile_template": "vagrantfile-windows_10.template"}], "provisioners": [{"execute_command": "{{ .Vars }} cmd /c \"{{ .Path }}\"", "remote_path": "/tmp/script.bat", "scripts": ["./scripts/enable-rdp.bat"], "type": "windows-shell"}, {"scripts": ["./scripts/vm-guest-tools.ps1"], "type": "powershell"}, {"restart_timeout": "{{user `restart_timeout`}}", "type": "windows-restart"}, {"scripts": ["./scripts/set-powerplan.ps1"], "type": "powershell"}, {"execute_command": "{{ .Vars }} cmd /c \"{{ .Path }}\"", "remote_path": "/tmp/script.bat", "scripts": ["./scripts/set-winrm-automatic.bat", "./scripts/uac-enable.bat", "./scripts/compile-dotnet-assemblies.bat", "./scripts/dis-updates.bat"], "type": "windows-shell"}], "variables": {"autounattend": "./answer_files/10/Autounattend.xml", "disk_size": "61440", "disk_type_id": "1", "memory": "2048", "headless": "false", "iso_checksum": "sha256:ef7312733a9f5d7d51cfa04ac497671995674ca5e1058d5164d6028f0938d668", "iso_url": "https://software-static.download.prss.microsoft.com/dbazure/988969d5-f34g-4e03-ac9d-1f9786c66750/19045.2006.220908-0225.22h2_release_svc_refresh_CLIENTENTERPRISEEVAL_OEMRET_x64FRE_en-us.iso", "restart_timeout": "5m", "vhv_enable": "false", "virtio_win_iso": "~/virtio-win.iso", "vm_name": "windows_10", "winrm_timeout": "6h", "vmx_version": "14"}}