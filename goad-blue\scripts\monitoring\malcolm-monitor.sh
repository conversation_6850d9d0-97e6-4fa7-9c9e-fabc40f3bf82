#!/bin/bash
# Enhanced Malcolm Monitoring Script for GOAD-Blue
# Provides comprehensive monitoring for Malcolm network analysis platform

set -e

# Configuration
MALCOLM_HOME="/opt/malcolm"
MALCOLM_USER="malcolm"
MALCOLM_WEB_URL="https://localhost"
ELASTICSEARCH_URL="http://localhost:9200"
KIBANA_URL="http://localhost:5601"
LOG_FILE="/var/log/goad-blue/malcolm-monitor.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "OK")
            echo -e "${GREEN}[OK]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
    esac
}

# Check <PERSON> installation
check_installation() {
    echo "=== Malcolm Installation Check ==="
    
    if [ -d "$MALCOLM_HOME" ]; then
        print_status "OK" "Malcolm home directory exists: $MALCOLM_HOME"
        
        if [ -f "$MALCOLM_HOME/docker-compose.yml" ]; then
            print_status "OK" "Docker Compose configuration found"
        else
            print_status "ERROR" "Docker Compose configuration not found"
            return 1
        fi
    else
        print_status "ERROR" "Malcolm home directory not found"
        return 1
    fi
    
    echo ""
}

# Check Docker and containers
check_docker() {
    echo "=== Malcolm Docker Status ==="
    
    if ! command -v docker >/dev/null 2>&1; then
        print_status "ERROR" "Docker not installed"
        return 1
    fi
    
    if ! systemctl is-active --quiet docker; then
        print_status "ERROR" "Docker service not running"
        return 1
    fi
    
    print_status "OK" "Docker service is running"
    
    # Check Malcolm containers
    cd "$MALCOLM_HOME" 2>/dev/null || return 1
    
    local containers=$(docker-compose ps --services 2>/dev/null)
    local running_containers=$(docker-compose ps --services --filter "status=running" 2>/dev/null | wc -l)
    local total_containers=$(echo "$containers" | wc -l)
    
    if [ "$running_containers" -eq "$total_containers" ] && [ "$total_containers" -gt 0 ]; then
        print_status "OK" "All Malcolm containers running ($running_containers/$total_containers)"
    elif [ "$running_containers" -gt 0 ]; then
        print_status "WARNING" "Some Malcolm containers running ($running_containers/$total_containers)"
    else
        print_status "ERROR" "No Malcolm containers running"
        return 1
    fi
    
    # Show container status
    echo "Container status:"
    docker-compose ps 2>/dev/null | grep -E "(Name|---)" -A 20 || echo "Unable to get container status"
    
    echo ""
}

# Check network connectivity
check_network() {
    echo "=== Malcolm Network Connectivity ==="
    
    # Check Malcolm web interface
    if curl -k -s --connect-timeout 10 "$MALCOLM_WEB_URL" >/dev/null; then
        print_status "OK" "Malcolm web interface accessible ($MALCOLM_WEB_URL)"
    else
        print_status "ERROR" "Malcolm web interface not accessible"
    fi
    
    # Check Elasticsearch
    if curl -s --connect-timeout 10 "$ELASTICSEARCH_URL" >/dev/null; then
        print_status "OK" "Elasticsearch accessible ($ELASTICSEARCH_URL)"
        
        # Get cluster health
        local es_health=$(curl -s "$ELASTICSEARCH_URL/_cluster/health" | jq -r '.status' 2>/dev/null || echo "unknown")
        case $es_health in
            "green")
                print_status "OK" "Elasticsearch cluster health: $es_health"
                ;;
            "yellow")
                print_status "WARNING" "Elasticsearch cluster health: $es_health"
                ;;
            "red")
                print_status "ERROR" "Elasticsearch cluster health: $es_health"
                ;;
            *)
                print_status "WARNING" "Elasticsearch cluster health: unknown"
                ;;
        esac
    else
        print_status "ERROR" "Elasticsearch not accessible"
    fi
    
    # Check Kibana
    if curl -s --connect-timeout 10 "$KIBANA_URL/api/status" >/dev/null; then
        print_status "OK" "Kibana accessible ($KIBANA_URL)"
    else
        print_status "ERROR" "Kibana not accessible"
    fi
    
    echo ""
}

# Check disk usage
check_disk_usage() {
    echo "=== Malcolm Disk Usage ==="
    
    if [ -d "$MALCOLM_HOME" ]; then
        local malcolm_size=$(du -sh "$MALCOLM_HOME" 2>/dev/null | cut -f1)
        print_status "INFO" "Malcolm directory size: $malcolm_size"
        
        # Check specific directories
        local dirs=("elasticsearch" "pcap" "suricata-logs" "zeek-logs")
        for dir in "${dirs[@]}"; do
            if [ -d "$MALCOLM_HOME/$dir" ]; then
                local dir_size=$(du -sh "$MALCOLM_HOME/$dir" 2>/dev/null | cut -f1)
                print_status "INFO" "$dir directory size: $dir_size"
            fi
        done
        
        # Check available space
        local available_space=$(df -h "$MALCOLM_HOME" | tail -1 | awk '{print $4}')
        local usage_percent=$(df -h "$MALCOLM_HOME" | tail -1 | awk '{print $5}' | sed 's/%//')
        
        if [ "$usage_percent" -lt 80 ]; then
            print_status "OK" "Disk usage: ${usage_percent}% (${available_space} available)"
        elif [ "$usage_percent" -lt 90 ]; then
            print_status "WARNING" "Disk usage: ${usage_percent}% (${available_space} available)"
        else
            print_status "ERROR" "Disk usage: ${usage_percent}% - Critical!"
        fi
    fi
    
    echo ""
}

# Check Elasticsearch indices
check_elasticsearch_indices() {
    echo "=== Elasticsearch Indices ==="
    
    local indices_response
    indices_response=$(curl -s "$ELASTICSEARCH_URL/_cat/indices?v&h=index,docs.count,store.size" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$indices_response" ]; then
        print_status "OK" "Elasticsearch indices retrieved"
        echo "$indices_response" | head -10
        
        # Count total documents
        local total_docs=$(curl -s "$ELASTICSEARCH_URL/_cat/indices?h=docs.count" 2>/dev/null | awk '{sum+=$1} END {print sum}')
        print_status "INFO" "Total documents indexed: ${total_docs:-0}"
    else
        print_status "ERROR" "Unable to retrieve Elasticsearch indices"
    fi
    
    echo ""
}

# Check PCAP processing
check_pcap_processing() {
    echo "=== PCAP Processing Status ==="
    
    # Check PCAP directory
    if [ -d "$MALCOLM_HOME/pcap" ]; then
        local pcap_count=$(find "$MALCOLM_HOME/pcap" -name "*.pcap*" 2>/dev/null | wc -l)
        local pcap_size=$(du -sh "$MALCOLM_HOME/pcap" 2>/dev/null | cut -f1)
        
        print_status "INFO" "PCAP files: $pcap_count (total size: $pcap_size)"
        
        # Check for recent PCAP files
        local recent_pcaps=$(find "$MALCOLM_HOME/pcap" -name "*.pcap*" -mtime -1 2>/dev/null | wc -l)
        if [ "$recent_pcaps" -gt 0 ]; then
            print_status "OK" "Recent PCAP files found: $recent_pcaps"
        else
            print_status "INFO" "No recent PCAP files (last 24 hours)"
        fi
    else
        print_status "WARNING" "PCAP directory not found"
    fi
    
    # Check Zeek logs
    if [ -d "$MALCOLM_HOME/zeek-logs" ]; then
        local zeek_logs=$(find "$MALCOLM_HOME/zeek-logs" -name "*.log" -mtime -1 2>/dev/null | wc -l)
        if [ "$zeek_logs" -gt 0 ]; then
            print_status "OK" "Recent Zeek logs found: $zeek_logs"
        else
            print_status "INFO" "No recent Zeek logs"
        fi
    fi
    
    # Check Suricata logs
    if [ -d "$MALCOLM_HOME/suricata-logs" ]; then
        local suricata_logs=$(find "$MALCOLM_HOME/suricata-logs" -name "*.json" -mtime -1 2>/dev/null | wc -l)
        if [ "$suricata_logs" -gt 0 ]; then
            print_status "OK" "Recent Suricata logs found: $suricata_logs"
        else
            print_status "INFO" "No recent Suricata logs"
        fi
    fi
    
    echo ""
}

# Check container resource usage
check_container_resources() {
    echo "=== Container Resource Usage ==="
    
    cd "$MALCOLM_HOME" 2>/dev/null || return 1
    
    # Get container stats
    local container_stats
    container_stats=$(docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$container_stats" ]; then
        print_status "OK" "Container resource usage:"
        echo "$container_stats" | grep -E "(CONTAINER|malcolm)" | head -10
    else
        print_status "WARNING" "Unable to retrieve container stats"
    fi
    
    echo ""
}

# Check log ingestion
check_log_ingestion() {
    echo "=== Log Ingestion Status ==="
    
    # Check recent document ingestion
    local recent_docs_query='{"query":{"range":{"@timestamp":{"gte":"now-1h"}}}}'
    local recent_docs
    recent_docs=$(curl -s -X GET "$ELASTICSEARCH_URL/_search" \
        -H "Content-Type: application/json" \
        -d "$recent_docs_query" 2>/dev/null | jq '.hits.total.value' 2>/dev/null)
    
    if [ -n "$recent_docs" ] && [ "$recent_docs" != "null" ]; then
        if [ "$recent_docs" -gt 0 ]; then
            print_status "OK" "Recent documents ingested (last hour): $recent_docs"
        else
            print_status "WARNING" "No recent documents ingested"
        fi
    else
        print_status "WARNING" "Unable to check recent document ingestion"
    fi
    
    echo ""
}

# Generate summary
generate_summary() {
    echo "=== Malcolm Health Summary ==="
    
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    print_status "INFO" "Report generated: $timestamp"
    
    # Calculate health score
    local health_score=100
    
    # Check Docker containers
    cd "$MALCOLM_HOME" 2>/dev/null || health_score=$((health_score - 30))
    local running_containers=$(docker-compose ps --services --filter "status=running" 2>/dev/null | wc -l || echo "0")
    local total_containers=$(docker-compose ps --services 2>/dev/null | wc -l || echo "1")
    
    if [ "$running_containers" -lt "$total_containers" ]; then
        health_score=$((health_score - 40))
    fi
    
    # Check web interface
    if ! curl -k -s --connect-timeout 5 "$MALCOLM_WEB_URL" >/dev/null; then
        health_score=$((health_score - 20))
    fi
    
    # Check Elasticsearch
    if ! curl -s --connect-timeout 5 "$ELASTICSEARCH_URL" >/dev/null; then
        health_score=$((health_score - 25))
    fi
    
    # Check disk usage
    local usage_percent=$(df -h "$MALCOLM_HOME" 2>/dev/null | tail -1 | awk '{print $5}' | sed 's/%//' || echo "0")
    if [ "$usage_percent" -gt 90 ]; then
        health_score=$((health_score - 15))
    elif [ "$usage_percent" -gt 80 ]; then
        health_score=$((health_score - 5))
    fi
    
    # Display health score
    if [ "$health_score" -ge 90 ]; then
        print_status "OK" "Overall health score: ${health_score}% - Excellent"
    elif [ "$health_score" -ge 70 ]; then
        print_status "WARNING" "Overall health score: ${health_score}% - Good"
    else
        print_status "ERROR" "Overall health score: ${health_score}% - Needs attention"
    fi
    
    echo ""
}

# Main function
main() {
    echo "========================================"
    echo "    GOAD-Blue Malcolm Monitor v1.0"
    echo "========================================"
    echo ""
    
    check_installation || exit 1
    check_docker || exit 1
    check_network
    check_disk_usage
    check_elasticsearch_indices
    check_pcap_processing
    check_container_resources
    check_log_ingestion
    generate_summary
    
    echo "========================================"
    echo "For detailed logs, check: $LOG_FILE"
    echo "========================================"
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "GOAD-Blue Malcolm Monitor"
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --quiet, -q    Quiet mode (minimal output)"
        echo ""
        exit 0
        ;;
    --quiet|-q)
        main > "$LOG_FILE" 2>&1
        echo "Monitoring completed. Check $LOG_FILE for details."
        ;;
    *)
        main
        ;;
esac
