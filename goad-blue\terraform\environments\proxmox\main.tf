# GOAD-Blue Proxmox Deployment
terraform {
  required_version = ">= 1.0"
  required_providers {
    proxmox = {
      source  = "telmate/proxmox"
      version = "2.9.14"
    }
  }
}

# Proxmox Provider Configuration
provider "proxmox" {
  pm_api_url      = var.proxmox_api_url
  pm_user         = var.proxmox_user
  pm_password     = var.proxmox_password
  pm_tls_insecure = var.proxmox_tls_insecure
  pm_parallel     = 2
  pm_timeout      = 600
}

# Local variables
locals {
  common_tags = "goad-blue,${var.environment}"
  ssh_keys    = file(var.ssh_public_key_path)
}

# Splunk SIEM Server
resource "proxmox_vm_qemu" "splunk_server" {
  count       = var.components.splunk.enabled ? 1 : 0
  name        = "${var.name_prefix}-splunk"
  target_node = var.proxmox_node
  
  # Template configuration
  clone      = var.templates.ubuntu
  full_clone = true
  
  # VM specifications
  cores   = var.components.splunk.cores
  sockets = 1
  memory  = var.components.splunk.memory
  
  # Storage configuration
  disk {
    slot     = 0
    size     = var.components.splunk.disk_size
    type     = "scsi"
    storage  = var.storage_pool
    iothread = 1
    ssd      = 1
  }
  
  # Network configuration
  network {
    model  = "virtio"
    bridge = var.network_bridge
  }
  
  # Cloud-init configuration
  os_type      = "cloud-init"
  ipconfig0    = "ip=${var.network.splunk_ip}/24,gw=${var.network.gateway}"
  nameserver   = var.network.nameserver
  searchdomain = var.network.search_domain
  
  # SSH configuration
  sshkeys = local.ssh_keys
  
  # VM settings
  agent                = 1
  define_connection_info = false
  
  tags = "${local.common_tags},siem,splunk"
  
  lifecycle {
    ignore_changes = [
      network,
      disk,
    ]
  }
}

# Security Onion Manager
resource "proxmox_vm_qemu" "security_onion_manager" {
  count       = var.components.security_onion.enabled ? 1 : 0
  name        = "${var.name_prefix}-so-manager"
  target_node = var.proxmox_node
  
  clone      = var.templates.ubuntu
  full_clone = true
  
  cores   = var.components.security_onion.manager_cores
  sockets = 1
  memory  = var.components.security_onion.manager_memory
  
  disk {
    slot     = 0
    size     = var.components.security_onion.manager_disk_size
    type     = "scsi"
    storage  = var.storage_pool
    iothread = 1
    ssd      = 1
  }
  
  # Management network
  network {
    model  = "virtio"
    bridge = var.network_bridge
  }
  
  # Monitoring network
  network {
    model  = "virtio"
    bridge = var.monitoring_bridge
  }
  
  os_type      = "cloud-init"
  ipconfig0    = "ip=${var.network.so_manager_ip}/24,gw=${var.network.gateway}"
  nameserver   = var.network.nameserver
  searchdomain = var.network.search_domain
  
  sshkeys = local.ssh_keys
  agent   = 1
  
  tags = "${local.common_tags},monitoring,security-onion,manager"
}

# Security Onion Sensors
resource "proxmox_vm_qemu" "security_onion_sensors" {
  count       = var.components.security_onion.enabled ? var.components.security_onion.sensor_count : 0
  name        = "${var.name_prefix}-so-sensor-${count.index + 1}"
  target_node = var.proxmox_node
  
  clone      = var.templates.ubuntu
  full_clone = true
  
  cores   = var.components.security_onion.sensor_cores
  sockets = 1
  memory  = var.components.security_onion.sensor_memory
  
  disk {
    slot     = 0
    size     = var.components.security_onion.sensor_disk_size
    type     = "scsi"
    storage  = var.storage_pool
    iothread = 1
    ssd      = 1
  }
  
  # Management network
  network {
    model  = "virtio"
    bridge = var.network_bridge
  }
  
  # Monitoring network
  network {
    model  = "virtio"
    bridge = var.monitoring_bridge
  }
  
  os_type   = "cloud-init"
  ipconfig0 = "ip=${cidrhost(var.network.sensor_subnet, count.index + 10)}/24,gw=${var.network.gateway}"
  nameserver = var.network.nameserver
  
  sshkeys = local.ssh_keys
  agent   = 1
  
  tags = "${local.common_tags},monitoring,security-onion,sensor"
}

# Velociraptor Server
resource "proxmox_vm_qemu" "velociraptor_server" {
  count       = var.components.velociraptor.enabled ? 1 : 0
  name        = "${var.name_prefix}-velociraptor"
  target_node = var.proxmox_node
  
  clone      = var.templates.ubuntu
  full_clone = true
  
  cores   = var.components.velociraptor.cores
  sockets = 1
  memory  = var.components.velociraptor.memory
  
  disk {
    slot     = 0
    size     = var.components.velociraptor.disk_size
    type     = "scsi"
    storage  = var.storage_pool
    iothread = 1
    ssd      = 1
  }
  
  network {
    model  = "virtio"
    bridge = var.network_bridge
  }
  
  os_type      = "cloud-init"
  ipconfig0    = "ip=${var.network.velociraptor_ip}/24,gw=${var.network.gateway}"
  nameserver   = var.network.nameserver
  searchdomain = var.network.search_domain
  
  sshkeys = local.ssh_keys
  agent   = 1
  
  tags = "${local.common_tags},endpoint,velociraptor"
}

# MISP Threat Intelligence Platform
resource "proxmox_vm_qemu" "misp_server" {
  count       = var.components.misp.enabled ? 1 : 0
  name        = "${var.name_prefix}-misp"
  target_node = var.proxmox_node
  
  clone      = var.templates.ubuntu
  full_clone = true
  
  cores   = var.components.misp.cores
  sockets = 1
  memory  = var.components.misp.memory
  
  disk {
    slot     = 0
    size     = var.components.misp.disk_size
    type     = "scsi"
    storage  = var.storage_pool
    iothread = 1
    ssd      = 1
  }
  
  network {
    model  = "virtio"
    bridge = var.network_bridge
  }
  
  os_type      = "cloud-init"
  ipconfig0    = "ip=${var.network.misp_ip}/24,gw=${var.network.gateway}"
  nameserver   = var.network.nameserver
  searchdomain = var.network.search_domain
  
  sshkeys = local.ssh_keys
  agent   = 1
  
  tags = "${local.common_tags},intelligence,misp"
}

# FLARE-VM (Windows Analysis VM)
resource "proxmox_vm_qemu" "flare_vm" {
  count       = var.components.flare_vm.enabled ? 1 : 0
  name        = "${var.name_prefix}-flare"
  target_node = var.proxmox_node
  
  clone      = var.templates.windows
  full_clone = true
  
  cores   = var.components.flare_vm.cores
  sockets = 1
  memory  = var.components.flare_vm.memory
  
  disk {
    slot     = 0
    size     = var.components.flare_vm.disk_size
    type     = "scsi"
    storage  = var.storage_pool
    iothread = 1
    ssd      = 1
  }
  
  # Isolated analysis network
  network {
    model  = "virtio"
    bridge = var.analysis_bridge
  }
  
  os_type   = "win10"
  ipconfig0 = "ip=${var.network.flare_ip}/24,gw=${var.network.analysis_gateway}"
  
  agent = 1
  
  tags = "${local.common_tags},analysis,flare-vm,windows"
}

# Output important information
output "vm_info" {
  description = "Information about created VMs"
  value = {
    splunk_server = var.components.splunk.enabled ? {
      name = proxmox_vm_qemu.splunk_server[0].name
      ip   = var.network.splunk_ip
      vmid = proxmox_vm_qemu.splunk_server[0].vmid
    } : null
    
    security_onion_manager = var.components.security_onion.enabled ? {
      name = proxmox_vm_qemu.security_onion_manager[0].name
      ip   = var.network.so_manager_ip
      vmid = proxmox_vm_qemu.security_onion_manager[0].vmid
    } : null
    
    velociraptor_server = var.components.velociraptor.enabled ? {
      name = proxmox_vm_qemu.velociraptor_server[0].name
      ip   = var.network.velociraptor_ip
      vmid = proxmox_vm_qemu.velociraptor_server[0].vmid
    } : null
    
    misp_server = var.components.misp.enabled ? {
      name = proxmox_vm_qemu.misp_server[0].name
      ip   = var.network.misp_ip
      vmid = proxmox_vm_qemu.misp_server[0].vmid
    } : null
  }
}

output "access_urls" {
  description = "Access URLs for GOAD-Blue components"
  value = {
    splunk_web        = var.components.splunk.enabled ? "https://${var.network.splunk_ip}:8000" : null
    security_onion    = var.components.security_onion.enabled ? "https://${var.network.so_manager_ip}" : null
    velociraptor      = var.components.velociraptor.enabled ? "https://${var.network.velociraptor_ip}:8889" : null
    misp              = var.components.misp.enabled ? "https://${var.network.misp_ip}" : null
  }
}
