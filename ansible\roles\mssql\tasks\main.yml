- name: Reboot before install (long timeout in case of update)
  win_reboot:
    reboot_timeout: 1200

- name: Set download_url
  set_fact:
    download_url: "{{ download_url_2022 if sql_version == 'MSSQL_2022' else download_url_2019}}"

- name: Set connection method
  set_fact:
    connection_type: "{{ connection_type_2022 if sql_version == 'MSSQL_2022' else connection_type_2019}}"

- name: Set mssql_service_instance
  set_fact:
    mssql_service_instance: "MSSQL${{ sql_instance_name }}"

- name: Set mssql_service_name
  set_fact:
    mssql_service_name: "{{ mssql_service_instance if sql_instance_name != 'MSSQLSERVER' else 'MSSQLSERVER' }}"

- name: Display mssql variables in use
  ansible.builtin.debug:
    msg: 
    - "MSSQL version        : {{ sql_version }}"
    - "MSSQL service name   : {{ mssql_service_name }}"
    - "MSSQL download url   : {{ download_url }}"
    - "MSSQL instance       : {{ sql_instance_name }}"
    - "MSSQL connection use : {{ connection_type }}"

- name: create a directory for installer download
  win_file: 
    path: c:\setup
    state: directory

- name: create a directory for installer extraction
  win_file: 
    path: c:\setup\mssql
    state: directory

- name: create a directory for media extraction
  win_file: 
    path: c:\setup\mssql\media
    state: directory

- name: create the configuration file
  win_template: 
    src: files/sql_conf.ini.{{sql_version}}.j2
    dest: c:\setup\mssql\sql_conf.ini

- name: check downloaded file exists
  win_stat:
    path: c:\setup\mssql\sql_installer.exe
  register: installer_file

- name: get the installer
  win_get_url:
      url: "{{download_url}}"
      dest: 'c:\setup\mssql\sql_installer.exe'
  when: not installer_file.stat.exists

- name: Add service account to Log on as a service
  win_user_right:
    name: SeServiceLogonRight
    users:
    - '{{ SQLSVCACCOUNT }}'
    action: add
  when: not SQLSVCACCOUNT == "NT AUTHORITY\\NETWORK SERVICE"

# - name: Setup service account
# Set-ADUser -Identity "{{SQLSVCACCOUNT}}" -ServicePrincipalNames @{Add='MSSQLSvc/castelblack.north.sevenkingdoms.local'}
# Get-ADUser -Identity "{{SQLSVCACCOUNT}}" | Set-ADAccountControl -TrustedToAuthForDelegation $true
# Set-ADUser -Identity "{{SQLSVCACCOUNT}}" -Add @{'msDS-AllowedToDelegateTo'=@('CIFS/winterfell.north.sevenkingdoms.local','CIFS/winterfell')}

# - name: check install already done
#   win_stat:
#     path: "C:\\Program Files\\Microsoft SQL Server\\MSSQL15.{{sql_instance_name}}"
#   register: mssql_install_already_done

- name: check MSSQL service already exist (if failed service do not exist, launch install)
  win_service:
    name: '{{mssql_service_name}}'
  register: mssql_install_already_done
  failed_when: mssql_install_already_done is not defined
  ignore_errors: yes

- debug: msg="{{mssql_install_already_done}}"

# Install the database with a domain admin user
- name: Install the database
  win_command: c:\setup\mssql\sql_installer.exe /configurationfile=c:\setup\mssql\sql_conf.ini /IACCEPTSQLSERVERLICENSETERMS /MEDIAPATH=c:\setup\mssql\media /QUIET /HIDEPROGRESSBAR
  args:
    chdir: c:\setup
  vars:
    ansible_become: yes
    ansible_become_method: runas
    ansible_become_user: "{{domain_admin}}"
    ansible_become_password: "{{domain_admin_password}}"
    ansible_become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  register: mssqlinstall
  until: "mssqlinstall is not failed"
  retries: 3
  delay: 120
  when: mssql_install_already_done.state is not defined or mssql_install_already_done.name is not defined
#  when: not mssql_install_already_done.stat.exists

# sql server 2022
- name: Add or update registry for ip port
  win_regedit:
    path: 'HKLM:\Software\Microsoft\Microsoft SQL Server\MSSQL16.{{ sql_instance_name }}\MSSQLServer\SuperSocketNetLib\Tcp\IPAll'
    name: TcpPort
    data: 1433
  register: win_reg
  when: sql_version == "MSSQL_2022"

# sql server 2019
- name: Add or update registry for ip port
  win_regedit:
    path: 'HKLM:\Software\Microsoft\Microsoft SQL Server\MSSQL15.{{ sql_instance_name }}\MSSQLServer\SuperSocketNetLib\Tcp\IPAll'
    name: TcpPort
    data: 1433
  register: win_reg
  when: sql_version == "MSSQL_2019"

- name: Reboot
  win_reboot:
  when: win_reg.changed

- name: Firewall | Allow MSSQL through Firewall
  win_dsc:
    resource_name: xFirewall
    Name: "Access for MSSQL (TCP-In)"
    Ensure: present
    Enabled: True
    Profile: "Domain"
    Direction: "Inbound"
    Localport: "1433"
    Protocol: "TCP"
    Description: "Opens the listener port for MSSQL"

- name: Firewall | Allow MSSQL discover through Firewall
  win_dsc:
    resource_name: xFirewall
    Name: "Access for MSSQL (UDP-In)"
    Ensure: present
    Enabled: True
    Profile: "Domain"
    Direction: "Inbound"
    Localport: "1434"
    Protocol: "UDP"
    Description: "Opens the discover port for MSSQL"

- name: Be sure service is started
  win_service:
    name: '{{mssql_service_name}}'
    force_dependent_services: yes
    state: started

- name: Wait for port 1433 to become open on the host, start checking every 5 seconds
  ansible.windows.win_wait_for:
    port: 1433
    delay: 5

- name: Add MSSQL admin
  win_shell: |
    SqlCmd {{connection_type}} -Q "CREATE LOGIN [{{item}}] FROM WINDOWS"
    SqlCmd {{connection_type}} -Q "SP_ADDSRVROLEMEMBER '{{item}}','SYSADMIN'"
  become: yes
  become_method: runas
  become_user: "{{SQLSVCACCOUNT}}"
  vars:
    ansible_become_pass: "{{SQLSVCPASSWORD}}"
  loop: "{{sql_sysadmins}}"

- name: Add IMPERSONATE on login
  win_shell: |
    SqlCmd {{connection_type}} -Q "CREATE LOGIN [{{item.key}}] FROM WINDOWS"
    SqlCmd {{connection_type}} -Q "GRANT IMPERSONATE ON LOGIN::[{{item.value}}] TO [{{item.key}}]"
  become: yes
  become_method: runas
  become_user: "{{SQLSVCACCOUNT}}"
  vars:
    ansible_become_pass: "{{SQLSVCPASSWORD}}"
  with_dict: "{{executeaslogin}}"

- name: Add IMPERSONATE on user
  win_shell: |
    SqlCmd {{connection_type}} -Q "CREATE LOGIN [{{item.key}}] FROM WINDOWS"
    SqlCmd {{connection_type}} -Q "USE {{item.value.db}};CREATE USER [{{item.value.user}}] FOR LOGIN [{{item.value.user}}]"
    SqlCmd {{connection_type}} -Q "USE {{item.value.db}};GRANT IMPERSONATE ON USER::[{{item.value.impersonate}}] TO [{{item.value.user}}]"
  become: yes
  become_method: runas
  become_user: "{{SQLSVCACCOUNT}}"
  vars:
    ansible_become_pass: "{{SQLSVCPASSWORD}}"
  with_dict: "{{executeasuser}}"

- name: Enable sa account
  win_shell: |
    SqlCmd {{connection_type}} -Q "ALTER LOGIN sa ENABLE"
    SqlCmd {{connection_type}} -Q "ALTER LOGIN sa WITH PASSWORD = '{{sa_password}}' , CHECK_POLICY=OFF"
  become: yes
  become_method: runas
  become_user: "{{SQLSVCACCOUNT}}"
  vars:
    ansible_become_pass: "{{SQLSVCPASSWORD}}"

- name: enable MSSQL authentication and windows authent
  win_shell: |
    SqlCmd {{connection_type}} -Q "EXEC xp_instance_regwrite N'HKEY_LOCAL_MACHINE', N'Software\Microsoft\MSSQLServer\MSSQLServer', N'LoginMode', REG_DWORD, 2"
  become: yes
  become_method: runas
  become_user: "{{SQLSVCACCOUNT}}"
  vars:
    ansible_become_pass: "{{SQLSVCPASSWORD}}"

- name: Restart service MSSQL
  win_service:
    name: '{{mssql_service_name}}'
    force_dependent_services: yes
    state: restarted
