[all:vars]
; domain_name : folder inside ad/
domain_name=NHA
; administrator user
admin_user=administrator

;force_dns_server
force_dns_server=no
dns_server=1.1.1.1

;dns server forwarder
dns_server_forwarder=1.1.1.1

; winrm connection (windows)
ansible_user=vagrant
ansible_password=vagrant
ansible_connection=winrm
ansible_winrm_server_cert_validation=ignore
ansible_winrm_operation_timeout_sec=400
ansible_winrm_read_timeout_sec=500

; LAB SCENARIO CONFIGURATION -----------------------------

; computers inside domain (mandatory)
; usage : build.yml, ad-relations.yml, ad-servers.yml, vulnerabilities.yml
[domain]
dc01
dc02
srv01
srv02
srv03

; domain controler (mandatory)
; usage : ad-acl.yml, ad-data.yml, ad-relations.yml, laps.yml
[dc]
dc01
dc02

; domain server to enroll (mandatory if you want servers)
; usage : ad-data.yml, ad-servers.yml, laps.yml
[server]
srv01
srv02
srv03

; workstation to enroll (mandatory if you want workstation)
; usage : ad-servers.yml, laps.yml
[workstation]

; parent domain controler (mandatory)
; usage : ad-servers.yml
[parent_dc]
dc01
dc02

; child domain controler (need a fqdn child_name.parent_name)
; usage : ad-servers.yml
[child_dc]
; external trust, need domain trust key in config (bidirectionnal)
; usage : ad-trusts.yml
[trust]
dc01
dc02

; install adcs
; usage : adcs.yml
[adcs]
dc01

; install custom template (dc)
; usage : adcs.yml
[adcs_customtemplates]
;dc01

; install iis with default website asp upload on 80
; usage : servers.yml
[iis]
srv01

; install mssql
; usage : servers.yml
[mssql]
srv02

; install mssql gui
; usage : servers.yml
[mssql_ssms]
srv02

; install webdav 
[webdav]
; install elk
; usage : elk.yml
[elk_server]
; add log agent for elk
; usage : elk.yml
[elk_log]

[laps_dc]
[laps_server]
[laps_workstation]

; allow computer update
; usage : update.yml
[update]
dc01
dc02
srv01
srv02
srv03

; disable update
; usage : update.yml
[no_update]
; allow defender (dc or server only)
; usage : security.yml
[defender_on]
dc01
dc02
srv01
srv02
srv03
; disable defender (dc or server only)
; usage : security.yml
[defender_off]
