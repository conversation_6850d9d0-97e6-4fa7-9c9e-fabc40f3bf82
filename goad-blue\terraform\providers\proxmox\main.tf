# GOAD-Blue Proxmox Provider Configuration
# This configuration deploys GOAD-Blue infrastructure on Proxmox VE

terraform {
  required_version = ">= 1.0"
  required_providers {
    proxmox = {
      source  = "telmate/proxmox"
      version = "~> 2.9"
    }
  }
  
  # Optional: Configure remote state backend
  # backend "s3" {
  #   bucket = "goad-blue-terraform-state"
  #   key    = "proxmox/terraform.tfstate"
  #   region = "us-east-1"
  # }
}

# Proxmox Provider Configuration
provider "proxmox" {
  pm_api_url      = var.proxmox_api_url
  pm_user         = var.proxmox_user
  pm_password     = var.proxmox_password
  pm_tls_insecure = var.proxmox_tls_insecure
  pm_parallel     = var.proxmox_parallel
  pm_timeout      = var.proxmox_timeout
  pm_debug        = var.proxmox_debug
}

# Local values for configuration
locals {
  # Common tags
  common_tags = {
    project     = var.project_name
    environment = var.environment
    managed_by  = "terraform"
    created_by  = var.created_by
    created_at  = timestamp()
  }
  
  # Network configuration
  network_config = {
    # Bridge configurations
    goad_blue_bridge = var.network_bridges.goad_blue
    goad_bridge      = var.network_bridges.goad
    internet_bridge  = var.network_bridges.internet
    
    # VLAN configurations
    goad_blue_vlan = var.network_vlans.goad_blue
    goad_vlan      = var.network_vlans.goad
    
    # Network CIDRs
    goad_blue_cidr        = var.network_cidrs.goad_blue
    goad_blue_cidr_suffix = split("/", var.network_cidrs.goad_blue)[1]
    goad_cidr             = var.network_cidrs.goad
    goad_cidr_suffix      = split("/", var.network_cidrs.goad)[1]
    
    # Gateway and DNS
    gateway     = var.network_gateway
    dns_servers = join(",", var.dns_servers)
    
    # Individual VM IP configurations
    splunk = {
      ip   = var.vm_ip_addresses.splunk
      cidr = split("/", var.network_cidrs.goad_blue)[1]
    }
    security_onion_manager = {
      ip         = var.vm_ip_addresses.security_onion_manager
      cidr       = split("/", var.network_cidrs.goad_blue)[1]
      monitor_ip = var.vm_ip_addresses.security_onion_manager_monitor
    }
    velociraptor = {
      ip   = var.vm_ip_addresses.velociraptor
      cidr = split("/", var.network_cidrs.goad_blue)[1]
    }
    misp = {
      ip   = var.vm_ip_addresses.misp
      cidr = split("/", var.network_cidrs.goad_blue)[1]
    }
  }
  
  # Storage configuration
  storage_config = {
    primary_storage = var.storage_pools.primary
    data_storage    = var.storage_pools.data
    disk_format     = var.disk_format
    ssd             = var.enable_ssd
  }
}

# GOAD-Blue Infrastructure Module
module "goad_blue" {
  source = "../../modules/proxmox"
  
  # Basic configuration
  name_prefix           = var.name_prefix
  target_node          = var.target_node
  ubuntu_template_name = var.ubuntu_template_name
  
  # VM authentication
  vm_user        = var.vm_user
  vm_password    = var.vm_password
  ssh_public_key = var.ssh_public_key
  
  # Component deployment flags
  deploy_splunk         = var.deploy_components.splunk
  deploy_security_onion = var.deploy_components.security_onion
  deploy_velociraptor   = var.deploy_components.velociraptor
  deploy_misp          = var.deploy_components.misp
  
  # Security Onion sensor count
  security_onion_sensor_count = var.security_onion_sensor_count
  
  # VM IDs
  vm_ids = var.vm_ids
  
  # VM resource configurations
  vm_configs = var.vm_configs
  
  # Network configuration
  network_config = local.network_config
  
  # Storage configuration
  storage_config = local.storage_config
  
  # Common tags
  common_tags = local.common_tags
  
  # Advanced configuration
  enable_ha         = var.enable_ha
  backup_schedule   = var.backup_schedule
  monitoring_config = var.monitoring_config
  security_config   = var.security_config
}

# Optional: Create additional resources
# DNS Records (if using external DNS)
# resource "dns_a_record_set" "splunk" {
#   count = var.deploy_components.splunk && var.create_dns_records ? 1 : 0
#   zone  = var.dns_zone
#   name  = "splunk"
#   addresses = [var.vm_ip_addresses.splunk]
#   ttl   = 300
# }

# Load Balancer (if using external load balancer)
# resource "proxmox_vm_qemu" "load_balancer" {
#   count = var.deploy_load_balancer ? 1 : 0
#   # Load balancer configuration
# }

# Monitoring VM (if deploying separate monitoring)
# resource "proxmox_vm_qemu" "monitoring" {
#   count = var.deploy_monitoring_vm ? 1 : 0
#   # Monitoring VM configuration
# }

# Backup Storage Configuration
resource "proxmox_storage" "backup_storage" {
  count = var.create_backup_storage ? 1 : 0
  
  storage_id = "goad-blue-backup"
  type       = "dir"
  path       = var.backup_storage_path
  content    = ["backup", "iso", "vztmpl"]
  nodes      = [var.target_node]
  
  # Optional: Enable shared storage
  shared = var.backup_storage_shared
}

# High Availability Configuration
resource "proxmox_ha_group" "goad_blue" {
  count = var.enable_ha ? 1 : 0
  
  group_name = "goad-blue-ha-group"
  nodes = {
    (var.target_node) = 1
    # Add additional nodes for HA
    # "pve-node-2" = 2
    # "pve-node-3" = 3
  }
  restricted   = false
  nofailback   = var.ha_nofailback
  comment      = "GOAD-Blue High Availability Group"
}

# HA Resources for critical VMs
resource "proxmox_ha_resource" "splunk" {
  count = var.enable_ha && var.deploy_components.splunk ? 1 : 0
  
  resource_id   = "vm:${module.goad_blue.splunk_vm_id}"
  state         = "started"
  group         = proxmox_ha_group.goad_blue[0].group_name
  max_restart   = var.ha_max_restart
  max_relocate  = var.ha_max_relocate
  comment       = "Splunk Enterprise HA Resource"
}

resource "proxmox_ha_resource" "security_onion_manager" {
  count = var.enable_ha && var.deploy_components.security_onion ? 1 : 0
  
  resource_id   = "vm:${module.goad_blue.security_onion_manager_vm_id}"
  state         = "started"
  group         = proxmox_ha_group.goad_blue[0].group_name
  max_restart   = var.ha_max_restart
  max_relocate  = var.ha_max_relocate
  comment       = "Security Onion Manager HA Resource"
}

# Optional: Firewall configuration can be managed separately
# Firewall rules are typically configured at the Proxmox cluster level
