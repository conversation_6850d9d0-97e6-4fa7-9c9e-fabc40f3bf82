---
# Load datas
- import_playbook: data.yml
  vars:
    data_path: "../ad/{{domain_name}}/data/"
  tags: 'data'

# set AD datas ==================================================================================================

- name: ACL inside AD
  hosts: dc
  roles:
  - { role: 'acl', tags: 'acl'}
  vars:
    ad_acls: "{{lab.domains[lab.hosts[dict_key].domain].acls | default({})}}"
    domain: "{{lab.hosts[dict_key].domain}}"
    domain_username: "{{domain}}\\{{admin_user}}"
    domain_password: "{{lab.domains[domain].domain_password}}"