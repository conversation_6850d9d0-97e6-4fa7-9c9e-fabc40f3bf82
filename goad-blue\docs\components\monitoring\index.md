# Network Monitoring

Network monitoring is a critical component of GOAD-Blue, providing real-time visibility into network traffic, detecting threats, and enabling rapid incident response. This section covers the comprehensive network security monitoring capabilities.

## 🎯 Overview

GOAD-<PERSON>'s network monitoring stack provides multi-layered visibility into network communications, enabling detection of advanced threats, lateral movement, and data exfiltration attempts.

```mermaid
graph TB
    subgraph "🌐 Network Monitoring Architecture"
        TAPS[📡 Network Taps<br/>Traffic Mirroring<br/>Passive Collection]
        SENSORS[🔍 Network Sensors<br/>Deep Packet Inspection<br/>Protocol Analysis]
        ANALYSIS[🧠 Analysis Engine<br/>Signature Detection<br/>Behavioral Analysis]
        STORAGE[💾 Data Storage<br/>PCAP Archive<br/>Metadata Indexing]
    end
    
    subgraph "🛡️ Detection Engines"
        SURICATA[🦅 Suricata<br/>IDS/IPS Engine<br/>Signature-based Detection]
        ZEEK[🐝 Zeek<br/>Network Analysis<br/>Protocol Parsing]
        STENOGRAPHER[📼 Stenographer<br/>Full Packet Capture<br/>Historical Analysis]
    end
    
    subgraph "📊 Visualization & Analysis"
        KIBANA[📈 Kibana<br/>Network Dashboards<br/>Traffic Analysis]
        GRAFANA[📊 Grafana<br/>Performance Metrics<br/>Health Monitoring]
        SQUERT[🔍 Squert<br/>Event Analysis<br/>Pivot Investigation]
    end
    
    subgraph "🎮 GOAD Network Traffic"
        GOAD_TRAFFIC[🏰 GOAD Traffic<br/>Domain Communications<br/>Attack Simulations]
        LATERAL[↔️ Lateral Movement<br/>SMB/RDP Traffic<br/>Credential Attacks]
        EXFIL[📤 Data Exfiltration<br/>DNS Tunneling<br/>Covert Channels]
    end
    
    GOAD_TRAFFIC --> TAPS
    LATERAL --> TAPS
    EXFIL --> TAPS
    
    TAPS --> SENSORS
    SENSORS --> SURICATA
    SENSORS --> ZEEK
    SENSORS --> STENOGRAPHER
    
    SURICATA --> ANALYSIS
    ZEEK --> ANALYSIS
    STENOGRAPHER --> STORAGE
    
    ANALYSIS --> KIBANA
    ANALYSIS --> GRAFANA
    ANALYSIS --> SQUERT
    
    classDef monitoring fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef detection fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef visualization fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef traffic fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class TAPS,SENSORS,ANALYSIS,STORAGE monitoring
    class SURICATA,ZEEK,STENOGRAPHER detection
    class KIBANA,GRAFANA,SQUERT visualization
    class GOAD_TRAFFIC,LATERAL,EXFIL traffic
```

## 🔧 Core Components

### **[Security Onion](security-onion.md)**
Comprehensive network security monitoring platform that integrates multiple open-source tools for threat detection and analysis.

**Key Features:**
- **Suricata IDS/IPS** - Signature-based threat detection
- **Zeek Network Analysis** - Protocol parsing and behavioral analysis
- **Elasticsearch** - Log storage and search capabilities
- **Kibana Dashboards** - Network traffic visualization
- **Full Packet Capture** - Complete network forensics

### **[Suricata](suricata.md)**
High-performance network IDS, IPS, and network security monitoring engine with multi-threading capabilities.

**Key Features:**
- **Real-time Intrusion Detection** - Signature-based threat detection
- **Protocol Analysis** - Deep packet inspection
- **File Extraction** - Automatic malware extraction
- **TLS/SSL Inspection** - Encrypted traffic analysis
- **Custom Rule Development** - Organization-specific detections

### **[Zeek](zeek.md)**
Powerful network analysis framework that provides comprehensive visibility into network communications and behaviors.

**Key Features:**
- **Protocol Parsing** - Comprehensive protocol support
- **Connection Logging** - Detailed network metadata
- **File Analysis** - Automatic file extraction and analysis
- **Custom Scripting** - Extensible detection capabilities
- **Intelligence Framework** - IOC matching and enrichment

## 📊 Monitoring Capabilities

### **Traffic Analysis**

```bash
# Real-time traffic monitoring
sudo tcpdump -i eth0 -w capture.pcap host ************/24

# Protocol distribution analysis
zeek-cut proto < conn.log | sort | uniq -c | sort -nr

# Top talkers identification
zeek-cut id.orig_h id.resp_h orig_bytes resp_bytes < conn.log | \
  awk '{print $1, $2, $3+$4}' | sort -k3 -nr | head -10

# DNS query analysis
zeek-cut query answers < dns.log | head -20

# HTTP request analysis
zeek-cut method host uri < http.log | head -20
```

### **Threat Detection**

```bash
# Suricata alert monitoring
tail -f /var/log/suricata/fast.log

# High-severity alerts
jq 'select(.alert.severity <= 2)' /var/log/suricata/eve.json

# Lateral movement detection
zeek-cut id.orig_h id.resp_h id.resp_p service < conn.log | \
  grep -E "(445|139|3389)" | sort | uniq -c | sort -nr

# Data exfiltration detection
zeek-cut id.orig_h id.resp_h orig_bytes < conn.log | \
  awk '$3 > 1000000 {print}' | sort -k3 -nr
```

### **Performance Monitoring**

```bash
# Sensor performance metrics
sudo so-status

# Packet drop monitoring
sudo ethtool -S eth0 | grep drop

# Disk usage monitoring
df -h /nsm

# Memory usage monitoring
free -h && ps aux --sort=-%mem | head -10
```

## 🚨 Alert Management

### **Alert Prioritization**

```yaml
# Alert severity mapping
alert_severity:
  critical:
    - malware_detected
    - lateral_movement
    - data_exfiltration
    - privilege_escalation
    
  high:
    - suspicious_dns
    - unusual_traffic_patterns
    - failed_authentication_spike
    - port_scanning
    
  medium:
    - policy_violations
    - unusual_protocols
    - geographic_anomalies
    - time_based_anomalies
    
  low:
    - informational_events
    - baseline_deviations
    - minor_policy_violations
```

### **Automated Response**

```python
# Automated alert response script
import json
import requests
from datetime import datetime

def process_suricata_alert(alert_data):
    """Process Suricata alerts and trigger responses"""
    
    severity = alert_data.get('alert', {}).get('severity', 3)
    signature = alert_data.get('alert', {}).get('signature', '')
    src_ip = alert_data.get('src_ip', '')
    dest_ip = alert_data.get('dest_ip', '')
    
    # Critical alerts require immediate action
    if severity <= 2:
        # Block source IP at firewall
        block_ip(src_ip)
        
        # Create incident ticket
        create_incident({
            'title': f'Critical Network Alert: {signature}',
            'description': f'High-severity alert from {src_ip} to {dest_ip}',
            'severity': 'critical',
            'source': 'Suricata'
        })
        
        # Notify SOC team
        notify_soc_team(alert_data)

def block_ip(ip_address):
    """Block IP address at network firewall"""
    firewall_api = "https://firewall.company.com/api/block"
    headers = {"Authorization": "Bearer YOUR_API_TOKEN"}
    
    payload = {
        "ip": ip_address,
        "action": "block",
        "duration": 3600,  # 1 hour
        "reason": "Automated block from GOAD-Blue"
    }
    
    response = requests.post(firewall_api, headers=headers, json=payload)
    return response.status_code == 200

def create_incident(incident_data):
    """Create incident in ticketing system"""
    ticket_api = "https://tickets.company.com/api/incidents"
    headers = {"Authorization": "Bearer YOUR_TICKET_TOKEN"}
    
    response = requests.post(ticket_api, headers=headers, json=incident_data)
    return response.json()
```

## 📈 Performance Optimization

### **Sensor Tuning**

```bash
# Suricata performance tuning
# Edit /etc/suricata/suricata.yaml

# Threading configuration
threading:
  set-cpu-affinity: yes
  cpu-affinity:
    - management-cpu-set:
        cpu: [ 0 ]
    - receive-cpu-set:
        cpu: [ 1, 2 ]
    - worker-cpu-set:
        cpu: [ 3, 4, 5, 6 ]

# AF_PACKET configuration
af-packet:
  - interface: eth1
    threads: 4
    cluster-id: 99
    cluster-type: cluster_flow
    defrag: yes
    use-mmap: yes
    tpacket-v3: yes
    ring-size: 2048
    block-size: 32768
```

### **Storage Optimization**

```bash
# Elasticsearch index optimization
curl -X PUT "localhost:9200/logstash-*/_settings" -H 'Content-Type: application/json' -d'
{
  "index": {
    "number_of_replicas": 0,
    "refresh_interval": "30s",
    "translog.flush_threshold_size": "1gb"
  }
}'

# PCAP storage management
# Compress old PCAP files
find /nsm/sensor_data/*/dailylogs/ -name "*.pcap" -mtime +7 -exec gzip {} \;

# Archive old logs
find /nsm/sensor_data/*/logs/ -name "*.log" -mtime +30 -exec tar -czf {}.tar.gz {} \; -delete
```

## 🔍 Investigation Workflows

### **Incident Response Workflow**

```mermaid
graph TD
    ALERT[🚨 Alert Triggered] --> TRIAGE[🔍 Initial Triage]
    TRIAGE --> VALIDATE[✅ Validate Alert]
    VALIDATE --> INVESTIGATE[🕵️ Deep Investigation]
    INVESTIGATE --> CONTAIN[🛡️ Containment]
    CONTAIN --> ERADICATE[🧹 Eradication]
    ERADICATE --> RECOVER[🔄 Recovery]
    RECOVER --> LESSONS[📚 Lessons Learned]
    
    VALIDATE --> FALSE_POSITIVE[❌ False Positive]
    FALSE_POSITIVE --> TUNE_RULES[⚙️ Tune Detection Rules]
    
    classDef process fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef action fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef outcome fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class ALERT,TRIAGE,VALIDATE,INVESTIGATE process
    class CONTAIN,ERADICATE,RECOVER action
    class FALSE_POSITIVE,TUNE_RULES,LESSONS outcome
```

### **Network Forensics**

```bash
# PCAP analysis for incident investigation
# Extract specific timeframe
tcpdump -r capture.pcap -w incident.pcap \
  'host ************* and (port 445 or port 3389)' \
  and 'greater 2023-01-01 10:00:00 and less 2023-01-01 11:00:00'

# Protocol-specific analysis
tshark -r incident.pcap -Y "smb2" -T fields \
  -e ip.src -e ip.dst -e smb2.cmd -e smb2.filename

# Extract files from PCAP
tcpflow -r incident.pcap -o extracted_files/

# DNS analysis
tshark -r incident.pcap -Y "dns" -T fields \
  -e ip.src -e dns.qry.name -e dns.resp.addr
```

## 🎓 Training Scenarios

### **Network Analysis Fundamentals**

1. **Traffic Baseline Establishment**
   - Normal network behavior identification
   - Protocol distribution analysis
   - Communication pattern mapping

2. **Threat Detection**
   - Signature-based detection tuning
   - Behavioral anomaly identification
   - False positive reduction

3. **Incident Investigation**
   - PCAP analysis techniques
   - Timeline reconstruction
   - Evidence collection and preservation

### **Advanced Network Security**

1. **Custom Rule Development**
   - Suricata rule writing
   - Zeek script development
   - Performance optimization

2. **Threat Hunting**
   - Proactive threat identification
   - IOC development and deployment
   - Advanced persistent threat detection

3. **Network Forensics**
   - Deep packet analysis
   - Malware communication analysis
   - Data exfiltration investigation

## 🔗 Integration Points

### **SIEM Integration**

```yaml
# Logstash configuration for network data
input {
  file {
    path => "/nsm/sensor_data/*/logs/suricata/eve.json"
    codec => "json"
    type => "suricata"
  }
  
  file {
    path => "/nsm/sensor_data/*/logs/zeek/*.log"
    type => "zeek"
  }
}

filter {
  if [type] == "suricata" {
    if [event_type] == "alert" {
      mutate {
        add_field => { "alert_signature" => "%{[alert][signature]}" }
        add_field => { "alert_severity" => "%{[alert][severity]}" }
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["goad-blue-elasticsearch:9200"]
    index => "goad-blue-network-%{+YYYY.MM.dd}"
  }
}
```

### **Threat Intelligence Integration**

```python
# MISP integration for IOC enrichment
from pymisp import PyMISP
import json

def enrich_with_misp(ip_address):
    """Enrich IP address with MISP threat intelligence"""
    misp = PyMISP('https://misp.goad-blue.local', 'YOUR_API_KEY')
    
    # Search for IP in MISP
    result = misp.search(value=ip_address, type_attribute='ip-dst')
    
    if result:
        return {
            'threat_level': result[0].get('threat_level_id'),
            'tags': [tag.name for tag in result[0].tags],
            'info': result[0].info
        }
    
    return None
```

## 📊 Metrics and KPIs

### **Network Security Metrics**

```yaml
# Key performance indicators
network_security_kpis:
  detection_metrics:
    - alert_volume_per_day
    - false_positive_rate
    - mean_time_to_detection
    - signature_coverage
    
  performance_metrics:
    - packet_drop_rate
    - sensor_cpu_utilization
    - storage_utilization
    - query_response_time
    
  operational_metrics:
    - incident_response_time
    - investigation_completion_rate
    - rule_tuning_frequency
    - analyst_productivity
```

---

!!! tip "Network Monitoring Best Practices"
    - Position sensors at critical network chokepoints
    - Implement layered detection with multiple engines
    - Regularly tune detection rules to reduce false positives
    - Maintain comprehensive packet capture for forensics
    - Monitor sensor performance and health continuously

!!! warning "Performance Considerations"
    Network monitoring can be resource-intensive. Ensure adequate CPU, memory, and storage for your traffic volume. Consider traffic sampling for high-bandwidth environments.

!!! info "Compliance Requirements"
    Network monitoring supports various compliance frameworks including PCI DSS, HIPAA, and SOX. Ensure proper data retention and access controls are implemented.
