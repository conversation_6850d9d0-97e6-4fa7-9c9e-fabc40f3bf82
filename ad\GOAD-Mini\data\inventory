[all:vars]
; domain_name : folder inside ad/
domain_name=GOAD-Mini
; administrator user
admin_user=administrator

force_dns_server=no
dns_server=x.x.x.x

;dns server forwarder
dns_server_forwarder=1.1.1.1

; winrm connection (windows)
ansible_user=vagrant
ansible_password=vagrant
ansible_connection=winrm
ansible_winrm_server_cert_validation=ignore
ansible_winrm_operation_timeout_sec=400
ansible_winrm_read_timeout_sec=500
# ansible_winrm_transport=basic
# ansible_port=5985

; proxy settings (the lab need internet for some install, if you are behind a proxy you should set the proxy here)
enable_http_proxy=no
ad_http_proxy=http://x.x.x.x:xxxx
ad_https_proxy=http://x.x.x.x:xxxx

; LAB SCENARIO CONFIGURATION -----------------------------

; computers inside domain (mandatory)
; usage : build.yml, ad-relations.yml, ad-servers.yml, vulnerabilities.yml
[domain]
dc01

; domain controler (mandatory)
; usage : ad-acl.yml, ad-data.yml, ad-relations.yml, laps.yml
[dc]
dc01

; domain server to enroll (mandatory if you want servers)
; usage : ad-data.yml, ad-servers.yml, laps.yml
[server]

; workstation to enroll (mandatory if you want workstation)
; usage : ad-servers.yml, laps.yml
[workstation]

; parent domain controler (mandatory)
; usage : ad-servers.yml
[parent_dc]
dc01

; child domain controler (need a fqdn child_name.parent_name)
; usage : ad-servers.yml
[child_dc]

; external trust, need domain trust entry in config (bidirectionnal)
; usage : ad-trusts.yml
[trust]

; install adcs
; usage : adcs.yml
[adcs]
dc01

; install custom template (dc)
; usage : adcs.yml
[adcs_customtemplates]

; install iis with default website asp upload on 80
; usage : servers.yml
[iis]

; install mssql
; usage : servers.yml
[mssql]

; install mssql gui
; usage : servers.yml
[mssql_ssms]

; install webdav 
[webdav]

; install elk
; usage : elk.yml
[elk_server]

; add log agent for elk
; usage : elk.yml
[elk_log]

[laps_dc]
[laps_server]
[laps_workstation]


; allow computer update
; usage : update.yml
[update]

; disable update
; usage : update.yml
[no_update]
dc01

; allow defender
; usage : security.yml
[defender_on]
dc01

; disable defender
; usage : security.yml
[defender_off]
