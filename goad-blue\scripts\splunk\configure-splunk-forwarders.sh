#!/bin/bash
# GOAD-<PERSON> Splunk Universal Forwarder Configuration Script
# Configures Splunk Universal Forwarders for GOAD environment integration

set -e

# Configuration
SPLUNK_FORWARDER_HOME="/opt/splunkforwarder"
SPLUNK_USER="splunk"
SPLUNK_INDEXER_HOST="${SPLUNK_INDEXER_HOST:-**************}"
SPLUNK_INDEXER_PORT="${SPLUNK_INDEXER_PORT:-9997}"
GOAD_BLUE_HOME="/opt/goad-blue"
LOG_FILE="/var/log/goad-blue/splunk-forwarder-config.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "OK")
            echo -e "${GREEN}[OK]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
    esac
}

# Install Splunk Universal Forwarder
install_universal_forwarder() {
    print_status "INFO" "Installing Splunk Universal Forwarder..."
    
    # Download and install UF
    local uf_version="9.1.2"
    local uf_build="b6b9c8185839"
    local uf_filename="splunkforwarder-${uf_version}-${uf_build}-linux-2.6-x86_64.tgz"
    local uf_url="https://download.splunk.com/products/universalforwarder/releases/${uf_version}/linux/${uf_filename}"
    
    cd /tmp
    if [ ! -f "$uf_filename" ]; then
        wget -O "$uf_filename" "$uf_url" || {
            print_status "ERROR" "Failed to download Universal Forwarder"
            exit 1
        }
    fi
    
    # Extract and install
    tar -xzf "$uf_filename" -C /opt/
    
    # Create splunk user if it doesn't exist
    if ! id "$SPLUNK_USER" &>/dev/null; then
        useradd -r -m -s /bin/bash -d $SPLUNK_FORWARDER_HOME $SPLUNK_USER
    fi
    
    chown -R $SPLUNK_USER:$SPLUNK_USER $SPLUNK_FORWARDER_HOME
    
    print_status "OK" "Universal Forwarder installed"
}

# Configure Universal Forwarder
configure_universal_forwarder() {
    print_status "INFO" "Configuring Universal Forwarder..."
    
    # Start UF for initial setup
    sudo -u $SPLUNK_USER $SPLUNK_FORWARDER_HOME/bin/splunk start --accept-license --answer-yes --no-prompt --seed-passwd changeme
    
    # Stop UF to configure
    sudo -u $SPLUNK_USER $SPLUNK_FORWARDER_HOME/bin/splunk stop
    
    # Configure outputs.conf
    cat > $SPLUNK_FORWARDER_HOME/etc/system/local/outputs.conf << EOF
[tcpout]
defaultGroup = goad_blue_indexers

[tcpout:goad_blue_indexers]
server = $SPLUNK_INDEXER_HOST:$SPLUNK_INDEXER_PORT
compressed = true

[tcpout-server://$SPLUNK_INDEXER_HOST:$SPLUNK_INDEXER_PORT]
EOF

    # Configure inputs.conf for GOAD-Blue
    cat > $SPLUNK_FORWARDER_HOME/etc/system/local/inputs.conf << 'EOF'
[default]
host = $decideOnStartup

# Windows Event Logs (for Windows systems)
[WinEventLog:Security]
disabled = false
index = goad_blue_windows

[WinEventLog:System]
disabled = false
index = goad_blue_windows

[WinEventLog:Application]
disabled = false
index = goad_blue_windows

[WinEventLog:Microsoft-Windows-Sysmon/Operational]
disabled = false
index = goad_blue_windows

[WinEventLog:Microsoft-Windows-PowerShell/Operational]
disabled = false
index = goad_blue_windows

# Linux System Logs
[monitor:///var/log/auth.log]
disabled = false
index = goad_blue_linux
sourcetype = linux_secure

[monitor:///var/log/secure]
disabled = false
index = goad_blue_linux
sourcetype = linux_secure

[monitor:///var/log/syslog]
disabled = false
index = goad_blue_linux
sourcetype = syslog

[monitor:///var/log/messages]
disabled = false
index = goad_blue_linux
sourcetype = syslog

# Linux Audit Logs
[monitor:///var/log/audit/audit.log]
disabled = false
index = goad_blue_linux
sourcetype = linux:audit

# Apache Logs
[monitor:///var/log/apache2/access.log]
disabled = false
index = goad_blue_linux
sourcetype = access_combined

[monitor:///var/log/apache2/error.log]
disabled = false
index = goad_blue_linux
sourcetype = apache_error

[monitor:///var/log/httpd/access_log]
disabled = false
index = goad_blue_linux
sourcetype = access_combined

[monitor:///var/log/httpd/error_log]
disabled = false
index = goad_blue_linux
sourcetype = apache_error

# Nginx Logs
[monitor:///var/log/nginx/access.log]
disabled = false
index = goad_blue_linux
sourcetype = nginx_access

[monitor:///var/log/nginx/error.log]
disabled = false
index = goad_blue_linux
sourcetype = nginx_error

# Suricata Logs
[monitor:///var/log/suricata/eve.json]
disabled = false
index = goad_blue_network
sourcetype = suricata:eve

[monitor:///var/log/suricata/fast.log]
disabled = false
index = goad_blue_network
sourcetype = suricata:alert

# Zeek Logs
[monitor:///opt/zeek/logs/current/*.log]
disabled = false
index = goad_blue_network
sourcetype = zeek

# GOAD-Blue specific logs
[monitor:///var/log/goad-blue/*.log]
disabled = false
index = goad_blue_main
sourcetype = goad_blue_logs

# Custom application logs
[monitor:///opt/goad/logs/*.log]
disabled = false
index = goad_blue_main
sourcetype = goad_logs
EOF

    # Set ownership
    chown -R $SPLUNK_USER:$SPLUNK_USER $SPLUNK_FORWARDER_HOME
    
    print_status "OK" "Universal Forwarder configured"
}

# Create Windows forwarder configuration
create_windows_forwarder_config() {
    print_status "INFO" "Creating Windows forwarder configuration..."
    
    mkdir -p "$GOAD_BLUE_HOME/configs/splunk/windows"
    
    # Windows inputs.conf
    cat > "$GOAD_BLUE_HOME/configs/splunk/windows/inputs.conf" << 'EOF'
[default]
host = $decideOnStartup

# Windows Event Logs
[WinEventLog:Security]
disabled = false
index = goad_blue_windows
renderXml = true

[WinEventLog:System]
disabled = false
index = goad_blue_windows
renderXml = true

[WinEventLog:Application]
disabled = false
index = goad_blue_windows
renderXml = true

[WinEventLog:Microsoft-Windows-Sysmon/Operational]
disabled = false
index = goad_blue_windows
renderXml = true

[WinEventLog:Microsoft-Windows-PowerShell/Operational]
disabled = false
index = goad_blue_windows
renderXml = true

[WinEventLog:Microsoft-Windows-WinRM/Operational]
disabled = false
index = goad_blue_windows
renderXml = true

[WinEventLog:Microsoft-Windows-TaskScheduler/Operational]
disabled = false
index = goad_blue_windows
renderXml = true

# Windows Performance Counters
[perfmon://Processor]
object = Processor
counters = % Processor Time
instances = *
interval = 60
index = goad_blue_windows
disabled = false

[perfmon://Memory]
object = Memory
counters = Available MBytes; Pages/sec
interval = 60
index = goad_blue_windows
disabled = false

[perfmon://LogicalDisk]
object = LogicalDisk
counters = % Free Space; Disk Transfers/sec
instances = *
interval = 60
index = goad_blue_windows
disabled = false

# Windows Registry Monitoring
[monitor://HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Run]
disabled = false
index = goad_blue_windows
sourcetype = WinRegistry

[monitor://HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services]
disabled = false
index = goad_blue_windows
sourcetype = WinRegistry

# File and Directory Monitoring
[monitor://C:\Windows\System32\drivers\etc\hosts]
disabled = false
index = goad_blue_windows
sourcetype = WinHostsFile

[monitor://C:\Windows\Temp\*]
disabled = false
index = goad_blue_windows
sourcetype = WinTempFiles
whitelist = \.exe$|\.dll$|\.bat$|\.cmd$|\.ps1$

[monitor://C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Startup\*]
disabled = false
index = goad_blue_windows
sourcetype = WinStartupFiles
EOF

    # Windows outputs.conf
    cat > "$GOAD_BLUE_HOME/configs/splunk/windows/outputs.conf" << EOF
[tcpout]
defaultGroup = goad_blue_indexers

[tcpout:goad_blue_indexers]
server = $SPLUNK_INDEXER_HOST:$SPLUNK_INDEXER_PORT
compressed = true

[tcpout-server://$SPLUNK_INDEXER_HOST:$SPLUNK_INDEXER_PORT]
EOF

    print_status "OK" "Windows forwarder configuration created"
}

# Create deployment scripts
create_deployment_scripts() {
    print_status "INFO" "Creating deployment scripts..."
    
    # Linux deployment script
    cat > "$GOAD_BLUE_HOME/scripts/deploy-linux-forwarder.sh" << 'EOF'
#!/bin/bash
# Deploy Splunk Universal Forwarder to Linux GOAD systems

SPLUNK_INDEXER_HOST="${1:-**************}"
SPLUNK_INDEXER_PORT="${2:-9997}"

echo "Deploying Splunk Universal Forwarder to Linux system..."
echo "Indexer: $SPLUNK_INDEXER_HOST:$SPLUNK_INDEXER_PORT"

# Download and install UF
cd /tmp
wget -O splunkforwarder.tgz "https://download.splunk.com/products/universalforwarder/releases/9.1.2/linux/splunkforwarder-9.1.2-b6b9c8185839-linux-2.6-x86_64.tgz"
tar -xzf splunkforwarder.tgz -C /opt/

# Create splunk user
useradd -r -m -s /bin/bash -d /opt/splunkforwarder splunk
chown -R splunk:splunk /opt/splunkforwarder

# Start and configure
sudo -u splunk /opt/splunkforwarder/bin/splunk start --accept-license --answer-yes --no-prompt --seed-passwd changeme
sudo -u splunk /opt/splunkforwarder/bin/splunk add forward-server $SPLUNK_INDEXER_HOST:$SPLUNK_INDEXER_PORT
sudo -u splunk /opt/splunkforwarder/bin/splunk add monitor /var/log/auth.log -index goad_blue_linux
sudo -u splunk /opt/splunkforwarder/bin/splunk add monitor /var/log/syslog -index goad_blue_linux
sudo -u splunk /opt/splunkforwarder/bin/splunk enable boot-start -user splunk

echo "Linux forwarder deployment completed"
EOF

    # Windows deployment script
    cat > "$GOAD_BLUE_HOME/scripts/deploy-windows-forwarder.ps1" << 'EOF'
# Deploy Splunk Universal Forwarder to Windows GOAD systems
param(
    [string]$IndexerHost = "**************",
    [string]$IndexerPort = "9997"
)

Write-Host "Deploying Splunk Universal Forwarder to Windows system..."
Write-Host "Indexer: $IndexerHost:$IndexerPort"

# Download UF
$ufUrl = "https://download.splunk.com/products/universalforwarder/releases/9.1.2/windows/splunkforwarder-9.1.2-b6b9c8185839-x64-release.msi"
$ufPath = "C:\temp\splunkforwarder.msi"

New-Item -ItemType Directory -Path "C:\temp" -Force
Invoke-WebRequest -Uri $ufUrl -OutFile $ufPath

# Install UF
Start-Process msiexec.exe -ArgumentList "/i $ufPath RECEIVING_INDEXER=$IndexerHost`:$IndexerPort LAUNCHSPLUNK=1 AGREETOLICENSE=Yes /quiet" -Wait

# Configure inputs
$inputsConf = @"
[WinEventLog:Security]
disabled = false
index = goad_blue_windows

[WinEventLog:System]
disabled = false
index = goad_blue_windows

[WinEventLog:Application]
disabled = false
index = goad_blue_windows

[WinEventLog:Microsoft-Windows-Sysmon/Operational]
disabled = false
index = goad_blue_windows

[WinEventLog:Microsoft-Windows-PowerShell/Operational]
disabled = false
index = goad_blue_windows
"@

$inputsConf | Out-File -FilePath "C:\Program Files\SplunkUniversalForwarder\etc\system\local\inputs.conf" -Encoding UTF8

# Restart service
Restart-Service SplunkForwarder

Write-Host "Windows forwarder deployment completed"
EOF

    chmod +x "$GOAD_BLUE_HOME/scripts/deploy-linux-forwarder.sh"
    
    print_status "OK" "Deployment scripts created"
}

# Enable boot start
enable_boot_start() {
    print_status "INFO" "Enabling Universal Forwarder boot start..."
    
    sudo -u $SPLUNK_USER $SPLUNK_FORWARDER_HOME/bin/splunk enable boot-start -user $SPLUNK_USER --accept-license --answer-yes --no-prompt
    
    print_status "OK" "Boot start enabled"
}

# Start Universal Forwarder
start_universal_forwarder() {
    print_status "INFO" "Starting Universal Forwarder..."
    
    systemctl start Splunkd
    systemctl enable Splunkd
    
    # Wait for UF to be ready
    local timeout=60
    local counter=0
    while ! sudo -u $SPLUNK_USER $SPLUNK_FORWARDER_HOME/bin/splunk status >/dev/null 2>&1; do
        if [ $counter -ge $timeout ]; then
            print_status "ERROR" "Timeout waiting for Universal Forwarder to start"
            exit 1
        fi
        sleep 5
        counter=$((counter + 5))
    done
    
    print_status "OK" "Universal Forwarder started successfully"
}

# Main function
main() {
    echo "========================================"
    echo "  GOAD-Blue Splunk Forwarder Setup"
    echo "========================================"
    echo ""
    
    # Check if UF is already installed
    if [ -d "$SPLUNK_FORWARDER_HOME" ]; then
        print_status "INFO" "Universal Forwarder already installed, configuring..."
        configure_universal_forwarder
    else
        install_universal_forwarder
        configure_universal_forwarder
        enable_boot_start
    fi
    
    create_windows_forwarder_config
    create_deployment_scripts
    start_universal_forwarder
    
    print_status "OK" "GOAD-Blue Splunk forwarder setup completed!"
    echo ""
    echo "Forwarder Status:"
    sudo -u $SPLUNK_USER $SPLUNK_FORWARDER_HOME/bin/splunk status
    echo ""
    echo "Deployment scripts created:"
    echo "  Linux: $GOAD_BLUE_HOME/scripts/deploy-linux-forwarder.sh"
    echo "  Windows: $GOAD_BLUE_HOME/scripts/deploy-windows-forwarder.ps1"
    echo ""
    echo "Windows config files:"
    echo "  $GOAD_BLUE_HOME/configs/splunk/windows/"
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "GOAD-Blue Splunk Forwarder Setup"
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --indexer-host HOST  Set indexer host (default: **************)"
        echo "  --indexer-port PORT  Set indexer port (default: 9997)"
        echo ""
        exit 0
        ;;
    --indexer-host)
        SPLUNK_INDEXER_HOST="$2"
        shift 2
        main
        ;;
    --indexer-port)
        SPLUNK_INDEXER_PORT="$2"
        shift 2
        main
        ;;
    *)
        main
        ;;
esac
