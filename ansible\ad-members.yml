---
# Load datas
- import_playbook: data.yml
  vars:
    data_path: "../ad/{{domain_name}}/data/"
  tags: 'data'

# set AD datas ==================================================================================================
- name: "play servers AD configuration"
  hosts: server
  roles:
    - { role: 'member_server', tags: 'server'}
  vars:
    member_domain: "{{lab.hosts[dict_key].domain}}"
    domain_username: "{{admin_user}}@{{member_domain}}"
    domain_password: "{{lab.domains[member_domain].domain_password}}"

- name: "play workstations AD configuration"
  hosts: workstation
  roles:
    - { role: 'commonwkstn', tags: 'workstation'}
  vars:
    member_domain: "{{lab.hosts[dict_key].domain}}"
    domain_username: "{{admin_user}}@{{member_domain}}"
    domain_password: "{{lab.domains[member_domain].domain_password}}"