"""
GOAD Integration Module
Handles integration between GOAD-Blue and existing GOAD environments
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from goad.log import Log


class GOADIntegration:
    """Handles integration with existing GOAD environments"""
    
    def __init__(self, config):
        self.config = config
        self.goad_path = Path(__file__).parent.parent.parent.parent
        self.goad_blue_path = Path(__file__).parent.parent.parent
        self.discovered_instances = []
    
    def integrate(self):
        """Main integration method"""
        Log.info("Starting GOAD integration...")
        
        # Discover existing GOAD instances
        if not self.discover_goad_instances():
            Log.error("No GOAD instances found")
            return False
        
        # Deploy monitoring agents
        if not self.deploy_monitoring_agents():
            Log.error("Failed to deploy monitoring agents")
            return False
        
        # Configure log forwarding
        if not self.configure_log_forwarding():
            Log.error("Failed to configure log forwarding")
            return False
        
        # Setup network monitoring
        if not self.setup_network_monitoring():
            Log.error("Failed to setup network monitoring")
            return False
        
        Log.success("GOAD integration completed successfully")
        return True
    
    def discover_goad_instances(self):
        """Discover existing GOAD instances"""
        Log.info("Discovering GOAD instances...")
        
        # Check for GOAD workspace directory
        workspace_path = self.goad_path / "workspace"
        if not workspace_path.exists():
            Log.warning("GOAD workspace not found")
            return False
        
        # Look for instance directories
        instance_dirs = [d for d in workspace_path.iterdir() if d.is_dir()]
        
        for instance_dir in instance_dirs:
            instance_file = instance_dir / "instance.json"
            if instance_file.exists():
                try:
                    with open(instance_file, 'r') as f:
                        instance_data = json.load(f)
                        self.discovered_instances.append({
                            'id': instance_data.get('instance_id'),
                            'lab': instance_data.get('lab'),
                            'provider': instance_data.get('provider'),
                            'path': instance_dir,
                            'status': instance_data.get('status', 'unknown')
                        })
                        Log.info(f"Found GOAD instance: {instance_data.get('instance_id')}")
                except Exception as e:
                    Log.warning(f"Failed to read instance file {instance_file}: {e}")
        
        if self.discovered_instances:
            Log.success(f"Discovered {len(self.discovered_instances)} GOAD instances")
            return True
        else:
            Log.warning("No GOAD instances discovered")
            return False
    
    def deploy_monitoring_agents(self):
        """Deploy monitoring agents to GOAD VMs"""
        Log.info("Deploying monitoring agents to GOAD VMs...")
        
        success = True
        
        for instance in self.discovered_instances:
            if instance['status'] != 'ready':
                Log.warning(f"Skipping instance {instance['id']} - not ready")
                continue
            
            Log.info(f"Deploying agents to instance: {instance['id']}")
            
            # Get inventory for this instance
            inventory_path = self.get_instance_inventory(instance)
            if not inventory_path:
                Log.error(f"No inventory found for instance {instance['id']}")
                success = False
                continue
            
            # Deploy Sysmon
            if not self.deploy_sysmon(inventory_path):
                Log.error(f"Failed to deploy Sysmon to {instance['id']}")
                success = False
            
            # Deploy SIEM agents
            if not self.deploy_siem_agents(inventory_path):
                Log.error(f"Failed to deploy SIEM agents to {instance['id']}")
                success = False
            
            # Deploy Velociraptor agents if enabled
            if self.config.is_component_enabled('velociraptor'):
                if not self.deploy_velociraptor_agents(inventory_path):
                    Log.error(f"Failed to deploy Velociraptor agents to {instance['id']}")
                    success = False
        
        return success
    
    def get_instance_inventory(self, instance):
        """Get Ansible inventory for a GOAD instance"""
        inventory_path = instance['path'] / "inventory"
        
        if inventory_path.exists():
            return inventory_path
        
        # Try alternative locations
        alt_paths = [
            instance['path'] / "ansible" / "inventory",
            instance['path'] / "provisioning" / "inventory"
        ]
        
        for path in alt_paths:
            if path.exists():
                return path
        
        return None
    
    def deploy_sysmon(self, inventory_path):
        """Deploy Sysmon to GOAD VMs"""
        Log.info("Deploying Sysmon...")
        
        ansible_dir = self.goad_blue_path / "ansible"
        playbook = ansible_dir / "goad-blue-sysmon.yml"
        
        if not playbook.exists():
            Log.error("Sysmon playbook not found")
            return False
        
        try:
            result = subprocess.run([
                "ansible-playbook", str(playbook),
                "-i", str(inventory_path)
            ], cwd=ansible_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                Log.success("Sysmon deployed successfully")
                return True
            else:
                Log.error(f"Sysmon deployment failed: {result.stderr}")
                return False
                
        except Exception as e:
            Log.error(f"Failed to deploy Sysmon: {e}")
            return False
    
    def deploy_siem_agents(self, inventory_path):
        """Deploy SIEM agents (Splunk UF or Beats)"""
        siem_type = self.config.get_value('siem', 'type')
        
        if siem_type == 'splunk':
            return self.deploy_splunk_forwarders(inventory_path)
        elif siem_type == 'elastic':
            return self.deploy_beats(inventory_path)
        
        return True  # No SIEM configured
    
    def deploy_splunk_forwarders(self, inventory_path):
        """Deploy Splunk Universal Forwarders"""
        Log.info("Deploying Splunk Universal Forwarders...")
        
        ansible_dir = self.goad_blue_path / "ansible"
        playbook = ansible_dir / "goad-blue-splunk-forwarders.yml"
        
        if not playbook.exists():
            Log.error("Splunk forwarder playbook not found")
            return False
        
        try:
            result = subprocess.run([
                "ansible-playbook", str(playbook),
                "-i", str(inventory_path)
            ], cwd=ansible_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                Log.success("Splunk forwarders deployed successfully")
                return True
            else:
                Log.error(f"Splunk forwarder deployment failed: {result.stderr}")
                return False
                
        except Exception as e:
            Log.error(f"Failed to deploy Splunk forwarders: {e}")
            return False
    
    def deploy_beats(self, inventory_path):
        """Deploy Elastic Beats"""
        Log.info("Deploying Elastic Beats...")
        
        ansible_dir = self.goad_blue_path / "ansible"
        playbook = ansible_dir / "goad-blue-beats.yml"
        
        if not playbook.exists():
            Log.error("Beats playbook not found")
            return False
        
        try:
            result = subprocess.run([
                "ansible-playbook", str(playbook),
                "-i", str(inventory_path)
            ], cwd=ansible_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                Log.success("Beats deployed successfully")
                return True
            else:
                Log.error(f"Beats deployment failed: {result.stderr}")
                return False
                
        except Exception as e:
            Log.error(f"Failed to deploy Beats: {e}")
            return False
    
    def deploy_velociraptor_agents(self, inventory_path):
        """Deploy Velociraptor agents"""
        Log.info("Deploying Velociraptor agents...")
        
        ansible_dir = self.goad_blue_path / "ansible"
        playbook = ansible_dir / "goad-blue-velociraptor-agents.yml"
        
        if not playbook.exists():
            Log.error("Velociraptor agent playbook not found")
            return False
        
        try:
            result = subprocess.run([
                "ansible-playbook", str(playbook),
                "-i", str(inventory_path)
            ], cwd=ansible_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                Log.success("Velociraptor agents deployed successfully")
                return True
            else:
                Log.error(f"Velociraptor agent deployment failed: {result.stderr}")
                return False
                
        except Exception as e:
            Log.error(f"Failed to deploy Velociraptor agents: {e}")
            return False
    
    def configure_log_forwarding(self):
        """Configure log forwarding from GOAD VMs to SIEM"""
        Log.info("Configuring log forwarding...")
        
        # This would configure Windows Event Log forwarding
        # and any custom log sources to the SIEM
        
        for instance in self.discovered_instances:
            if instance['status'] != 'ready':
                continue
            
            inventory_path = self.get_instance_inventory(instance)
            if not inventory_path:
                continue
            
            # Configure log forwarding
            ansible_dir = self.goad_blue_path / "ansible"
            playbook = ansible_dir / "goad-blue-log-forwarding.yml"
            
            if playbook.exists():
                try:
                    result = subprocess.run([
                        "ansible-playbook", str(playbook),
                        "-i", str(inventory_path)
                    ], cwd=ansible_dir, capture_output=True, text=True)
                    
                    if result.returncode != 0:
                        Log.error(f"Log forwarding configuration failed for {instance['id']}")
                        return False
                        
                except Exception as e:
                    Log.error(f"Failed to configure log forwarding: {e}")
                    return False
        
        Log.success("Log forwarding configured successfully")
        return True
    
    def setup_network_monitoring(self):
        """Setup network monitoring for GOAD traffic"""
        Log.info("Setting up network monitoring...")
        
        # This would configure Security Onion or Malcolm to monitor
        # network traffic between GOAD VMs
        
        if self.config.is_component_enabled('security_onion'):
            return self.configure_security_onion_monitoring()
        elif self.config.is_component_enabled('malcolm'):
            return self.configure_malcolm_monitoring()
        
        return True  # No network monitoring configured
    
    def configure_security_onion_monitoring(self):
        """Configure Security Onion to monitor GOAD network"""
        Log.info("Configuring Security Onion network monitoring...")
        
        # Implementation would configure Security Onion sensors
        # to monitor the GOAD network segments
        
        Log.warning("Security Onion network monitoring configuration not yet implemented")
        return True
    
    def configure_malcolm_monitoring(self):
        """Configure Malcolm to monitor GOAD network"""
        Log.info("Configuring Malcolm network monitoring...")
        
        # Implementation would configure Malcolm to capture
        # and analyze GOAD network traffic
        
        Log.warning("Malcolm network monitoring configuration not yet implemented")
        return True
