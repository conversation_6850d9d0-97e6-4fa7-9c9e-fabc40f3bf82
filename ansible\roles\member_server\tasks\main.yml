# DNSCHANGE
- name: "prioritize the {{domain_adapter}} interface (local) as the default for routing"
  ansible.windows.win_shell:
    Set-NetIPInterface -InterfaceAlias "{{domain_adapter}}" -InterfaceMetric 10
  when: two_adapters

- name: "Set configure dns to {{dns_domain}}"
  win_dns_client:
    adapter_names: "{{domain_adapter}}"
    ipv4_addresses:
    - "{{hostvars[dns_domain].ansible_host}}"
    log_path: C:\dns_log.txt

- name: Verify File Server Role is installed.
  win_feature:
    name: File-Services, FS-FileServer
    state: present
    include_management_tools: True

- name: "Add member server"
  win_domain_membership:
    dns_domain_name: "{{member_domain}}"
    domain_admin_user: "{{domain_username}}"
    domain_admin_password: "{{domain_password}}"
#    domain_ou_path: "{{member_domain_ou_path}}"
    state: domain
  register: domain_state

- name: Reboot if needed
  win_reboot:
    reboot_timeout: 1500
    post_reboot_delay: 100
  when: domain_state.reboot_required

