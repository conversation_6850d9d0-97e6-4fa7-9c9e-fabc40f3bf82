goad_blue:
  name: "goad-blue-lab"
  provider: "vmware"
  version: "3.1.0"

siem:
  type: "splunk"  # splunk, elastic, none
  enabled: true

components:
  security_onion:
    enabled: true
    version: "2.4"
    manager_ip: "**************"
    sensor_count: 2
  malcolm:
    enabled: false
    version: "latest"
    ip: "**************"
  velociraptor:
    enabled: true
    server_ip: "**************"
    server_port: 8000
  misp:
    enabled: true
    ip: "**************"
    admin_email: "<EMAIL>"
  flare_vm:
    enabled: true
    ip: "**************"
    isolated: true

network:
  base_cidr: "*************/24"
  siem_subnet: "*************/26"        # *************-62
  monitoring_subnet: "**************/26"  # **************-126
  red_team_subnet: "*************28/26"   # *************29-190
  analysis_subnet: "*************92/26"   # ***************-254

integration:
  goad_enabled: true
  auto_discover: true
  agent_deployment: true
  log_forwarding: true
  network_monitoring: true

splunk:
  admin_password: "ChangeMePlease123!"
  license_file: ""  # Path to Splunk license file
  apps:
    - "Splunk_TA_windows"
    - "Splunk_TA_nix"
    - "TA-suricata"
    - "MISP42"
  indexes:
    - name: "goad_blue_windows"
      maxDataSize: "10GB"
    - name: "goad_blue_network"
      maxDataSize: "5GB"
    - name: "goad_blue_threat_intel"
      maxDataSize: "1GB"

elastic:
  version: "8.11.0"
  password: "ChangeMePlease123!"
  cluster_name: "goad-blue-cluster"
  indices:
    - name: "goad-blue-windows"
      shards: 1
      replicas: 0
    - name: "goad-blue-network"
      shards: 1
      replicas: 0

security_onion:
  admin_password: "ChangeMePlease123!"
  components:
    - suricata
    - zeek
    - wazuh
    - elasticsearch
    - kibana
  rules:
    - emerging-threats
    - custom-goad-rules

misp:
  admin_password: "ChangeMePlease123!"
  database_password: "MispDbPassword123!"
  feeds:
    - "CIRCL OSINT Feed"
    - "Malware Domain List"
  organizations:
    - name: "GOAD-Blue Lab"
      description: "GOAD-Blue Training Environment"

velociraptor:
  admin_password: "ChangeMePlease123!"
  gui_port: 8889
  frontend_port: 8000
  artifacts:
    - "Windows.EventLogs.PowershellScriptblock"
    - "Windows.System.PowerShell"
    - "Windows.Detection.Kerberoasting"

flare_vm:
  admin_password: "ChangeMePlease123!"
  tools:
    - "PEStudio"
    - "IDA Free"
    - "x64dbg"
    - "Wireshark"
    - "Volatility"
    - "YARA"
