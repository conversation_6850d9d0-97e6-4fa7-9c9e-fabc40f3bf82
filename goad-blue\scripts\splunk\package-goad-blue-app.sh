#!/bin/bash
# Package GOAD-Blue Splunk App for Distribution
# Creates a distributable Splunk app package

set -e

# Configuration
GOAD_BLUE_HOME="/opt/goad-blue"
PACKAGE_DIR="$GOAD_BLUE_HOME/packages"
APP_NAME="goad_blue_security"
APP_VERSION="1.0.0"
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
PACKAGE_NAME="${APP_NAME}_${APP_VERSION}_${TIMESTAMP}.tar.gz"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "OK")
            echo -e "${GREEN}[OK]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
    esac
}

# Create package directory structure
create_package_structure() {
    print_status "INFO" "Creating package structure..."
    
    mkdir -p "$PACKAGE_DIR/build/$APP_NAME"
    mkdir -p "$PACKAGE_DIR/build/$APP_NAME/default"
    mkdir -p "$PACKAGE_DIR/build/$APP_NAME/local/data/ui/views"
    mkdir -p "$PACKAGE_DIR/build/$APP_NAME/metadata"
    mkdir -p "$PACKAGE_DIR/build/$APP_NAME/static"
    mkdir -p "$PACKAGE_DIR/build/$APP_NAME/appserver/static"
    mkdir -p "$PACKAGE_DIR/build/$APP_NAME/lookups"
    mkdir -p "$PACKAGE_DIR/build/$APP_NAME/bin"
    
    print_status "OK" "Package structure created"
}

# Create app manifest
create_app_manifest() {
    print_status "INFO" "Creating app manifest..."
    
    cat > "$PACKAGE_DIR/build/$APP_NAME/app.manifest" << EOF
{
  "schemaVersion": "2.0.0",
  "info": {
    "title": "GOAD-Blue Security",
    "id": {
      "group": null,
      "name": "$APP_NAME",
      "version": "$APP_VERSION"
    },
    "author": [
      {
        "name": "GOAD-Blue Team",
        "email": "<EMAIL>",
        "company": "GOAD-Blue Project"
      }
    ],
    "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "description": "Comprehensive security monitoring and analysis dashboards for GOAD-Blue cybersecurity training environment",
    "classification": {
      "intendedAudience": "Security Professionals",
      "categories": ["Security", "Monitoring", "Training"],
      "developmentStatus": "Production/Stable"
    },
    "commonInformationModels": {
      "Authentication": ">=4.0.0",
      "Network_Traffic": ">=4.0.0",
      "Malware": ">=4.0.0",
      "Intrusion_Detection": ">=4.0.0"
    },
    "license": {
      "name": "MIT",
      "text": "MIT License",
      "uri": "https://opensource.org/licenses/MIT"
    },
    "privacyPolicy": {
      "name": "GOAD-Blue Privacy Policy",
      "text": "This app does not collect personal data",
      "uri": "https://goad-blue.local/privacy"
    },
    "releaseNotes": {
      "name": "GOAD-Blue Release Notes",
      "text": "Initial release with comprehensive security dashboards",
      "uri": "https://goad-blue.local/release-notes"
    }
  },
  "dependencies": {
    "Splunk_SA_CIM": {
      "version": ">=4.0.0",
      "package": "https://splunkbase.splunk.com/app/1621/"
    }
  },
  "tasks": [],
  "inputGroups": {},
  "incompatibleApps": {},
  "platformRequirements": {
    "splunk": {
      "Enterprise": ">=8.0.0"
    }
  }
}
EOF

    print_status "OK" "App manifest created"
}

# Create README
create_readme() {
    print_status "INFO" "Creating README..."
    
    cat > "$PACKAGE_DIR/build/$APP_NAME/README.md" << 'EOF'
# GOAD-Blue Security App

## Overview

The GOAD-Blue Security App provides comprehensive security monitoring and analysis dashboards specifically designed for the GOAD-Blue cybersecurity training environment.

## Features

### Dashboards
- **Main Overview**: High-level security posture and event summary
- **Authentication Monitoring**: Windows authentication events and analysis
- **Network Monitoring**: Suricata alerts and network traffic analysis
- **Threat Hunting**: Advanced threat detection and hunting queries
- **Incident Response**: Forensics and incident investigation tools

### Technical Add-ons Included
- Windows Event Log parsing
- Suricata IDS/IPS events
- Linux audit logs
- PowerShell activity monitoring
- Network traffic analysis
- DNS query monitoring

### Saved Searches and Alerts
- Failed logon attempt detection
- Privilege escalation monitoring
- Suspicious PowerShell activity
- High-severity network alerts

## Installation

1. Install the Splunk Common Information Model (CIM) if not already installed
2. Install this app through Splunk Web or extract to `$SPLUNK_HOME/etc/apps/`
3. Restart Splunk
4. Configure data inputs to send logs to the appropriate indexes:
   - `goad_blue_windows` - Windows event logs
   - `goad_blue_linux` - Linux system logs
   - `goad_blue_network` - Network security logs
   - `goad_blue_security` - Security alerts and events

## Data Sources

### Windows Systems
- Security Event Log (EventCode 4624, 4625, 4648, etc.)
- System Event Log
- Application Event Log
- Sysmon Events
- PowerShell Operational Log

### Linux Systems
- /var/log/auth.log
- /var/log/syslog
- /var/log/audit/audit.log
- Apache/Nginx access and error logs

### Network Security
- Suricata EVE JSON logs
- Zeek connection logs
- DNS query logs
- HTTP transaction logs

## Configuration

### Index Configuration
Ensure the following indexes are created in your Splunk environment:

```
[goad_blue_windows]
homePath = $SPLUNK_DB/goad_blue_windows/db
coldPath = $SPLUNK_DB/goad_blue_windows/colddb
thawedPath = $SPLUNK_DB/goad_blue_windows/thaweddb

[goad_blue_linux]
homePath = $SPLUNK_DB/goad_blue_linux/db
coldPath = $SPLUNK_DB/goad_blue_linux/colddb
thawedPath = $SPLUNK_DB/goad_blue_linux/thaweddb

[goad_blue_network]
homePath = $SPLUNK_DB/goad_blue_network/db
coldPath = $SPLUNK_DB/goad_blue_network/colddb
thawedPath = $SPLUNK_DB/goad_blue_network/thaweddb

[goad_blue_security]
homePath = $SPLUNK_DB/goad_blue_security/db
coldPath = $SPLUNK_DB/goad_blue_security/colddb
thawedPath = $SPLUNK_DB/goad_blue_security/thaweddb
```

### Forwarder Configuration
Use the included forwarder configuration files to set up Universal Forwarders on GOAD systems.

## Macros

The app includes several useful macros:
- `goad_blue_windows_events` - All Windows events
- `goad_blue_network_events` - All network events
- `authentication_events` - Authentication-related events
- `privilege_events` - Privilege escalation events
- `network_alerts` - Network security alerts

## Support

For support and documentation, visit: https://goad-blue.local/docs

## Version History

- v1.0.0 - Initial release with core dashboards and monitoring capabilities

## License

MIT License - See LICENSE file for details
EOF

    print_status "OK" "README created"
}

# Copy app files
copy_app_files() {
    print_status "INFO" "Copying app files..."
    
    local splunk_apps_dir="/opt/splunk/etc/apps"
    local source_app_dir="$splunk_apps_dir/$APP_NAME"
    
    if [ -d "$source_app_dir" ]; then
        # Copy configuration files
        cp -r "$source_app_dir/default"/* "$PACKAGE_DIR/build/$APP_NAME/default/" 2>/dev/null || true
        cp -r "$source_app_dir/local"/* "$PACKAGE_DIR/build/$APP_NAME/local/" 2>/dev/null || true
        cp -r "$source_app_dir/metadata"/* "$PACKAGE_DIR/build/$APP_NAME/metadata/" 2>/dev/null || true
        
        print_status "OK" "App files copied from Splunk installation"
    else
        print_status "WARNING" "Source app directory not found, creating minimal structure"
        
        # Create minimal app.conf
        cat > "$PACKAGE_DIR/build/$APP_NAME/default/app.conf" << EOF
[install]
is_configured = 1
state = enabled

[ui]
is_visible = 1
label = GOAD-Blue Security

[launcher]
author = GOAD-Blue Team
description = GOAD-Blue Cybersecurity Training Platform
version = $APP_VERSION

[package]
id = $APP_NAME
check_for_updates = 0
EOF
    fi
}

# Create installation script
create_installation_script() {
    print_status "INFO" "Creating installation script..."
    
    cat > "$PACKAGE_DIR/build/$APP_NAME/install.sh" << 'EOF'
#!/bin/bash
# GOAD-Blue Security App Installation Script

SPLUNK_HOME="${SPLUNK_HOME:-/opt/splunk}"
APP_NAME="goad_blue_security"

echo "Installing GOAD-Blue Security App..."

# Check if Splunk is installed
if [ ! -d "$SPLUNK_HOME" ]; then
    echo "Error: Splunk not found at $SPLUNK_HOME"
    exit 1
fi

# Copy app files
cp -r "$APP_NAME" "$SPLUNK_HOME/etc/apps/"
chown -R splunk:splunk "$SPLUNK_HOME/etc/apps/$APP_NAME"

echo "App installed successfully!"
echo "Please restart Splunk to complete the installation."
echo ""
echo "After restart, access dashboards at:"
echo "https://your-splunk-server:8000/en-US/app/$APP_NAME/"
EOF

    chmod +x "$PACKAGE_DIR/build/$APP_NAME/install.sh"
    
    print_status "OK" "Installation script created"
}

# Create lookup files
create_lookup_files() {
    print_status "INFO" "Creating lookup files..."
    
    # MITRE ATT&CK lookup
    cat > "$PACKAGE_DIR/build/$APP_NAME/lookups/mitre_attack.csv" << 'EOF'
technique_id,technique_name,tactic,description
T1078,Valid Accounts,Defense Evasion,Adversaries may obtain and abuse credentials of existing accounts
T1110,Brute Force,Credential Access,Adversaries may use brute force techniques to gain access to accounts
T1021,Remote Services,Lateral Movement,Adversaries may use valid accounts to log into a service
T1055,Process Injection,Defense Evasion,Adversaries may inject code into processes
T1059,Command and Scripting Interpreter,Execution,Adversaries may abuse command and script interpreters
T1003,OS Credential Dumping,Credential Access,Adversaries may attempt to dump credentials
T1082,System Information Discovery,Discovery,An adversary may attempt to get detailed information about the operating system
T1083,File and Directory Discovery,Discovery,Adversaries may enumerate files and directories
T1057,Process Discovery,Discovery,Adversaries may attempt to get information about running processes
T1018,Remote System Discovery,Discovery,Adversaries may attempt to get a listing of other systems
EOF

    # Common ports lookup
    cat > "$PACKAGE_DIR/build/$APP_NAME/lookups/common_ports.csv" << 'EOF'
port,service,description,risk_level
21,FTP,File Transfer Protocol,Medium
22,SSH,Secure Shell,Low
23,Telnet,Telnet Protocol,High
25,SMTP,Simple Mail Transfer Protocol,Medium
53,DNS,Domain Name System,Low
80,HTTP,Hypertext Transfer Protocol,Low
110,POP3,Post Office Protocol v3,Medium
135,RPC,Microsoft RPC,High
139,NetBIOS,NetBIOS Session Service,High
143,IMAP,Internet Message Access Protocol,Medium
443,HTTPS,HTTP Secure,Low
445,SMB,Server Message Block,High
993,IMAPS,IMAP over SSL,Low
995,POP3S,POP3 over SSL,Low
1433,MSSQL,Microsoft SQL Server,High
3389,RDP,Remote Desktop Protocol,High
5985,WinRM,Windows Remote Management,Medium
5986,WinRM-HTTPS,Windows Remote Management over HTTPS,Medium
EOF

    print_status "OK" "Lookup files created"
}

# Create package
create_package() {
    print_status "INFO" "Creating package archive..."
    
    cd "$PACKAGE_DIR/build"
    tar -czf "../$PACKAGE_NAME" "$APP_NAME"
    
    # Create checksum
    cd "$PACKAGE_DIR"
    sha256sum "$PACKAGE_NAME" > "${PACKAGE_NAME}.sha256"
    
    print_status "OK" "Package created: $PACKAGE_DIR/$PACKAGE_NAME"
}

# Generate package info
generate_package_info() {
    print_status "INFO" "Generating package information..."
    
    cat > "$PACKAGE_DIR/${APP_NAME}_${APP_VERSION}_info.txt" << EOF
GOAD-Blue Security App Package Information
==========================================

Package Name: $PACKAGE_NAME
Version: $APP_VERSION
Created: $(date)
Size: $(du -h "$PACKAGE_DIR/$PACKAGE_NAME" | cut -f1)

Contents:
- GOAD-Blue Security App
- Installation scripts
- Documentation
- Lookup files
- Dashboard configurations
- Technical Add-on configurations

Installation:
1. Extract the package
2. Run the installation script: ./install.sh
3. Restart Splunk
4. Configure data inputs

Checksums:
$(cat "${PACKAGE_NAME}.sha256")

Support: https://goad-blue.local/docs
EOF

    print_status "OK" "Package information generated"
}

# Main function
main() {
    echo "========================================"
    echo "  GOAD-Blue Splunk App Packager"
    echo "========================================"
    echo ""
    
    # Create package structure
    create_package_structure
    
    # Create app components
    create_app_manifest
    create_readme
    copy_app_files
    create_installation_script
    create_lookup_files
    
    # Create final package
    create_package
    generate_package_info
    
    # Cleanup build directory
    rm -rf "$PACKAGE_DIR/build"
    
    print_status "OK" "GOAD-Blue Splunk app packaging completed!"
    echo ""
    echo "Package Details:"
    echo "  File: $PACKAGE_DIR/$PACKAGE_NAME"
    echo "  Size: $(du -h "$PACKAGE_DIR/$PACKAGE_NAME" | cut -f1)"
    echo "  Checksum: $PACKAGE_DIR/${PACKAGE_NAME}.sha256"
    echo "  Info: $PACKAGE_DIR/${APP_NAME}_${APP_VERSION}_info.txt"
    echo ""
    echo "To install:"
    echo "  1. Extract: tar -xzf $PACKAGE_NAME"
    echo "  2. Install: cd $APP_NAME && ./install.sh"
    echo "  3. Restart Splunk"
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "GOAD-Blue Splunk App Packager"
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --version VER  Set app version (default: $APP_VERSION)"
        echo ""
        exit 0
        ;;
    --version)
        APP_VERSION="$2"
        shift 2
        main
        ;;
    *)
        main
        ;;
esac
