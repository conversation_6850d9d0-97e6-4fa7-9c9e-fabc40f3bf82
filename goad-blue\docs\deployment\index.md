# Deployment Guide

This section covers Infrastructure as Code (IaC) deployment strategies, automation, and best practices for deploying GOAD-Blue across different environments.

## 🏗️ Deployment Architecture

### **Infrastructure as Code Stack**

```mermaid
graph TB
    subgraph "📦 Image Building (Packer)"
        PACKER_TEMPLATES[📋 Packer Templates<br/>VM Image Definitions]
        BASE_IMAGES[🐧 Base Images<br/>OS Templates]
        CUSTOM_IMAGES[🎨 Custom Images<br/>Pre-configured VMs]
    end
    
    subgraph "🌍 Infrastructure Provisioning (Terraform)"
        TF_MODULES[🧩 Terraform Modules<br/>Reusable Infrastructure]
        PROVIDERS[☁️ Infrastructure Providers<br/>AWS, Azure, GCP, VMware<br/>Proxmox, VirtualBox, Hyper-V]
        RESOURCES[🏗️ Infrastructure Resources<br/>VMs, Networks, Storage]
    end
    
    subgraph "⚙️ Configuration Management (Ansible)"
        PLAYBOOKS[📚 Ansible Playbooks<br/>Configuration Automation]
        ROLES[🎭 Ansible Roles<br/>Component Configuration]
        INVENTORIES[📋 Dynamic Inventories<br/>Target Management]
    end
    
    subgraph "🎯 Orchestration Layer"
        DEPLOY_ENGINE[🚀 Deployment Engine<br/>Workflow Orchestration]
        CI_CD[🔄 CI/CD Pipeline<br/>Automated Deployment]
        MONITORING[📊 Deployment Monitoring<br/>Health & Status]
    end
    
    BASE_IMAGES --> PACKER_TEMPLATES
    PACKER_TEMPLATES --> CUSTOM_IMAGES
    
    CUSTOM_IMAGES --> TF_MODULES
    PROVIDERS --> TF_MODULES
    TF_MODULES --> RESOURCES
    
    RESOURCES --> INVENTORIES
    INVENTORIES --> ROLES
    ROLES --> PLAYBOOKS
    
    PLAYBOOKS --> DEPLOY_ENGINE
    DEPLOY_ENGINE --> CI_CD
    CI_CD --> MONITORING
    
    classDef packer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef terraform fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef ansible fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef orchestration fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class PACKER_TEMPLATES,BASE_IMAGES,CUSTOM_IMAGES packer
    class TF_MODULES,PROVIDERS,RESOURCES terraform
    class PLAYBOOKS,ROLES,INVENTORIES ansible
    class DEPLOY_ENGINE,CI_CD,MONITORING orchestration
```

## 📦 Packer Image Building

### **Image Templates Structure**

```
packer/
├── templates/
│   ├── base/
│   │   ├── ubuntu-22.04.json
│   │   └── windows-server-2019.json
│   ├── components/
│   │   ├── goad-blue-splunk.json
│   │   ├── goad-blue-security-onion.json
│   │   ├── goad-blue-velociraptor.json
│   │   └── goad-blue-flare-vm.json
│   └── providers/
│       ├── aws/
│       ├── azure/
│       ├── gcp/
│       ├── vmware/
│       ├── proxmox/
│       ├── virtualbox/
│       └── hyperv/
├── scripts/
│   ├── ubuntu/
│   └── windows/
└── files/
    ├── configs/
    └── certificates/
```

### **Splunk Server Template**

```json
{
  "variables": {
    "vm_name": "goad-blue-splunk-server",
    "vm_version": "1.0.0",
    "iso_url": "https://releases.ubuntu.com/22.04/ubuntu-22.04.3-live-server-amd64.iso",
    "iso_checksum": "sha256:a4acfda10b18da50e2ec50ccaf860d7f20b389df8765611142305c0e911d16fd",
    "ssh_username": "ubuntu",
    "ssh_password": "ubuntu"
  },
  "builders": [
    {
      "type": "vmware-iso",
      "vm_name": "{{user `vm_name`}}",
      "guest_os_type": "ubuntu-64",
      "version": "19",
      "disk_size": 102400,
      "memory": 8192,
      "cpus": 4,
      "network": "nat",
      "iso_url": "{{user `iso_url`}}",
      "iso_checksum": "{{user `iso_checksum`}}",
      "ssh_username": "{{user `ssh_username`}}",
      "ssh_password": "{{user `ssh_password`}}",
      "ssh_timeout": "20m",
      "boot_wait": "5s",
      "boot_command": [
        "<enter><wait><f6><wait><esc><wait>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs><bs><bs><bs><bs><bs><bs><bs>",
        "<bs><bs><bs>",
        "/casper/vmlinuz initrd=/casper/initrd ",
        "autoinstall ds=nocloud-net;s=http://{{.HTTPIP}}:{{.HTTPPort}}/ubuntu/ ",
        "--- <enter>"
      ],
      "http_directory": "http",
      "shutdown_command": "echo 'ubuntu' | sudo -S shutdown -P now"
    }
  ],
  "provisioners": [
    {
      "type": "shell",
      "script": "scripts/ubuntu/base-setup.sh",
      "execute_command": "echo 'ubuntu' | sudo -S sh -c '{{ .Vars }} {{ .Path }}'"
    },
    {
      "type": "shell",
      "script": "scripts/ubuntu/splunk-install.sh",
      "execute_command": "echo 'ubuntu' | sudo -S sh -c '{{ .Vars }} {{ .Path }}'"
    },
    {
      "type": "file",
      "source": "files/configs/splunk/",
      "destination": "/tmp/splunk-configs/"
    },
    {
      "type": "shell",
      "script": "scripts/ubuntu/splunk-configure.sh",
      "execute_command": "echo 'ubuntu' | sudo -S sh -c '{{ .Vars }} {{ .Path }}'"
    }
  ],
  "post-processors": [
    {
      "type": "manifest",
      "output": "manifest.json",
      "strip_path": true
    }
  ]
}
```

### **Build Scripts**

```bash
#!/bin/bash
# scripts/build-images.sh

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PACKER_DIR="$(dirname "$SCRIPT_DIR")/packer"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

build_image() {
    local template=$1
    local provider=${2:-vmware}
    
    log_info "Building image: $template for provider: $provider"
    
    cd "$PACKER_DIR"
    
    if packer build -var-file="variables/$provider.json" "templates/$template"; then
        log_success "Successfully built $template"
    else
        log_error "Failed to build $template"
        return 1
    fi
}

# Build all images
build_image "base/ubuntu-22.04.json"
build_image "components/goad-blue-splunk.json"
build_image "components/goad-blue-security-onion.json"
build_image "components/goad-blue-velociraptor.json"
build_image "components/goad-blue-misp.json"

log_success "All images built successfully"
```

## 🌍 Terraform Infrastructure

### **Module Structure**

```
terraform/
├── modules/
│   ├── network/
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   └── outputs.tf
│   ├── compute/
│   │   ├── main.tf
│   │   ├── variables.tf
│   │   └── outputs.tf
│   └── security/
│       ├── main.tf
│       ├── variables.tf
│       └── outputs.tf
├── environments/
│   ├── development/
│   ├── testing/
│   └── production/
└── providers/
    ├── aws/
    ├── azure/
    ├── gcp/
    ├── vmware/
    ├── proxmox/
    └── virtualbox/
```

### **Main Infrastructure Module**

```hcl
# terraform/modules/goad-blue/main.tf
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
    vsphere = {
      source  = "hashicorp/vsphere"
      version = "~> 2.0"
    }
    proxmox = {
      source  = "telmate/proxmox"
      version = "~> 2.9"
    }
  }
}

# Data sources
data "aws_availability_zones" "available" {
  state = "available"
}

# VPC and Networking
module "network" {
  source = "../network"
  
  name_prefix        = var.name_prefix
  vpc_cidr          = var.vpc_cidr
  availability_zones = data.aws_availability_zones.available.names
  
  public_subnets  = var.public_subnets
  private_subnets = var.private_subnets
  
  enable_nat_gateway = var.enable_nat_gateway
  enable_vpn_gateway = var.enable_vpn_gateway
  
  tags = var.tags
}

# Security Groups
module "security" {
  source = "../security"
  
  name_prefix = var.name_prefix
  vpc_id      = module.network.vpc_id
  
  allowed_cidr_blocks = var.allowed_cidr_blocks
  
  tags = var.tags
}

# Compute Resources
module "compute" {
  source = "../compute"
  
  name_prefix = var.name_prefix
  
  # Network configuration
  vpc_id              = module.network.vpc_id
  private_subnet_ids  = module.network.private_subnet_ids
  public_subnet_ids   = module.network.public_subnet_ids
  
  # Security configuration
  security_group_ids = module.security.security_group_ids
  
  # Instance configuration
  instances = var.instances
  
  # Storage configuration
  ebs_optimized = var.ebs_optimized
  
  tags = var.tags
}

# Load Balancer (optional)
resource "aws_lb" "goad_blue" {
  count = var.enable_load_balancer ? 1 : 0
  
  name               = "${var.name_prefix}-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [module.security.alb_security_group_id]
  subnets           = module.network.public_subnet_ids
  
  enable_deletion_protection = var.enable_deletion_protection
  
  tags = var.tags
}
```

### **Variables Definition**

```hcl
# terraform/modules/goad-blue/variables.tf
variable "name_prefix" {
  description = "Prefix for all resource names"
  type        = string
  default     = "goad-blue"
}

variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnets" {
  description = "CIDR blocks for public subnets"
  type        = list(string)
  default     = ["********/24", "********/24"]
}

variable "private_subnets" {
  description = "CIDR blocks for private subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24"]
}

variable "instances" {
  description = "Instance configurations"
  type = map(object({
    instance_type = string
    ami_id        = string
    subnet_type   = string
    volume_size   = number
    volume_type   = string
  }))
  default = {
    splunk = {
      instance_type = "t3.large"
      ami_id        = "ami-12345678"
      subnet_type   = "private"
      volume_size   = 100
      volume_type   = "gp3"
    }
    security_onion = {
      instance_type = "t3.xlarge"
      ami_id        = "ami-87654321"
      subnet_type   = "private"
      volume_size   = 200
      volume_type   = "gp3"
    }
  }
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default = {
    Project     = "GOAD-Blue"
    Environment = "development"
    ManagedBy   = "Terraform"
  }
}
```

### **Environment Configuration**

```hcl
# terraform/environments/production/main.tf
module "goad_blue" {
  source = "../../modules/goad-blue"
  
  name_prefix = "goad-blue-prod"
  
  vpc_cidr = "**********/16"
  
  public_subnets  = ["**********/24", "**********/24", "**********/24"]
  private_subnets = ["***********/24", "***********/24", "***********/24"]
  
  instances = {
    splunk_indexer_1 = {
      instance_type = "c5.2xlarge"
      ami_id        = var.splunk_ami_id
      subnet_type   = "private"
      volume_size   = 500
      volume_type   = "gp3"
    }
    splunk_indexer_2 = {
      instance_type = "c5.2xlarge"
      ami_id        = var.splunk_ami_id
      subnet_type   = "private"
      volume_size   = 500
      volume_type   = "gp3"
    }
    splunk_search_head = {
      instance_type = "c5.xlarge"
      ami_id        = var.splunk_ami_id
      subnet_type   = "private"
      volume_size   = 200
      volume_type   = "gp3"
    }
    security_onion_manager = {
      instance_type = "c5.2xlarge"
      ami_id        = var.security_onion_ami_id
      subnet_type   = "private"
      volume_size   = 300
      volume_type   = "gp3"
    }
    security_onion_sensor_1 = {
      instance_type = "c5.xlarge"
      ami_id        = var.security_onion_ami_id
      subnet_type   = "private"
      volume_size   = 200
      volume_type   = "gp3"
    }
  }
  
  enable_load_balancer = true
  enable_nat_gateway   = true
  
  tags = {
    Project     = "GOAD-Blue"
    Environment = "production"
    ManagedBy   = "Terraform"
    Owner       = "Security-Team"
  }
}
```

## ⚙️ Ansible Configuration

### **Playbook Structure**

```
ansible/
├── playbooks/
│   ├── site.yml
│   ├── splunk.yml
│   ├── security-onion.yml
│   └── velociraptor.yml
├── roles/
│   ├── common/
│   ├── splunk-server/
│   ├── security-onion/
│   └── velociraptor/
├── inventory/
│   ├── production/
│   ├── development/
│   └── group_vars/
└── files/
    ├── certificates/
    └── configurations/
```

### **Main Site Playbook**

```yaml
# ansible/playbooks/site.yml
---
- name: GOAD-Blue Infrastructure Deployment
  hosts: localhost
  gather_facts: false
  tasks:
    - name: Display deployment information
      debug:
        msg: |
          Starting GOAD-Blue deployment
          Environment: {{ environment }}
          Components: {{ enabled_components | join(', ') }}

- name: Configure common settings
  hosts: all
  become: true
  roles:
    - common
  tags:
    - common
    - base

- name: Deploy SIEM components
  hosts: siem
  become: true
  roles:
    - role: splunk-server
      when: siem_type == "splunk"
    - role: elastic-stack
      when: siem_type == "elastic"
  tags:
    - siem

- name: Deploy monitoring components
  hosts: monitoring
  become: true
  roles:
    - role: security-onion
      when: "'security_onion' in enabled_components"
    - role: malcolm
      when: "'malcolm' in enabled_components"
  tags:
    - monitoring

- name: Deploy endpoint components
  hosts: endpoint
  become: true
  roles:
    - role: velociraptor
      when: "'velociraptor' in enabled_components"
  tags:
    - endpoint

- name: Deploy threat intelligence
  hosts: intelligence
  become: true
  roles:
    - role: misp
      when: "'misp' in enabled_components"
  tags:
    - intelligence

- name: Configure GOAD integration
  hosts: all
  become: true
  roles:
    - goad-integration
  when: goad_integration_enabled
  tags:
    - integration
```

### **Splunk Server Role**

```yaml
# ansible/roles/splunk-server/tasks/main.yml
---
- name: Create splunk user
  user:
    name: splunk
    system: true
    shell: /bin/bash
    home: /opt/splunk
    create_home: false

- name: Download Splunk Enterprise
  get_url:
    url: "{{ splunk_download_url }}"
    dest: "/tmp/splunk-{{ splunk_version }}.tgz"
    mode: '0644'
  when: not splunk_installed.stat.exists

- name: Extract Splunk
  unarchive:
    src: "/tmp/splunk-{{ splunk_version }}.tgz"
    dest: /opt
    owner: splunk
    group: splunk
    remote_src: true
  when: not splunk_installed.stat.exists

- name: Set Splunk ownership
  file:
    path: /opt/splunk
    owner: splunk
    group: splunk
    recurse: true

- name: Configure Splunk
  template:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    owner: splunk
    group: splunk
    mode: '0644'
  loop:
    - src: server.conf.j2
      dest: /opt/splunk/etc/system/local/server.conf
    - src: web.conf.j2
      dest: /opt/splunk/etc/system/local/web.conf
    - src: inputs.conf.j2
      dest: /opt/splunk/etc/system/local/inputs.conf
  notify: restart splunk

- name: Start and enable Splunk
  systemd:
    name: splunk
    state: started
    enabled: true
    daemon_reload: true

- name: Install GOAD-Blue apps
  unarchive:
    src: "{{ item }}"
    dest: /opt/splunk/etc/apps/
    owner: splunk
    group: splunk
  loop: "{{ goad_blue_apps }}"
  notify: restart splunk

- name: Configure indexes
  template:
    src: indexes.conf.j2
    dest: /opt/splunk/etc/apps/goad_blue/local/indexes.conf
    owner: splunk
    group: splunk
  notify: restart splunk
```

### **Dynamic Inventory**

```python
#!/usr/bin/env python3
# ansible/inventory/dynamic_inventory.py

import json
import boto3
import sys
from typing import Dict, List, Any

class GoadBlueInventory:
    def __init__(self):
        self.inventory = {
            '_meta': {
                'hostvars': {}
            }
        }
        self.ec2 = boto3.client('ec2')
        
    def get_inventory(self) -> Dict[str, Any]:
        """Generate dynamic inventory from AWS EC2 instances."""
        instances = self._get_ec2_instances()
        
        for instance in instances:
            self._add_instance_to_inventory(instance)
            
        return self.inventory
        
    def _get_ec2_instances(self) -> List[Dict]:
        """Get EC2 instances with GOAD-Blue tags."""
        response = self.ec2.describe_instances(
            Filters=[
                {
                    'Name': 'tag:Project',
                    'Values': ['GOAD-Blue']
                },
                {
                    'Name': 'instance-state-name',
                    'Values': ['running']
                }
            ]
        )
        
        instances = []
        for reservation in response['Reservations']:
            instances.extend(reservation['Instances'])
            
        return instances
        
    def _add_instance_to_inventory(self, instance: Dict):
        """Add instance to inventory with appropriate groups."""
        instance_id = instance['InstanceId']
        private_ip = instance.get('PrivateIpAddress', '')
        public_ip = instance.get('PublicIpAddress', '')
        
        # Get tags
        tags = {tag['Key']: tag['Value'] for tag in instance.get('Tags', [])}
        component = tags.get('Component', 'unknown')
        environment = tags.get('Environment', 'unknown')
        
        # Add to component group
        if component not in self.inventory:
            self.inventory[component] = {'hosts': []}
        self.inventory[component]['hosts'].append(private_ip)
        
        # Add to environment group
        env_group = f"{environment}_{component}"
        if env_group not in self.inventory:
            self.inventory[env_group] = {'hosts': []}
        self.inventory[env_group]['hosts'].append(private_ip)
        
        # Add host variables
        self.inventory['_meta']['hostvars'][private_ip] = {
            'instance_id': instance_id,
            'public_ip': public_ip,
            'component': component,
            'environment': environment,
            'ansible_host': private_ip,
            'ansible_user': 'ubuntu'
        }

if __name__ == '__main__':
    inventory = GoadBlueInventory()
    print(json.dumps(inventory.get_inventory(), indent=2))
```

### **Proxmox Deployment Example**

```hcl
# terraform/providers/proxmox/main.tf
terraform {
  required_providers {
    proxmox = {
      source  = "telmate/proxmox"
      version = "~> 2.9"
    }
  }
}

provider "proxmox" {
  pm_api_url      = var.proxmox_api_url
  pm_user         = var.proxmox_user
  pm_password     = var.proxmox_password
  pm_tls_insecure = true
}

# Splunk Enterprise VM
resource "proxmox_vm_qemu" "splunk_enterprise" {
  name        = "goad-blue-splunk"
  target_node = var.target_node
  vmid        = 200

  cores    = 8
  memory   = 16384
  sockets  = 1
  cpu      = "host"

  disk {
    slot     = 0
    type     = "scsi"
    storage  = "local-lvm"
    size     = "500G"
    format   = "qcow2"
  }

  network {
    model  = "virtio"
    bridge = "vmbr2"
    tag    = 100
  }

  os_type    = "cloud-init"
  ciuser     = "ubuntu"
  cipassword = "goadblue123!"
  ipconfig0  = "ip=*************0/24,gw=*************"

  tags = "goad-blue,splunk"
}

# Security Onion Manager VM
resource "proxmox_vm_qemu" "security_onion_manager" {
  name        = "goad-blue-so-manager"
  target_node = var.target_node
  vmid        = 270

  cores    = 16
  memory   = 32768
  sockets  = 1
  cpu      = "host"

  disk {
    slot     = 0
    type     = "scsi"
    storage  = "local-lvm"
    size     = "1000G"
    format   = "qcow2"
  }

  # Management network
  network {
    model  = "virtio"
    bridge = "vmbr2"
    tag    = 100
  }

  # Monitoring network
  network {
    model  = "virtio"
    bridge = "vmbr1"
    tag    = 56
  }

  os_type    = "cloud-init"
  ciuser     = "ubuntu"
  cipassword = "goadblue123!"
  ipconfig0  = "ip=**************/24,gw=*************"
  ipconfig1  = "ip=*************/24"

  tags = "goad-blue,security-onion"
}
```

## 🚀 Deployment Automation

### **CI/CD Pipeline**

```yaml
# .github/workflows/deploy.yml
name: GOAD-Blue Deployment

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  AWS_REGION: us-east-1
  TERRAFORM_VERSION: 1.6.0
  ANSIBLE_VERSION: 6.0.0

jobs:
  validate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}
          
      - name: Terraform Format Check
        run: terraform fmt -check -recursive
        
      - name: Terraform Validate
        run: |
          cd terraform/environments/development
          terraform init -backend=false
          terraform validate
          
      - name: Ansible Lint
        run: |
          pip install ansible-lint
          ansible-lint ansible/playbooks/

  build-images:
    runs-on: ubuntu-latest
    needs: validate
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Packer
        run: |
          wget https://releases.hashicorp.com/packer/1.9.4/packer_1.9.4_linux_amd64.zip
          unzip packer_1.9.4_linux_amd64.zip
          sudo mv packer /usr/local/bin/
          
      - name: Build Images
        run: |
          cd packer
          packer build templates/components/goad-blue-splunk.json
          
  deploy-infrastructure:
    runs-on: ubuntu-latest
    needs: build-images
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}
          
      - name: Terraform Deploy
        run: |
          cd terraform/environments/development
          terraform init
          terraform plan
          terraform apply -auto-approve
          
  configure-systems:
    runs-on: ubuntu-latest
    needs: deploy-infrastructure
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Ansible
        run: |
          pip install ansible==${{ env.ANSIBLE_VERSION }}
          
      - name: Configure Systems
        run: |
          cd ansible
          ansible-playbook -i inventory/dynamic_inventory.py playbooks/site.yml
```

---

!!! info "Deployment Resources"
    For detailed deployment information, see:
    
    - [Packer Templates](packer.md)
    - [Terraform Modules](terraform.md)
    - [Ansible Playbooks](ansible.md)
    - [CI/CD Pipelines](cicd.md)

!!! tip "Environment Management"
    Use separate Terraform workspaces or directories for different environments (dev, test, prod) to maintain isolation and prevent accidental changes.

!!! warning "Security Considerations"
    Always use secure methods for storing and accessing credentials:
    
    - Use AWS IAM roles instead of access keys when possible
    - Store secrets in secure vaults (AWS Secrets Manager, HashiCorp Vault)
    - Implement least privilege access principles
    - Regularly rotate credentials and certificates
