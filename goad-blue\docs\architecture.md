# GOAD-Blue Architecture

## System Architecture Overview

GOAD-Blue implements a layered architecture that seamlessly integrates with existing GOAD environments while providing comprehensive blue team capabilities. The architecture is designed for scalability, modularity, and real-world applicability.

## 🏗️ High-Level Architecture

### System Overview

```mermaid
graph TB
    subgraph "🎮 Presentation Layer"
        CLI[🖥️ GOAD-Blue CLI<br/>Interactive Management]
        WEB[🌐 Web Interfaces<br/>Splunk, Security Onion, etc.]
        API[🔌 REST APIs<br/>Automation & Integration]
    end

    subgraph "🎯 Orchestration Layer"
        MGR[🎛️ Blue Team Manager<br/>Component Orchestration]
        CFG[⚙️ Configuration Manager<br/>Settings & Policies]
        INT[🔗 Integration Engine<br/>GOAD Connectivity]
    end

    subgraph "🛡️ Security Components Layer"
        SIEM[📊 SIEM Platform<br/>Splunk/Elastic Stack]
        MON[🔍 Network Monitoring<br/>Security Onion/Malcolm]
        END[💻 Endpoint Security<br/>Velociraptor/Sysmon]
        INTEL[🧠 Threat Intelligence<br/>MISP Platform]
        ANAL[🔬 Malware Analysis<br/>FLARE-VM Environment]
    end

    subgraph "🏗️ Infrastructure Layer"
        PACK[📦 Packer Templates<br/>VM Image Building]
        TERRA[🌍 Terraform Modules<br/>Infrastructure Provisioning]
        ANS[🔧 Ansible Playbooks<br/>Configuration Management]
    end

    subgraph "💾 Data Layer"
        LOGS[(📝 Log Storage<br/>Events & Alerts)]
        PCAP[(🌐 Network Captures<br/>Traffic Analysis)]
        INTEL_DATA[(🎯 Threat Intel<br/>IOCs & TTPs)]
        CONFIG[(⚙️ Configuration<br/>Settings & State)]
    end

    %% Presentation to Orchestration
    CLI --> MGR
    WEB --> MGR
    API --> MGR

    %% Orchestration to Components
    MGR --> SIEM
    MGR --> MON
    MGR --> END
    MGR --> INTEL
    MGR --> ANAL

    %% Configuration Flow
    CFG --> CONFIG
    INT --> LOGS

    %% Data Flow
    SIEM --> LOGS
    MON --> PCAP
    END --> LOGS
    INTEL --> INTEL_DATA
    ANAL --> LOGS

    %% Infrastructure Flow
    PACK --> TERRA
    TERRA --> ANS
    ANS --> SIEM
    ANS --> MON
    ANS --> END

    %% Styling
    classDef presentation fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef orchestration fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef components fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef infrastructure fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef data fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class CLI,WEB,API presentation
    class MGR,CFG,INT orchestration
    class SIEM,MON,END,INTEL,ANAL components
    class PACK,TERRA,ANS infrastructure
    class LOGS,PCAP,INTEL_DATA,CONFIG data
```

### Component Interaction Flow

```mermaid
sequenceDiagram
    participant User as 👤 Security Analyst
    participant CLI as 🖥️ GOAD-Blue CLI
    participant MGR as 🎛️ Blue Team Manager
    participant SIEM as 📊 SIEM Platform
    participant MON as 🔍 Network Monitor
    participant GOAD as 🎯 GOAD Environment

    User->>CLI: Deploy GOAD-Blue
    CLI->>MGR: Initialize Components

    par Deploy SIEM
        MGR->>SIEM: Deploy & Configure
        SIEM-->>MGR: Ready
    and Deploy Monitoring
        MGR->>MON: Deploy & Configure
        MON-->>MGR: Ready
    end

    MGR->>GOAD: Discover Instances
    GOAD-->>MGR: Instance Details

    MGR->>GOAD: Deploy Agents
    GOAD-->>SIEM: Log Stream
    GOAD-->>MON: Network Traffic

    User->>GOAD: Execute Attack
    GOAD->>MON: Generate Traffic
    GOAD->>SIEM: Generate Logs

    MON->>SIEM: Send Alerts
    SIEM->>User: Alert Notification

    User->>SIEM: Investigate
    SIEM-->>User: Analysis Results
```

## 🔧 Component Architecture

### **SIEM Architecture**

The SIEM layer provides centralized log collection, analysis, and correlation:

```mermaid
graph TB
    subgraph "📥 Data Sources"
        WIN[🪟 Windows Events<br/>Event Logs, Sysmon]
        NET[🌐 Network Logs<br/>Suricata, Zeek]
        END[💻 Endpoint Data<br/>Process, File Activity]
        THREAT[🎯 Threat Intel<br/>IOCs, TTPs]
    end

    subgraph "🚚 Collection Layer"
        UF[📦 Universal Forwarders<br/>Splunk Agents]
        BEATS[🥁 Elastic Beats<br/>Winlogbeat, Filebeat]
        AGENTS[🤖 Custom Agents<br/>API Collectors]
    end

    subgraph "⚙️ Processing Pipeline"
        PARSE[📋 Parsing<br/>Log Format Recognition]
        NORM[🔄 Normalization<br/>CIM/ECS Mapping]
        ENRICH[✨ Enrichment<br/>GeoIP, DNS, TI]
        CORR[🔗 Correlation<br/>Event Linking]
    end

    subgraph "💾 Storage Tier"
        HOT[(🔥 Hot Storage<br/>Recent Data)]
        WARM[(🌡️ Warm Storage<br/>Historical Data)]
        COLD[(❄️ Cold Storage<br/>Archive Data)]
    end

    subgraph "📊 Analysis & Visualization"
        SEARCH[🔍 Search Interface<br/>SPL/KQL Queries]
        DASH[📈 Dashboards<br/>Real-time Views]
        ALERT[🚨 Alerting<br/>Automated Detection]
        REPORT[📄 Reporting<br/>Scheduled Reports]
        HUNT[🎯 Threat Hunting<br/>Proactive Search]
    end

    %% Data Flow
    WIN --> UF
    NET --> BEATS
    END --> AGENTS
    THREAT --> UF

    UF --> PARSE
    BEATS --> PARSE
    AGENTS --> PARSE

    PARSE --> NORM
    NORM --> ENRICH
    ENRICH --> CORR

    CORR --> HOT
    HOT --> WARM
    WARM --> COLD

    HOT --> SEARCH
    WARM --> SEARCH
    SEARCH --> DASH
    SEARCH --> ALERT
    SEARCH --> REPORT
    SEARCH --> HUNT

    %% Styling
    classDef sources fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef collection fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef processing fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef storage fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef analysis fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class WIN,NET,END,THREAT sources
    class UF,BEATS,AGENTS collection
    class PARSE,NORM,ENRICH,CORR processing
    class HOT,WARM,COLD storage
    class SEARCH,DASH,ALERT,REPORT,HUNT analysis
```

### **SIEM Data Processing Flow**

```mermaid
flowchart TD
    START([📥 Raw Log Data]) --> INGEST{🔍 Data Type?}

    INGEST -->|Windows Events| WIN_PROC[🪟 Windows Processing<br/>• Event ID Mapping<br/>• User/Computer Enrichment<br/>• Privilege Analysis]
    INGEST -->|Network Traffic| NET_PROC[🌐 Network Processing<br/>• Protocol Analysis<br/>• GeoIP Enrichment<br/>• Reputation Scoring]
    INGEST -->|Endpoint Data| END_PROC[💻 Endpoint Processing<br/>• Process Tree Building<br/>• File Hash Lookup<br/>• Behavioral Analysis]

    WIN_PROC --> CORRELATE[🔗 Event Correlation]
    NET_PROC --> CORRELATE
    END_PROC --> CORRELATE

    CORRELATE --> RULES{📋 Detection Rules}

    RULES -->|Match| ALERT_GEN[🚨 Generate Alert<br/>• Severity Assignment<br/>• Context Addition<br/>• Notification Trigger]
    RULES -->|No Match| STORE[💾 Store for Analysis]

    ALERT_GEN --> NOTIFY[📢 Notification<br/>• Email/Slack/Teams<br/>• Ticket Creation<br/>• Dashboard Update]

    STORE --> INDEX[📚 Index for Search]
    INDEX --> DASHBOARD[📊 Dashboard Display]

    NOTIFY --> INVESTIGATE[🔍 Investigation<br/>• Timeline Analysis<br/>• Pivot Searches<br/>• Evidence Collection]

    classDef process fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef action fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef storage fill:#e3f2fd,stroke:#1976d2,stroke-width:2px

    class WIN_PROC,NET_PROC,END_PROC,CORRELATE,ALERT_GEN process
    class INGEST,RULES decision
    class NOTIFY,INVESTIGATE action
    class STORE,INDEX,DASHBOARD storage
```

### **Network Monitoring Architecture**

Network monitoring provides comprehensive visibility into network traffic and threats:

```mermaid
graph TB
    subgraph "🌐 Traffic Sources"
        SPAN[📡 SPAN Ports<br/>Switch Port Mirroring]
        TAP[🔌 Network TAPs<br/>Physical Taps]
        MIRROR[🪞 Traffic Mirroring<br/>Virtual Switches]
        GOAD_NET[🎯 GOAD Network<br/>Lab Traffic]
    end

    subgraph "📊 Collection Infrastructure"
        SENSOR_MGR[🎛️ Sensor Manager<br/>Security Onion Manager]
        SENSOR1[📡 Forward Sensor<br/>Log Processing]
        SENSOR2[🔍 Search Sensor<br/>Heavy Node]
        SENSOR3[🌐 Standalone Sensor<br/>Remote Location]
    end

    subgraph "⚙️ Processing Engines"
        SURICATA[🛡️ Suricata IDS/IPS<br/>• Signature Detection<br/>• Protocol Analysis<br/>• File Extraction]
        ZEEK[🔍 Zeek NSM<br/>• Protocol Logging<br/>• Behavioral Analysis<br/>• Script Framework]
        WAZUH[🛡️ Wazuh HIDS<br/>• Host Monitoring<br/>• File Integrity<br/>• Rootkit Detection]
    end

    subgraph "📈 Analysis Platforms"
        SO[🧅 Security Onion<br/>• Centralized Management<br/>• Hunt Interface<br/>• Case Management]
        MALCOLM[🕵️ Malcolm<br/>• Network Forensics<br/>• PCAP Analysis<br/>• Arkime Integration]
        CUSTOM[🔧 Custom Analytics<br/>• ML Detection<br/>• Behavioral Baselines<br/>• Custom Rules]
    end

    subgraph "💾 Storage & Analysis"
        PCAP_STORE[(📦 PCAP Storage<br/>Full Packet Capture)]
        LOG_STORE[(📝 Log Storage<br/>Structured Logs)]
        ELASTIC[(🔍 Elasticsearch<br/>Search & Analytics)]
        ARKIME[(🌐 Arkime<br/>PCAP Indexing)]
    end

    %% Traffic Flow
    SPAN --> SENSOR1
    TAP --> SENSOR2
    MIRROR --> SENSOR3
    GOAD_NET --> SENSOR1

    %% Sensor Management
    SENSOR_MGR --> SENSOR1
    SENSOR_MGR --> SENSOR2
    SENSOR_MGR --> SENSOR3

    %% Processing Flow
    SENSOR1 --> SURICATA
    SENSOR1 --> ZEEK
    SENSOR2 --> WAZUH
    SENSOR3 --> SURICATA

    %% Platform Integration
    SURICATA --> SO
    ZEEK --> SO
    ZEEK --> MALCOLM
    WAZUH --> SO

    %% Storage Flow
    SO --> LOG_STORE
    SO --> ELASTIC
    MALCOLM --> PCAP_STORE
    MALCOLM --> ARKIME
    CUSTOM --> ELASTIC

    %% Styling
    classDef sources fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef collection fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef processing fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef platforms fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef storage fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class SPAN,TAP,MIRROR,GOAD_NET sources
    class SENSOR_MGR,SENSOR1,SENSOR2,SENSOR3 collection
    class SURICATA,ZEEK,WAZUH processing
    class SO,MALCOLM,CUSTOM platforms
    class PCAP_STORE,LOG_STORE,ELASTIC,ARKIME storage
```

### **Network Detection Pipeline**

```mermaid
flowchart TD
    PACKET([📦 Network Packet]) --> CAPTURE{🎯 Capture Point}

    CAPTURE -->|SPAN Port| SPAN_PROC[📡 SPAN Processing<br/>• High Volume<br/>• Potential Drops<br/>• Cost Effective]
    CAPTURE -->|Network TAP| TAP_PROC[🔌 TAP Processing<br/>• Full Duplex<br/>• No Drops<br/>• Higher Cost]
    CAPTURE -->|Virtual Mirror| VIRT_PROC[🪞 Virtual Processing<br/>• VM Traffic<br/>• Hypervisor Level<br/>• Lab Environments]

    SPAN_PROC --> ANALYSIS[🔍 Traffic Analysis]
    TAP_PROC --> ANALYSIS
    VIRT_PROC --> ANALYSIS

    ANALYSIS --> SURICATA_RULES{🛡️ Suricata Rules}
    ANALYSIS --> ZEEK_SCRIPTS{🔍 Zeek Scripts}

    SURICATA_RULES -->|Signature Match| IDS_ALERT[🚨 IDS Alert<br/>• Rule ID<br/>• Severity<br/>• Source/Dest]
    SURICATA_RULES -->|No Match| ZEEK_ANALYSIS[🔍 Zeek Analysis]

    ZEEK_SCRIPTS -->|Behavioral Anomaly| BEHAVIOR_ALERT[⚠️ Behavioral Alert<br/>• Protocol Anomaly<br/>• Volume Spike<br/>• New Connection]
    ZEEK_SCRIPTS -->|Normal Traffic| LOG_ONLY[📝 Log Only]

    ZEEK_ANALYSIS --> PROTOCOL_LOG[📋 Protocol Logs<br/>• conn.log<br/>• dns.log<br/>• http.log]

    IDS_ALERT --> CORRELATE[🔗 Event Correlation]
    BEHAVIOR_ALERT --> CORRELATE
    PROTOCOL_LOG --> CORRELATE

    CORRELATE --> ENRICH[✨ Enrichment<br/>• GeoIP Lookup<br/>• Reputation Check<br/>• Threat Intel]

    ENRICH --> SIEM_FORWARD[📊 Forward to SIEM]
    ENRICH --> PCAP_STORE[💾 Store PCAP]

    LOG_ONLY --> ARCHIVE[📚 Archive Logs]

    classDef capture fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef processing fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef detection fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef alert fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef storage fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class SPAN_PROC,TAP_PROC,VIRT_PROC capture
    class ANALYSIS,ZEEK_ANALYSIS,CORRELATE,ENRICH processing
    class SURICATA_RULES,ZEEK_SCRIPTS detection
    class IDS_ALERT,BEHAVIOR_ALERT alert
    class PROTOCOL_LOG,LOG_ONLY,PCAP_STORE,ARCHIVE,SIEM_FORWARD storage
```

### **Endpoint Monitoring Architecture**

Endpoint monitoring provides detailed visibility into host-based activities:

```mermaid
graph TB
    subgraph "Endpoint Agents"
        VELO_AGENT[Velociraptor Agent]
        SYSMON[Sysmon]
        WINLOG[Windows Event Log]
        CUSTOM_AGENT[Custom Agents]
    end
    
    subgraph "Collection Services"
        VELO_SERVER[Velociraptor Server]
        LOG_COLLECTOR[Log Collectors]
        API_GATEWAY[API Gateway]
    end
    
    subgraph "Processing Pipeline"
        PARSER[Event Parser]
        ENRICHER[Data Enricher]
        CORRELATOR[Event Correlator]
    end
    
    subgraph "Analysis Engines"
        BEHAVIOR[Behavioral Analysis]
        ANOMALY[Anomaly Detection]
        HUNTING[Threat Hunting]
    end
    
    subgraph "Response Capabilities"
        ISOLATION[Host Isolation]
        REMEDIATION[Automated Remediation]
        FORENSICS[Live Forensics]
    end
    
    VELO_AGENT --> VELO_SERVER
    SYSMON --> LOG_COLLECTOR
    WINLOG --> LOG_COLLECTOR
    CUSTOM_AGENT --> API_GATEWAY
    
    VELO_SERVER --> PARSER
    LOG_COLLECTOR --> PARSER
    API_GATEWAY --> PARSER
    
    PARSER --> ENRICHER
    ENRICHER --> CORRELATOR
    
    CORRELATOR --> BEHAVIOR
    CORRELATOR --> ANOMALY
    CORRELATOR --> HUNTING
    
    BEHAVIOR --> ISOLATION
    ANOMALY --> REMEDIATION
    HUNTING --> FORENSICS
```

## 🌐 Network Architecture

### **Network Segmentation Strategy**

GOAD-Blue implements a comprehensive segmented network architecture for security, performance, and isolation:

```mermaid
graph TB
    subgraph "🏢 Management Subnet (*************/26)"
        direction TB
        SIEM_MGMT[📊 SIEM Management<br/>**************<br/>Splunk/Elastic]
        ADMIN[👤 Admin Workstation<br/>*************<br/>Management Access]
        BACKUP[💾 Backup Server<br/>192.168.100.15<br/>Data Protection]
        DNS[🌐 DNS Server<br/>192.168.100.2<br/>Name Resolution]
    end

    subgraph "🔍 Monitoring Subnet (192.168.100.64/26)"
        direction TB
        SO_MGMT[🧅 Security Onion Manager<br/>192.168.100.70<br/>Central Management]
        SO_SENSOR[📡 Security Onion Sensors<br/>192.168.100.71-75<br/>Traffic Analysis]
        MALCOLM_SRV[🕵️ Malcolm Server<br/>192.168.100.80<br/>Network Forensics]
        VELO_SRV[🦖 Velociraptor Server<br/>192.168.100.85<br/>Endpoint Visibility]
    end

    subgraph "🎯 GOAD Production Subnet (192.168.100.128/26)"
        direction TB
        GOAD_DC1[🏰 Kingslanding DC<br/>192.168.100.140<br/>sevenkingdoms.local]
        GOAD_DC2[❄️ Winterfell DC<br/>192.168.100.141<br/>north.sevenkingdoms.local]
        GOAD_DC3[🐉 Meereen DC<br/>192.168.100.142<br/>essos.local]
        GOAD_SRV1[⚔️ Castelblack<br/>192.168.100.150<br/>File Server]
        GOAD_SRV2[🏛️ Braavos<br/>192.168.100.151<br/>Web Server]
    end

    subgraph "🔬 Analysis Subnet (192.168.100.192/26)"
        direction TB
        FLARE[🔥 FLARE-VM<br/>192.168.100.200<br/>Malware Analysis]
        SANDBOX[📦 Analysis Sandbox<br/>192.168.100.201<br/>Isolated Testing]
        ISOLATED[🔒 Isolated Environment<br/>192.168.100.202<br/>Air-Gapped Analysis]
    end

    subgraph "🌍 External Connectivity"
        direction TB
        INTERNET[🌐 Internet Gateway<br/>External Access]
        THREAT_FEEDS[🎯 Threat Intel Feeds<br/>IOC Sources]
        UPDATES[📦 Update Services<br/>Software Updates]
        VPN[🔐 VPN Gateway<br/>Remote Access]
    end

    %% Inter-subnet Communication
    SIEM_MGMT <--> SO_MGMT
    SIEM_MGMT <--> VELO_SRV
    SO_MGMT <--> SO_SENSOR
    SO_SENSOR -.-> GOAD_DC1
    SO_SENSOR -.-> GOAD_DC2
    SO_SENSOR -.-> GOAD_SRV1
    VELO_SRV <--> GOAD_DC1
    VELO_SRV <--> GOAD_DC2
    VELO_SRV <--> GOAD_SRV1

    %% External Connectivity
    FLARE -.-> INTERNET
    THREAT_FEEDS --> SIEM_MGMT
    UPDATES --> ADMIN
    VPN --> ADMIN

    %% Analysis Network Isolation
    FLARE -.-> SANDBOX
    SANDBOX -.-> ISOLATED

    %% Styling
    classDef management fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef monitoring fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    classDef production fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    classDef analysis fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    classDef external fill:#fce4ec,stroke:#c2185b,stroke-width:3px

    class SIEM_MGMT,ADMIN,BACKUP,DNS management
    class SO_MGMT,SO_SENSOR,MALCOLM_SRV,VELO_SRV monitoring
    class GOAD_DC1,GOAD_DC2,GOAD_DC3,GOAD_SRV1,GOAD_SRV2 production
    class FLARE,SANDBOX,ISOLATED analysis
    class INTERNET,THREAT_FEEDS,UPDATES,VPN external
```

### **Network Security Zones**

```mermaid
graph LR
    subgraph "🔒 Security Zones"
        direction TB

        subgraph "🟢 Trusted Zone"
            MGMT_ZONE[🏢 Management<br/>• Admin Access<br/>• SIEM Platform<br/>• Backup Services]
        end

        subgraph "🟡 Monitoring Zone"
            MON_ZONE[🔍 Monitoring<br/>• Security Tools<br/>• Log Collection<br/>• Traffic Analysis]
        end

        subgraph "🟠 Production Zone"
            PROD_ZONE[🎯 GOAD Environment<br/>• Domain Controllers<br/>• Member Servers<br/>• Attack Targets]
        end

        subgraph "🔴 Quarantine Zone"
            QUAR_ZONE[🔬 Analysis<br/>• Malware Analysis<br/>• Isolated Testing<br/>• Forensic Work]
        end

        subgraph "⚫ External Zone"
            EXT_ZONE[🌍 Internet<br/>• Threat Feeds<br/>• Updates<br/>• Remote Access]
        end
    end

    %% Zone Interactions with Firewall Rules
    MGMT_ZONE <==> MON_ZONE
    MON_ZONE <==> PROD_ZONE
    MGMT_ZONE -.-> PROD_ZONE
    QUAR_ZONE -.-> EXT_ZONE
    MGMT_ZONE <==> EXT_ZONE

    %% Firewall Rules
    MGMT_ZONE -.->|"🛡️ Firewall Rules<br/>• SSH/RDP Access<br/>• HTTPS Management<br/>• Log Forwarding"| MON_ZONE
    MON_ZONE -.->|"🛡️ Firewall Rules<br/>• Agent Communication<br/>• Log Collection<br/>• Alert Forwarding"| PROD_ZONE
    QUAR_ZONE -.->|"🛡️ Firewall Rules<br/>• Outbound Only<br/>• DNS/HTTP(S)<br/>• No Lateral Movement"| EXT_ZONE
```

### **Traffic Flow Patterns**

```mermaid
sequenceDiagram
    participant GOAD as GOAD Environment
    participant SENSOR as Network Sensors
    participant SIEM as SIEM Platform
    participant ANALYST as Security Analyst
    participant RESPONSE as Response System
    
    GOAD->>SENSOR: Network Traffic
    SENSOR->>SENSOR: Traffic Analysis
    SENSOR->>SIEM: Alerts & Logs
    SIEM->>SIEM: Correlation
    SIEM->>ANALYST: Alert Notification
    ANALYST->>SIEM: Investigation
    ANALYST->>RESPONSE: Response Action
    RESPONSE->>GOAD: Containment
```

## 🔄 Data Flow Architecture

### **Log Processing Pipeline**

```mermaid
graph LR
    subgraph "Sources"
        A[Windows Events]
        B[Network Logs]
        C[Application Logs]
        D[Security Tools]
    end
    
    subgraph "Collection"
        E[Forwarders]
        F[Agents]
        G[APIs]
    end
    
    subgraph "Processing"
        H[Parsing]
        I[Filtering]
        J[Enrichment]
        K[Normalization]
    end
    
    subgraph "Storage"
        L[Hot Storage]
        M[Warm Storage]
        N[Cold Storage]
    end
    
    subgraph "Analysis"
        O[Real-time]
        P[Batch]
        Q[Interactive]
    end
    
    A --> E
    B --> F
    C --> G
    D --> E
    
    E --> H
    F --> I
    G --> J
    
    H --> K
    I --> K
    J --> K
    
    K --> L
    L --> M
    M --> N
    
    L --> O
    M --> P
    N --> Q
```

### **Threat Intelligence Flow**

```mermaid
graph TD
    subgraph "External Sources"
        FEEDS[Threat Feeds]
        OSINT[OSINT Sources]
        COMMERCIAL[Commercial Intel]
    end
    
    subgraph "MISP Platform"
        INGEST[Data Ingestion]
        PROCESS[Processing Engine]
        CORRELATE[Correlation Engine]
        STORE[Intelligence Store]
    end
    
    subgraph "Distribution"
        SIEM_FEED[SIEM Feed]
        API_FEED[API Feed]
        MANUAL[Manual Export]
    end
    
    subgraph "Consumption"
        DETECTION[Detection Rules]
        HUNTING[Threat Hunting]
        ANALYSIS[Incident Analysis]
    end
    
    FEEDS --> INGEST
    OSINT --> INGEST
    COMMERCIAL --> INGEST
    
    INGEST --> PROCESS
    PROCESS --> CORRELATE
    CORRELATE --> STORE
    
    STORE --> SIEM_FEED
    STORE --> API_FEED
    STORE --> MANUAL
    
    SIEM_FEED --> DETECTION
    API_FEED --> HUNTING
    MANUAL --> ANALYSIS
```

## 🔧 Deployment Architecture

### **Infrastructure as Code Pipeline**

```mermaid
graph TB
    subgraph "📦 Image Building Layer"
        direction TB
        BASE_OS[🐧 Base OS Images<br/>Ubuntu 22.04 LTS<br/>Windows Server 2019/2022]
        PACKER_TEMPLATES[📋 Packer Templates<br/>• goad-blue-splunk-server.json<br/>• goad-blue-security-onion.json<br/>• goad-blue-flare-vm.json]
        CUSTOM_IMAGES[🎨 Custom Images<br/>• Pre-configured VMs<br/>• Security Tools Installed<br/>• Hardened & Optimized]
    end

    subgraph "🏗️ Infrastructure Provisioning"
        direction TB
        TERRAFORM_MODULES[🌍 Terraform Modules<br/>• AWS Module<br/>• Azure Module<br/>• VMware Module]
        CLOUD_PROVIDERS[☁️ Cloud Providers<br/>• AWS EC2/VPC<br/>• Azure VMs/VNets<br/>• VMware vSphere]
        INFRA_RESOURCES[🏢 Infrastructure Resources<br/>• Virtual Machines<br/>• Networks & Subnets<br/>• Security Groups]
    end

    subgraph "⚙️ Configuration Management"
        direction TB
        ANSIBLE_PLAYBOOKS[📚 Ansible Playbooks<br/>• goad-blue-site.yml<br/>• Component-specific books<br/>• Integration playbooks]
        ANSIBLE_ROLES[🎭 Ansible Roles<br/>• SIEM Configuration<br/>• Agent Deployment<br/>• Security Hardening]
        CONFIG_FILES[📄 Configuration Files<br/>• Service Configs<br/>• Detection Rules<br/>• Dashboard Definitions]
    end

    subgraph "🎯 Orchestration & Automation"
        direction TB
        DEPLOY_SCRIPTS[🚀 Deployment Scripts<br/>• goad-blue-installer.sh<br/>• Component Managers<br/>• Health Checks]
        CI_CD_PIPELINE[🔄 CI/CD Pipeline<br/>• GitHub Actions<br/>• Automated Testing<br/>• Deployment Validation]
        MONITORING[📊 Deployment Monitoring<br/>• Resource Utilization<br/>• Service Health<br/>• Performance Metrics]
    end

    %% Flow Connections
    BASE_OS --> PACKER_TEMPLATES
    PACKER_TEMPLATES --> CUSTOM_IMAGES

    CUSTOM_IMAGES --> TERRAFORM_MODULES
    CLOUD_PROVIDERS --> TERRAFORM_MODULES
    TERRAFORM_MODULES --> INFRA_RESOURCES

    INFRA_RESOURCES --> ANSIBLE_PLAYBOOKS
    ANSIBLE_ROLES --> ANSIBLE_PLAYBOOKS
    ANSIBLE_PLAYBOOKS --> CONFIG_FILES

    CONFIG_FILES --> DEPLOY_SCRIPTS
    DEPLOY_SCRIPTS --> CI_CD_PIPELINE
    CI_CD_PIPELINE --> MONITORING

    %% Styling
    classDef building fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef provisioning fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef configuration fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef orchestration fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class BASE_OS,PACKER_TEMPLATES,CUSTOM_IMAGES building
    class TERRAFORM_MODULES,CLOUD_PROVIDERS,INFRA_RESOURCES provisioning
    class ANSIBLE_PLAYBOOKS,ANSIBLE_ROLES,CONFIG_FILES configuration
    class DEPLOY_SCRIPTS,CI_CD_PIPELINE,MONITORING orchestration
```

### **Deployment Workflow**

```mermaid
sequenceDiagram
    participant Dev as 👨‍💻 Developer
    participant Git as 📚 Git Repository
    participant CI as 🔄 CI/CD Pipeline
    participant Packer as 📦 Packer
    participant Terraform as 🌍 Terraform
    participant Ansible as ⚙️ Ansible
    participant Infra as 🏗️ Infrastructure
    participant Monitor as 📊 Monitoring

    Dev->>Git: Push Code Changes
    Git->>CI: Trigger Pipeline

    CI->>CI: Run Tests & Validation

    par Image Building
        CI->>Packer: Build VM Images
        Packer->>Packer: Install & Configure Software
        Packer-->>CI: Images Ready
    end

    CI->>Terraform: Plan Infrastructure
    Terraform->>Infra: Provision Resources
    Infra-->>Terraform: Resources Created
    Terraform-->>CI: Infrastructure Ready

    CI->>Ansible: Configure Systems
    Ansible->>Infra: Deploy Configurations
    Ansible->>Infra: Install Agents
    Ansible->>Infra: Apply Security Settings
    Infra-->>Ansible: Configuration Complete
    Ansible-->>CI: Deployment Successful

    CI->>Monitor: Start Health Checks
    Monitor->>Infra: Validate Services
    Monitor->>Monitor: Collect Metrics
    Monitor-->>CI: All Systems Healthy

    CI-->>Dev: Deployment Complete

    Note over Dev,Monitor: Continuous monitoring and alerting active
```

### **Multi-Platform Deployment**

=== "VMware Deployment"
    ```mermaid
    graph LR
        PACKER[Packer Build] --> TEMPLATE[VM Template]
        TEMPLATE --> TERRAFORM[Terraform Deploy]
        TERRAFORM --> VSPHERE[vSphere Infrastructure]
        VSPHERE --> ANSIBLE[Ansible Configure]
        ANSIBLE --> READY[Ready Environment]
    ```

=== "AWS Deployment"
    ```mermaid
    graph LR
        PACKER[Packer Build] --> AMI[Amazon AMI]
        AMI --> TERRAFORM[Terraform Deploy]
        TERRAFORM --> EC2[EC2 Instances]
        EC2 --> ANSIBLE[Ansible Configure]
        ANSIBLE --> READY[Ready Environment]
    ```

=== "Azure Deployment"
    ```mermaid
    graph LR
        PACKER[Packer Build] --> IMAGE[Azure Image]
        IMAGE --> TERRAFORM[Terraform Deploy]
        TERRAFORM --> VM[Azure VMs]
        VM --> ANSIBLE[Ansible Configure]
        ANSIBLE --> READY[Ready Environment]
    ```

## 🔒 Security Architecture

### **Defense in Depth**

```mermaid
graph TB
    subgraph "Perimeter Security"
        FW[Firewalls]
        IPS[Intrusion Prevention]
        WAF[Web Application Firewall]
    end
    
    subgraph "Network Security"
        VLAN[Network Segmentation]
        NAC[Network Access Control]
        MONITOR[Network Monitoring]
    end
    
    subgraph "Host Security"
        AV[Antivirus/EDR]
        HIPS[Host IPS]
        CONFIG[Configuration Management]
    end
    
    subgraph "Application Security"
        AUTH[Authentication]
        AUTHZ[Authorization]
        ENCRYPT[Encryption]
    end
    
    subgraph "Data Security"
        DLP[Data Loss Prevention]
        BACKUP[Backup & Recovery]
        RETENTION[Data Retention]
    end
    
    FW --> VLAN
    IPS --> NAC
    WAF --> MONITOR
    
    VLAN --> AV
    NAC --> HIPS
    MONITOR --> CONFIG
    
    AV --> AUTH
    HIPS --> AUTHZ
    CONFIG --> ENCRYPT
    
    AUTH --> DLP
    AUTHZ --> BACKUP
    ENCRYPT --> RETENTION
```

### **Access Control Model**

```mermaid
graph LR
    subgraph "Identity Management"
        USERS[Users]
        GROUPS[Groups]
        ROLES[Roles]
    end
    
    subgraph "Authentication"
        LOCAL[Local Auth]
        LDAP[LDAP/AD]
        SSO[Single Sign-On]
    end
    
    subgraph "Authorization"
        RBAC[Role-Based Access]
        ABAC[Attribute-Based Access]
        POLICIES[Access Policies]
    end
    
    subgraph "Resources"
        SIEM_ACCESS[SIEM Access]
        ADMIN_ACCESS[Admin Access]
        DATA_ACCESS[Data Access]
    end
    
    USERS --> LOCAL
    GROUPS --> LDAP
    ROLES --> SSO
    
    LOCAL --> RBAC
    LDAP --> ABAC
    SSO --> POLICIES
    
    RBAC --> SIEM_ACCESS
    ABAC --> ADMIN_ACCESS
    POLICIES --> DATA_ACCESS
```

## 📊 Performance Architecture

### **Scalability Considerations**

- **Horizontal Scaling** - Add more instances as load increases
- **Vertical Scaling** - Increase resources for existing instances
- **Load Balancing** - Distribute load across multiple instances
- **Caching** - Reduce database load with intelligent caching
- **Data Partitioning** - Distribute data across multiple storage systems

### **Performance Monitoring**

```mermaid
graph TB
    subgraph "Infrastructure Metrics"
        CPU[CPU Utilization]
        MEM[Memory Usage]
        DISK[Disk I/O]
        NET[Network Traffic]
    end
    
    subgraph "Application Metrics"
        RESP[Response Times]
        THROUGH[Throughput]
        ERROR[Error Rates]
        AVAIL[Availability]
    end
    
    subgraph "Business Metrics"
        DETECT[Detection Rate]
        FALSE_POS[False Positives]
        MTTR[Mean Time to Response]
        COVERAGE[Coverage Metrics]
    end
    
    CPU --> RESP
    MEM --> THROUGH
    DISK --> ERROR
    NET --> AVAIL
    
    RESP --> DETECT
    THROUGH --> FALSE_POS
    ERROR --> MTTR
    AVAIL --> COVERAGE
```

## 📋 Additional Architecture Diagrams

For more detailed architectural views, explore these comprehensive diagram collections:

### **Network Topology**
- **[Complete Network Topology](diagrams/network-topology.md)** - Detailed network layout with all subnets and components
- **[Data Flow Architecture](diagrams/network-topology.md#data-flow-architecture)** - How data moves through the system
- **[Security Architecture](diagrams/network-topology.md#security-architecture)** - Defense-in-depth implementation

### **Deployment Architecture**
- **[Complete Deployment Pipeline](diagrams/deployment-flow.md)** - End-to-end deployment process
- **[Component Deployment Sequence](diagrams/deployment-flow.md#component-deployment-sequence)** - Step-by-step deployment flow
- **[Multi-Platform Options](diagrams/deployment-flow.md#multi-platform-deployment-options)** - Platform-specific deployment paths

### **Component Interactions**
- **[Component Ecosystem](diagrams/component-interactions.md)** - How all components work together
- **[Attack Detection Workflow](diagrams/component-interactions.md#attack-detection-workflow)** - Real-time detection process
- **[Health Monitoring](diagrams/component-interactions.md#component-health-monitoring)** - System health and monitoring

## 🎯 Architecture Principles

### **Design Philosophy**

GOAD-Blue architecture follows these core principles:

1. **🔧 Modularity** - Components can be deployed independently
2. **📈 Scalability** - Horizontal and vertical scaling support
3. **🔒 Security** - Defense-in-depth with network segmentation
4. **🔄 Reliability** - High availability and fault tolerance
5. **🎛️ Observability** - Comprehensive monitoring and logging
6. **🔗 Integration** - Seamless GOAD environment integration

### **Performance Considerations**

- **Resource Optimization** - Efficient resource utilization across components
- **Network Efficiency** - Optimized data flow and minimal latency
- **Storage Management** - Tiered storage with automated lifecycle management
- **Processing Power** - Distributed processing for high-volume environments

### **Security Architecture**

- **Zero Trust Model** - Verify every connection and transaction
- **Network Segmentation** - Isolated security zones with controlled access
- **Encryption Everywhere** - Data protection at rest and in transit
- **Least Privilege** - Minimal access rights for all components
- **Continuous Monitoring** - Real-time security posture assessment

---

!!! info "Implementation Details"
    For specific implementation details of each component, refer to the [Components](components/) section of the documentation.

!!! tip "Deployment Guidance"
    Check out the [Deployment](deployment/) section for detailed deployment instructions and best practices.

!!! note "Interactive Diagrams"
    All Mermaid diagrams in this documentation are interactive. You can zoom, pan, and click on elements for better visibility.
