[all:vars]
; This is the global inventory file, data here will override all lab or provider inventory datas
; modify this to add layouts to VMs
; https://learn.microsoft.com/en-us/windows-hardware/manufacture/desktop/windows-language-pack-default-values
; French  : 0000040C
; US      : 00000409
; German  : 00000407
; Spanish : 0000040A
; the first in the list will be the default layout (here: FR | US)
keyboard_layouts=["0000040C", "00000409"]

; Uncoment to not use SSL in ansible (usefull if you get Digest initialization failed: initialization error with vagrant)
# ansible_winrm_transport=basic
# ansible_port=5985

; modify this to add a default route
add_route=no
route_gateway=192.168.56.1
route_network=10.0.0.0/8

; modify this to enable http proxy
enable_http_proxy=no
ad_http_proxy=http://x.x.x.x:xxxx
ad_https_proxy=http://x.x.x.x:xxxx

; dns server fallback forwarder
;dns_server_forwarder=1.1.1.1