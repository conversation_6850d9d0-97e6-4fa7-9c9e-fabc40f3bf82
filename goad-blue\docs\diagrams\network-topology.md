# GOAD-Blue Network Topology Diagrams

## Complete Network Topology

```mermaid
graph TB
    subgraph "🌐 External Network"
        INTERNET[🌍 Internet]
        THREAT_FEEDS[🎯 Threat Intelligence Feeds<br/>• MISP Communities<br/>• Commercial Feeds<br/>• OSINT Sources]
        UPDATES[📦 Software Updates<br/>• OS Updates<br/>• Security Patches<br/>• Tool Updates]
        VPN_ENDPOINT[🔐 VPN Endpoint<br/>Remote Access]
    end
    
    subgraph "🏢 GOAD-Blue Management Network (192.168.100.0/26)"
        direction TB
        FIREWALL[🛡️ Firewall/Router<br/>192.168.100.1<br/>Gateway & Security]
        
        SPLUNK[📊 Splunk Enterprise<br/>192.168.100.10<br/>• SIEM Platform<br/>• 8GB RAM, 4 vCPU<br/>• 100GB Storage]
        
        ELASTIC[🔍 Elastic Stack<br/>192.168.100.11<br/>• Alternative SIEM<br/>• 8GB RAM, 4 vCPU<br/>• 100GB Storage]
        
        MISP_SERVER[🧠 MISP Platform<br/>192.168.100.30<br/>• Threat Intelligence<br/>• 4GB RAM, 2 vCPU<br/>• 50GB Storage]
        
        ADMIN_WS[👤 Admin Workstation<br/>192.168.100.5<br/>• Management Access<br/>• 4GB RAM, 2 vCPU]
        
        BACKUP_SRV[💾 Backup Server<br/>192.168.100.15<br/>• Data Protection<br/>• 2GB RAM, 2 vCPU<br/>• 500GB Storage]
    end
    
    subgraph "🔍 Monitoring Network (192.168.100.64/26)"
        direction TB
        SO_MANAGER[🧅 Security Onion Manager<br/>192.168.100.70<br/>• Central Management<br/>• 16GB RAM, 8 vCPU<br/>• 200GB Storage]
        
        SO_SENSOR1[📡 Security Onion Sensor 1<br/>192.168.100.71<br/>• Forward Node<br/>• 8GB RAM, 4 vCPU<br/>• 100GB Storage]
        
        SO_SENSOR2[📡 Security Onion Sensor 2<br/>192.168.100.72<br/>• Search Node<br/>• 16GB RAM, 8 vCPU<br/>• 200GB Storage]
        
        MALCOLM[🕵️ Malcolm Platform<br/>192.168.100.80<br/>• Network Forensics<br/>• 8GB RAM, 4 vCPU<br/>• 200GB Storage]
        
        VELOCIRAPTOR[🦖 Velociraptor Server<br/>192.168.100.85<br/>• Endpoint Visibility<br/>• 4GB RAM, 2 vCPU<br/>• 50GB Storage]
    end
    
    subgraph "🎯 GOAD Production Network (192.168.100.128/26)"
        direction TB
        DC_KINGSLANDING[🏰 Kingslanding DC<br/>192.168.100.140<br/>• sevenkingdoms.local<br/>• Windows Server 2019<br/>• 4GB RAM, 2 vCPU]
        
        DC_WINTERFELL[❄️ Winterfell DC<br/>192.168.100.141<br/>• north.sevenkingdoms.local<br/>• Windows Server 2019<br/>• 4GB RAM, 2 vCPU]
        
        DC_MEEREEN[🐉 Meereen DC<br/>192.168.100.142<br/>• essos.local<br/>• Windows Server 2019<br/>• 4GB RAM, 2 vCPU]
        
        SRV_CASTELBLACK[⚔️ Castelblack<br/>192.168.100.150<br/>• File Server<br/>• Windows Server 2019<br/>• 4GB RAM, 2 vCPU]
        
        SRV_BRAAVOS[🏛️ Braavos<br/>192.168.100.151<br/>• Web Server<br/>• Windows Server 2019<br/>• 4GB RAM, 2 vCPU]
        
        WS_TYRELL[🌹 Tyrell Workstation<br/>192.168.100.160<br/>• User Workstation<br/>• Windows 10<br/>• 4GB RAM, 2 vCPU]
    end
    
    subgraph "🔬 Analysis Network (192.168.100.192/26)"
        direction TB
        FLARE_VM[🔥 FLARE-VM<br/>192.168.100.200<br/>• Malware Analysis<br/>• Windows 10<br/>• 8GB RAM, 4 vCPU<br/>• 100GB Storage]
        
        SANDBOX[📦 Analysis Sandbox<br/>192.168.100.201<br/>• Isolated Testing<br/>• Ubuntu 22.04<br/>• 4GB RAM, 2 vCPU]
        
        ISOLATED_NET[🔒 Isolated Environment<br/>192.168.100.202<br/>• Air-Gapped Analysis<br/>• No Internet Access]
    end
    
    %% External Connections
    INTERNET --> FIREWALL
    THREAT_FEEDS --> MISP_SERVER
    UPDATES --> ADMIN_WS
    VPN_ENDPOINT --> FIREWALL
    
    %% Management Network Internal
    FIREWALL --> SPLUNK
    FIREWALL --> ELASTIC
    FIREWALL --> MISP_SERVER
    FIREWALL --> ADMIN_WS
    FIREWALL --> BACKUP_SRV
    
    %% Cross-Network Management
    SPLUNK <--> SO_MANAGER
    SPLUNK <--> VELOCIRAPTOR
    ELASTIC <--> SO_MANAGER
    MISP_SERVER <--> SPLUNK
    ADMIN_WS --> SO_MANAGER
    ADMIN_WS --> VELOCIRAPTOR
    
    %% Monitoring Network Internal
    SO_MANAGER --> SO_SENSOR1
    SO_MANAGER --> SO_SENSOR2
    SO_SENSOR1 <--> MALCOLM
    SO_SENSOR2 <--> MALCOLM
    
    %% Monitoring to Production (Data Collection)
    SO_SENSOR1 -.->|Traffic Monitoring| DC_KINGSLANDING
    SO_SENSOR1 -.->|Traffic Monitoring| DC_WINTERFELL
    SO_SENSOR2 -.->|Traffic Monitoring| SRV_CASTELBLACK
    SO_SENSOR2 -.->|Traffic Monitoring| SRV_BRAAVOS
    
    VELOCIRAPTOR <-->|Agent Communication| DC_KINGSLANDING
    VELOCIRAPTOR <-->|Agent Communication| DC_WINTERFELL
    VELOCIRAPTOR <-->|Agent Communication| DC_MEEREEN
    VELOCIRAPTOR <-->|Agent Communication| SRV_CASTELBLACK
    VELOCIRAPTOR <-->|Agent Communication| SRV_BRAAVOS
    VELOCIRAPTOR <-->|Agent Communication| WS_TYRELL
    
    %% Log Forwarding to SIEM
    DC_KINGSLANDING -->|Logs| SPLUNK
    DC_WINTERFELL -->|Logs| SPLUNK
    DC_MEEREEN -->|Logs| SPLUNK
    SRV_CASTELBLACK -->|Logs| SPLUNK
    SRV_BRAAVOS -->|Logs| SPLUNK
    WS_TYRELL -->|Logs| SPLUNK
    
    %% Production Network Internal (GOAD Traffic)
    DC_KINGSLANDING <--> DC_WINTERFELL
    DC_KINGSLANDING <--> DC_MEEREEN
    DC_KINGSLANDING <--> SRV_CASTELBLACK
    DC_WINTERFELL <--> SRV_BRAAVOS
    WS_TYRELL <--> DC_KINGSLANDING
    
    %% Analysis Network (Limited Connectivity)
    FLARE_VM -.->|Controlled Internet| FIREWALL
    SANDBOX -.-> ISOLATED_NET
    
    %% Styling
    classDef external fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef management fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef monitoring fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    classDef production fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    classDef analysis fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    
    class INTERNET,THREAT_FEEDS,UPDATES,VPN_ENDPOINT external
    class FIREWALL,SPLUNK,ELASTIC,MISP_SERVER,ADMIN_WS,BACKUP_SRV management
    class SO_MANAGER,SO_SENSOR1,SO_SENSOR2,MALCOLM,VELOCIRAPTOR monitoring
    class DC_KINGSLANDING,DC_WINTERFELL,DC_MEEREEN,SRV_CASTELBLACK,SRV_BRAAVOS,WS_TYRELL production
    class FLARE_VM,SANDBOX,ISOLATED_NET analysis
```

## Data Flow Architecture

```mermaid
flowchart TD
    subgraph "🎯 Attack Simulation (GOAD)"
        ATTACKER[👨‍💻 Red Team Operator]
        ATTACK_TOOLS[⚔️ Attack Tools<br/>• PowerView<br/>• Mimikatz<br/>• BloodHound]
        TARGET_SYSTEMS[🎯 Target Systems<br/>• Domain Controllers<br/>• Member Servers<br/>• Workstations]
    end
    
    subgraph "📊 Data Collection Layer"
        NETWORK_TAPS[🌐 Network Monitoring<br/>• SPAN Ports<br/>• Traffic Mirroring<br/>• Packet Capture]
        
        ENDPOINT_AGENTS[💻 Endpoint Agents<br/>• Sysmon<br/>• Velociraptor<br/>• Windows Event Log]
        
        LOG_FORWARDERS[📤 Log Forwarders<br/>• Splunk UF<br/>• Elastic Beats<br/>• Custom Collectors]
    end
    
    subgraph "⚙️ Processing & Analysis"
        NETWORK_ANALYSIS[🔍 Network Analysis<br/>• Suricata IDS<br/>• Zeek NSM<br/>• Protocol Analysis]
        
        LOG_PROCESSING[📋 Log Processing<br/>• Parsing<br/>• Normalization<br/>• Enrichment]
        
        CORRELATION[🔗 Event Correlation<br/>• Multi-source<br/>• Timeline Analysis<br/>• Pattern Matching]
    end
    
    subgraph "🧠 Intelligence & Detection"
        THREAT_INTEL[🎯 Threat Intelligence<br/>• IOC Matching<br/>• TTP Correlation<br/>• Attribution]
        
        DETECTION_RULES[📏 Detection Rules<br/>• Signature-based<br/>• Behavioral<br/>• ML/Analytics]
        
        ALERT_GENERATION[🚨 Alert Generation<br/>• Severity Scoring<br/>• Context Addition<br/>• Deduplication]
    end
    
    subgraph "📊 Visualization & Response"
        DASHBOARDS[📈 Dashboards<br/>• Real-time Views<br/>• Executive Reports<br/>• Operational Metrics]
        
        INVESTIGATION[🔍 Investigation<br/>• Timeline Analysis<br/>• Pivot Searches<br/>• Evidence Collection]
        
        RESPONSE[🛡️ Response Actions<br/>• Containment<br/>• Remediation<br/>• Documentation]
    end
    
    %% Attack Flow
    ATTACKER --> ATTACK_TOOLS
    ATTACK_TOOLS --> TARGET_SYSTEMS
    
    %% Data Collection
    TARGET_SYSTEMS --> NETWORK_TAPS
    TARGET_SYSTEMS --> ENDPOINT_AGENTS
    TARGET_SYSTEMS --> LOG_FORWARDERS
    
    %% Processing Flow
    NETWORK_TAPS --> NETWORK_ANALYSIS
    ENDPOINT_AGENTS --> LOG_PROCESSING
    LOG_FORWARDERS --> LOG_PROCESSING
    
    NETWORK_ANALYSIS --> CORRELATION
    LOG_PROCESSING --> CORRELATION
    
    %% Intelligence & Detection
    CORRELATION --> THREAT_INTEL
    CORRELATION --> DETECTION_RULES
    THREAT_INTEL --> ALERT_GENERATION
    DETECTION_RULES --> ALERT_GENERATION
    
    %% Visualization & Response
    ALERT_GENERATION --> DASHBOARDS
    ALERT_GENERATION --> INVESTIGATION
    INVESTIGATION --> RESPONSE
    
    %% Feedback Loops
    RESPONSE -.-> DETECTION_RULES
    INVESTIGATION -.-> THREAT_INTEL
    
    %% Styling
    classDef attack fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef collection fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef processing fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef intelligence fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef response fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class ATTACKER,ATTACK_TOOLS,TARGET_SYSTEMS attack
    class NETWORK_TAPS,ENDPOINT_AGENTS,LOG_FORWARDERS collection
    class NETWORK_ANALYSIS,LOG_PROCESSING,CORRELATION processing
    class THREAT_INTEL,DETECTION_RULES,ALERT_GENERATION intelligence
    class DASHBOARDS,INVESTIGATION,RESPONSE response
```

## Security Architecture

```mermaid
graph TB
    subgraph "🛡️ Defense in Depth Layers"
        
        subgraph "🌐 Perimeter Security"
            FIREWALL_RULES[🔥 Firewall Rules<br/>• Network Segmentation<br/>• Access Control<br/>• Traffic Filtering]
            IPS[🛡️ Intrusion Prevention<br/>• Signature Detection<br/>• Anomaly Detection<br/>• Automatic Blocking]
            VPN_SECURITY[🔐 VPN Security<br/>• Multi-factor Auth<br/>• Certificate-based<br/>• Split Tunneling]
        end
        
        subgraph "🌐 Network Security"
            NETWORK_SEGMENTATION[🏗️ Network Segmentation<br/>• VLAN Isolation<br/>• Subnet Separation<br/>• Micro-segmentation]
            TRAFFIC_MONITORING[📊 Traffic Monitoring<br/>• Flow Analysis<br/>• Bandwidth Monitoring<br/>• Anomaly Detection]
            DNS_SECURITY[🌐 DNS Security<br/>• DNS Filtering<br/>• Sinkholing<br/>• DGA Detection]
        end
        
        subgraph "💻 Host Security"
            ENDPOINT_PROTECTION[🛡️ Endpoint Protection<br/>• Antivirus/EDR<br/>• Application Control<br/>• Device Control]
            HOST_MONITORING[👁️ Host Monitoring<br/>• Process Monitoring<br/>• File Integrity<br/>• Registry Monitoring]
            PATCH_MANAGEMENT[🔧 Patch Management<br/>• Vulnerability Scanning<br/>• Update Deployment<br/>• Compliance Tracking]
        end
        
        subgraph "📊 Application Security"
            ACCESS_CONTROL[🔐 Access Control<br/>• Authentication<br/>• Authorization<br/>• Session Management]
            DATA_PROTECTION[🔒 Data Protection<br/>• Encryption at Rest<br/>• Encryption in Transit<br/>• Data Classification]
            AUDIT_LOGGING[📝 Audit Logging<br/>• Activity Logging<br/>• Compliance Reporting<br/>• Forensic Analysis]
        end
        
    end
    
    %% Security Flow
    FIREWALL_RULES --> NETWORK_SEGMENTATION
    IPS --> TRAFFIC_MONITORING
    VPN_SECURITY --> DNS_SECURITY
    
    NETWORK_SEGMENTATION --> ENDPOINT_PROTECTION
    TRAFFIC_MONITORING --> HOST_MONITORING
    DNS_SECURITY --> PATCH_MANAGEMENT
    
    ENDPOINT_PROTECTION --> ACCESS_CONTROL
    HOST_MONITORING --> DATA_PROTECTION
    PATCH_MANAGEMENT --> AUDIT_LOGGING
    
    %% Feedback and Coordination
    AUDIT_LOGGING -.-> FIREWALL_RULES
    DATA_PROTECTION -.-> IPS
    ACCESS_CONTROL -.-> VPN_SECURITY
    
    %% Styling
    classDef perimeter fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef network fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef host fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef application fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    
    class FIREWALL_RULES,IPS,VPN_SECURITY perimeter
    class NETWORK_SEGMENTATION,TRAFFIC_MONITORING,DNS_SECURITY network
    class ENDPOINT_PROTECTION,HOST_MONITORING,PATCH_MANAGEMENT host
    class ACCESS_CONTROL,DATA_PROTECTION,AUDIT_LOGGING application
```
