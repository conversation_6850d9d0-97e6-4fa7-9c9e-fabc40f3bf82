# Endpoint Detection and Response

Endpoint Detection and Response (EDR) is a critical component of GOAD-Blue, providing comprehensive visibility into endpoint activities, detecting advanced threats, and enabling rapid incident response across the GOAD environment.

## 🎯 Overview

GOAD-Blue's endpoint monitoring capabilities provide deep visibility into system activities, process execution, file operations, and network connections across all endpoints in the GOAD environment.

```mermaid
graph TB
    subgraph "🖥️ Endpoint Monitoring Architecture"
        AGENTS[🤖 Endpoint Agents<br/>Lightweight Collectors<br/>Real-time Monitoring]
        COLLECTION[📊 Data Collection<br/>Process Events<br/>File Operations<br/>Network Activity]
        ANALYSIS[🧠 Behavioral Analysis<br/>Anomaly Detection<br/>Threat Hunting]
        RESPONSE[🚨 Automated Response<br/>Isolation & Remediation<br/>Threat Containment]
    end
    
    subgraph "🛡️ Detection Engines"
        VELOCIRAPTOR[🦖 Velociraptor<br/>Digital Forensics<br/>Incident Response]
        WAZUH[🛡️ Wazuh<br/>Host-based IDS<br/>Log Analysis]
        OSQUERY[🔍 OSQuery<br/>SQL-based Queries<br/>System Introspection]
        SYSMON[👁️ Sysmon<br/>Windows Monitoring<br/>Enhanced Logging]
    end
    
    subgraph "📊 Analytics & Intelligence"
        HUNTING[🔍 Threat Hunting<br/>Proactive Detection<br/>IOC Matching]
        FORENSICS[🕵️ Digital Forensics<br/>Evidence Collection<br/>Timeline Analysis]
        INTELLIGENCE[🧠 Threat Intelligence<br/>IOC Correlation<br/>Attribution]
        AUTOMATION[🤖 SOAR Integration<br/>Automated Workflows<br/>Response Orchestration]
    end
    
    subgraph "🎮 GOAD Endpoints"
        DC[👑 Domain Controllers<br/>Critical Infrastructure<br/>Authentication Services]
        SERVERS[⚔️ Member Servers<br/>File Servers<br/>Application Servers]
        WORKSTATIONS[🖥️ Workstations<br/>User Endpoints<br/>Admin Systems]
        LINUX[🐧 Linux Systems<br/>Security Tools<br/>Management Systems]
    end
    
    DC --> AGENTS
    SERVERS --> AGENTS
    WORKSTATIONS --> AGENTS
    LINUX --> AGENTS
    
    AGENTS --> COLLECTION
    COLLECTION --> VELOCIRAPTOR
    COLLECTION --> WAZUH
    COLLECTION --> OSQUERY
    COLLECTION --> SYSMON
    
    VELOCIRAPTOR --> ANALYSIS
    WAZUH --> ANALYSIS
    OSQUERY --> HUNTING
    SYSMON --> FORENSICS
    
    ANALYSIS --> HUNTING
    ANALYSIS --> FORENSICS
    ANALYSIS --> INTELLIGENCE
    ANALYSIS --> AUTOMATION
    
    HUNTING --> RESPONSE
    FORENSICS --> RESPONSE
    INTELLIGENCE --> RESPONSE
    AUTOMATION --> RESPONSE
    
    classDef endpoint fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef detection fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef analytics fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef goad fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class AGENTS,COLLECTION,ANALYSIS,RESPONSE endpoint
    class VELOCIRAPTOR,WAZUH,OSQUERY,SYSMON detection
    class HUNTING,FORENSICS,INTELLIGENCE,AUTOMATION analytics
    class DC,SERVERS,WORKSTATIONS,LINUX goad
```

## 🔧 Core Components

### **[Velociraptor](velociraptor.md)**
Advanced digital forensics and incident response platform providing comprehensive endpoint visibility and rapid response capabilities.

**Key Features:**
- **Real-time Monitoring** - Continuous endpoint surveillance
- **Digital Forensics** - Evidence collection and analysis
- **Threat Hunting** - Proactive threat detection
- **Incident Response** - Automated containment and remediation
- **Artifact Collection** - Comprehensive system artifacts

### **[Wazuh](wazuh.md)**
Open-source security monitoring platform providing host-based intrusion detection, log analysis, and compliance monitoring.

**Key Features:**
- **Host-based IDS** - Real-time intrusion detection
- **Log Analysis** - Centralized log collection and analysis
- **File Integrity Monitoring** - Critical file change detection
- **Vulnerability Assessment** - System vulnerability scanning
- **Compliance Monitoring** - Regulatory compliance checks

### **[OSQuery](osquery.md)**
SQL-based operating system instrumentation framework for security monitoring, compliance, and system administration.

**Key Features:**
- **SQL Interface** - Query system information using SQL
- **Cross-platform** - Windows, Linux, and macOS support
- **Real-time Queries** - Live system interrogation
- **Historical Analysis** - Time-based system state analysis
- **Custom Extensions** - Extensible plugin architecture

### **[Sysmon](sysmon.md)**
Windows system monitoring tool providing detailed information about process creation, network connections, and file operations.

**Key Features:**
- **Process Monitoring** - Detailed process creation logging
- **Network Monitoring** - Network connection tracking
- **File Operations** - File creation and modification tracking
- **Registry Monitoring** - Registry key and value changes
- **Image/DLL Loading** - Module loading detection

## 📊 Endpoint Monitoring Capabilities

### **Process Monitoring**

```sql
-- OSQuery: Monitor process creation
SELECT 
    datetime(time, 'unixepoch') as event_time,
    pid,
    name,
    path,
    cmdline,
    parent,
    uid
FROM process_events 
WHERE time > (strftime('%s', 'now') - 3600)
ORDER BY time DESC;

-- Detect suspicious process execution
SELECT 
    p.pid,
    p.name,
    p.path,
    p.cmdline,
    pp.name as parent_name
FROM processes p
JOIN processes pp ON p.parent = pp.pid
WHERE p.path LIKE '%temp%' 
   OR p.path LIKE '%public%'
   OR p.cmdline LIKE '%powershell%'
   OR p.cmdline LIKE '%cmd%';
```

### **File System Monitoring**

```yaml
# Velociraptor artifact for file monitoring
name: Windows.Events.FileCreation
description: Monitor file creation events in suspicious locations

sources:
  - precondition: SELECT OS From info() where OS = 'windows'
    
    query: |
      SELECT 
        timestamp(epoch=EventTime) AS EventTime,
        Computer,
        EventData.Image as ProcessName,
        EventData.TargetFilename as FileName,
        EventData.ProcessId as PID,
        EventData.User as User
      FROM watch_etw(guid="{5770385F-C22A-43E0-BF4C-06F5698FFBD9}")
      WHERE EventID = 11
        AND (EventData.TargetFilename =~ ".*\\\\Windows\\\\Temp\\\\.*"
          OR EventData.TargetFilename =~ ".*\\\\Users\\\\Public\\\\.*"
          OR EventData.TargetFilename =~ ".*\\\\ProgramData\\\\.*")
```

### **Network Connection Monitoring**

```bash
# Wazuh rule for network monitoring
<rule id="100001" level="5">
  <if_sid>18101</if_sid>
  <field name="win.eventdata.destinationPort">445|139|3389</field>
  <description>SMB/RDP connection detected</description>
  <group>lateral_movement,</group>
</rule>

<rule id="100002" level="10">
  <if_sid>100001</if_sid>
  <field name="win.eventdata.sourceIp">!192.168.56.</field>
  <description>External SMB/RDP connection attempt</description>
  <group>intrusion_attempt,</group>
</rule>
```

## 🔍 Threat Hunting Capabilities

### **Advanced Persistent Threat Detection**

```sql
-- Hunt for living-off-the-land techniques
SELECT 
    p.pid,
    p.name,
    p.cmdline,
    p.parent,
    pp.name as parent_name,
    f.path as file_path
FROM processes p
JOIN processes pp ON p.parent = pp.pid
LEFT JOIN file_events f ON p.pid = f.pid
WHERE (p.name IN ('powershell.exe', 'cmd.exe', 'wmic.exe', 'rundll32.exe')
   AND p.cmdline LIKE '%bypass%')
   OR (p.name = 'certutil.exe' AND p.cmdline LIKE '%download%')
   OR (p.name = 'bitsadmin.exe' AND p.cmdline LIKE '%transfer%');
```

### **Credential Harvesting Detection**

```yaml
# Velociraptor hunt for credential access
name: Hunt.CredentialAccess
description: Detect credential harvesting activities

parameters:
  - name: ProcessRegex
    default: "(mimikatz|procdump|lsass)"
    
  - name: CommandLineRegex
    default: "(sekurlsa|logonpasswords|dcsync)"

sources:
  - query: |
      SELECT 
        Fqdn,
        timestamp(epoch=EventTime) AS EventTime,
        EventData.Image as ProcessPath,
        EventData.CommandLine as CommandLine,
        EventData.ProcessId as PID,
        EventData.User as User
      FROM Artifact.Windows.EventLogs.Sysmon()
      WHERE EventID = 1
        AND (EventData.Image =~ ProcessRegex
          OR EventData.CommandLine =~ CommandLineRegex)
```

### **Lateral Movement Detection**

```python
# Custom detection script for lateral movement
import json
import requests
from datetime import datetime, timedelta

def detect_lateral_movement():
    """Detect lateral movement patterns across endpoints"""
    
    # Query for authentication events across multiple systems
    query = """
    SELECT 
        source_ip,
        dest_hostname,
        user_name,
        COUNT(*) as connection_count,
        COUNT(DISTINCT dest_hostname) as unique_targets
    FROM authentication_events 
    WHERE event_time > datetime('now', '-1 hour')
      AND event_type = 'successful_login'
      AND logon_type = 3  -- Network logon
    GROUP BY source_ip, user_name
    HAVING unique_targets > 3
    ORDER BY unique_targets DESC;
    """
    
    # Execute query via Velociraptor API
    response = requests.post(
        "https://velociraptor.goad-blue.local/api/v1/hunts",
        headers={"Authorization": "Bearer YOUR_API_TOKEN"},
        json={
            "artifacts": ["Custom.LateralMovementDetection"],
            "condition": "label(client_id) =~ 'GOAD'"
        }
    )
    
    return response.json()

def analyze_process_injection():
    """Detect process injection techniques"""
    
    injection_query = """
    SELECT 
        Computer,
        EventTime,
        EventData.SourceImage as SourceProcess,
        EventData.TargetImage as TargetProcess,
        EventData.SourceProcessId as SourcePID,
        EventData.TargetProcessId as TargetPID
    FROM Artifact.Windows.EventLogs.Sysmon()
    WHERE EventID = 8  -- Process injection
      AND EventTime > now() - 3600
    ORDER BY EventTime DESC;
    """
    
    return execute_velociraptor_query(injection_query)
```

## 🚨 Automated Response Capabilities

### **Threat Containment**

```yaml
# Velociraptor server artifact for automated response
name: Server.Utils.AutomatedResponse
description: Automated threat response workflows

parameters:
  - name: ThreatLevel
    default: "HIGH"
    
  - name: ResponseAction
    default: "ISOLATE"

sources:
  - query: |
      -- Monitor for high-severity alerts
      SELECT * FROM watch_monitoring(
        artifact="Server.Monitor.Health"
      ) WHERE Severity = "HIGH"
      
  - query: |
      -- Execute response action
      SELECT hunt(
        artifacts=["Windows.Remediation.Quarantine"],
        condition=format(string="client_id = '%s'", args=ClientId)
      ) FROM scope()
      WHERE ResponseAction = "ISOLATE"
```

### **Evidence Collection**

```python
# Automated evidence collection script
class IncidentResponseCollector:
    def __init__(self, velociraptor_client):
        self.client = velociraptor_client
        
    def collect_evidence(self, client_id, incident_type):
        """Collect evidence based on incident type"""
        
        artifacts = []
        
        if incident_type == "malware":
            artifacts.extend([
                "Windows.Forensics.ProcessMemory",
                "Windows.Forensics.FilenameSearch",
                "Windows.System.Handles",
                "Windows.Network.Netstat"
            ])
            
        elif incident_type == "lateral_movement":
            artifacts.extend([
                "Windows.EventLogs.RDPAuth",
                "Windows.EventLogs.SMBFiles", 
                "Windows.System.Services",
                "Windows.Registry.RecentDocs"
            ])
            
        elif incident_type == "data_exfiltration":
            artifacts.extend([
                "Windows.Network.PacketCapture",
                "Windows.Forensics.USN",
                "Windows.System.Pslist",
                "Windows.Forensics.Timeline"
            ])
            
        # Execute collection
        hunt_id = self.client.create_hunt(
            artifacts=artifacts,
            condition=f"client_id = '{client_id}'"
        )
        
        return hunt_id
        
    def analyze_collected_data(self, hunt_id):
        """Analyze collected evidence"""
        
        results = self.client.get_hunt_results(hunt_id)
        
        analysis = {
            "timeline": self.build_timeline(results),
            "iocs": self.extract_iocs(results),
            "recommendations": self.generate_recommendations(results)
        }
        
        return analysis
```

## 📈 Performance and Scalability

### **Agent Performance Monitoring**

```bash
# Monitor Velociraptor client performance
velociraptor --config client.config.yaml query \
  "SELECT * FROM info() WHERE OS = 'windows'"

# Check agent resource usage
velociraptor --config client.config.yaml query \
  "SELECT pid, name, resident_size, user_time, system_time 
   FROM processes() 
   WHERE name = 'velociraptor.exe'"

# Monitor collection queue
velociraptor --config client.config.yaml query \
  "SELECT * FROM monitoring() WHERE Component = 'Client'"
```

### **Scalability Configuration**

```yaml
# Velociraptor server configuration for scale
version: *******
Client:
  server_urls:
    - https://velociraptor-01.goad-blue.local:8000/
    - https://velociraptor-02.goad-blue.local:8000/
  
  max_poll: 600
  max_poll_std: 30
  
Frontend:
  bind_address: 0.0.0.0
  bind_port: 8000
  
  # Load balancing
  proxy_header: "X-Forwarded-For"
  
Datastore:
  implementation: MySQL
  mysql_connection_string: "velociraptor:password@tcp(mysql.goad-blue.local:3306)/velociraptor"
  
  # Performance tuning
  mysql_max_open_conns: 100
  mysql_max_idle_conns: 10
```

## 🎓 Training Scenarios

### **Endpoint Security Fundamentals**

1. **Agent Deployment and Management**
   - Mass deployment strategies
   - Configuration management
   - Health monitoring and troubleshooting

2. **Basic Threat Detection**
   - Process monitoring and analysis
   - File system surveillance
   - Network connection tracking

3. **Incident Response Basics**
   - Evidence collection procedures
   - Timeline reconstruction
   - Containment strategies

### **Advanced Endpoint Security**

1. **Advanced Threat Hunting**
   - Custom query development
   - Behavioral analysis techniques
   - IOC development and deployment

2. **Digital Forensics**
   - Memory analysis
   - Disk forensics
   - Network forensics

3. **Automation and Integration**
   - SOAR platform integration
   - Custom artifact development
   - Automated response workflows

## 🔗 Integration Points

### **SIEM Integration**

```yaml
# Logstash configuration for endpoint data
input {
  http {
    port => 8080
    codec => "json"
    type => "velociraptor"
  }
  
  beats {
    port => 5044
    type => "wazuh"
  }
}

filter {
  if [type] == "velociraptor" {
    mutate {
      add_field => { "data_source" => "endpoint" }
      add_field => { "tool" => "velociraptor" }
    }
    
    # Parse Velociraptor artifacts
    if [artifact] == "Windows.EventLogs.Sysmon" {
      mutate { add_field => { "event_category" => "sysmon" } }
    }
  }
  
  if [type] == "wazuh" {
    mutate {
      add_field => { "data_source" => "endpoint" }
      add_field => { "tool" => "wazuh" }
    }
  }
}

output {
  elasticsearch {
    hosts => ["goad-blue-elasticsearch:9200"]
    index => "goad-blue-endpoint-%{+YYYY.MM.dd}"
  }
}
```

### **Threat Intelligence Integration**

```python
# MISP integration for IOC enrichment
from pymisp import PyMISP
import velociraptor_api

def enrich_with_threat_intel(artifact_data):
    """Enrich endpoint artifacts with threat intelligence"""
    
    misp = PyMISP('https://misp.goad-blue.local', 'YOUR_API_KEY')
    
    # Extract IOCs from artifact data
    iocs = extract_iocs(artifact_data)
    
    enriched_data = []
    for ioc in iocs:
        # Search MISP for IOC
        misp_result = misp.search(value=ioc['value'], type_attribute=ioc['type'])
        
        if misp_result:
            ioc['threat_intel'] = {
                'threat_level': misp_result[0].get('threat_level_id'),
                'tags': [tag.name for tag in misp_result[0].tags],
                'first_seen': misp_result[0].get('timestamp'),
                'info': misp_result[0].get('info')
            }
            
        enriched_data.append(ioc)
    
    return enriched_data

def extract_iocs(artifact_data):
    """Extract IOCs from Velociraptor artifact data"""
    iocs = []
    
    # Extract file hashes
    if 'hash' in artifact_data:
        iocs.append({
            'type': 'sha256',
            'value': artifact_data['hash']['sha256'],
            'context': 'file_hash'
        })
    
    # Extract IP addresses
    if 'network_connections' in artifact_data:
        for conn in artifact_data['network_connections']:
            iocs.append({
                'type': 'ip-dst',
                'value': conn['remote_addr'],
                'context': 'network_connection'
            })
    
    return iocs
```

## 📊 Metrics and KPIs

### **Endpoint Security Metrics**

```yaml
# Key performance indicators for endpoint security
endpoint_security_kpis:
  coverage_metrics:
    - agent_deployment_percentage
    - endpoint_visibility_score
    - data_collection_completeness
    - response_capability_coverage
    
  detection_metrics:
    - threat_detection_rate
    - false_positive_rate
    - mean_time_to_detection
    - alert_investigation_time
    
  response_metrics:
    - mean_time_to_containment
    - incident_resolution_time
    - automated_response_success_rate
    - evidence_collection_completeness
    
  operational_metrics:
    - agent_performance_impact
    - data_storage_efficiency
    - query_response_time
    - system_availability
```

---

!!! tip "Endpoint Security Best Practices"
    - Deploy agents across all critical systems
    - Implement layered detection with multiple tools
    - Regularly update detection rules and signatures
    - Practice incident response procedures
    - Monitor agent performance and health

!!! warning "Performance Impact"
    Endpoint monitoring can impact system performance. Monitor resource usage and tune configurations appropriately for your environment.

!!! info "Privacy Considerations"
    Ensure endpoint monitoring complies with privacy regulations and organizational policies. Implement appropriate data retention and access controls.
