---
# GOAD-Blue Integration with existing GOAD environment
- name: Integrate GOAD-Blue with GOAD Environment
  hosts: localhost
  gather_facts: false
  vars_files:
    - vars/goad-blue-config.yml
  
  tasks:
    - name: Display integration banner
      debug:
        msg: |
          ====================================================
          🔗 GOAD-Blue Integration Starting 🔗
          ====================================================
          Integrating with existing GOAD environment...
          ====================================================

    - name: Discover GOAD instances
      include_tasks: tasks/discover_goad_instances.yml

    - name: Validate GOAD instances
      include_tasks: tasks/validate_goad_instances.yml

# Deploy monitoring agents to GOAD VMs
- name: Deploy Sysmon to GOAD Windows VMs
  hosts: goad_windows
  vars:
    sysmon_config_url: "https://raw.githubusercontent.com/SwiftOnSecurity/sysmon-config/master/sysmonconfig-export.xml"
  
  tasks:
    - name: Install Chocolatey
      win_chocolatey:
        name: chocolatey
        state: present

    - name: Install Sysmon via Chocolatey
      win_chocolatey:
        name: sysmon
        state: present

    - name: Download GOAD-Blue Sysmon configuration
      win_get_url:
        url: "{{ sysmon_config_url }}"
        dest: "C:\\Windows\\goad-blue-sysmonconfig.xml"

    - name: Apply GOAD-Blue Sysmon configuration
      win_command: sysmon -accepteula -i C:\Windows\goad-blue-sysmonconfig.xml
      args:
        creates: C:\Windows\SysmonDrv.sys

    - name: Configure Windows Event Log forwarding
      win_template:
        src: windows/wef-subscription.xml.j2
        dest: "C:\\Windows\\Temp\\goad-blue-subscription.xml"

# Deploy SIEM agents based on configuration
- name: Deploy Splunk Universal Forwarders
  hosts: goad_windows
  when: goad_blue_config.siem.type == "splunk" and goad_blue_config.siem.enabled
  vars:
    splunk_forwarder_url: "{{ hostvars[groups['splunk_servers'][0]]['ansible_default_ipv4']['address'] }}:9997"
    splunk_admin_password: "{{ vault_splunk_admin_password | default('ChangeMePlease123!') }}"
  
  tasks:
    - name: Download Splunk Universal Forwarder
      win_get_url:
        url: "https://download.splunk.com/products/universalforwarder/releases/9.1.2/windows/splunkforwarder-9.1.2-b6b9c8185839-x64-release.msi"
        dest: "C:\\temp\\splunkforwarder.msi"

    - name: Install Splunk Universal Forwarder
      win_package:
        path: "C:\\temp\\splunkforwarder.msi"
        arguments: 
          - RECEIVING_INDEXER="{{ splunk_forwarder_url }}"
          - WINEVENTLOG_APP_ENABLE=1
          - WINEVENTLOG_SEC_ENABLE=1
          - WINEVENTLOG_SYS_ENABLE=1
          - AGREETOLICENSE=Yes
          - LAUNCHSPLUNK=1
        state: present

    - name: Configure Splunk forwarder inputs
      win_template:
        src: splunk/inputs.conf.j2
        dest: "C:\\Program Files\\SplunkUniversalForwarder\\etc\\system\\local\\inputs.conf"
      notify: restart splunk forwarder

    - name: Configure Splunk forwarder outputs
      win_template:
        src: splunk/outputs.conf.j2
        dest: "C:\\Program Files\\SplunkUniversalForwarder\\etc\\system\\local\\outputs.conf"
      notify: restart splunk forwarder

  handlers:
    - name: restart splunk forwarder
      win_service:
        name: SplunkForwarder
        state: restarted

- name: Deploy Elastic Beats
  hosts: goad_windows
  when: goad_blue_config.siem.type == "elastic" and goad_blue_config.siem.enabled
  vars:
    elasticsearch_host: "{{ hostvars[groups['elastic_servers'][0]]['ansible_default_ipv4']['address'] }}"
  
  tasks:
    - name: Download Winlogbeat
      win_get_url:
        url: "https://artifacts.elastic.co/downloads/beats/winlogbeat/winlogbeat-8.11.0-windows-x86_64.zip"
        dest: "C:\\temp\\winlogbeat.zip"

    - name: Extract Winlogbeat
      win_unzip:
        src: "C:\\temp\\winlogbeat.zip"
        dest: "C:\\Program Files\\"
        creates: "C:\\Program Files\\winlogbeat-8.11.0-windows-x86_64"

    - name: Configure Winlogbeat
      win_template:
        src: beats/winlogbeat.yml.j2
        dest: "C:\\Program Files\\winlogbeat-8.11.0-windows-x86_64\\winlogbeat.yml"

    - name: Install Winlogbeat service
      win_command: |
        C:\Program Files\winlogbeat-8.11.0-windows-x86_64\winlogbeat.exe --path.home "C:\Program Files\winlogbeat-8.11.0-windows-x86_64" --path.config "C:\Program Files\winlogbeat-8.11.0-windows-x86_64" --path.data "C:\ProgramData\winlogbeat" --path.logs "C:\ProgramData\winlogbeat\logs" -c "C:\Program Files\winlogbeat-8.11.0-windows-x86_64\winlogbeat.yml" install
      args:
        creates: C:\ProgramData\winlogbeat

    - name: Start Winlogbeat service
      win_service:
        name: winlogbeat
        state: started
        start_mode: auto

# Deploy Velociraptor agents if enabled
- name: Deploy Velociraptor Agents
  hosts: goad_windows
  when: goad_blue_config.components.velociraptor.enabled
  vars:
    velociraptor_server: "{{ hostvars[groups['velociraptor_servers'][0]]['ansible_default_ipv4']['address'] }}"
  
  tasks:
    - name: Download Velociraptor client
      win_get_url:
        url: "https://{{ velociraptor_server }}:8000/api/v1/DownloadClient?os=windows"
        dest: "C:\\temp\\velociraptor-client.exe"
        validate_certs: no

    - name: Download Velociraptor client config
      win_get_url:
        url: "https://{{ velociraptor_server }}:8000/api/v1/DownloadClientConfig"
        dest: "C:\\temp\\client.config.yaml"
        validate_certs: no

    - name: Install Velociraptor service
      win_command: |
        C:\temp\velociraptor-client.exe --config C:\temp\client.config.yaml service install
      args:
        creates: C:\Program Files\Velociraptor

    - name: Start Velociraptor service
      win_service:
        name: Velociraptor
        state: started
        start_mode: auto

# Configure network monitoring
- name: Configure Network Monitoring
  hosts: localhost
  gather_facts: false
  vars_files:
    - vars/goad-blue-config.yml
  
  tasks:
    - name: Configure Security Onion for GOAD network monitoring
      include_tasks: tasks/configure_security_onion_goad.yml
      when: goad_blue_config.components.security_onion.enabled

    - name: Configure Malcolm for GOAD network monitoring
      include_tasks: tasks/configure_malcolm_goad.yml
      when: goad_blue_config.components.malcolm.enabled

# Final integration tasks
- name: Finalize GOAD Integration
  hosts: localhost
  gather_facts: false
  vars_files:
    - vars/goad-blue-config.yml
  
  tasks:
    - name: Create GOAD-specific detection rules
      include_tasks: tasks/create_goad_detection_rules.yml

    - name: Configure threat intelligence feeds
      include_tasks: tasks/configure_threat_intel_feeds.yml
      when: goad_blue_config.components.misp.enabled

    - name: Validate integration
      include_tasks: tasks/validate_goad_integration.yml

    - name: Generate integration report
      template:
        src: reports/integration_report.html.j2
        dest: "{{ playbook_dir }}/output/goad_integration_report.html"

    - name: Display integration completion
      debug:
        msg: |
          ====================================================
          ✅ GOAD-Blue Integration Complete! ✅
          ====================================================
          
          Integrated Components:
          {% for instance in discovered_goad_instances %}
          - {{ instance.id }}: {{ instance.status }}
          {% endfor %}
          
          Monitoring Agents Deployed:
          - Sysmon: ✅
          {% if goad_blue_config.siem.enabled %}
          - {{ goad_blue_config.siem.type | title }} Agent: ✅
          {% endif %}
          {% if goad_blue_config.components.velociraptor.enabled %}
          - Velociraptor Agent: ✅
          {% endif %}
          
          Integration report: {{ playbook_dir }}/output/goad_integration_report.html
          ====================================================
