---
# Install MISP dependencies

- name: Update package cache (Debian/Ubuntu)
  apt:
    update_cache: yes
    cache_valid_time: 3600
  when: ansible_os_family == "Debian"

- name: Update package cache (RedHat/CentOS)
  yum:
    update_cache: yes
  when: ansible_os_family == "RedHat"

- name: Install system dependencies (Debian/Ubuntu)
  apt:
    name:
      - curl
      - gcc
      - git
      - gnupg-agent
      - make
      - python3
      - python3-dev
      - python3-pip
      - python3-venv
      - software-properties-common
      - sudo
      - vim
      - zip
      - unzip
      - wget
      - build-essential
      - libxml2-dev
      - libxslt1-dev
      - zlib1g-dev
      - libffi-dev
      - libssl-dev
      - libpq-dev
      - pkg-config
      - libjpeg-dev
      - libfreetype6-dev
      - libpng-dev
      - mariadb-client
      - mariadb-server
      - apache2
      - apache2-doc
      - apache2-utils
      - libapache2-mod-php
      - php
      - php-cli
      - php-dev
      - php-json
      - php-xml
      - php-mysql
      - php-opcache
      - php-readline
      - php-mbstring
      - php-zip
      - php-intl
      - php-bcmath
      - php-gd
      - php-redis
      - php-gnupg
      - redis-server
      - ssdeep
      - libfuzzy-dev
      - clamav
      - clamav-daemon
      - clamav-freshclam
    state: present
  when: ansible_os_family == "Debian"

- name: Install system dependencies (RedHat/CentOS)
  yum:
    name:
      - curl
      - gcc
      - git
      - make
      - python3
      - python3-devel
      - python3-pip
      - sudo
      - vim
      - zip
      - unzip
      - wget
      - gcc-c++
      - libxml2-devel
      - libxslt-devel
      - zlib-devel
      - libffi-devel
      - openssl-devel
      - postgresql-devel
      - pkgconfig
      - libjpeg-turbo-devel
      - freetype-devel
      - libpng-devel
      - mariadb
      - mariadb-server
      - httpd
      - php
      - php-cli
      - php-devel
      - php-json
      - php-xml
      - php-mysqlnd
      - php-opcache
      - php-mbstring
      - php-zip
      - php-intl
      - php-bcmath
      - php-gd
      - php-redis
      - redis
      - ssdeep
      - ssdeep-devel
      - clamav
      - clamav-update
    state: present
  when: ansible_os_family == "RedHat"

- name: Install EPEL repository (RedHat/CentOS)
  yum:
    name: epel-release
    state: present
  when: ansible_os_family == "RedHat"

- name: Install Remi repository (RedHat/CentOS)
  yum:
    name: "https://rpms.remirepo.net/enterprise/remi-release-{{ ansible_distribution_major_version }}.rpm"
    state: present
  when: ansible_os_family == "RedHat"

- name: Enable PHP 8.1 module (RedHat/CentOS 8+)
  shell: dnf module enable php:remi-8.1 -y
  when: 
    - ansible_os_family == "RedHat"
    - ansible_distribution_major_version|int >= 8

- name: Install Python dependencies
  pip:
    name:
      - virtualenv
      - setuptools
      - wheel
      - pyzmq
      - redis
      - python-magic
      - lief
      - pydeep2
      - python-socketio
      - flask-socketio
    state: present
    executable: pip3

- name: Install Node.js repository (Debian/Ubuntu)
  shell: |
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
  when: ansible_os_family == "Debian"

- name: Install Node.js repository (RedHat/CentOS)
  shell: |
    curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
  when: ansible_os_family == "RedHat"

- name: Install Node.js
  package:
    name: nodejs
    state: present

- name: Install global npm packages
  npm:
    name: "{{ item }}"
    global: yes
  loop:
    - npm
    - grunt-cli
    - bower

- name: Create MISP user
  user:
    name: misp
    system: yes
    shell: /bin/bash
    home: /var/www/MISP
    create_home: no

- name: Add MISP user to www-data group (Debian/Ubuntu)
  user:
    name: misp
    groups: www-data
    append: yes
  when: ansible_os_family == "Debian"

- name: Add MISP user to apache group (RedHat/CentOS)
  user:
    name: misp
    groups: apache
    append: yes
  when: ansible_os_family == "RedHat"

- name: Install composer
  shell: |
    curl -sS https://getcomposer.org/installer | php
    mv composer.phar /usr/local/bin/composer
    chmod +x /usr/local/bin/composer
  args:
    creates: /usr/local/bin/composer

- name: Verify composer installation
  command: composer --version
  register: composer_version
  changed_when: false

- name: Display composer version
  debug:
    msg: "Composer version: {{ composer_version.stdout }}"
