# GOAD-Blue Documentation Requirements
# Install with: pip install -r requirements-docs.txt

# Core MkDocs
mkdocs>=1.5.0
mkdocs-material>=9.4.0

# Essential Plugins
mkdocs-git-revision-date-localized-plugin>=1.2.0
mkdocs-minify-plugin>=0.7.0

# Extensions
pymdown-extensions>=10.0.0
markdown-include>=0.8.0

# Optional but recommended plugins
mkdocs-awesome-pages-plugin>=2.9.0
mkdocs-redirects>=1.2.0
mkdocs-macros-plugin>=1.0.0

# Additional useful plugins (optional)
# mkdocs-mermaid2-plugin>=0.6.0  # Alternative Mermaid support
# mkdocs-pdf-export-plugin>=0.5.0  # PDF export capability
# mkdocs-table-reader-plugin>=2.0.0  # CSV/Excel table support

# VM and Infrastructure Dependencies Documentation
# These are documented here but installed separately on VMs

# Python packages for VM automation
# ansible>=6.0.0
# ansible-core>=2.13.0
# pywinrm>=0.4.3
# requests>=2.28.0
# pyyaml>=6.0
# jinja2>=3.1.0
# netaddr>=0.8.0
# dnspython>=2.2.0

# VM Provisioning Tools (install separately)
# vagrant>=2.3.0
# virtualbox>=7.0.0
# vmware-workstation>=17.0.0
# docker>=24.0.0
# docker-compose>=2.20.0
# packer>=1.9.0
# terraform>=1.5.0
