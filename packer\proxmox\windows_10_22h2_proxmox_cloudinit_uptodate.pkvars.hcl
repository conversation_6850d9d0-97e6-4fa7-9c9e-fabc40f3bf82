winrm_username        = "vagrant"
winrm_password        = "vagrant"
vm_name               = "Windows10x64-22h2-cloudinit-qcow2-uptodate"
template_description  = "Windows 10 - 22h2 - 64-bit - template built with <PERSON><PERSON> - {{isotime \"2006-01-02 03:04:05\"}}"
iso_file              = "local:iso/Windows-10-22h2_x64_en-us.iso"
autounattend_iso      = "./iso/Autounattend_windows10_cloudinit_uptodate.iso"
autounattend_checksum = "sha256:bb5a28744077fd0121a04d5955f0b2f7a25d8aa13a1548a7223a4c2e2f1aed61"
vm_cpu_cores          = "2"
vm_memory             = "4096"
vm_disk_size          = "80G"
vm_sockets            = "1"
os                    = "win10"
vm_disk_format        = "qcow2"