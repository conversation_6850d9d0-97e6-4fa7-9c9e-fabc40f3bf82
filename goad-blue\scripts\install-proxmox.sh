#!/bin/bash
# GOAD-Blue Proxmox Installation Script
# This script automates the deployment of GOAD-Blue on Proxmox VE

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Default values
PROXMOX_HOST=""
PROXMOX_USER="root@pam"
PROXMOX_PASSWORD=""
PROXMOX_NODE="pve"
SSH_KEY_PATH="$HOME/.ssh/id_rsa.pub"
CONFIG_FILE="$PROJECT_ROOT/terraform/environments/proxmox/terraform.tfvars"

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_usage() {
    cat << EOF
GOAD-Blue Proxmox Installation Script

Usage: $0 [OPTIONS]

Options:
    -h, --host PROXMOX_HOST     Proxmox host IP or FQDN (required)
    -u, --user USERNAME         Proxmox username (default: root@pam)
    -p, --password PASSWORD     Proxmox password (will prompt if not provided)
    -n, --node NODE_NAME        Proxmox node name (default: pve)
    -k, --ssh-key PATH          SSH public key path (default: ~/.ssh/id_rsa.pub)
    -c, --config FILE           Configuration file path
    --minimal                   Deploy minimal configuration
    --help                      Show this help message

Examples:
    $0 --host ************* --password mypassword
    $0 -h proxmox.example.com -u admin@pve -p secret123 -n pve-node1
    $0 --host ************* --minimal

EOF
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if running on Linux/macOS
    if [[ "$OSTYPE" != "linux-gnu"* ]] && [[ "$OSTYPE" != "darwin"* ]]; then
        log_error "This script requires Linux or macOS"
        exit 1
    fi
    
    # Check required commands
    local required_commands=("terraform" "ansible" "ssh" "curl")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            log_error "Required command not found: $cmd"
            log_info "Please install $cmd and try again"
            exit 1
        fi
    done
    
    # Check SSH key
    if [[ ! -f "$SSH_KEY_PATH" ]]; then
        log_error "SSH public key not found: $SSH_KEY_PATH"
        log_info "Generate SSH key with: ssh-keygen -t rsa -b 4096"
        exit 1
    fi
    
    # Check Python and pip
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is required"
        exit 1
    fi
    
    # Install Python dependencies
    if ! python3 -c "import proxmoxer" &> /dev/null; then
        log_info "Installing Python dependencies..."
        pip3 install proxmoxer requests
    fi
    
    log_success "Prerequisites check completed"
}

test_proxmox_connection() {
    log_info "Testing Proxmox connection..."
    
    # Test API connectivity
    local api_url="https://${PROXMOX_HOST}:8006/api2/json"
    
    if ! curl -k -s --connect-timeout 10 "$api_url" > /dev/null; then
        log_error "Cannot connect to Proxmox API: $api_url"
        log_info "Please check:"
        log_info "  - Proxmox host is reachable"
        log_info "  - Port 8006 is open"
        log_info "  - Proxmox web interface is running"
        exit 1
    fi
    
    # Test authentication
    local auth_response
    auth_response=$(curl -k -s -d "username=${PROXMOX_USER}&password=${PROXMOX_PASSWORD}" \
        "${api_url}/access/ticket" | python3 -c "import sys, json; print(json.load(sys.stdin).get('data', {}).get('ticket', ''))" 2>/dev/null)
    
    if [[ -z "$auth_response" ]]; then
        log_error "Proxmox authentication failed"
        log_info "Please check username and password"
        exit 1
    fi
    
    log_success "Proxmox connection successful"
}

check_proxmox_requirements() {
    log_info "Checking Proxmox requirements..."
    
    # Check if templates exist
    log_info "Checking for required VM templates..."
    
    # This would require proxmoxer to check templates
    # For now, just warn the user
    log_warning "Please ensure the following templates exist:"
    log_warning "  - ubuntu-22.04-template"
    log_warning "  - windows-10-template (if using FLARE-VM)"
    log_warning ""
    log_warning "Create templates with: $PROJECT_ROOT/scripts/create-proxmox-templates.sh"
    
    log_success "Proxmox requirements check completed"
}

create_terraform_config() {
    log_info "Creating Terraform configuration..."
    
    local config_dir="$PROJECT_ROOT/terraform/environments/proxmox"
    
    # Create terraform.tfvars from example
    if [[ ! -f "$CONFIG_FILE" ]]; then
        cp "$config_dir/terraform.tfvars.example" "$CONFIG_FILE"
        
        # Update configuration with provided values
        sed -i.bak \
            -e "s|proxmox_api_url.*=.*|proxmox_api_url = \"https://${PROXMOX_HOST}:8006/api2/json\"|" \
            -e "s|proxmox_user.*=.*|proxmox_user = \"${PROXMOX_USER}\"|" \
            -e "s|proxmox_password.*=.*|proxmox_password = \"${PROXMOX_PASSWORD}\"|" \
            -e "s|proxmox_node.*=.*|proxmox_node = \"${PROXMOX_NODE}\"|" \
            -e "s|ssh_public_key_path.*=.*|ssh_public_key_path = \"${SSH_KEY_PATH}\"|" \
            "$CONFIG_FILE"
        
        rm -f "${CONFIG_FILE}.bak"
        
        log_success "Configuration file created: $CONFIG_FILE"
    else
        log_info "Using existing configuration: $CONFIG_FILE"
    fi
}

deploy_infrastructure() {
    log_info "Deploying infrastructure with Terraform..."
    
    cd "$PROJECT_ROOT/terraform/environments/proxmox"
    
    # Initialize Terraform
    log_info "Initializing Terraform..."
    terraform init
    
    # Plan deployment
    log_info "Planning deployment..."
    terraform plan -var-file="terraform.tfvars"
    
    # Ask for confirmation
    echo ""
    read -p "Do you want to proceed with the deployment? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Deployment cancelled"
        exit 0
    fi
    
    # Apply configuration
    log_info "Applying Terraform configuration..."
    terraform apply -var-file="terraform.tfvars" -auto-approve
    
    log_success "Infrastructure deployment completed"
}

configure_systems() {
    log_info "Configuring systems with Ansible..."
    
    cd "$PROJECT_ROOT/ansible"
    
    # Generate dynamic inventory
    log_info "Generating Ansible inventory..."
    python3 inventory/proxmox_inventory.py > inventory/proxmox_hosts.yml
    
    # Run Ansible playbooks
    log_info "Running Ansible configuration..."
    ansible-playbook -i inventory/proxmox_hosts.yml playbooks/site.yml
    
    log_success "System configuration completed"
}

show_deployment_info() {
    log_success "GOAD-Blue deployment completed successfully!"
    echo ""
    log_info "Access Information:"
    
    # Get output from Terraform
    cd "$PROJECT_ROOT/terraform/environments/proxmox"
    
    local splunk_ip
    local so_ip
    local velo_ip
    local misp_ip
    
    splunk_ip=$(terraform output -raw splunk_ip 2>/dev/null || echo "N/A")
    so_ip=$(terraform output -raw security_onion_ip 2>/dev/null || echo "N/A")
    velo_ip=$(terraform output -raw velociraptor_ip 2>/dev/null || echo "N/A")
    misp_ip=$(terraform output -raw misp_ip 2>/dev/null || echo "N/A")
    
    echo ""
    echo "🔗 Web Interfaces:"
    [[ "$splunk_ip" != "N/A" ]] && echo "  📊 Splunk:           https://$splunk_ip:8000"
    [[ "$so_ip" != "N/A" ]] && echo "  🧅 Security Onion:   https://$so_ip"
    [[ "$velo_ip" != "N/A" ]] && echo "  🦖 Velociraptor:     https://$velo_ip:8889"
    [[ "$misp_ip" != "N/A" ]] && echo "  🧠 MISP:             https://$misp_ip"
    echo ""
    echo "📚 Documentation: https://goad-blue.readthedocs.io"
    echo "🐛 Issues: https://github.com/your-org/goad-blue/issues"
    echo ""
    log_info "Default credentials are in the documentation"
    log_warning "Remember to change default passwords!"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--host)
            PROXMOX_HOST="$2"
            shift 2
            ;;
        -u|--user)
            PROXMOX_USER="$2"
            shift 2
            ;;
        -p|--password)
            PROXMOX_PASSWORD="$2"
            shift 2
            ;;
        -n|--node)
            PROXMOX_NODE="$2"
            shift 2
            ;;
        -k|--ssh-key)
            SSH_KEY_PATH="$2"
            shift 2
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        --minimal)
            MINIMAL_DEPLOYMENT=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$PROXMOX_HOST" ]]; then
    log_error "Proxmox host is required"
    show_usage
    exit 1
fi

# Prompt for password if not provided
if [[ -z "$PROXMOX_PASSWORD" ]]; then
    echo -n "Enter Proxmox password for $PROXMOX_USER: "
    read -s PROXMOX_PASSWORD
    echo ""
fi

# Main execution
main() {
    log_info "Starting GOAD-Blue Proxmox deployment..."
    log_info "Target: $PROXMOX_USER@$PROXMOX_HOST (node: $PROXMOX_NODE)"
    echo ""
    
    check_prerequisites
    test_proxmox_connection
    check_proxmox_requirements
    create_terraform_config
    deploy_infrastructure
    configure_systems
    show_deployment_info
    
    log_success "Deployment completed successfully!"
}

# Run main function
main "$@"
