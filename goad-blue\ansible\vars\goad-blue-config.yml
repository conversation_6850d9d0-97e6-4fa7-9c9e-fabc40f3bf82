---
# GOAD-Blue Main Configuration Variables

# Global Configuration
goad_blue_config:
  version: "1.0.0"
  environment: "lab"  # lab, production, training
  domain: "goad-blue.local"
  
  # Network Configuration
  network:
    management:
      subnet: "192.168.1.0/24"
      gateway: "192.168.1.1"
      dns: ["192.168.1.1", "8.8.8.8"]
    
    siem:
      subnet: "192.168.100.0/26"
      gateway: "192.168.100.1"
      dns: ["192.168.1.1"]
    
    monitoring:
      subnet: "192.168.100.64/26"
      gateway: "192.168.100.65"
      dns: ["192.168.1.1"]
    
    analysis:
      subnet: "192.168.100.128/26"
      gateway: "192.168.100.129"
      dns: ["192.168.1.1"]
    
    goad:
      subnet: "192.168.56.0/24"
      gateway: "192.168.56.1"
      dns: ["*************"]
  
  # SIEM Configuration
  siem:
    type: "splunk"  # splunk, elastic
    version: "9.1.2"
    admin_user: "admin"
    admin_password: "{{ vault_siem_admin_password }}"
    license_file: "/opt/goad-blue/licenses/splunk.lic"
    
    # Splunk specific
    splunk:
      indexer_cluster: false
      search_head_cluster: false
      deployment_server: true
      universal_forwarder: true
    
    # Elastic specific
    elastic:
      elasticsearch_nodes: 3
      kibana_enabled: true
      logstash_enabled: true
      beats_enabled: true
  
  # Security Monitoring
  security_monitoring:
    security_onion:
      enabled: true
      version: "2.3.240"
      mode: "standalone"  # standalone, distributed
      monitor_interfaces: ["eth1", "eth2"]
    
    malcolm:
      enabled: false
      version: "6.5.0"
      pcap_processing: true
      zeek_enabled: true
      suricata_enabled: true
    
    velociraptor:
      enabled: true
      version: "0.7.0"
      server_url: "https://192.168.100.85:8889"
      client_auto_deploy: true
    
    misp:
      enabled: true
      version: "2.4.170"
      feeds_enabled: true
      auto_sync: true
  
  # Analysis Environment
  analysis:
    flare_vm:
      enabled: true
      version: "2023.1"
      analysis_user: "analyst"
      network_isolation: true
      snapshot_management: true
    
    remnux:
      enabled: false
      version: "7.0"
      docker_enabled: true
  
  # GOAD Integration
  goad:
    path: "/opt/goad"
    provider: "vmware"  # vmware, virtualbox, proxmox
    domain: "sevenkingdoms.local"
    dc_ip: "*************"
    dc_user: "Administrator"
    dc_password: "{{ vault_goad_admin_password }}"
    
    # Agent deployment
    agent_deployment:
      windows_agents: true
      linux_agents: true
      auto_discovery: true
      deployment_method: "ansible"  # ansible, gpo, manual
  
  # SSL/TLS Configuration
  ssl:
    ca_cert_path: "/opt/goad-blue/ssl/ca.crt"
    ca_key_path: "/opt/goad-blue/ssl/ca.key"
    cert_validity_days: 365
    
    # Certificate subjects
    ca_subject: "/C=US/ST=Lab/L=Lab/O=GOAD-Blue/CN=GOAD-Blue-CA"
    server_subject: "/C=US/ST=Lab/L=Lab/O=GOAD-Blue/CN=goad-blue.local"
  
  # Backup Configuration
  backup:
    enabled: true
    schedule: "0 2 * * *"  # Daily at 2 AM
    retention_days: 30
    backup_path: "/opt/goad-blue/backups"
    
    components:
      - siem_configs
      - security_onion_configs
      - velociraptor_configs
      - misp_data
      - ssl_certificates
  
  # Logging Configuration
  logging:
    level: "INFO"  # DEBUG, INFO, WARNING, ERROR
    syslog_enabled: true
    syslog_server: "**************"
    log_retention_days: 90
    
    # Component logging
    components:
      splunk:
        log_level: "INFO"
        max_log_size: "100MB"
        log_rotation: true
      
      velociraptor:
        log_level: "INFO"
        audit_logging: true
      
      security_onion:
        log_level: "INFO"
        sensor_logging: true

# Deployment Configuration
deployment:
  mode: "full"  # minimal, standard, full
  parallel_deployment: true
  validation_enabled: true
  rollback_enabled: true
  
  # Resource allocation
  resources:
    minimal:
      total_memory: "32GB"
      total_cpu: "8 cores"
      total_storage: "500GB"
    
    standard:
      total_memory: "64GB"
      total_cpu: "16 cores"
      total_storage: "1TB"
    
    full:
      total_memory: "128GB"
      total_cpu: "32 cores"
      total_storage: "2TB"

# Training Configuration
training:
  scenarios_enabled: true
  data_generation: true
  automated_validation: true
  reporting_enabled: true
  
  # Available scenarios
  scenarios:
    - kerberoasting
    - lateral_movement
    - malware_analysis
    - apt_simulation
    - incident_response
  
  # Training data
  data_generation:
    duration: "24h"
    event_rate: "moderate"  # low, moderate, high
    realistic_timing: true

# Monitoring and Alerting
monitoring:
  health_checks: true
  performance_monitoring: true
  capacity_monitoring: true
  
  # Alert thresholds
  thresholds:
    cpu_usage: 80
    memory_usage: 85
    disk_usage: 90
    log_ingestion_rate: 1000  # events per second
  
  # Notification channels
  notifications:
    email:
      enabled: true
      smtp_server: "localhost"
      admin_email: "<EMAIL>"
    
    webhook:
      enabled: false
      url: "https://hooks.slack.com/services/..."
    
    syslog:
      enabled: true
      server: "**************"
      facility: "local0"
