"""
GOAD-Blue Team Manager
Orchestrates blue team component deployment and management
"""

import os
import sys
import time
from pathlib import Path
from goad.log import Log
from goad_blue.components.siem_manager import SiemManager
from goad_blue.components.monitoring_manager import MonitoringManager
from goad_blue.components.analysis_manager import AnalysisManager
from goad_blue.integration.goad_integration import GOADIntegration


class BlueTeamManager:
    """Main manager for GOAD-Blue components"""
    
    def __init__(self):
        self.config = None
        self.siem_manager = None
        self.monitoring_manager = None
        self.analysis_manager = None
        self.goad_integration = None
        self.current_instance_id = ""
    
    def init(self, config, args):
        """Initialize the blue team manager"""
        self.config = config
        
        # Initialize component managers
        self.siem_manager = SiemManager(config)
        self.monitoring_manager = MonitoringManager(config)
        self.analysis_manager = AnalysisManager(config)
        self.goad_integration = GOADIntegration(config)
        
        Log.info("GOAD-Blue Team Manager initialized")
        return self
    
    def load_default_configuration(self):
        """Load default configuration"""
        Log.info("Loading default GOAD-Blue configuration")
    
    def show_components(self):
        """Show available and enabled components"""
        print("\n🔧 Available Components:")
        print("-" * 30)
        
        # SIEM
        siem_type = self.config.get_value('siem', 'type', 'none')
        siem_enabled = self.config.get_value('siem', 'enabled', False)
        print(f"SIEM ({siem_type}): {'✅ Enabled' if siem_enabled else '❌ Disabled'}")
        
        # Other components
        components = self.config.config['components']
        for name, config in components.items():
            status = "✅ Enabled" if config.get('enabled', False) else "❌ Disabled"
            print(f"{name.replace('_', ' ').title()}: {status}")
    
    def interactive_configure(self):
        """Interactive configuration of components"""
        Log.info("Starting interactive configuration...")
        
        # SIEM Selection
        print("\n🔎 SIEM Configuration:")
        siem_options = ["splunk", "elastic", "none"]
        print("Available SIEM options:")
        for i, option in enumerate(siem_options, 1):
            print(f"  {i}. {option.title()}")
        
        while True:
            try:
                choice = input("Select SIEM [1-3]: ").strip()
                if choice in ['1', '2', '3']:
                    siem_type = siem_options[int(choice) - 1]
                    self.config.set_value('siem', 'type', siem_type)
                    self.config.set_value('siem', 'enabled', siem_type != 'none')
                    Log.success(f"SIEM set to: {siem_type}")
                    break
                else:
                    print("Invalid choice. Please enter 1, 2, or 3.")
            except (ValueError, KeyboardInterrupt):
                print("Configuration cancelled.")
                return
        
        # Component Selection
        print("\n🔧 Component Configuration:")
        components = {
            'security_onion': 'Security Onion (Suricata, Zeek, Wazuh)',
            'malcolm': 'Malcolm (Network Forensics)',
            'velociraptor': 'Velociraptor (Endpoint Visibility)',
            'misp': 'MISP (Threat Intelligence)',
            'flare_vm': 'FLARE-VM (Malware Analysis)'
        }
        
        for component, description in components.items():
            current_status = self.config.is_component_enabled(component)
            default = "Y" if current_status else "N"
            
            while True:
                response = input(f"Enable {description}? [Y/n] (current: {default}): ").strip().lower()
                if response in ['', 'y', 'yes']:
                    self.config.enable_component(component)
                    break
                elif response in ['n', 'no']:
                    self.config.disable_component(component)
                    break
                else:
                    print("Please answer Y or N.")
        
        # Save configuration
        self.config.save_config()
        Log.success("Configuration saved successfully")
    
    def set_siem(self, siem_type):
        """Set SIEM type"""
        allowed_siems = ['splunk', 'elastic', 'none']
        if siem_type not in allowed_siems:
            raise ValueError(f"Invalid SIEM type. Allowed: {', '.join(allowed_siems)}")
        
        self.config.set_value('siem', 'type', siem_type)
        self.config.set_value('siem', 'enabled', siem_type != 'none')
        Log.success(f"SIEM set to: {siem_type}")
    
    def enable_component(self, component):
        """Enable a component"""
        if self.config.enable_component(component):
            Log.success(f"Component '{component}' enabled")
        else:
            Log.error(f"Unknown component: {component}")
            self.list_available_components()
    
    def disable_component(self, component):
        """Disable a component"""
        if self.config.disable_component(component):
            Log.success(f"Component '{component}' disabled")
        else:
            Log.error(f"Unknown component: {component}")
    
    def list_available_components(self):
        """List available components"""
        print("Available components:")
        for component in self.config.config['components'].keys():
            print(f"  - {component}")
    
    def show_configuration(self):
        """Show current configuration"""
        self.config.show()
    
    def install_all_components(self):
        """Install all enabled components"""
        Log.info("Starting installation of all enabled components...")
        
        success = True
        
        # Install SIEM first
        if self.config.get_value('siem', 'enabled', False):
            Log.info("Installing SIEM...")
            if not self.install_siem():
                success = False
        
        # Install monitoring components
        if not self.install_monitoring_components():
            success = False
        
        # Install analysis components
        if not self.install_analysis_components():
            success = False
        
        return success
    
    def install_siem(self):
        """Install SIEM component"""
        siem_type = self.config.get_value('siem', 'type')
        if siem_type and siem_type != 'none':
            return self.siem_manager.install(siem_type)
        return True
    
    def install_monitoring_components(self):
        """Install monitoring components"""
        success = True
        
        if self.config.is_component_enabled('security_onion'):
            Log.info("Installing Security Onion...")
            if not self.monitoring_manager.install_security_onion():
                success = False
        
        if self.config.is_component_enabled('malcolm'):
            Log.info("Installing Malcolm...")
            if not self.monitoring_manager.install_malcolm():
                success = False
        
        if self.config.is_component_enabled('velociraptor'):
            Log.info("Installing Velociraptor...")
            if not self.monitoring_manager.install_velociraptor():
                success = False
        
        return success
    
    def install_analysis_components(self):
        """Install analysis components"""
        success = True
        
        if self.config.is_component_enabled('misp'):
            Log.info("Installing MISP...")
            if not self.analysis_manager.install_misp():
                success = False
        
        if self.config.is_component_enabled('flare_vm'):
            Log.info("Installing FLARE-VM...")
            if not self.analysis_manager.install_flare_vm():
                success = False
        
        return success
    
    def show_status(self):
        """Show status of all components"""
        print("\n🛡️  GOAD-Blue Component Status:")
        print("=" * 40)
        
        # SIEM Status
        siem_status = self.siem_manager.get_status()
        print(f"SIEM: {siem_status}")
        
        # Monitoring Status
        monitoring_status = self.monitoring_manager.get_status()
        for component, status in monitoring_status.items():
            print(f"{component}: {status}")
        
        # Analysis Status
        analysis_status = self.analysis_manager.get_status()
        for component, status in analysis_status.items():
            print(f"{component}: {status}")
    
    def start_all_components(self):
        """Start all components"""
        Log.info("Starting all GOAD-Blue components...")
        self.siem_manager.start()
        self.monitoring_manager.start_all()
        self.analysis_manager.start_all()
    
    def stop_all_components(self):
        """Stop all components"""
        Log.info("Stopping all GOAD-Blue components...")
        self.siem_manager.stop()
        self.monitoring_manager.stop_all()
        self.analysis_manager.stop_all()
    
    def integrate_with_goad(self):
        """Integrate with existing GOAD environment"""
        return self.goad_integration.integrate()
    
    def simulate_attack(self, attack_type):
        """Simulate attack for testing detection"""
        Log.info(f"Simulating {attack_type} attack...")
        # This would integrate with GOAD to run specific attacks
        # For now, just log the action
        Log.warning("Attack simulation not yet implemented")
    
    def test_detection_capabilities(self):
        """Test detection capabilities"""
        Log.info("Testing detection capabilities...")
        # This would run a series of tests to validate detection
        Log.warning("Detection testing not yet implemented")
    
    def generate_detection_report(self):
        """Generate detection and analysis report"""
        Log.info("Generating detection report...")
        # This would create a comprehensive report
        report_path = Path(__file__).parent.parent / "reports" / f"detection_report_{int(time.time())}.html"
        Log.warning("Report generation not yet implemented")
        return str(report_path)
    
    def get_current_instance_id(self):
        """Get current instance ID"""
        return self.current_instance_id
    
    def inline_settings(self):
        """Get inline settings string"""
        siem = self.config.get_value('siem', 'type', 'none')
        provider = self.config.get_value('goad_blue', 'provider', 'unknown')
        enabled_count = len(self.config.get_enabled_components())
        return f"GOAD-Blue[{provider}|{siem}|{enabled_count} components]"
