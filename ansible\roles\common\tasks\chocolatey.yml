- name: Ensure chocolatey is installed
  win_chocolatey:
    name:
    - chocolatey
    - chocolatey-core.extension
    state: present

- name: Disable enhanced exit codes
  win_chocolatey_feature:
    name: useEnhancedExitCodes
    state: disabled

- name: Install multiple packages sequentially
  win_chocolatey:
    name: '{{ item }}'
    state: present
  with_items:
  - notepadplusplus
  - putty
  - python
  - git
  - 7zip
  - sysinternals
  - wget
  - pstools
  ignore_errors: yes