terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
    vsphere = {
      source  = "hashicorp/vsphere"
      version = "~> 2.0"
    }
  }
}

# Load GOAD-Blue configuration
locals {
  config_file = "${path.module}/../goad-blue-config.yml"
  goad_blue_config = yamldecode(file(local.config_file))
  
  # Extract configuration values
  provider_type = local.goad_blue_config.goad_blue.provider
  lab_name = local.goad_blue_config.goad_blue.name
  
  # Network configuration
  base_cidr = local.goad_blue_config.network.base_cidr
  siem_subnet = local.goad_blue_config.network.siem_subnet
  monitoring_subnet = local.goad_blue_config.network.monitoring_subnet
  red_team_subnet = local.goad_blue_config.network.red_team_subnet
  analysis_subnet = local.goad_blue_config.network.analysis_subnet
  
  # Component flags
  siem_enabled = local.goad_blue_config.siem.enabled
  siem_type = local.goad_blue_config.siem.type
  security_onion_enabled = local.goad_blue_config.components.security_onion.enabled
  malcolm_enabled = local.goad_blue_config.components.malcolm.enabled
  velociraptor_enabled = local.goad_blue_config.components.velociraptor.enabled
  misp_enabled = local.goad_blue_config.components.misp.enabled
  flare_vm_enabled = local.goad_blue_config.components.flare_vm.enabled
  
  # Common tags
  common_tags = {
    Project = "GOAD-Blue"
    Environment = "lab"
    ManagedBy = "Terraform"
  }
}

# Provider configurations
provider "aws" {
  count  = local.provider_type == "aws" ? 1 : 0
  region = var.aws_region
}

provider "azurerm" {
  count = local.provider_type == "azure" ? 1 : 0
  features {}
}

provider "vsphere" {
  count = local.provider_type == "vmware" ? 1 : 0
  
  user                 = var.vsphere_user
  password             = var.vsphere_password
  vsphere_server       = var.vsphere_server
  allow_unverified_ssl = true
}

# Variables
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

variable "azure_location" {
  description = "Azure location"
  type        = string
  default     = "East US"
}

variable "vsphere_user" {
  description = "vSphere username"
  type        = string
  default     = ""
}

variable "vsphere_password" {
  description = "vSphere password"
  type        = string
  default     = ""
  sensitive   = true
}

variable "vsphere_server" {
  description = "vSphere server"
  type        = string
  default     = ""
}

# Conditional module calls based on provider
module "aws_infrastructure" {
  count  = local.provider_type == "aws" ? 1 : 0
  source = "./modules/aws"
  
  lab_name = local.lab_name
  base_cidr = local.base_cidr
  siem_subnet = local.siem_subnet
  monitoring_subnet = local.monitoring_subnet
  red_team_subnet = local.red_team_subnet
  analysis_subnet = local.analysis_subnet
  
  siem_enabled = local.siem_enabled
  siem_type = local.siem_type
  security_onion_enabled = local.security_onion_enabled
  malcolm_enabled = local.malcolm_enabled
  velociraptor_enabled = local.velociraptor_enabled
  misp_enabled = local.misp_enabled
  flare_vm_enabled = local.flare_vm_enabled
  
  tags = local.common_tags
}

module "azure_infrastructure" {
  count  = local.provider_type == "azure" ? 1 : 0
  source = "./modules/azure"
  
  lab_name = local.lab_name
  location = var.azure_location
  base_cidr = local.base_cidr
  siem_subnet = local.siem_subnet
  monitoring_subnet = local.monitoring_subnet
  red_team_subnet = local.red_team_subnet
  analysis_subnet = local.analysis_subnet
  
  siem_enabled = local.siem_enabled
  siem_type = local.siem_type
  security_onion_enabled = local.security_onion_enabled
  malcolm_enabled = local.malcolm_enabled
  velociraptor_enabled = local.velociraptor_enabled
  misp_enabled = local.misp_enabled
  flare_vm_enabled = local.flare_vm_enabled
  
  tags = local.common_tags
}

module "vmware_infrastructure" {
  count  = local.provider_type == "vmware" ? 1 : 0
  source = "./modules/vmware"
  
  lab_name = local.lab_name
  base_cidr = local.base_cidr
  siem_subnet = local.siem_subnet
  monitoring_subnet = local.monitoring_subnet
  red_team_subnet = local.red_team_subnet
  analysis_subnet = local.analysis_subnet
  
  siem_enabled = local.siem_enabled
  siem_type = local.siem_type
  security_onion_enabled = local.security_onion_enabled
  malcolm_enabled = local.malcolm_enabled
  velociraptor_enabled = local.velociraptor_enabled
  misp_enabled = local.misp_enabled
  flare_vm_enabled = local.flare_vm_enabled
}

# Outputs
output "lab_info" {
  description = "GOAD-Blue lab information"
  value = {
    lab_name = local.lab_name
    provider = local.provider_type
    siem_type = local.siem_type
    enabled_components = [
      for component, config in local.goad_blue_config.components :
      component if config.enabled
    ]
  }
}

output "network_info" {
  description = "Network configuration"
  value = {
    base_cidr = local.base_cidr
    siem_subnet = local.siem_subnet
    monitoring_subnet = local.monitoring_subnet
    red_team_subnet = local.red_team_subnet
    analysis_subnet = local.analysis_subnet
  }
}

output "access_urls" {
  description = "Access URLs for GOAD-Blue components"
  value = local.provider_type == "aws" ? (
    length(module.aws_infrastructure) > 0 ? module.aws_infrastructure[0].access_urls : {}
  ) : local.provider_type == "azure" ? (
    length(module.azure_infrastructure) > 0 ? module.azure_infrastructure[0].access_urls : {}
  ) : local.provider_type == "vmware" ? (
    length(module.vmware_infrastructure) > 0 ? module.vmware_infrastructure[0].access_urls : {}
  ) : {}
}
