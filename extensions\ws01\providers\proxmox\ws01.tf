variable "config_ws01_ext" {
  type = map(object({
    name               = string
    desc               = string
    cores              = number
    memory             = number
    clone              = string
    dns                = string
    ip                 = string
    gateway            = string
  }))

  default = {
    "ws01" = {
       name               = "GOAD-WS01"
       desc               = "WS01 - windows 10 - *************"
       cores              = 2
       memory             = 4096
       clone              = "Windows10_22h2_x64"
       dns                = "************"
       ip                 = "*************/24"
       gateway            = "************"
    }
  }
}

locals {
  vm_config = merge(config_ws01_ext, var.vm_config)
}
