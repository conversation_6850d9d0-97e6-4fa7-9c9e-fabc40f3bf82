---
# Load datas
- import_playbook: data.yml
  vars:
    data_path: "../ad/{{domain_name}}/data/"
  tags: 'data'

- name: "Setup vulnerabilities with tasks"
  hosts: domain
  tasks:
    - include_role:
        name: "vulns/{{vuln}}"
      vars:
        vulns_vars : "{{ lab.hosts[dict_key].vulns_vars[vuln] | default({}) }}"
        domain: "{{lab.hosts[dict_key].domain}}"
        domain_username: "{{domain}}\\{{admin_user}}"
        domain_password: "{{lab.domains[domain].domain_password}}"
      loop: "{{lab.hosts[dict_key].vulns | default([]) }}"
      loop_control:
        loop_var: vuln

    - include_role:
        name: "ps"
      vars:
        script_path: "../ad/{{domain_name}}/scripts"
        ps_script: "{{script_path}}/{{item}}"
      loop: "{{lab.hosts[dict_key].scripts | default([]) }}"