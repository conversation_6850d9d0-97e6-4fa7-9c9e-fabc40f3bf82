site_name: GOAD-Blue Documentation
site_description: Full-Spectrum Cybersecurity Training Platform - Blue Team Enhancement for GOAD
site_author: GOAD-Blue Team
site_url: https://goad-blue.readthedocs.io/

# Repository
repo_name: GOAD-Blue
repo_url: https://github.com/your-org/goad-blue
edit_uri: edit/main/docs/

# Copyright
copyright: Copyright &copy; 2024 GOAD-Blue Project

# Configuration
theme:
  name: material
  language: en
  palette:
    # Palette toggle for light mode
    - scheme: default
      primary: blue
      accent: light blue
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    # Palette toggle for dark mode
    - scheme: slate
      primary: blue
      accent: light blue
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
  font:
    text: Roboto
    code: Roboto Mono
  features:
    - navigation.tabs
    - navigation.tabs.sticky
    - navigation.sections
    - navigation.expand
    - navigation.path
    - navigation.indexes
    - toc.follow
    - toc.integrate
    - search.suggest
    - search.highlight
    - search.share
    - content.code.copy
    - content.code.annotate
    - content.tabs.link
    - content.tooltips
    - content.action.edit
    - content.action.view
  icon:
    repo: fontawesome/brands/github
    edit: material/pencil
    view: material/eye
    logo: material/shield-check

# Plugins
plugins:
  - search:
      lang: en
  # - git-revision-date-localized:
      # enable_creation_date: true
  # - minify:
      # minify_html: true
  - tags

# Extensions
markdown_extensions:
  - abbr
  - admonition
  - attr_list
  - def_list
  - footnotes
  - md_in_html
  - toc:
      permalink: true
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.betterem:
      smart_enable: all
  - pymdownx.caret
  - pymdownx.details
  - pymdownx.emoji:
      emoji_generator: !!python/name:materialx.emoji.to_svg
      emoji_index: !!python/name:materialx.emoji.twemoji
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.keys
  - pymdownx.magiclink:
      repo_url_shorthand: true
      user: your-org
      repo: goad-blue
  - pymdownx.mark
  - pymdownx.smartsymbols
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.tilde

# Extra
extra:
  version:
    provider: mike
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/your-org/goad-blue
    - icon: fontawesome/brands/docker
      link: https://hub.docker.com/r/goadblue/
    - icon: fontawesome/brands/twitter
      link: https://twitter.com/goadblue
  tags:
    Installation: installation
    Configuration: configuration
    Components: components
    Training: training
    Troubleshooting: troubleshooting

# Navigation
nav:
  - Home:
    - index.md
    - Overview: overview.md
    - Architecture: architecture.md
    - Quick Start: quick-start.md
    - First Steps: first-steps.md

  - Getting Started:
    - getting-started/index.md
    - Prerequisites: getting-started/prerequisites.md
    - Installation: getting-started/installation.md
    - First Steps: first-steps.md
    - Verification: getting-started/verification.md
  
  - Configuration:
    - configuration/index.md
    - Basic Configuration: configuration/basic.md
    - Advanced Configuration: configuration/advanced.md
    - Network Setup: configuration/network.md
    - Security Settings: configuration/security.md
    - Pfsense: configuration/pfsense.md
    - Provider Specific: 
      # - configuration/providers/index.md
      - VMware: configuration/providers/vmware.md
      - AWS: configuration/providers/aws.md
      - Azure: configuration/providers/azure.md
      - VirtualBox: configuration/providers/virtualbox.md
      - Proxmox: configuration/providers/proxmox.md
  
  - Components:
    - components/index.md
    - SIEM Platforms:
      - components/siem/index.md
      - Splunk Enterprise: components/siem/splunk.md
      - Elastic Stack: components/siem/elastic.md
    - Network Monitoring:
      - components/monitoring/index.md
      - Security Onion: components/monitoring/security-onion.md
      - Malcolm: components/monitoring/malcolm.md
    - Endpoint Visibility:
      - components/endpoint/index.md
      - Velociraptor: components/endpoint/velociraptor.md
      - Sysmon: components/endpoint/sysmon.md
    - Threat Intelligence:
      - MISP: components/threat-intel/misp.md
    - Malware Analysis:
      - components/analysis/index.md
      - FLARE-VM: components/analysis/flare-vm.md
  
  - GOAD Integration:
    - integration/index.md
    - Discovery Process: integration/discovery.md
    - Agent Deployment: integration/agents.md
    - Network Integration: integration/network.md
    - Data Flow: integration/data-flow.md
    - Troubleshooting: integration/troubleshooting.md
  
  - Training & Use Cases:
    - training/index.md
    - Red vs Blue Exercises: training/red-vs-blue.md
    - SOC Training: training/soc-training.md
    - Threat Hunting: training/threat-hunting.md
    - Incident Response: training/incident-response.md
    - Digital Forensics: training/digital-forensics.md
    - Scenarios:
      - training/scenarios/index.md
      - Kerberoasting Detection: training/scenarios/kerberoasting.md
      - Lateral Movement: training/scenarios/lateral-movement.md
      - Malware Analysis: training/scenarios/malware-analysis.md
      - APT Simulation: training/scenarios/apt-simulation.md
  
  - Deployment:
    - deployment/index.md
    - Infrastructure as Code:
      - deployment/iac/index.md
      - Packer Templates: deployment/iac/packer.md
      - Terraform Modules: deployment/iac/terraform.md
      - Ansible Playbooks: deployment/iac/ansible.md
    - Cloud Deployment:
      - deployment/cloud/index.md
      - AWS Deployment: deployment/cloud/aws.md
      - Azure Deployment: deployment/cloud/azure.md
    - On-Premises:
      - deployment/on-premises/index.md
      - VMware ESXi: deployment/on-premises/esxi.md
      - Proxmox: deployment/proxmox.md
  
  - Operations:
    - operations/index.md
    - Monitoring: operations/monitoring.md
    - Maintenance: operations/maintenance.md
    - Backup & Recovery: operations/backup.md
    - Performance Tuning: operations/performance.md
    - Security Hardening: operations/security.md
  
  - API Reference:
    - api/index.md
    - CLI Commands: api/cli.md
    - Python API: api/python.md
    - REST API: api/rest.md
    - Configuration API: api/configuration.md
  
  - Troubleshooting:
    - troubleshooting/index.md
    - Common Issues: troubleshooting/common-issues.md
    - Installation Problems: troubleshooting/installation.md
    - Component Issues: troubleshooting/components.md
    - Integration Problems: troubleshooting/integration.md
    - Performance Issues: troubleshooting/performance.md
    - Log Analysis: troubleshooting/log-analysis.md

  - Provider Deployment:
    - providers/index.md
    - AWS Deployment: providers/aws.md
    - Azure Deployment: providers/azure.md
    - VMware Deployment: providers/vmware.md
    - VirtualBox Deployment: providers/virtualbox.md
    - Proxmox Deployment: providers/proxmox.md

  - Development:
    - development/index.md
    - Contributing: development/contributing.md
    - Development Setup: development/setup.md
    - Testing: development/testing.md
    - Custom Components: development/custom-components.md
    - Plugin Development: development/plugins.md
  
  - Reference:
    - reference/index.md
    - Glossary: reference/glossary.md
    - FAQ: reference/faq.md
    - Changelog: reference/changelog.md
    - License: reference/license.md
    - Credits: reference/credits.md

# Custom CSS
extra_css:
  - stylesheets/extra.css

# Custom JavaScript
extra_javascript:
  - javascripts/mathjax.js
  - https://polyfill.io/v3/polyfill.min.js?features=es6
  - https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js
