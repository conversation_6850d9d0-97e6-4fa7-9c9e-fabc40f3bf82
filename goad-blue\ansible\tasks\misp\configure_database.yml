---
# Configure MariaDB/MySQL for MISP

- name: Start and enable MariaDB service
  systemd:
    name: "{{ 'mariadb' if ansible_os_family == 'Debian' else 'mysqld' }}"
    state: started
    enabled: yes

- name: Install PyMySQL for Ansible MySQL modules
  pip:
    name: PyMySQL
    state: present
    executable: pip3

- name: Secure MariaDB installation
  mysql_user:
    name: root
    password: "{{ misp.database.root_password }}"
    login_unix_socket: /var/run/mysqld/mysqld.sock
    state: present
  ignore_errors: yes

- name: Remove anonymous MySQL users
  mysql_user:
    name: ""
    host_all: yes
    state: absent
    login_user: root
    login_password: "{{ misp.database.root_password }}"

- name: Remove MySQL test database
  mysql_db:
    name: test
    state: absent
    login_user: root
    login_password: "{{ misp.database.root_password }}"

- name: Create MISP database
  mysql_db:
    name: "{{ misp.database.name }}"
    state: present
    login_user: root
    login_password: "{{ misp.database.root_password }}"
    encoding: utf8mb4
    collation: utf8mb4_unicode_ci

- name: Create MISP database user
  mysql_user:
    name: "{{ misp.database.user }}"
    password: "{{ misp.database.password }}"
    priv: "{{ misp.database.name }}.*:ALL"
    host: localhost
    state: present
    login_user: root
    login_password: "{{ misp.database.root_password }}"

- name: Configure MISP database connection
  template:
    src: database.php.j2
    dest: /var/www/MISP/app/Config/database.php
    owner: misp
    group: www-data
    mode: '0644'
    backup: yes

- name: Import MISP database schema
  mysql_db:
    name: "{{ misp.database.name }}"
    state: import
    target: /var/www/MISP/INSTALL/MYSQL.sql
    login_user: "{{ misp.database.user }}"
    login_password: "{{ misp.database.password }}"

- name: Configure MariaDB for MISP
  template:
    src: misp-mysql.cnf.j2
    dest: /etc/mysql/mariadb.conf.d/99-misp.cnf
    mode: '0644'
  notify: restart mariadb
  when: ansible_os_family == "Debian"

- name: Configure MySQL for MISP (RedHat/CentOS)
  template:
    src: misp-mysql.cnf.j2
    dest: /etc/my.cnf.d/misp.cnf
    mode: '0644'
  notify: restart mysqld
  when: ansible_os_family == "RedHat"

- name: Restart MariaDB to apply configuration
  systemd:
    name: "{{ 'mariadb' if ansible_os_family == 'Debian' else 'mysqld' }}"
    state: restarted

- name: Wait for MariaDB to be ready
  wait_for:
    port: 3306
    host: localhost
    delay: 5
    timeout: 30

- name: Verify MISP database connection
  mysql_db:
    name: "{{ misp.database.name }}"
    state: present
    login_user: "{{ misp.database.user }}"
    login_password: "{{ misp.database.password }}"
  register: db_connection_test

- name: Display database connection status
  debug:
    msg: "MISP database connection: {{ 'successful' if db_connection_test is succeeded else 'failed' }}"
