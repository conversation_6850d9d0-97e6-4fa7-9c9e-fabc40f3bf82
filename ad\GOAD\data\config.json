{"lab": {"hosts": {"dc01": {"hostname": "kingslanding", "type": "dc", "local_admin_password": "8dCT-DJjgScp", "domain": "sevenkingdoms.local", "path": "DC=sevenkingdoms,DC=local", "local_groups": {"Administrators": ["sevenkingdoms\\robert.baratheon", "sevenkingdoms\\cersei.lannister", "sevenkingdoms\\DragonRider"], "Remote Desktop Users": ["sevenkingdoms\\Small Council", "sevenkingdoms\\Baratheon"]}, "scripts": ["sidhistory.ps1"], "vulns": ["disable_firewall", "adcs_esc10_case1", "adcs_esc10_case2"], "security": ["account_is_sensitive"], "security_vars": {"account_is_sensitive": {"renly": {"account": "ren<PERSON>.baratheon"}}}}, "dc02": {"hostname": "winterfell", "type": "dc", "local_admin_password": "NgtI75cKV+Pu", "domain": "north.sevenkingdoms.local", "path": "DC=north,DC=sevenkingdoms,DC=local", "local_groups": {"Administrators": ["north\\eddard.stark", "north\\catelyn.stark", "north\\robb.stark"], "Remote Desktop Users": ["north\\Stark"]}, "scripts": ["asrep_roasting.ps1", "constrained_delegation_use_any.ps1", "constrained_delegation_kerb_only.ps1", "ntlm_relay.ps1", "responder.ps1", "gpo_abuse.ps1", "rdp_scheduler.ps1"], "vulns": ["disable_firewall", "directory", "credentials", "autologon", "files", "enable_llmnr", "enable_nbt-ns", "shares"], "vulns_vars": {"directory": {"setup": "c:\\setup"}, "credentials": {"TERMSRV/castelblack": {"username": "north\\robb.stark", "secret": "sexy<PERSON>y", "runas": "north\\robb.stark", "runas_password": "sexy<PERSON>y"}}, "autologon": {"robb.stark": {"username": "north\\robb.stark", "password": "sexy<PERSON>y"}}, "files": {"rdp": {"src": "dc02/bot_rdp.ps1", "dest": "c:\\setup\\bot_rdp.ps1"}, "sysvol_fake_script": {"src": "dc02/sysvol_scripts/script.ps1", "dest": "C:\\Windows\\SYSVOL\\domain\\scripts\\script.ps1"}, "sysvol_secret": {"src": "dc02/sysvol_scripts/secret.ps1", "dest": "C:\\Windows\\SYSVOL\\domain\\scripts\\secret.ps1"}}}}, "srv02": {"hostname": "castelblack", "type": "server", "local_admin_password": "NgtI75cKV+Pu", "domain": "north.sevenkingdoms.local", "path": "DC=north,DC=sevenkingdoms,DC=local", "use_laps": false, "local_groups": {"Administrators": ["north\\jeor.mormont"], "Remote Desktop Users": ["north\\Night Watch", "north\\Mormont", "north\\Stark"]}, "scripts": [], "vulns": ["directory", "disable_firewall", "openshares", "files", "permissions"], "vulns_vars": {"directory": {"shares": "C:\\shares", "all": "C:\\shares\\all"}, "files": {"website": {"src": "srv02/wwwroot", "dest": "C:\\inetpub\\"}, "letter_in_shares": {"src": "srv02/all/arya.txt", "dest": "C:\\shares\\all\\arya.txt"}}, "permissions": {"IIS_IUSRS_upload": {"path": "C:\\inetpub\\wwwroot\\upload", "user": "IIS_IUSRS", "rights": "FullControl"}}, "shares": {"thewall": {"path": "C:\\thewall", "list": "yes", "full": "NORTH\\Stark", "change": "NORTH\\jon.snow,NORTH\\samwell.tarly", "read": "Users"}}}, "mssql": {"sa_password": "Sup1_sa_P@ssw0rd!", "svcaccount": "sql_svc", "sysadmins": ["NORTH\\jon.snow"], "executeaslogin": {"NORTH\\samwell.tarly": "sa", "NORTH\\brandon.stark": "NORTH\\jon.snow"}, "executeasuser": {"arya_master_dbo": {"user": "NORTH\\arya.stark", "db": "master", "impersonate": "dbo"}, "arya_dbms_dbo": {"user": "NORTH\\arya.stark", "db": "msdb", "impersonate": "dbo"}}, "linked_servers": {"BRAAVOS": {"data_src": "braavos.essos.local", "users_mapping": [{"local_login": "NORTH\\jon.snow", "remote_login": "sa", "remote_password": "sa_P@ssw0rd!Ess0s"}]}}}}, "dc03": {"hostname": "meereen", "type": "dc", "local_admin_password": "Ufe-bVXSx9rk", "domain": "essos.local", "path": "DC=essos,DC=local", "local_groups": {"Administrators": ["essos\\daenerys.targaryen", "esso<PERSON>\\greatmaster"], "Remote Desktop Users": ["essos\\Targaryen"]}, "scripts": ["asrep_roasting2.ps1"], "vulns": ["ntlmdowngrade", "disable_firewall", "adcs_esc7", "adcs_esc13", "adcs_esc15"], "vulns_vars": {"adcs_esc7": {"viserys": {"ca_manager": "essos\\viserys.targaryen"}}, "adcs_esc13": {"esc13": {"adcs_esc13_group": "greatmaster", "adcs_esc13_template": "ESC13"}}}}, "srv03": {"hostname": "<PERSON>avos", "type": "server", "local_admin_password": "978i2pF43UJ-", "domain": "essos.local", "path": "DC=essos,DC=local", "use_laps": true, "local_groups": {"Administrators": ["essos\\khal.drogo"]}, "Remote Desktop Users": ["essos\\Dothraki"], "scripts": [], "vulns": ["openshares", "disable_firewall", "adcs_esc6", "adcs_esc11"], "security": ["enable_run_as_ppl"], "mssql": {"sa_password": "sa_P@ssw0rd!Ess0s", "svcaccount": "sql_svc", "sysadmins": ["ESSOS\\khal.drogo"], "executeaslogin": {"ESSOS\\jorah.mormont": "sa"}, "executeasuser": {}, "linked_servers": {"CASTELBLACK": {"data_src": "castelblack.north.sevenkingdoms.local", "users_mapping": [{"local_login": "ESSOS\\khal.drogo", "remote_login": "sa", "remote_password": "Sup1_sa_P@ssw0rd!"}]}}}}}, "domains": {"essos.local": {"dc": "dc03", "domain_password": "Ufe-bVXSx9rk", "netbios_name": "ESSOS", "ca_server": "<PERSON><PERSON><PERSON><PERSON>", "trust": "sevenkingdoms.local", "laps_path": "OU=Laps,DC=essos,DC=local", "organisation_units": {}, "laps_readers": ["jorah.mormont", "Spys"], "groups": {"universal": {"greatmaster": {"path": "CN=Users,DC=essos,DC=local"}}, "global": {"Targaryen": {"managed_by": "viserys.targaryen", "path": "CN=Users,DC=essos,DC=local"}, "Dothraki": {"managed_by": "khal.drogo", "path": "CN=Users,DC=essos,DC=local"}, "Dragons": {"managed_by": "Administrator", "path": "CN=Users,DC=essos,DC=local"}, "QueenProtector": {"managed_by": "Administrator", "path": "CN=Users,DC=essos,DC=local", "members": ["ESSOS\\Dragons"]}, "Domain Admins": {"managed_by": "Administrator", "path": "CN=Users,DC=essos,DC=local", "members": ["ESSOS\\QueenProtector"]}}, "domainlocal": {"DragonsFriends": {"managed_by": "daenerys.targaryen", "path": "CN=Users,DC=essos,DC=local"}, "Spys": {"path": "CN=Users,DC=essos,DC=local"}}}, "multi_domain_groups_member": {"DragonsFriends": ["sevenkingdoms.local\\tyron.lannister", "essos.local\\daenerys.targaryen"], "Spys": ["sevenkingdoms.local\\Small Council"]}, "gmsa": {"gmsa_account": {"gMSA_Name": "gmsaDragon", "gMSA_FQDN": "gmsaDragon.essos.local", "gMSA_SPNs": ["HTTP/braavos", "HTTP/braavos.essos.local"], "gMSA_HostNames": ["<PERSON>avos"]}}, "acls": {"GenericAll_khal_viserys": {"for": "khal.drogo", "to": "viserys.targaryen", "right": "GenericAll", "inheritance": "None"}, "GenericAll_spy_jorah": {"for": "Spys", "to": "jorah.mormont", "right": "GenericAll", "inheritance": "None"}, "GenericAll_khal_esc4": {"for": "khal.drogo", "to": "CN=ESC4,CN=Certificate Templates,CN=Public Key Services,CN=Services,CN=Configuration,DC=essos,DC=local", "right": "GenericAll", "inheritance": "None"}, "WriteProperty_petyer_domadmin": {"for": "viserys.targaryen", "to": "jorah.mormont", "right": "WriteProperty", "inheritance": "All"}, "GenericWrite_DragonsFriends_braavos": {"for": "DragonsFriends", "to": "braavos$", "right": "GenericWrite", "inheritance": "None"}, "GenericAll_missandei_khal": {"for": "<PERSON><PERSON><PERSON>", "to": "khal.drogo", "right": "GenericAll", "inheritance": "None"}, "GenericAll_gmsaDragon_drogo": {"for": "gmsaDragon$", "to": "drogon", "right": "GenericAll", "inheritance": "None"}, "GenericWrite_missandei_viserys": {"for": "<PERSON><PERSON><PERSON>", "to": "viserys.targaryen", "right": "GenericWrite", "inheritance": "None"}}, "users": {"daenerys.targaryen": {"firstname": "da<PERSON>ys", "surname": "targaryen", "password": "BurnThemAll!", "city": "-", "description": "Darnerys Targaryen", "groups": ["<PERSON><PERSON><PERSON><PERSON>", "Domain Admins"], "path": "CN=Users,DC=essos,DC=local"}, "viserys.targaryen": {"firstname": "viserys", "surname": "targaryen", "password": "GoldCrown", "city": "-", "description": "<PERSON><PERSON><PERSON>", "groups": ["<PERSON><PERSON><PERSON><PERSON>"], "path": "CN=Users,DC=essos,DC=local"}, "khal.drogo": {"firstname": "khal", "surname": "drogo", "password": "horse", "city": "-", "description": "<PERSON><PERSON>", "groups": ["<PERSON><PERSON><PERSON>"], "path": "CN=Users,DC=essos,DC=local"}, "jorah.mormont": {"firstname": "jorah", "surname": "mormont", "password": "H0nnor!", "city": "-", "description": "<PERSON><PERSON>", "groups": ["<PERSON><PERSON><PERSON><PERSON>"], "path": "CN=Users,DC=essos,DC=local"}, "missandei": {"firstname": "<PERSON><PERSON><PERSON>", "surname": "-", "password": "fr3edom", "city": "-", "description": "<PERSON><PERSON><PERSON>", "groups": [], "path": "CN=Users,DC=essos,DC=local"}, "drogon": {"firstname": "drogon", "surname": "-", "password": "<PERSON><PERSON><PERSON><PERSON>", "city": "-", "description": "drogon", "groups": ["Dragons"], "path": "CN=Users,DC=essos,DC=local"}, "sql_svc": {"firstname": "sql", "surname": "service", "password": "YouWillNotKerboroast1ngMeeeeee", "city": "-", "description": "sql service", "groups": [], "path": "CN=Users,DC=essos,DC=local", "spns": ["MSSQLSvc/braavos.essos.local:1433", "MSSQLSvc/braavos.essos.local"]}}}, "north.sevenkingdoms.local": {"dc": "dc02", "domain_password": "NgtI75cKV+Pu", "netbios_name": "NORTH", "trust": "", "laps_path": "OU=Laps,DC=north,DC=sevenkingdoms,DC=local", "organisation_units": {}, "groups": {"universal": {}, "global": {"Stark": {"managed_by": "eddard.stark", "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local"}, "Night Watch": {"managed_by": "jeor.mormont", "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local"}, "Mormont": {"managed_by": "jeor.mormont", "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local"}}, "domainlocal": {"AcrossTheSea": {"path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local"}}}, "multi_domain_groups_member": {}, "acls": {"anonymous_rpc": {"for": "NT AUTHORITY\\ANONYMOUS LOGON", "to": "DC=North,DC=sevenkingdoms,DC=local", "right": "ReadProperty", "inheritance": "All"}, "anonymous_rpc2": {"for": "NT AUTHORITY\\ANONYMOUS LOGON", "to": "DC=North,DC=sevenkingdoms,DC=local", "right": "GenericExecute", "inheritance": "All"}}, "users": {"arya.stark": {"firstname": "<PERSON><PERSON>", "surname": "<PERSON>", "password": "<PERSON>le", "city": "Winterfell", "description": "<PERSON><PERSON>", "groups": ["<PERSON>"], "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local"}, "eddard.stark": {"firstname": "Eddard", "surname": "<PERSON>", "password": "FightP3aceAndHonor!", "city": "King's Landing", "description": "<PERSON><PERSON><PERSON>", "groups": ["<PERSON>", "Domain Admins"], "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local"}, "catelyn.stark": {"firstname": "<PERSON><PERSON><PERSON>", "surname": "<PERSON>", "password": "rob<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city": "King's Landing", "description": "<PERSON><PERSON><PERSON>", "groups": ["<PERSON>"], "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local"}, "robb.stark": {"firstname": "<PERSON><PERSON>", "surname": "<PERSON>", "password": "sexy<PERSON>y", "city": "Winterfell", "description": "<PERSON><PERSON>", "groups": ["<PERSON>"], "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local"}, "sansa.stark": {"firstname": "Sansa", "surname": "<PERSON>", "password": "345ertdfg", "city": "Winterfell", "description": "<PERSON><PERSON>", "groups": ["<PERSON>"], "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local", "spns": ["HTTP/eyrie.north.sevenkingdoms.local"]}, "brandon.stark": {"firstname": "<PERSON>", "surname": "<PERSON>", "password": "iseedeadpeople", "city": "Winterfell", "description": "<PERSON>", "groups": ["<PERSON>"], "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local"}, "rickon.stark": {"firstname": "<PERSON><PERSON>", "surname": "<PERSON>", "password": "Winter2022", "city": "Winterfell", "description": "<PERSON><PERSON>", "groups": ["<PERSON>"], "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local"}, "hodor": {"firstname": "hodor", "surname": "hodor", "password": "hodor", "city": "Winterfell", "description": "Brainless Giant", "groups": ["<PERSON>"], "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local"}, "jon.snow": {"firstname": "<PERSON>", "surname": "Snow", "password": "iknownothing", "city": "Castel Black", "description": "<PERSON>", "groups": ["<PERSON>", "Night Watch"], "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local", "spns": ["HTTP/thewall.north.sevenkingdoms.local"]}, "samwell.tarly": {"firstname": "<PERSON><PERSON>", "surname": "Tarly", "password": "Heartsbane", "city": "Castel Black", "description": "<PERSON><PERSON> (Password : <PERSON><PERSON><PERSON>)", "groups": ["Night Watch"], "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local"}, "jeor.mormont": {"firstname": "<PERSON><PERSON>", "surname": "<PERSON>t", "password": "_L0ngCl@w_", "city": "Castel Black", "description": "<PERSON><PERSON>", "groups": ["Night Watch", "<PERSON>t"], "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local"}, "sql_svc": {"firstname": "sql", "surname": "service", "password": "YouWillNotKerboroast1ngMeeeeee", "city": "-", "description": "sql service", "groups": [], "path": "CN=Users,DC=North,DC=sevenkingdoms,DC=local", "spns": ["MSSQLSvc/castelblack.north.sevenkingdoms.local:1433", "MSSQLSvc/castelblack.north.sevenkingdoms.local"]}}}, "sevenkingdoms.local": {"dc": "dc01", "domain_password": "8dCT-DJjgScp", "netbios_name": "SEVENKINGDOMS", "trust": "essos.local", "laps_path": "OU=Laps,DC=sevenkingdoms,DC=local", "organisation_units": {"Vale": {"path": "DC=sevenkingdoms,DC=local"}, "IronIslands": {"path": "DC=sevenkingdoms,DC=local"}, "Riverlands": {"path": "DC=sevenkingdoms,DC=local"}, "Crownlands": {"path": "DC=sevenkingdoms,DC=local"}, "Stormlands": {"path": "DC=sevenkingdoms,DC=local"}, "Westerlands": {"path": "DC=sevenkingdoms,DC=local"}, "Reach": {"path": "DC=sevenkingdoms,DC=local"}, "Dorne": {"path": "DC=sevenkingdoms,DC=local"}}, "groups": {"universal": {}, "global": {"Lannister": {"managed_by": "tywin.lannister", "path": "OU=Westerlands,DC=sevenkingdoms,DC=local"}, "Baratheon": {"managed_by": "robert.bar<PERSON>eon", "path": "OU=Stormlands,DC=sevenkingdoms,DC=local"}, "Small Council": {"path": "OU=Crownlands,DC=sevenkingdoms,DC=local"}, "DragonStone": {"path": "OU=Crownlands,DC=sevenkingdoms,DC=local"}, "KingsGuard": {"path": "OU=Crownlands,DC=sevenkingdoms,DC=local"}, "DragonRider": {"path": "OU=Crownlands,DC=sevenkingdoms,DC=local"}}, "domainlocal": {"AcrossTheNarrowSea": {"path": "CN=Users,DC=sevenkingdoms,DC=local"}}}, "multi_domain_groups_member": {"AcrossTheNarrowSea": ["essos.local\\daenerys.targaryen"]}, "acls": {"forcechangepassword_tywin_jaime": {"for": "tywin.lannister", "to": "jaime.lannister", "right": "Ext-User-Force-Change-Password", "inheritance": "None"}, "GenericWrite_on_user_jaimie_joffrey": {"for": "jaime.lannister", "to": "joffrey.baratheon", "right": "GenericWrite", "inheritance": "None"}, "Writedacl_joffrey_tyron": {"for": "joffrey.baratheon", "to": "tyron.lannister", "right": "WriteDacl", "inheritance": "None"}, "self-self-membership-on-group_tyron_small_council": {"for": "tyron.lannister", "to": "Small Council", "right": "Ext-Self-Self-Membership", "inheritance": "None"}, "addmember_smallcouncil_DragonStone": {"for": "Small Council", "to": "DragonStone", "right": "Ext-Write-Self-Membership", "inheritance": "All"}, "write_owner_dragonstone_kingsguard": {"for": "DragonStone", "to": "Kings<PERSON><PERSON>", "right": "WriteOwner", "inheritance": "None"}, "GenericAll_kingsguard_stanis": {"for": "Kings<PERSON><PERSON>", "to": "stannis.baratheon", "right": "GenericAll", "inheritance": "None"}, "GenericAll_stanis_dc": {"for": "stannis.baratheon", "to": "kingslanding$", "right": "GenericAll", "inheritance": "None"}, "GenericAll_group_acrrosdom_dc": {"for": "AcrossTheNarrowSea", "to": "kingslanding$", "right": "GenericAll", "inheritance": "None"}, "GenericAll_varys_domadmin": {"for": "lord.varys", "to": "Domain Admins", "right": "GenericAll", "inheritance": "None"}, "GenericAll_varys_domadmin_holder": {"for": "lord.varys", "to": "CN=AdminSDHolder,CN=System,DC=sevenkingdoms,DC=local", "right": "GenericAll", "inheritance": "None"}, "WriteDACL_renly_Crownlands": {"for": "ren<PERSON>.baratheon", "to": "OU=Crownlands,DC=sevenkingdoms,DC=local", "right": "WriteDacl", "inheritance": "None"}}, "users": {"tywin.lannister": {"firstname": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "password": "powerkingftw135", "city": "Casterly Rock", "description": "<PERSON><PERSON>", "groups": ["<PERSON><PERSON><PERSON>"], "path": "OU=Crownlands,DC=sevenkingdoms,DC=local"}, "jaime.lannister": {"firstname": "<PERSON>", "surname": "<PERSON><PERSON><PERSON>", "password": "cersei", "city": "King's Landing", "description": "<PERSON>", "groups": ["<PERSON><PERSON><PERSON>"], "path": "OU=Crownlands,DC=sevenkingdoms,DC=local"}, "cersei.lannister": {"firstname": "Cersei", "surname": "<PERSON><PERSON><PERSON>", "password": "il0vejaime", "city": "King's Landing", "description": "<PERSON><PERSON><PERSON>", "groups": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Domain Admins", "Small Council"], "path": "OU=Crownlands,DC=sevenkingdoms,DC=local"}, "tyron.lannister": {"firstname": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "password": "Alc00L&S3x", "city": "King's Landing", "description": "<PERSON><PERSON>", "groups": ["<PERSON><PERSON><PERSON>"], "path": "OU=Westerlands,DC=sevenkingdoms,DC=local"}, "robert.baratheon": {"firstname": "<PERSON>", "surname": "<PERSON><PERSON><PERSON>", "password": "iamthekingoftheworld", "city": "King's Landing", "description": "<PERSON>", "groups": ["<PERSON><PERSON><PERSON>", "Domain Admins", "Small Council", "Protected Users"], "path": "OU=Crownlands,DC=sevenkingdoms,DC=local"}, "joffrey.baratheon": {"firstname": "<PERSON><PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "password": "1killerlion", "city": "King's Landing", "description": "<PERSON><PERSON><PERSON>", "groups": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "path": "OU=Crownlands,DC=sevenkingdoms,DC=local"}, "renly.baratheon": {"firstname": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "password": "lorasty<PERSON>", "city": "King's Landing", "description": "<PERSON><PERSON>", "groups": ["<PERSON><PERSON><PERSON>", "Small Council"], "path": "OU=Crownlands,DC=sevenkingdoms,DC=local"}, "stannis.baratheon": {"firstname": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "password": "Drag0nst0ne", "city": "King's Landing", "description": "<PERSON><PERSON>", "groups": ["<PERSON><PERSON><PERSON>", "Small Council"], "path": "OU=Crownlands,DC=sevenkingdoms,DC=local"}, "petyer.baelish": {"firstname": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON>", "password": "@littlefinger@", "city": "King's Landing", "description": "<PERSON><PERSON>", "groups": ["Small Council"], "path": "OU=Crownlands,DC=sevenkingdoms,DC=local"}, "lord.varys": {"firstname": "Lord", "surname": "<PERSON><PERSON><PERSON>", "password": "_W1sper_$", "city": "King's Landing", "description": "Lord <PERSON>", "groups": ["Small Council"], "path": "OU=Crownlands,DC=sevenkingdoms,DC=local"}, "maester.pycelle": {"firstname": "<PERSON><PERSON>", "surname": "<PERSON><PERSON><PERSON><PERSON>", "password": "MaesterOfMaesters", "city": "King's Landing", "description": "<PERSON><PERSON>", "groups": ["Small Council"], "path": "OU=Crownlands,DC=sevenkingdoms,DC=local"}}}}}}