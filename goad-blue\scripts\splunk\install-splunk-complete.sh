#!/bin/bash
# Complete GOAD-Blue Splunk Installation Script
# Installs Splunk Enterprise, dashboards, CIM, TAs, and configures everything

set -e

# Configuration
GOAD_BLUE_HOME="/opt/goad-blue"
SCRIPT_DIR="$GOAD_BLUE_HOME/scripts/splunk"
LOG_FILE="/var/log/goad-blue/splunk-complete-install.log"
SPLUNK_ADMIN_PASSWORD="${SPLUNK_ADMIN_PASSWORD:-ChangeMeNow!}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging function
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "OK")
            echo -e "${GREEN}[OK]${NC} $message"
            log_message "OK: $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            log_message "WARNING: $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            log_message "ERROR: $message"
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            log_message "INFO: $message"
            ;;
        "HEADER")
            echo -e "${CYAN}$message${NC}"
            log_message "HEADER: $message"
            ;;
    esac
}

# Print header
print_header() {
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}========================================${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if running as root
    if [ "$EUID" -ne 0 ]; then
        print_status "ERROR" "This script must be run as root"
        exit 1
    fi
    
    # Check system resources
    local memory_gb=$(free -g | grep Mem | awk '{print $2}')
    local disk_gb=$(df -BG / | tail -1 | awk '{print $4}' | sed 's/G//')
    local cpu_cores=$(nproc)
    
    print_status "INFO" "System Resources:"
    print_status "INFO" "  Memory: ${memory_gb}GB"
    print_status "INFO" "  Disk Available: ${disk_gb}GB"
    print_status "INFO" "  CPU Cores: $cpu_cores"
    
    # Check minimum requirements
    if [ "$memory_gb" -lt 8 ]; then
        print_status "WARNING" "Recommended minimum 8GB RAM for Splunk Enterprise"
    fi
    
    if [ "$disk_gb" -lt 100 ]; then
        print_status "WARNING" "Recommended minimum 100GB free disk space"
    fi
    
    # Check if Splunk is already installed
    if [ -d "/opt/splunk" ]; then
        print_status "INFO" "Splunk appears to be already installed"
        if systemctl is-active --quiet Splunkd; then
            print_status "INFO" "Splunk is currently running"
        else
            print_status "WARNING" "Splunk is installed but not running"
        fi
    fi
    
    print_status "OK" "Prerequisites check completed"
    echo ""
}

# Install Splunk Enterprise
install_splunk_enterprise() {
    print_header "Installing Splunk Enterprise"
    
    if [ -f "$SCRIPT_DIR/install-splunk.sh" ]; then
        print_status "INFO" "Running Splunk Enterprise installation..."
        export SPLUNK_ADMIN_PASSWORD="$SPLUNK_ADMIN_PASSWORD"
        bash "$SCRIPT_DIR/install-splunk.sh"
        print_status "OK" "Splunk Enterprise installation completed"
    else
        print_status "ERROR" "Splunk installation script not found: $SCRIPT_DIR/install-splunk.sh"
        exit 1
    fi
    
    echo ""
}

# Install dashboards and CIM
install_dashboards_and_cim() {
    print_header "Installing Dashboards and CIM"
    
    if [ -f "$SCRIPT_DIR/install-goad-blue-dashboards.sh" ]; then
        print_status "INFO" "Running dashboard and CIM installation..."
        bash "$SCRIPT_DIR/install-goad-blue-dashboards.sh"
        print_status "OK" "Dashboards and CIM installation completed"
    else
        print_status "ERROR" "Dashboard installation script not found: $SCRIPT_DIR/install-goad-blue-dashboards.sh"
        exit 1
    fi
    
    echo ""
}

# Configure forwarders
configure_forwarders() {
    print_header "Configuring Universal Forwarders"
    
    if [ -f "$SCRIPT_DIR/configure-splunk-forwarders.sh" ]; then
        print_status "INFO" "Running forwarder configuration..."
        bash "$SCRIPT_DIR/configure-splunk-forwarders.sh"
        print_status "OK" "Forwarder configuration completed"
    else
        print_status "WARNING" "Forwarder configuration script not found, skipping..."
    fi
    
    echo ""
}

# Package app
package_app() {
    print_header "Packaging GOAD-Blue App"
    
    if [ -f "$SCRIPT_DIR/package-goad-blue-app.sh" ]; then
        print_status "INFO" "Creating distributable app package..."
        bash "$SCRIPT_DIR/package-goad-blue-app.sh"
        print_status "OK" "App packaging completed"
    else
        print_status "WARNING" "App packaging script not found, skipping..."
    fi
    
    echo ""
}

# Verify installation
verify_installation() {
    print_header "Verifying Installation"
    
    # Check Splunk service
    if systemctl is-active --quiet Splunkd; then
        print_status "OK" "Splunk service is running"
    else
        print_status "ERROR" "Splunk service is not running"
        return 1
    fi
    
    # Check web interface
    if curl -k -s --connect-timeout 10 https://localhost:8000 >/dev/null; then
        print_status "OK" "Splunk web interface is accessible"
    else
        print_status "ERROR" "Splunk web interface is not accessible"
        return 1
    fi
    
    # Check management interface
    if curl -k -s --connect-timeout 10 https://localhost:8089 >/dev/null; then
        print_status "OK" "Splunk management interface is accessible"
    else
        print_status "ERROR" "Splunk management interface is not accessible"
        return 1
    fi
    
    # Check GOAD-Blue app
    if [ -d "/opt/splunk/etc/apps/goad_blue_security" ]; then
        print_status "OK" "GOAD-Blue Security app is installed"
    else
        print_status "WARNING" "GOAD-Blue Security app not found"
    fi
    
    # Check indexes
    local indexes_check=$(/opt/splunk/bin/splunk list index -auth "admin:$SPLUNK_ADMIN_PASSWORD" 2>/dev/null | grep -c "goad_blue" || echo "0")
    if [ "$indexes_check" -gt 0 ]; then
        print_status "OK" "GOAD-Blue indexes are configured ($indexes_check found)"
    else
        print_status "WARNING" "GOAD-Blue indexes not found"
    fi
    
    print_status "OK" "Installation verification completed"
    echo ""
}

# Display access information
display_access_info() {
    print_header "Access Information"
    
    local server_ip=$(hostname -I | awk '{print $1}')
    
    echo "Splunk Enterprise is now ready!"
    echo ""
    echo "Access URLs:"
    echo "  Web Interface: https://$server_ip:8000"
    echo "  Web Interface (localhost): https://localhost:8000"
    echo "  Management API: https://$server_ip:8089"
    echo ""
    echo "Credentials:"
    echo "  Username: admin"
    echo "  Password: $SPLUNK_ADMIN_PASSWORD"
    echo ""
    echo "GOAD-Blue Dashboards:"
    echo "  Main Overview: https://$server_ip:8000/en-US/app/goad_blue_security/goad_blue_overview"
    echo "  Authentication: https://$server_ip:8000/en-US/app/goad_blue_security/authentication_monitoring"
    echo "  Network: https://$server_ip:8000/en-US/app/goad_blue_security/network_monitoring"
    echo "  Threat Hunting: https://$server_ip:8000/en-US/app/goad_blue_security/threat_hunting"
    echo "  Incident Response: https://$server_ip:8000/en-US/app/goad_blue_security/incident_response"
    echo ""
    echo "Data Ingestion:"
    echo "  Forwarder Port: $server_ip:9997"
    echo "  HTTP Event Collector: https://$server_ip:8088"
    echo ""
    echo "Indexes:"
    echo "  goad_blue_windows - Windows event logs"
    echo "  goad_blue_linux - Linux system logs"
    echo "  goad_blue_network - Network security logs"
    echo "  goad_blue_security - Security alerts"
    echo ""
    echo "Monitoring:"
    echo "  Status: systemctl status Splunkd"
    echo "  Logs: tail -f /opt/splunk/var/log/splunk/splunkd.log"
    echo "  Monitor: /opt/goad-blue/scripts/splunk-monitor.sh"
    echo ""
}

# Create post-installation tasks
create_post_installation_tasks() {
    print_header "Creating Post-Installation Tasks"
    
    # Create quick start guide
    cat > "$GOAD_BLUE_HOME/docs/splunk-quick-start.md" << 'EOF'
# GOAD-Blue Splunk Quick Start Guide

## Initial Setup Complete

Your GOAD-Blue Splunk environment is now ready! Here are the next steps:

### 1. Deploy Forwarders to GOAD Systems

#### Linux Systems:
```bash
# Copy and run on each Linux GOAD system
curl -o deploy-forwarder.sh https://your-splunk-server/static/deploy-linux-forwarder.sh
chmod +x deploy-forwarder.sh
sudo ./deploy-forwarder.sh ************** 9997
```

#### Windows Systems:
```powershell
# Copy and run on each Windows GOAD system
Invoke-WebRequest -Uri "https://your-splunk-server/static/deploy-windows-forwarder.ps1" -OutFile "deploy-forwarder.ps1"
PowerShell -ExecutionPolicy Bypass -File deploy-forwarder.ps1 -IndexerHost "**************" -IndexerPort "9997"
```

### 2. Configure Network Monitoring

#### Suricata Integration:
- Configure Suricata to output EVE JSON format
- Forward logs to index: goad_blue_network
- Sourcetype: suricata:eve

#### Zeek Integration:
- Configure Zeek log forwarding
- Forward logs to index: goad_blue_network
- Sourcetype: zeek

### 3. Validate Data Ingestion

Check that data is flowing:
```
index=goad_blue_* | stats count by index, sourcetype
```

### 4. Customize Dashboards

- Modify time ranges in dashboards
- Add custom searches and visualizations
- Configure alerting thresholds

### 5. Set Up Alerting

- Configure email settings in Splunk
- Enable saved searches for alerting
- Set up notification channels

## Troubleshooting

### Common Issues:
1. **No data in indexes**: Check forwarder connectivity
2. **Dashboard errors**: Verify CIM is installed
3. **Performance issues**: Adjust resource allocation

### Support:
- Documentation: /opt/goad-blue/docs/
- Logs: /var/log/goad-blue/
- Monitoring: goad-monitor splunk
EOF

    # Create maintenance script
    cat > "$GOAD_BLUE_HOME/scripts/splunk-maintenance.sh" << 'EOF'
#!/bin/bash
# GOAD-Blue Splunk Maintenance Script

echo "=== GOAD-Blue Splunk Maintenance ==="

# Check disk usage
echo "Disk Usage:"
du -sh /opt/splunk/var/lib/splunk/

# Check index sizes
echo ""
echo "Index Sizes:"
/opt/splunk/bin/splunk list index -auth admin:ChangeMeNow! | grep -E "(goad_blue|main)"

# Clean old logs
echo ""
echo "Cleaning old logs..."
find /opt/splunk/var/log/splunk/ -name "*.log.*" -mtime +7 -delete

# Restart if needed
if [ "$1" = "--restart" ]; then
    echo "Restarting Splunk..."
    systemctl restart Splunkd
fi

echo "Maintenance completed"
EOF

    chmod +x "$GOAD_BLUE_HOME/scripts/splunk-maintenance.sh"
    
    print_status "OK" "Post-installation tasks created"
    echo ""
}

# Main installation function
main() {
    print_header "GOAD-Blue Complete Splunk Installation"
    echo "This script will install and configure:"
    echo "  - Splunk Enterprise"
    echo "  - Common Information Model (CIM)"
    echo "  - Technical Add-ons (TAs)"
    echo "  - GOAD-Blue Security Dashboards"
    echo "  - Universal Forwarder Configuration"
    echo "  - Monitoring and Maintenance Tools"
    echo ""
    
    log_message "Starting complete GOAD-Blue Splunk installation"
    
    # Installation steps
    check_prerequisites
    install_splunk_enterprise
    install_dashboards_and_cim
    configure_forwarders
    package_app
    verify_installation
    create_post_installation_tasks
    display_access_info
    
    log_message "Complete GOAD-Blue Splunk installation finished"
    
    print_header "Installation Complete!"
    print_status "OK" "GOAD-Blue Splunk environment is ready for use"
    echo ""
    echo "Next Steps:"
    echo "  1. Access Splunk Web: https://$(hostname -I | awk '{print $1}'):8000"
    echo "  2. Deploy forwarders to GOAD systems"
    echo "  3. Configure network monitoring tools"
    echo "  4. Review quick start guide: $GOAD_BLUE_HOME/docs/splunk-quick-start.md"
    echo ""
}

# Handle command line arguments
case "${1:-}" in
    --help|-h)
        echo "GOAD-Blue Complete Splunk Installation"
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h              Show this help message"
        echo "  --password PASSWORD     Set Splunk admin password"
        echo "  --skip-forwarders      Skip forwarder configuration"
        echo "  --skip-packaging       Skip app packaging"
        echo "  --verify-only          Only run verification checks"
        echo ""
        echo "Environment Variables:"
        echo "  SPLUNK_ADMIN_PASSWORD  Splunk admin password (default: ChangeMeNow!)"
        echo ""
        exit 0
        ;;
    --password)
        SPLUNK_ADMIN_PASSWORD="$2"
        shift 2
        main
        ;;
    --skip-forwarders)
        configure_forwarders() { echo "Skipping forwarder configuration..."; }
        shift
        main
        ;;
    --skip-packaging)
        package_app() { echo "Skipping app packaging..."; }
        shift
        main
        ;;
    --verify-only)
        verify_installation
        ;;
    *)
        main
        ;;
esac
