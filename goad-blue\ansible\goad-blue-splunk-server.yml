---
# GOAD-<PERSON> Splunk Server Configuration
- name: Configure GOAD-Blue Splunk Server
  hosts: splunk_servers
  become: yes
  vars:
    splunk_version: "{{ splunk_version | default('9.1.2') }}"
    splunk_build: "{{ splunk_build | default('b6b9c8185839') }}"
    splunk_admin_password: "{{ vault_splunk_admin_password | default('ChangeMePlease123!') }}"
    splunk_home: "/opt/splunk"
    splunk_user: "splunk"
  
  tasks:
    - name: Create splunk user
      user:
        name: "{{ splunk_user }}"
        system: yes
        shell: /bin/bash
        home: "{{ splunk_home }}"
        create_home: no

    - name: Create splunk directories
      file:
        path: "{{ item }}"
        state: directory
        owner: "{{ splunk_user }}"
        group: "{{ splunk_user }}"
        mode: '0755'
      loop:
        - "{{ splunk_home }}"
        - "/tmp/splunk_install"

    - name: Download Splunk Enterprise
      get_url:
        url: "https://download.splunk.com/products/splunk/releases/{{ splunk_version }}/linux/splunk-{{ splunk_version }}-{{ splunk_build }}-Linux-x86_64.tgz"
        dest: "/tmp/splunk_install/splunk.tgz"
        mode: '0644'
        timeout: 300

    - name: Extract Splunk
      unarchive:
        src: "/tmp/splunk_install/splunk.tgz"
        dest: "/opt"
        remote_src: yes
        owner: "{{ splunk_user }}"
        group: "{{ splunk_user }}"
        creates: "{{ splunk_home }}/bin/splunk"

    - name: Set Splunk ownership
      file:
        path: "{{ splunk_home }}"
        owner: "{{ splunk_user }}"
        group: "{{ splunk_user }}"
        recurse: yes

    - name: Start Splunk and accept license
      command: "{{ splunk_home }}/bin/splunk start --accept-license --answer-yes --no-prompt --seed-passwd {{ splunk_admin_password }}"
      become_user: "{{ splunk_user }}"
      args:
        creates: "{{ splunk_home }}/var/lib/splunk/kvstore"

    - name: Enable Splunk boot start
      command: "{{ splunk_home }}/bin/splunk enable boot-start -user {{ splunk_user }}"
      args:
        creates: /etc/systemd/system/Splunkd.service

    - name: Configure Splunk for GOAD-Blue
      template:
        src: "{{ item.src }}"
        dest: "{{ item.dest }}"
        owner: "{{ splunk_user }}"
        group: "{{ splunk_user }}"
        mode: '0644'
      loop:
        - src: splunk/inputs.conf.j2
          dest: "{{ splunk_home }}/etc/system/local/inputs.conf"
        - src: splunk/indexes.conf.j2
          dest: "{{ splunk_home }}/etc/system/local/indexes.conf"
        - src: splunk/outputs.conf.j2
          dest: "{{ splunk_home }}/etc/system/local/outputs.conf"
      notify: restart splunk

    - name: Create GOAD-Blue indexes
      uri:
        url: "https://localhost:8089/services/data/indexes"
        method: POST
        user: admin
        password: "{{ splunk_admin_password }}"
        validate_certs: no
        body_format: form-urlencoded
        body:
          name: "{{ item.name }}"
          maxDataSize: "{{ item.maxDataSize }}"
        status_code: [201, 409]  # 409 if index already exists
      loop:
        - { name: "goad_blue_windows", maxDataSize: "10GB" }
        - { name: "goad_blue_network", maxDataSize: "5GB" }
        - { name: "goad_blue_threat_intel", maxDataSize: "1GB" }
        - { name: "goad_blue_endpoint", maxDataSize: "5GB" }

    - name: Install GOAD-Blue Splunk apps
      include_tasks: tasks/install_splunk_app.yml
      vars:
        app_name: "{{ item.name }}"
        app_source: "{{ item.source }}"
        app_repo: "{{ item.repo | default('') }}"
      loop:
        - { name: "Splunk_TA_windows", source: "splunkbase" }
        - { name: "Splunk_TA_nix", source: "splunkbase" }
        - { name: "TA-suricata", source: "github", repo: "https://github.com/splunk/TA-suricata" }
        - { name: "MISP42", source: "splunkbase" }

    - name: Create GOAD-Blue dashboards
      copy:
        src: "{{ item }}"
        dest: "{{ splunk_home }}/etc/apps/search/local/data/ui/views/"
        owner: "{{ splunk_user }}"
        group: "{{ splunk_user }}"
        mode: '0644'
      with_fileglob:
        - "splunk/dashboards/*.xml"
      notify: restart splunk

    - name: Configure firewall for Splunk
      ufw:
        rule: allow
        port: "{{ item }}"
        proto: tcp
      loop:
        - "8000"   # Web interface
        - "8089"   # Management port
        - "9997"   # Forwarder port

    - name: Create GOAD-Blue saved searches
      template:
        src: splunk/savedsearches.conf.j2
        dest: "{{ splunk_home }}/etc/apps/search/local/savedsearches.conf"
        owner: "{{ splunk_user }}"
        group: "{{ splunk_user }}"
        mode: '0644'
      notify: restart splunk

    - name: Clean up installation files
      file:
        path: "/tmp/splunk_install"
        state: absent

  handlers:
    - name: restart splunk
      systemd:
        name: Splunkd
        state: restarted
        enabled: yes
