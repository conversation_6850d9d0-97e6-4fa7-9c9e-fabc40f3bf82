# Component Issues

This guide covers troubleshooting specific GOAD-Blue components including SIEM, monitoring tools, and endpoint agents.

## 🔍 SIEM Issues

### **Splunk Problems**

#### **Splunk Won't Start**

**Symptoms:**
- Service fails to start
- Web interface not accessible
- License errors

**Diagnostic Commands:**
```bash
# Check Splunk status
sudo systemctl status splunk
sudo /opt/splunk/bin/splunk status

# Check Splunk logs
sudo tail -f /opt/splunk/var/log/splunk/splunkd.log

# Verify configuration
sudo /opt/splunk/bin/splunk validate config
```

**Common Solutions:**
```bash
# Fix ownership issues
sudo chown -R splunk:splunk /opt/splunk

# Reset admin password
sudo /opt/splunk/bin/splunk edit user admin -password newpassword -auth admin:changeme

# Check license
sudo /opt/splunk/bin/splunk show license

# Restart with clean start
sudo /opt/splunk/bin/splunk stop
sudo /opt/splunk/bin/splunk clean eventdata
sudo /opt/splunk/bin/splunk start
```

#### **No Data in Splunk Indexes**

**Problem:** Logs not appearing in Splunk despite forwarders running.

**Troubleshooting Steps:**
```bash
# Check forwarder connectivity
sudo /opt/splunkforwarder/bin/splunk list forward-server

# Test forwarder connection
sudo /opt/splunkforwarder/bin/splunk test connection

# Check index configuration
# In Splunk Web: Settings > Indexes

# Verify inputs configuration
sudo /opt/splunkforwarder/bin/splunk list input
```

**Configuration Fixes:**
```conf
# inputs.conf
[monitor:///var/log/goad-blue/]
index = goad_blue_main
sourcetype = goad_blue_log

[monitor:///var/log/syslog]
index = goad_blue_system
sourcetype = syslog

# outputs.conf
[tcpout]
defaultGroup = default-autolb-group

[tcpout:default-autolb-group]
server = **************:9997
```

### **Elastic Stack Problems**

#### **Elasticsearch Issues**

**Common Problems:**
- Cluster health red/yellow
- Out of memory errors
- Disk space issues
- Slow queries

**Diagnostic Commands:**
```bash
# Check cluster health
curl -X GET "localhost:9200/_cluster/health?pretty"

# Check node status
curl -X GET "localhost:9200/_nodes/stats?pretty"

# Check index status
curl -X GET "localhost:9200/_cat/indices?v"

# Monitor performance
curl -X GET "localhost:9200/_cat/thread_pool?v"
```

**Solutions:**
```bash
# Increase heap size (elasticsearch.yml)
echo "-Xms4g" >> /etc/elasticsearch/jvm.options
echo "-Xmx4g" >> /etc/elasticsearch/jvm.options

# Clear old indices
curl -X DELETE "localhost:9200/goad-blue-*-$(date -d '30 days ago' +%Y.%m.%d)"

# Restart Elasticsearch
sudo systemctl restart elasticsearch
```

#### **Logstash Issues**

**Problem:** Logstash not processing logs correctly.

**Troubleshooting:**
```bash
# Check Logstash status
sudo systemctl status logstash

# Test configuration
sudo /usr/share/logstash/bin/logstash --config.test_and_exit

# Monitor pipeline
curl -X GET "localhost:9600/_node/stats/pipelines?pretty"
```

**Configuration Example:**
```ruby
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][log_type] == "goad_blue" {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{GREEDYDATA:message}" }
    }
  }
}

output {
  elasticsearch {
    hosts => ["localhost:9200"]
    index => "goad-blue-%{+YYYY.MM.dd}"
  }
}
```

## 🛡️ Security Monitoring Issues

### **Security Onion Problems**

#### **Sensors Not Detecting Traffic**

**Problem:** Suricata/Zeek not seeing network traffic.

**Diagnostic Steps:**
```bash
# Check sensor status
sudo so-status

# Verify interface configuration
sudo so-sensor-status

# Check traffic on monitoring interface
sudo tcpdump -i eth1 -c 10

# Review sensor logs
sudo tail -f /opt/so/log/suricata/suricata.log
sudo tail -f /opt/so/log/zeek/current/conn.log
```

**Solutions:**
```bash
# Restart sensors
sudo so-sensor-restart

# Reconfigure monitoring interface
sudo so-setup

# Check SPAN/TAP configuration
# Ensure traffic is mirrored to sensor interface

# Update rules
sudo so-rule-update
```

#### **High False Positive Rate**

**Problem:** Too many false positive alerts.

**Tuning Strategies:**
```bash
# Disable noisy rules
sudo so-rule-disable sid:2100498

# Create local rules for whitelisting
# /opt/so/saltstack/local/salt/suricata/files/rules/local.rules
pass tcp $HOME_NET any -> $EXTERNAL_NET 80 (msg:"Allow HTTP"; sid:1000001;)

# Adjust thresholds
# /opt/so/saltstack/local/salt/suricata/files/threshold.conf
threshold gen_id 1, sig_id 2100498, type limit, track by_src, count 10, seconds 60
```

### **Velociraptor Issues**

#### **Agents Not Connecting**

**Problem:** Endpoint agents not communicating with server.

**Troubleshooting:**
```bash
# Check server status
sudo systemctl status velociraptor

# Verify server configuration
sudo cat /etc/velociraptor/server.config.yaml

# Check agent logs
# Windows: C:\Program Files\Velociraptor\logs\
# Linux: /var/log/velociraptor/

# Test connectivity
telnet 192.168.100.85 8000
```

**Solutions:**
```bash
# Restart Velociraptor server
sudo systemctl restart velociraptor

# Regenerate client configuration
sudo velociraptor config generate

# Check firewall
sudo ufw allow 8000/tcp
sudo ufw allow 8889/tcp

# Redeploy agents
python3 goad-blue.py redeploy_agents --force
```

#### **Hunt Performance Issues**

**Problem:** Velociraptor hunts running slowly or timing out.

**Optimization:**
```yaml
# server.config.yaml
Client:
  max_poll: 60
  max_poll_std: 30

Frontend:
  concurrency: 10
  max_upload_size: 10485760

Datastore:
  implementation: FileBaseDataStore
  location: /opt/velociraptor/datastore
  filestore_directory: /opt/velociraptor/filestore
```

## 📊 Monitoring and Alerting Issues

### **MISP Integration Problems**

#### **MISP Not Accessible**

**Problem:** Cannot access MISP web interface.

**Solutions:**
```bash
# Check MISP services
sudo systemctl status apache2
sudo systemctl status mysql
sudo systemctl status redis-server

# Check MISP logs
sudo tail -f /var/log/apache2/misp_error.log

# Reset MISP admin password
cd /var/www/MISP
sudo -u www-data php app/Console/<NAME_EMAIL> newpassword
```

#### **Feed Synchronization Issues**

**Problem:** MISP feeds not updating.

**Troubleshooting:**
```bash
# Check feed status
cd /var/www/MISP
sudo -u www-data php app/Console/cake Server fetchFeed 1

# Update feeds manually
sudo -u www-data php app/Console/cake Server cacheFeed all

# Check cron jobs
sudo crontab -u www-data -l
```

### **TheHive Integration Issues**

#### **Case Creation Problems**

**Problem:** Cannot create cases in TheHive.

**Solutions:**
```bash
# Check TheHive status
sudo systemctl status thehive

# Verify Elasticsearch connection
curl -X GET "localhost:9200/_cluster/health"

# Check TheHive logs
sudo tail -f /var/log/thehive/application.log

# Reset admin password
cd /opt/thehive
sudo bin/thehive -Dconfig.file=conf/application.conf -Dlogger.file=conf/logback.xml -Dpidfile.path=/dev/null migrate-database
```

## 🔧 Infrastructure Component Issues

### **Docker Container Problems**

#### **Containers Won't Start**

**Problem:** Docker containers failing to start.

**Diagnostic Commands:**
```bash
# Check Docker status
sudo systemctl status docker

# List all containers
docker ps -a

# Check container logs
docker logs container_name

# Inspect container configuration
docker inspect container_name
```

**Solutions:**
```bash
# Restart Docker service
sudo systemctl restart docker

# Remove and recreate containers
docker-compose down
docker-compose up -d

# Clean up Docker system
docker system prune -a

# Check disk space
df -h /var/lib/docker
```

#### **Container Resource Issues**

**Problem:** Containers consuming too many resources.

**Monitoring:**
```bash
# Monitor container resources
docker stats

# Check container resource limits
docker inspect container_name | grep -A 10 "Resources"

# Set resource limits
docker run -m 512m --cpus="1.0" image_name
```

### **Network Component Issues**

#### **pfSense Configuration Problems**

**Problem:** pfSense firewall not working correctly.

**Solutions:**
```bash
# Access pfSense console
# Via web interface: https://*************

# Reset to factory defaults
# From console: option 4

# Check firewall rules
# Firewall > Rules

# Verify NAT configuration
# Firewall > NAT

# Monitor logs
# Status > System Logs > Firewall
```

#### **Network Segmentation Issues**

**Problem:** VLANs or network isolation not working.

**Troubleshooting:**
```bash
# Check VLAN configuration
ip link show
bridge vlan show

# Test connectivity between segments
ping -c 3 **************  # Management
ping -c 3 **************  # DMZ
ping -c 3 **************  # Internal

# Verify routing
ip route show
```

## 🔄 Component Recovery Procedures

### **Service Recovery Scripts**

```bash
#!/bin/bash
# Component recovery script

component=$1

case $component in
    "splunk")
        echo "Recovering Splunk..."
        sudo systemctl stop splunk
        sudo /opt/splunk/bin/splunk fsck --all-indexes
        sudo systemctl start splunk
        ;;
    "elasticsearch")
        echo "Recovering Elasticsearch..."
        sudo systemctl stop elasticsearch
        sudo rm -rf /var/lib/elasticsearch/nodes/*/indices/corrupted*
        sudo systemctl start elasticsearch
        ;;
    "velociraptor")
        echo "Recovering Velociraptor..."
        sudo systemctl stop velociraptor
        sudo rm -rf /opt/velociraptor/datastore/locks/*
        sudo systemctl start velociraptor
        ;;
    *)
        echo "Unknown component: $component"
        echo "Available: splunk, elasticsearch, velociraptor"
        ;;
esac
```

### **Health Check Automation**

```python
#!/usr/bin/env python3
# Component health check script

import subprocess
import requests
import json
from datetime import datetime

def check_splunk():
    try:
        response = requests.get('https://localhost:8000/services/server/info', 
                              auth=('admin', 'password'), verify=False, timeout=10)
        return response.status_code == 200
    except:
        return False

def check_elasticsearch():
    try:
        response = requests.get('http://localhost:9200/_cluster/health', timeout=10)
        data = response.json()
        return data['status'] in ['green', 'yellow']
    except:
        return False

def check_velociraptor():
    try:
        response = requests.get('https://localhost:8889/api/v1/GetVersion', 
                              verify=False, timeout=10)
        return response.status_code == 200
    except:
        return False

def main():
    components = {
        'Splunk': check_splunk,
        'Elasticsearch': check_elasticsearch,
        'Velociraptor': check_velociraptor
    }
    
    results = {}
    for name, check_func in components.items():
        results[name] = check_func()
        status = "✓" if results[name] else "✗"
        print(f"{status} {name}: {'Healthy' if results[name] else 'Unhealthy'}")
    
    # Log results
    with open('/var/log/goad-blue-health.json', 'w') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'results': results
        }, f)

if __name__ == "__main__":
    main()
```

---

!!! tip "Component Maintenance"
    - Regularly check component health and logs
    - Implement automated monitoring and alerting
    - Keep components updated with security patches
    - Monitor resource usage and scale as needed
    - Maintain backup and recovery procedures

!!! warning "Critical Components"
    - SIEM components are critical for log analysis
    - Network monitoring affects threat detection
    - Endpoint agents provide visibility into systems
    - Always test changes in non-production environments first

!!! info "Performance Optimization"
    - Tune component configurations for your environment
    - Monitor resource usage and adjust allocations
    - Implement log rotation and retention policies
    - Use load balancing for high-availability deployments
