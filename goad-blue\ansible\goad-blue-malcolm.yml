---
# GOAD-<PERSON> Deployment Playbook
# Deploys and configures <PERSON> for network traffic analysis

- name: Deploy <PERSON>
  hosts: malcolm_servers
  become: yes
  vars_files:
    - vars/goad-blue-config.yml
    - vars/malcolm-config.yml
  
  pre_tasks:
    - name: Validate <PERSON> requirements
      assert:
        that:
          - ansible_memtotal_mb >= 8192
          - ansible_processor_vcpus >= 2
        fail_msg: "<PERSON> requires minimum 8GB RAM and 2 CPU cores"

    - name: Install Docker and Docker Compose
      include_tasks: tasks/common/install_docker.yml

  tasks:
    - name: Clone Malcolm repository
      git:
        repo: "{{ malcolm.repository_url }}"
        dest: "{{ malcolm.install_path }}"
        version: "{{ malcolm.version }}"
        force: yes

    - name: Configure Malcolm environment
      include_tasks: tasks/malcolm/configure_environment.yml

    - name: Configure Malcolm authentication
      include_tasks: tasks/malcolm/configure_auth.yml

    - name: Configure <PERSON> network settings
      include_tasks: tasks/malcolm/configure_network.yml

    - name: Configure <PERSON> for GOAD integration
      include_tasks: tasks/malcolm/configure_goad_integration.yml

    - name: Start Malcolm services
      include_tasks: tasks/malcolm/start_services.yml

    - name: Configure Zeek for GOAD monitoring
      include_tasks: tasks/malcolm/configure_zeek.yml

    - name: Configure Suricata rules
      include_tasks: tasks/malcolm/configure_suricata.yml

    - name: Configure file extraction
      include_tasks: tasks/malcolm/configure_file_extraction.yml

    - name: Configure PCAP processing
      include_tasks: tasks/malcolm/configure_pcap_processing.yml

    - name: Import GOAD-Blue dashboards
      include_tasks: tasks/malcolm/import_dashboards.yml

    - name: Configure log forwarding to SIEM
      include_tasks: tasks/malcolm/configure_siem_forwarding.yml

    - name: Validate Malcolm deployment
      include_tasks: tasks/malcolm/validate_deployment.yml

  post_tasks:
    - name: Generate Malcolm access information
      template:
        src: templates/malcolm_access_info.j2
        dest: "{{ playbook_dir }}/output/malcolm_access.yml"
      delegate_to: localhost

    - name: Display Malcolm deployment summary
      debug:
        msg: |
          Malcolm Deployment Complete:
          Version: {{ malcolm.version }}
          Web Interface: https://{{ ansible_default_ipv4.address }}
          Kibana: https://{{ ansible_default_ipv4.address }}/kibana
          Admin User: {{ malcolm.admin_user }}
          PCAP Upload: https://{{ ansible_default_ipv4.address }}/upload

- name: Configure Malcolm Sensors
  hosts: malcolm_sensors
  become: yes
  vars_files:
    - vars/goad-blue-config.yml
    - vars/malcolm-config.yml
  
  tasks:
    - name: Install network capture tools
      package:
        name:
          - tcpdump
          - tshark
          - netsniff-ng
        state: present

    - name: Configure network interfaces for monitoring
      include_tasks: tasks/malcolm/configure_sensor_interfaces.yml

    - name: Install Zeek sensor
      include_tasks: tasks/malcolm/install_zeek_sensor.yml

    - name: Configure PCAP forwarding to Malcolm
      include_tasks: tasks/malcolm/configure_pcap_forwarding.yml

    - name: Configure GOAD network monitoring
      include_tasks: tasks/malcolm/configure_goad_sensor_monitoring.yml

    - name: Start sensor services
      include_tasks: tasks/malcolm/start_sensor_services.yml

    - name: Validate sensor connectivity
      include_tasks: tasks/malcolm/validate_sensor.yml
