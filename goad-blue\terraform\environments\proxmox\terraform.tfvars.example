# Proxmox Connection Configuration
proxmox_api_url      = "https://192.168.1.100:8006/api2/json"
proxmox_user         = "root@pam"
proxmox_password     = "your-proxmox-password"
proxmox_tls_insecure = true
proxmox_node         = "pve"

# General Configuration
name_prefix = "goad-blue-lab"
environment = "development"

# Storage Configuration
storage_pool = "local-lvm"  # or "zfs-pool" for ZFS storage

# Network Configuration
network_bridge    = "vmbr1"     # Main GOAD-Blue network
monitoring_bridge = "vmbr1"     # Can be same as main or separate
analysis_bridge   = "vmbr2"     # Isolated network for analysis

# Network IP Configuration
network = {
  gateway          = "*************"
  nameserver       = "*******"
  search_domain    = "goad-blue.local"
  splunk_ip        = "*************0"
  so_manager_ip    = "**************"
  sensor_subnet    = "**************/26"
  velociraptor_ip  = "**************"
  misp_ip          = "**************"
  flare_ip         = "**************"
  analysis_gateway = "*************"
}

# SSH Configuration
ssh_public_key_path = "~/.ssh/id_rsa.pub"

# VM Templates (must be created beforehand)
templates = {
  ubuntu  = "ubuntu-22.04-template"
  windows = "windows-10-template"
}

# Component Configuration
components = {
  # Splunk SIEM
  splunk = {
    enabled   = true
    cores     = 4
    memory    = 8192    # 8GB RAM
    disk_size = "100G"
  }
  
  # Security Onion Network Monitoring
  security_onion = {
    enabled            = true
    manager_cores      = 8
    manager_memory     = 16384   # 16GB RAM for manager
    manager_disk_size  = "200G"
    sensor_count       = 2       # Number of sensors
    sensor_cores       = 4
    sensor_memory      = 8192    # 8GB RAM per sensor
    sensor_disk_size   = "100G"
  }
  
  # Velociraptor Endpoint Monitoring
  velociraptor = {
    enabled   = true
    cores     = 2
    memory    = 4096    # 4GB RAM
    disk_size = "50G"
  }
  
  # MISP Threat Intelligence
  misp = {
    enabled   = true
    cores     = 2
    memory    = 4096    # 4GB RAM
    disk_size = "50G"
  }
  
  # FLARE-VM Malware Analysis (optional)
  flare_vm = {
    enabled   = false   # Set to true if needed
    cores     = 4
    memory    = 8192    # 8GB RAM
    disk_size = "100G"
  }
}

# Resource Limits (adjust based on your Proxmox host)
resource_limits = {
  max_cpu_cores  = 32      # Total CPU cores available
  max_memory_mb  = 65536   # 64GB total RAM
  max_storage_gb = 1000    # 1TB total storage
  max_vms        = 10      # Maximum VMs
}

# Backup Configuration
backup_config = {
  enabled        = true
  schedule       = "daily"
  retention_days = 30
  storage_pool   = "backup-storage"  # Configure backup storage
  compression    = "lzo"
}

# Monitoring Configuration
monitoring_config = {
  enabled            = true
  prometheus_enabled = true
  grafana_enabled    = true
  alert_email        = "<EMAIL>"
  slack_webhook      = ""  # Optional Slack webhook URL
}

# Security Configuration
security_config = {
  firewall_enabled   = true
  ssh_key_only       = true
  disable_root_login = true
  fail2ban_enabled   = true
  automatic_updates  = true
}

# GOAD Integration
goad_integration = {
  enabled          = true
  goad_network     = "************/24"  # Existing GOAD network
  auto_discover    = true
  agent_deployment = true
}

# Development Options
development_mode      = false  # Set to true for reduced resources
skip_provisioning     = false  # Set to true to skip Ansible
enable_console_access = true   # Enable VNC console

# Tags
tags = {
  Project     = "GOAD-Blue"
  Environment = "development"
  ManagedBy   = "Terraform"
  Owner       = "Security-Team"
  Purpose     = "Blue-Team-Training"
}

# Production Configuration Example (uncomment and modify as needed)
# components = {
#   splunk = {
#     enabled   = true
#     cores     = 8
#     memory    = 16384
#     disk_size = "500G"
#   }
#   security_onion = {
#     enabled            = true
#     manager_cores      = 16
#     manager_memory     = 32768
#     manager_disk_size  = "1T"
#     sensor_count       = 4
#     sensor_cores       = 8
#     sensor_memory      = 16384
#     sensor_disk_size   = "500G"
#   }
#   velociraptor = {
#     enabled   = true
#     cores     = 4
#     memory    = 8192
#     disk_size = "200G"
#   }
#   misp = {
#     enabled   = true
#     cores     = 4
#     memory    = 8192
#     disk_size = "200G"
#   }
#   flare_vm = {
#     enabled   = true
#     cores     = 8
#     memory    = 16384
#     disk_size = "500G"
#   }
# }

# High Availability Configuration (enterprise)
# ha_config = {
#   enabled                = true
#   splunk_cluster_enabled = true
#   splunk_indexers        = 3
#   splunk_search_heads    = 2
#   so_manager_ha          = true
#   load_balancer_enabled  = true
# }

# Storage Configuration for ZFS
# storage_config = {
#   type           = "zfs"
#   pool_name      = "rpool/vmdata"
#   compression    = "lz4"
#   deduplication  = false
#   snapshots      = true
#   replication    = false
# }

# Network VLAN Configuration (advanced)
# vlan_config = {
#   management_vlan = 100
#   monitoring_vlan = 200
#   analysis_vlan   = 300
#   trunk_enabled   = true
# }
