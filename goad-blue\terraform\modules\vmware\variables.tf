# VMware vSphere variables for GOAD-Blue

variable "lab_name" {
  description = "Name of the GOAD-Blue lab"
  type        = string
}

variable "datacenter_name" {
  description = "vSphere datacenter name"
  type        = string
  default     = "Datacenter"
}

variable "cluster_name" {
  description = "vSphere cluster name"
  type        = string
  default     = "Cluster"
}

variable "datastore_name" {
  description = "vSphere datastore name"
  type        = string
  default     = "datastore1"
}

variable "network_name" {
  description = "vSphere network name"
  type        = string
  default     = "VM Network"
}

# Network configuration
variable "base_cidr" {
  description = "Base CIDR for the lab network"
  type        = string
}

variable "siem_subnet" {
  description = "SIEM subnet CIDR"
  type        = string
}

variable "monitoring_subnet" {
  description = "Monitoring subnet CIDR"
  type        = string
}

variable "red_team_subnet" {
  description = "Red team subnet CIDR"
  type        = string
}

variable "analysis_subnet" {
  description = "Analysis subnet CIDR"
  type        = string
}

# Component flags
variable "siem_enabled" {
  description = "Enable SIEM deployment"
  type        = bool
  default     = true
}

variable "siem_type" {
  description = "SIEM type (splunk or elastic)"
  type        = string
  default     = "splunk"
}

variable "security_onion_enabled" {
  description = "Enable Security Onion deployment"
  type        = bool
  default     = true
}

variable "malcolm_enabled" {
  description = "Enable Malcolm deployment"
  type        = bool
  default     = false
}

variable "velociraptor_enabled" {
  description = "Enable Velociraptor deployment"
  type        = bool
  default     = true
}

variable "misp_enabled" {
  description = "Enable MISP deployment"
  type        = bool
  default     = true
}

variable "flare_vm_enabled" {
  description = "Enable FLARE-VM deployment"
  type        = bool
  default     = true
}

# Template UUIDs (these would be set after Packer builds)
variable "siem_template_uuid" {
  description = "UUID of the SIEM template"
  type        = string
  default     = ""
}

variable "security_onion_template_uuid" {
  description = "UUID of the Security Onion template"
  type        = string
  default     = ""
}

variable "malcolm_template_uuid" {
  description = "UUID of the Malcolm template"
  type        = string
  default     = ""
}

variable "velociraptor_template_uuid" {
  description = "UUID of the Velociraptor template"
  type        = string
  default     = ""
}

variable "misp_template_uuid" {
  description = "UUID of the MISP template"
  type        = string
  default     = ""
}

variable "flare_vm_template_uuid" {
  description = "UUID of the FLARE-VM template"
  type        = string
  default     = ""
}
