# GOAD-Blue Components

GOAD-Blue consists of several integrated security components that work together to provide comprehensive blue team capabilities. Each component serves a specific purpose in the overall security architecture.

## 🏗️ Component Architecture

```mermaid
graph TB
    subgraph "📊 SIEM Layer"
        SPLUNK[📈 Splunk Enterprise<br/>Central Logging & Analysis]
        ELASTIC[🔍 Elastic Stack<br/>Search & Analytics]
    end
    
    subgraph "🔍 Network Monitoring"
        SO[🧅 Security Onion<br/>Network Security Monitoring]
        MALCOLM[🕵️ <PERSON><br/>Network Forensics]
    end
    
    subgraph "💻 Endpoint Visibility"
        VELO[🦖 Velociraptor<br/>Endpoint Detection & Response]
        SYSMON[👁️ Sysmon<br/>Windows Activity Monitoring]
    end
    
    subgraph "🧠 Threat Intelligence"
        MISP[🎯 MISP<br/>Threat Intelligence Platform]
        FEEDS[📡 Threat Feeds<br/>External Intelligence]
    end
    
    subgraph "🔬 Malware Analysis"
        FLARE[🔥 FLARE-VM<br/>Malware Analysis Toolkit]
        SANDBOX[📦 Analysis Sandbox<br/>Automated Analysis]
    end
    
    %% Data Flow
    SO --> SPLUNK
    SO --> ELASTIC
    MALCOLM --> SPLUNK
    VELO --> SPLUNK
    SYSMON --> SPLUNK
    MISP --> SPLUNK
    FEEDS --> MISP
    FLARE --> MISP
    SANDBOX --> SPLUNK
    
    %% Styling
    classDef siem fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef monitoring fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef endpoint fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef intelligence fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef analysis fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    
    class SPLUNK,ELASTIC siem
    class SO,MALCOLM monitoring
    class VELO,SYSMON endpoint
    class MISP,FEEDS intelligence
    class FLARE,SANDBOX analysis
```

## 📊 SIEM Platforms

### **Splunk Enterprise**
- **Purpose**: Centralized log management and security analytics
- **Key Features**: Real-time search, alerting, dashboards, machine learning
- **Use Cases**: Log analysis, correlation, compliance reporting
- **Integration**: Universal Forwarders, REST API, custom apps

### **Elastic Stack (ELK)**
- **Purpose**: Open-source search and analytics platform
- **Key Features**: Elasticsearch, Logstash, Kibana, Beats
- **Use Cases**: Log aggregation, visualization, threat hunting
- **Integration**: Beats agents, REST API, custom pipelines

[📖 Learn More: SIEM Platforms](siem/)

## 🔍 Network Monitoring

### **Security Onion**
- **Purpose**: Network security monitoring and threat hunting
- **Key Features**: Suricata IDS, Zeek NSM, Wazuh HIDS, Hunt platform
- **Use Cases**: Network traffic analysis, intrusion detection, incident response
- **Integration**: PCAP analysis, log forwarding, case management

### **Malcolm**
- **Purpose**: Network forensics and packet analysis
- **Key Features**: Arkime integration, Zeek logs, Suricata alerts
- **Use Cases**: Network forensics, traffic analysis, threat hunting
- **Integration**: PCAP storage, Elasticsearch backend

[📖 Learn More: Network Monitoring](monitoring/)

## 💻 Endpoint Visibility

### **Velociraptor**
- **Purpose**: Endpoint visibility and digital forensics
- **Key Features**: Live response, artifact collection, hunt capabilities
- **Use Cases**: Incident response, threat hunting, forensic analysis
- **Integration**: Agent deployment, API access, custom artifacts

### **Sysmon**
- **Purpose**: Windows system activity monitoring
- **Key Features**: Process creation, network connections, file modifications
- **Use Cases**: Endpoint monitoring, attack detection, forensic analysis
- **Integration**: Windows Event Log, custom configuration

[📖 Learn More: Endpoint Visibility](endpoint/)

## 🧠 Threat Intelligence

### **MISP (Malware Information Sharing Platform)**
- **Purpose**: Threat intelligence sharing and management
- **Key Features**: IOC management, event correlation, threat sharing
- **Use Cases**: Threat intelligence, IOC enrichment, attribution analysis
- **Integration**: API feeds, SIEM integration, automated enrichment

[📖 Learn More: Threat Intelligence](threat-intel/)

## 🔬 Malware Analysis

### **FLARE-VM**
- **Purpose**: Windows-based malware analysis environment
- **Key Features**: Static analysis tools, dynamic analysis, reverse engineering
- **Use Cases**: Malware analysis, reverse engineering, threat research
- **Integration**: Isolated network, sample collection, result sharing

[📖 Learn More: Malware Analysis](analysis/)

## 🔧 Component Selection Guide

### **Minimal Deployment**
For basic blue team capabilities:
- ✅ **SIEM Platform** (Splunk or Elastic)
- ✅ **Security Onion** (Network monitoring)
- ✅ **Velociraptor** (Endpoint visibility)

### **Standard Deployment**
For comprehensive security operations:
- ✅ **SIEM Platform** (Splunk or Elastic)
- ✅ **Security Onion** (Network monitoring)
- ✅ **Velociraptor** (Endpoint visibility)
- ✅ **MISP** (Threat intelligence)

### **Advanced Deployment**
For research and advanced analysis:
- ✅ **SIEM Platform** (Splunk or Elastic)
- ✅ **Security Onion** (Network monitoring)
- ✅ **Malcolm** (Network forensics)
- ✅ **Velociraptor** (Endpoint visibility)
- ✅ **MISP** (Threat intelligence)
- ✅ **FLARE-VM** (Malware analysis)

### **Enterprise Deployment**
For production environments:
- ✅ **Elastic Stack** (Scalable SIEM)
- ✅ **Security Onion** (Distributed sensors)
- ✅ **Malcolm** (Network forensics)
- ✅ **Velociraptor** (Enterprise EDR)
- ✅ **MISP** (Threat intelligence)
- ✅ **Custom Integrations**

## 📈 Resource Requirements

| Component | RAM | CPU | Storage | Network |
|-----------|-----|-----|---------|---------|
| **Splunk Enterprise** | 8GB | 4 cores | 100GB | 1Gbps |
| **Elastic Stack** | 8GB | 4 cores | 100GB | 1Gbps |
| **Security Onion Manager** | 16GB | 8 cores | 200GB | 1Gbps |
| **Security Onion Sensor** | 8GB | 4 cores | 100GB | 1Gbps |
| **Malcolm** | 8GB | 4 cores | 200GB | 1Gbps |
| **Velociraptor** | 4GB | 2 cores | 50GB | 100Mbps |
| **MISP** | 4GB | 2 cores | 50GB | 100Mbps |
| **FLARE-VM** | 8GB | 4 cores | 100GB | 100Mbps |

## 🔗 Component Integration

### **Data Flow Integration**
```mermaid
sequenceDiagram
    participant GOAD as 🎯 GOAD Environment
    participant Agents as 🤖 Monitoring Agents
    participant SIEM as 📊 SIEM Platform
    participant Intel as 🧠 Threat Intelligence
    participant Analyst as 👤 Security Analyst
    
    GOAD->>Agents: Generate Activity
    Agents->>SIEM: Forward Logs/Events
    SIEM->>Intel: Query IOCs
    Intel-->>SIEM: Return Context
    SIEM->>Analyst: Generate Alert
    Analyst->>SIEM: Investigate
    Analyst->>Intel: Update IOCs
```

### **API Integration**
All components provide REST APIs for:
- **Configuration Management**
- **Data Export/Import**
- **Status Monitoring**
- **Custom Automation**

### **Single Sign-On (SSO)**
Optional SSO integration supports:
- **LDAP/Active Directory**
- **SAML 2.0**
- **OAuth 2.0**
- **Multi-Factor Authentication**

## 🎯 Use Case Mapping

### **Red Team vs Blue Team Exercises**
- **SIEM**: Central correlation and alerting
- **Network Monitoring**: Traffic analysis and detection
- **Endpoint Visibility**: Host-based detection
- **Threat Intelligence**: IOC correlation

### **SOC Training**
- **SIEM**: Alert triage and investigation
- **Network Monitoring**: Traffic analysis skills
- **Endpoint Visibility**: Incident response
- **Threat Intelligence**: Context and attribution

### **Threat Hunting**
- **SIEM**: Hypothesis testing and data analysis
- **Network Monitoring**: Behavioral analysis
- **Endpoint Visibility**: Host-based hunting
- **Threat Intelligence**: IOC development

### **Incident Response**
- **SIEM**: Timeline reconstruction
- **Network Monitoring**: Network forensics
- **Endpoint Visibility**: Live response
- **Malware Analysis**: Sample analysis

## 🔧 Configuration Management

### **Centralized Configuration**
```yaml
# goad-blue-config.yml
components:
  splunk:
    enabled: true
    version: "9.1.2"
    license: "enterprise"
    apps: ["ES", "ITSI", "MLTK"]
  
  security_onion:
    enabled: true
    version: "2.4.60"
    sensors: 2
    manager_ha: false
  
  velociraptor:
    enabled: true
    version: "0.7.0"
    server_ha: false
    agents: "auto-deploy"
```

### **Component Dependencies**
- **SIEM** ← All other components (log destination)
- **Network Monitoring** ← GOAD environment (traffic source)
- **Endpoint Visibility** ← GOAD VMs (agent deployment)
- **Threat Intelligence** ← External feeds (IOC sources)

---

!!! info "Component Details"
    Each component has detailed documentation covering installation, configuration, and operation. Use the navigation menu to explore specific components.

!!! tip "Start Small"
    Begin with a minimal deployment and add components as needed. This approach reduces complexity and allows you to learn each component thoroughly.

!!! warning "Resource Planning"
    Ensure your infrastructure can support the selected components. Monitor resource usage and scale as needed.
