#!/bin/bash
# GOAD-Blue Documentation Build Script

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[DOCS INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[DOCS SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[DOCS WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[DOCS ERROR]${NC} $1"
}

print_banner() {
    echo -e "${BLUE}"
    cat << "EOF"
   ____  ____    _    ____       ____  _            
  / ___|/ __ \  / \  |  _ \     | __ )| |_   _  ___ 
 | |  _| |  | |/ _ \ | | | |____|  _ \| | | | |/ _ \
 | |_| | |__| / ___ \| |_| |_____| |_) | | |_| |  __/
  \____|\____/_/   \_\____/      |____/|_|\__,_|\___|
                                                     
EOF
    echo -e "${NC}"
    echo -e "${BLUE}📚 Documentation Builder${NC}"
    echo "=" * 50
}

check_dependencies() {
    log_info "Checking documentation dependencies..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is required but not installed"
        exit 1
    fi
    
    # Check pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 is required but not installed"
        exit 1
    fi
    
    log_success "Dependencies check passed"
}

install_requirements() {
    log_info "Installing documentation requirements..."

    cd "$PROJECT_ROOT"

    if [ -f "requirements-docs-minimal.txt" ]; then
        log_info "Installing minimal requirements first..."
        pip3 install -r requirements-docs-minimal.txt

        if [ -f "requirements-docs.txt" ]; then
            log_info "Attempting to install full requirements..."
            if pip3 install -r requirements-docs.txt; then
                log_success "Full documentation requirements installed"
            else
                log_warning "Some optional plugins failed to install, continuing with minimal setup"
            fi
        fi
    elif [ -f "requirements-docs.txt" ]; then
        log_info "Installing requirements from requirements-docs.txt..."
        if pip3 install -r requirements-docs.txt; then
            log_success "Documentation requirements installed"
        else
            log_warning "Some requirements failed, installing basic requirements"
            pip3 install mkdocs mkdocs-material pymdown-extensions
        fi
    else
        log_warning "No requirements file found, installing basic requirements"
        pip3 install mkdocs mkdocs-material pymdown-extensions
    fi
}

validate_config() {
    log_info "Validating MkDocs configuration..."
    
    cd "$PROJECT_ROOT"
    
    if [ ! -f "mkdocs.yml" ]; then
        log_error "mkdocs.yml not found in project root"
        exit 1
    fi
    
    # Test configuration
    if mkdocs config-check; then
        log_success "MkDocs configuration is valid"
    else
        log_error "MkDocs configuration validation failed"
        exit 1
    fi
}

build_docs() {
    log_info "Building documentation..."
    
    cd "$PROJECT_ROOT"
    
    # Clean previous build
    if [ -d "site" ]; then
        rm -rf site
        log_info "Cleaned previous build"
    fi
    
    # Build documentation
    if mkdocs build --strict; then
        log_success "Documentation built successfully"
        log_info "Documentation available in: $PROJECT_ROOT/site/"
    else
        log_error "Documentation build failed"
        exit 1
    fi
}

serve_docs() {
    log_info "Starting documentation server..."
    
    cd "$PROJECT_ROOT"
    
    log_success "Documentation server starting..."
    log_info "Access documentation at: http://localhost:8000"
    log_info "Press Ctrl+C to stop the server"
    
    mkdocs serve --dev-addr=0.0.0.0:8000
}

deploy_docs() {
    log_info "Deploying documentation to GitHub Pages..."
    
    cd "$PROJECT_ROOT"
    
    if [ ! -d ".git" ]; then
        log_error "Not a git repository. Cannot deploy to GitHub Pages."
        exit 1
    fi
    
    # Check if gh-pages branch exists
    if git show-ref --verify --quiet refs/heads/gh-pages; then
        log_info "gh-pages branch exists"
    else
        log_info "Creating gh-pages branch"
        git checkout --orphan gh-pages
        git rm -rf .
        git commit --allow-empty -m "Initial gh-pages commit"
        git checkout main  # or master
    fi
    
    # Deploy to GitHub Pages
    if mkdocs gh-deploy --force; then
        log_success "Documentation deployed to GitHub Pages"
    else
        log_error "GitHub Pages deployment failed"
        exit 1
    fi
}

show_help() {
    echo "GOAD-Blue Documentation Builder"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  install     Install documentation dependencies"
    echo "  build       Build static documentation"
    echo "  serve       Serve documentation locally (default)"
    echo "  deploy      Deploy to GitHub Pages"
    echo "  validate    Validate MkDocs configuration"
    echo "  clean       Clean build artifacts"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Serve documentation locally"
    echo "  $0 build             # Build static documentation"
    echo "  $0 deploy            # Deploy to GitHub Pages"
}

clean_build() {
    log_info "Cleaning build artifacts..."
    
    cd "$PROJECT_ROOT"
    
    # Remove build directory
    if [ -d "site" ]; then
        rm -rf site
        log_info "Removed site/ directory"
    fi
    
    # Remove Python cache
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -type f -name "*.pyc" -delete 2>/dev/null || true
    
    log_success "Build artifacts cleaned"
}

main() {
    print_banner
    
    # Parse command line arguments
    COMMAND="${1:-serve}"
    
    case "$COMMAND" in
        "install")
            check_dependencies
            install_requirements
            ;;
        "build")
            check_dependencies
            validate_config
            build_docs
            ;;
        "serve")
            check_dependencies
            validate_config
            log_info "Building and serving documentation..."
            serve_docs
            ;;
        "deploy")
            check_dependencies
            validate_config
            build_docs
            deploy_docs
            ;;
        "validate")
            check_dependencies
            validate_config
            ;;
        "clean")
            clean_build
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# Handle script interruption
trap 'echo -e "\n${RED}Documentation build interrupted${NC}"; exit 1' INT

# Run main function
main "$@"
