#!/bin/bash
# GOAD-Blue Installer Script
# Interactive installer for GOAD-Blue components

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_ROOT/goad-blue-config.yml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# ASCII Art Banner
print_banner() {
    echo -e "${BLUE}"
    cat << "EOF"
   ____  ____    _    ____       ____  _            
  / ___|/ __ \  / \  |  _ \     | __ )| |_   _  ___ 
 | |  _| |  | |/ _ \ | | | |____|  _ \| | | | |/ _ \
 | |_| | |__| / ___ \| |_| |_____| |_) | | |_| |  __/
  \____|\____/_/   \_\____/      |____/|_|\__,_|\___|
                                                     
EOF
    echo -e "${NC}"
    echo -e "${PURPLE}🛡️  Full-Spectrum Cybersecurity Training Platform 🛡️${NC}"
    echo "=" * 60
}

log_info() {
    echo -e "${BLUE}[GOAD-Blue INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[GOAD-Blue SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[GOAD-Blue WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[GOAD-Blue ERROR]${NC} $1"
}

prompt_yes_no() {
    local prompt="$1"
    local default="${2:-n}"
    local response
    
    while true; do
        if [[ "$default" == "y" ]]; then
            read -p "$prompt [Y/n]: " response
            response=${response:-y}
        else
            read -p "$prompt [y/N]: " response
            response=${response:-n}
        fi
        
        case "$response" in
            [Yy]|[Yy][Ee][Ss]) return 0 ;;
            [Nn]|[Nn][Oo]) return 1 ;;
            *) echo "Please answer yes or no." ;;
        esac
    done
}

select_option() {
    local prompt="$1"
    shift
    local options=("$@")
    local choice
    
    echo "$prompt"
    for i in "${!options[@]}"; do
        echo "$((i+1)). ${options[i]}"
    done
    
    while true; do
        read -p "Enter choice [1-${#options[@]}]: " choice
        if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le "${#options[@]}" ]; then
            echo "${options[$((choice-1))]}"
            return
        else
            echo "Invalid choice. Please enter a number between 1 and ${#options[@]}."
        fi
    done
}

check_dependencies() {
    log_info "Checking dependencies..."
    
    local missing_deps=()
    
    # Check for required tools
    command -v packer >/dev/null 2>&1 || missing_deps+=("packer")
    command -v terraform >/dev/null 2>&1 || missing_deps+=("terraform")
    command -v ansible >/dev/null 2>&1 || missing_deps+=("ansible")
    command -v docker >/dev/null 2>&1 || missing_deps+=("docker")
    command -v python3 >/dev/null 2>&1 || missing_deps+=("python3")
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        log_info "Please install the missing dependencies and run the installer again."
        exit 1
    fi
    
    log_success "All dependencies found"
}

check_goad_installation() {
    log_info "Checking for existing GOAD installation..."
    
    if [ -f "$PROJECT_ROOT/../goad.py" ]; then
        log_success "GOAD installation detected - integration will be enabled"
        return 0
    else
        log_warning "GOAD installation not found - running in standalone mode"
        return 1
    fi
}

generate_config() {
    log_info "Starting GOAD-Blue Configuration..."
    
    # Provider selection
    echo -e "\n${BLUE}🏗️  Infrastructure Provider Selection:${NC}"
    PROVIDER=$(select_option "Select deployment provider:" "vmware" "virtualbox" "aws" "azure" "proxmox")
    
    # SIEM selection
    echo -e "\n${BLUE}🔎 SIEM Platform Selection:${NC}"
    SIEM_TYPE=$(select_option "Select SIEM platform:" "splunk" "elastic" "none")
    SIEM_ENABLED=$([[ "$SIEM_TYPE" != "none" ]] && echo "true" || echo "false")
    
    # Component selection
    echo -e "\n${BLUE}🔧 Blue Team Components Selection:${NC}"
    SECURITY_ONION_ENABLED=$(prompt_yes_no "Deploy Security Onion (Suricata, Zeek, Wazuh)?" "y" && echo "true" || echo "false")
    MALCOLM_ENABLED=$(prompt_yes_no "Deploy Malcolm (Network Forensics)?" "n" && echo "true" || echo "false")
    VELOCIRAPTOR_ENABLED=$(prompt_yes_no "Deploy Velociraptor (Endpoint Visibility)?" "y" && echo "true" || echo "false")
    MISP_ENABLED=$(prompt_yes_no "Deploy MISP (Threat Intelligence)?" "y" && echo "true" || echo "false")
    FLARE_VM_ENABLED=$(prompt_yes_no "Deploy FLARE-VM (Malware Analysis)?" "y" && echo "true" || echo "false")
    
    # Network configuration
    echo -e "\n${BLUE}🌐 Network Configuration:${NC}"
    read -p "Base CIDR [192.168.100.0/24]: " BASE_CIDR
    BASE_CIDR=${BASE_CIDR:-192.168.100.0/24}
    
    # Generate configuration file
    cat > "$CONFIG_FILE" << EOF
goad_blue:
  name: "goad-blue-lab"
  provider: "$PROVIDER"
  version: "3.1.0"

siem:
  type: "$SIEM_TYPE"
  enabled: $SIEM_ENABLED

components:
  security_onion:
    enabled: $SECURITY_ONION_ENABLED
    version: "2.4"
  malcolm:
    enabled: $MALCOLM_ENABLED
  velociraptor:
    enabled: $VELOCIRAPTOR_ENABLED
  misp:
    enabled: $MISP_ENABLED
  flare_vm:
    enabled: $FLARE_VM_ENABLED

network:
  base_cidr: "$BASE_CIDR"
  siem_subnet: "192.168.100.0/26"
  monitoring_subnet: "192.168.100.64/26"
  red_team_subnet: "192.168.100.128/26"
  analysis_subnet: "192.168.100.192/26"

integration:
  goad_enabled: true
  auto_discover: true
  agent_deployment: true
EOF
    
    log_success "GOAD-Blue configuration saved to $CONFIG_FILE"
}

build_images() {
    log_info "Building GOAD-Blue VM images with Packer..."
    
    cd "$PROJECT_ROOT/packer"
    
    if [[ "$SIEM_ENABLED" == "true" && "$SIEM_TYPE" == "splunk" ]]; then
        log_info "Building GOAD-Blue Splunk server image..."
        if [ -f "goad-blue-splunk-server.json" ]; then
            packer build goad-blue-splunk-server.json || {
                log_error "Failed to build Splunk server image"
                return 1
            }
        else
            log_warning "Splunk server Packer template not found"
        fi
    fi
    
    if [[ "$SIEM_ENABLED" == "true" && "$SIEM_TYPE" == "elastic" ]]; then
        log_info "Building GOAD-Blue Elastic Stack image..."
        if [ -f "goad-blue-elastic-stack.json" ]; then
            packer build goad-blue-elastic-stack.json || {
                log_error "Failed to build Elastic Stack image"
                return 1
            }
        else
            log_warning "Elastic Stack Packer template not found"
        fi
    fi
    
    if [[ "$SECURITY_ONION_ENABLED" == "true" ]]; then
        log_info "Building GOAD-Blue Security Onion image..."
        if [ -f "goad-blue-security-onion.json" ]; then
            packer build goad-blue-security-onion.json || {
                log_error "Failed to build Security Onion image"
                return 1
            }
        else
            log_warning "Security Onion Packer template not found"
        fi
    fi
    
    if [[ "$VELOCIRAPTOR_ENABLED" == "true" ]]; then
        log_info "Building GOAD-Blue Velociraptor server image..."
        if [ -f "goad-blue-velociraptor-server.json" ]; then
            packer build goad-blue-velociraptor-server.json || {
                log_error "Failed to build Velociraptor server image"
                return 1
            }
        else
            log_warning "Velociraptor server Packer template not found"
        fi
    fi
    
    if [[ "$FLARE_VM_ENABLED" == "true" ]]; then
        log_info "Building GOAD-Blue FLARE-VM image..."
        if [ -f "goad-blue-flare-vm.json" ]; then
            packer build goad-blue-flare-vm.json || {
                log_error "Failed to build FLARE-VM image"
                return 1
            }
        else
            log_warning "FLARE-VM Packer template not found"
        fi
    fi
    
    log_success "Image building completed"
}

deploy_infrastructure() {
    log_info "Deploying GOAD-Blue infrastructure with Terraform..."
    
    cd "$PROJECT_ROOT/terraform"
    
    if [ ! -f "goad-blue-main.tf" ]; then
        log_error "Terraform configuration not found"
        return 1
    fi
    
    terraform init || {
        log_error "Terraform init failed"
        return 1
    }
    
    terraform plan -var-file="../goad-blue-config.yml" || {
        log_error "Terraform plan failed"
        return 1
    }
    
    if prompt_yes_no "Apply Terraform configuration?"; then
        terraform apply -var-file="../goad-blue-config.yml" -auto-approve || {
            log_error "Terraform apply failed"
            return 1
        }
        log_success "Infrastructure deployed successfully"
    else
        log_warning "Terraform apply cancelled"
        return 1
    fi
}

configure_systems() {
    log_info "Configuring GOAD-Blue systems with Ansible..."
    
    cd "$PROJECT_ROOT/ansible"
    
    if [ ! -f "goad-blue-site.yml" ]; then
        log_error "Ansible playbook not found"
        return 1
    fi
    
    # Generate inventory from Terraform output
    if [ -f "../terraform/terraform.tfstate" ]; then
        terraform output -json > inventory/terraform_inventory.json 2>/dev/null || {
            log_warning "Failed to generate Terraform inventory"
        }
    fi
    
    # Run Ansible playbooks
    ansible-playbook -i inventory/ goad-blue-site.yml || {
        log_error "Ansible configuration failed"
        return 1
    }
    
    log_success "System configuration completed"
}

integrate_with_goad() {
    if check_goad_installation; then
        log_info "Integrating with existing GOAD environment..."
        
        cd "$PROJECT_ROOT"
        python3 goad-blue.py --integrate-goad || {
            log_error "GOAD integration failed"
            return 1
        }
        
        log_success "GOAD integration completed"
    else
        log_info "No GOAD installation found - skipping integration"
    fi
}

show_completion_info() {
    echo -e "\n${GREEN}🎉 GOAD-Blue Installation Completed! 🎉${NC}"
    echo "=" * 50
    echo -e "${BLUE}Access Information:${NC}"
    
    if [[ "$SIEM_TYPE" == "splunk" ]]; then
        echo "• Splunk Web: https://**************:8000"
        echo "  Username: admin"
        echo "  Password: ChangeMePlease123!"
    elif [[ "$SIEM_TYPE" == "elastic" ]]; then
        echo "• Kibana: https://**************:5601"
        echo "  Username: elastic"
        echo "  Password: ChangeMePlease123!"
    fi
    
    if [[ "$SECURITY_ONION_ENABLED" == "true" ]]; then
        echo "• Security Onion: https://**************"
    fi
    
    if [[ "$MISP_ENABLED" == "true" ]]; then
        echo "• MISP: https://**************"
        echo "  Username: <EMAIL>"
    fi
    
    if [[ "$VELOCIRAPTOR_ENABLED" == "true" ]]; then
        echo "• Velociraptor: https://**************:8889"
    fi
    
    echo -e "\n${BLUE}Next Steps:${NC}"
    echo "1. Access the web interfaces above"
    echo "2. Run attack simulations using GOAD"
    echo "3. Monitor detections in your SIEM"
    echo "4. Practice incident response workflows"
    
    echo -e "\n${BLUE}Management Commands:${NC}"
    echo "• Start GOAD-Blue: python3 goad-blue.py -t start"
    echo "• Stop GOAD-Blue: python3 goad-blue.py -t stop"
    echo "• Status Check: python3 goad-blue.py -t status"
    echo "• Interactive Mode: python3 goad-blue.py"
}

main() {
    print_banner
    
    log_info "Starting GOAD-Blue installation..."
    
    # Check dependencies
    check_dependencies
    
    # Check for GOAD
    check_goad_installation
    
    # Interactive configuration
    if prompt_yes_no "Run interactive configuration?" "y"; then
        generate_config
    else
        if [ ! -f "$CONFIG_FILE" ]; then
            log_error "No configuration file found. Please run interactive configuration."
            exit 1
        fi
        log_info "Using existing configuration file"
    fi
    
    # Load configuration variables
    source <(python3 -c "
import yaml
with open('$CONFIG_FILE', 'r') as f:
    config = yaml.safe_load(f)
    print(f'SIEM_TYPE={config[\"siem\"][\"type\"]}')
    print(f'SIEM_ENABLED={str(config[\"siem\"][\"enabled\"]).lower()}')
    print(f'SECURITY_ONION_ENABLED={str(config[\"components\"][\"security_onion\"][\"enabled\"]).lower()}')
    print(f'VELOCIRAPTOR_ENABLED={str(config[\"components\"][\"velociraptor\"][\"enabled\"]).lower()}')
    print(f'MISP_ENABLED={str(config[\"components\"][\"misp\"][\"enabled\"]).lower()}')
    print(f'FLARE_VM_ENABLED={str(config[\"components\"][\"flare_vm\"][\"enabled\"]).lower()}')
")
    
    # Build images
    if prompt_yes_no "Build VM images with Packer?" "y"; then
        build_images || exit 1
    fi
    
    # Deploy infrastructure
    if prompt_yes_no "Deploy infrastructure with Terraform?" "y"; then
        deploy_infrastructure || exit 1
    fi
    
    # Configure systems
    if prompt_yes_no "Configure systems with Ansible?" "y"; then
        configure_systems || exit 1
    fi
    
    # Integrate with GOAD
    if prompt_yes_no "Integrate with existing GOAD environment?" "y"; then
        integrate_with_goad
    fi
    
    # Show completion information
    show_completion_info
    
    log_success "GOAD-Blue installation completed successfully!"
}

# Handle script interruption
trap 'echo -e "\n${RED}Installation interrupted by user${NC}"; exit 1' INT

# Run main function
main "$@"
