# GOAD-Blue Deployment Flow Diagrams

## Complete Deployment Pipeline

```mermaid
graph TB
    subgraph "🚀 Deployment Initiation"
        USER[👤 User/Administrator]
        CLI[🖥️ GOAD-Blue CLI<br/>goad-blue.py --interactive]
        INSTALLER[📦 Interactive Installer<br/>goad-blue-installer.sh]
    end
    
    subgraph "✅ Prerequisites & Validation"
        DEPS_CHECK[🔍 Dependency Check<br/>• Python 3.8+<br/>• Packer<br/>• Terraform<br/>• Ansible<br/>• Docker]
        
        GOAD_DISCOVERY[🎯 GOAD Discovery<br/>• Scan for existing GOAD<br/>• Validate compatibility<br/>• Map network topology]
        
        RESOURCE_CHECK[💻 Resource Validation<br/>• RAM: 32GB minimum<br/>• Storage: 500GB<br/>• CPU: 8 cores<br/>• Network interfaces]
    end
    
    subgraph "⚙️ Configuration Phase"
        CONFIG_WIZARD[🧙‍♂️ Configuration Wizard<br/>• Component selection<br/>• Provider choice<br/>• Network settings]
        
        COMPONENT_SELECT[🔧 Component Selection<br/>• SIEM: Splunk/Elastic<br/>• Security Onion<br/>• Velociraptor<br/>• MISP<br/>• FLARE-VM]
        
        NETWORK_CONFIG[🌐 Network Configuration<br/>• Subnet allocation<br/>• IP addressing<br/>• Firewall rules]
    end
    
    subgraph "📦 Image Building (Packer)"
        PACKER_INIT[🏗️ Packer Initialization<br/>• Load templates<br/>• Validate configs<br/>• Download ISOs]
        
        BASE_IMAGES[🐧 Base Image Building<br/>• Ubuntu 22.04 LTS<br/>• Windows Server 2019<br/>• Windows 10]
        
        CUSTOM_IMAGES[🎨 Custom Image Creation<br/>• Install security tools<br/>• Apply configurations<br/>• Security hardening]
    end
    
    subgraph "🏗️ Infrastructure Provisioning (Terraform)"
        TERRAFORM_INIT[🌍 Terraform Init<br/>• Provider setup<br/>• Module loading<br/>• State initialization]
        
        INFRA_PLAN[📋 Infrastructure Planning<br/>• Resource calculation<br/>• Dependency mapping<br/>• Cost estimation]
        
        INFRA_DEPLOY[🚀 Infrastructure Deployment<br/>• VM creation<br/>• Network setup<br/>• Storage allocation]
    end
    
    subgraph "⚙️ System Configuration (Ansible)"
        ANSIBLE_PREP[📚 Ansible Preparation<br/>• Inventory generation<br/>• Playbook validation<br/>• Credential setup]
        
        COMPONENT_DEPLOY[🔧 Component Deployment<br/>• SIEM installation<br/>• Security tool setup<br/>• Agent deployment]
        
        INTEGRATION_CONFIG[🔗 Integration Configuration<br/>• GOAD agent deployment<br/>• Log forwarding<br/>• Network monitoring]
    end
    
    subgraph "✅ Validation & Testing"
        HEALTH_CHECK[🏥 Health Checks<br/>• Service status<br/>• Connectivity tests<br/>• Performance validation]
        
        DATA_FLOW_TEST[📊 Data Flow Testing<br/>• Log ingestion<br/>• Alert generation<br/>• Dashboard population]
        
        INTEGRATION_TEST[🔗 Integration Testing<br/>• GOAD connectivity<br/>• Agent communication<br/>• End-to-end workflow]
    end
    
    subgraph "🎉 Completion & Handover"
        ACCESS_INFO[📋 Access Information<br/>• Web interface URLs<br/>• Default credentials<br/>• Configuration details]
        
        DOCUMENTATION[📚 Documentation<br/>• Deployment report<br/>• Configuration backup<br/>• User guides]
        
        READY[✅ Environment Ready<br/>• Training scenarios<br/>• Attack simulations<br/>• Monitoring active]
    end
    
    %% Flow Connections
    USER --> CLI
    USER --> INSTALLER
    CLI --> DEPS_CHECK
    INSTALLER --> DEPS_CHECK
    
    DEPS_CHECK --> GOAD_DISCOVERY
    GOAD_DISCOVERY --> RESOURCE_CHECK
    RESOURCE_CHECK --> CONFIG_WIZARD
    
    CONFIG_WIZARD --> COMPONENT_SELECT
    COMPONENT_SELECT --> NETWORK_CONFIG
    NETWORK_CONFIG --> PACKER_INIT
    
    PACKER_INIT --> BASE_IMAGES
    BASE_IMAGES --> CUSTOM_IMAGES
    CUSTOM_IMAGES --> TERRAFORM_INIT
    
    TERRAFORM_INIT --> INFRA_PLAN
    INFRA_PLAN --> INFRA_DEPLOY
    INFRA_DEPLOY --> ANSIBLE_PREP
    
    ANSIBLE_PREP --> COMPONENT_DEPLOY
    COMPONENT_DEPLOY --> INTEGRATION_CONFIG
    INTEGRATION_CONFIG --> HEALTH_CHECK
    
    HEALTH_CHECK --> DATA_FLOW_TEST
    DATA_FLOW_TEST --> INTEGRATION_TEST
    INTEGRATION_TEST --> ACCESS_INFO
    
    ACCESS_INFO --> DOCUMENTATION
    DOCUMENTATION --> READY
    
    %% Styling
    classDef initiation fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef validation fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef configuration fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef building fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef provisioning fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef deployment fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef testing fill:#fff8e1,stroke:#ff8f00,stroke-width:2px
    classDef completion fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    
    class USER,CLI,INSTALLER initiation
    class DEPS_CHECK,GOAD_DISCOVERY,RESOURCE_CHECK validation
    class CONFIG_WIZARD,COMPONENT_SELECT,NETWORK_CONFIG configuration
    class PACKER_INIT,BASE_IMAGES,CUSTOM_IMAGES building
    class TERRAFORM_INIT,INFRA_PLAN,INFRA_DEPLOY provisioning
    class ANSIBLE_PREP,COMPONENT_DEPLOY,INTEGRATION_CONFIG deployment
    class HEALTH_CHECK,DATA_FLOW_TEST,INTEGRATION_TEST testing
    class ACCESS_INFO,DOCUMENTATION,READY completion
```

## Component Deployment Sequence

```mermaid
sequenceDiagram
    participant User as 👤 User
    participant CLI as 🖥️ CLI
    participant Packer as 📦 Packer
    participant Terraform as 🌍 Terraform
    participant Ansible as ⚙️ Ansible
    participant SIEM as 📊 SIEM
    participant Monitor as 🔍 Monitoring
    participant GOAD as 🎯 GOAD
    
    User->>CLI: Start Deployment
    CLI->>CLI: Validate Prerequisites
    CLI->>User: Configuration Wizard
    User->>CLI: Select Components
    
    Note over CLI,Packer: Image Building Phase
    CLI->>Packer: Build SIEM Image
    Packer->>Packer: Install Splunk/Elastic
    Packer-->>CLI: SIEM Image Ready
    
    CLI->>Packer: Build Monitoring Images
    Packer->>Packer: Install Security Onion
    Packer->>Packer: Install Velociraptor
    Packer-->>CLI: Monitoring Images Ready
    
    Note over CLI,Terraform: Infrastructure Phase
    CLI->>Terraform: Plan Infrastructure
    Terraform->>Terraform: Calculate Resources
    Terraform-->>CLI: Plan Complete
    
    CLI->>Terraform: Deploy Infrastructure
    Terraform->>Terraform: Create VMs
    Terraform->>Terraform: Setup Networks
    Terraform-->>CLI: Infrastructure Ready
    
    Note over CLI,Ansible: Configuration Phase
    CLI->>Ansible: Configure SIEM
    Ansible->>SIEM: Deploy Configuration
    Ansible->>SIEM: Create Indexes
    Ansible->>SIEM: Setup Dashboards
    SIEM-->>Ansible: SIEM Configured
    Ansible-->>CLI: SIEM Ready
    
    CLI->>Ansible: Configure Monitoring
    Ansible->>Monitor: Deploy Security Onion
    Ansible->>Monitor: Configure Velociraptor
    Ansible->>Monitor: Setup Network Monitoring
    Monitor-->>Ansible: Monitoring Configured
    Ansible-->>CLI: Monitoring Ready
    
    Note over CLI,GOAD: Integration Phase
    CLI->>Ansible: Integrate with GOAD
    Ansible->>GOAD: Discover Instances
    GOAD-->>Ansible: Instance Details
    
    Ansible->>GOAD: Deploy Agents
    Ansible->>GOAD: Configure Log Forwarding
    Ansible->>GOAD: Setup Network Monitoring
    GOAD-->>Ansible: Integration Complete
    Ansible-->>CLI: GOAD Integrated
    
    Note over CLI,User: Validation Phase
    CLI->>SIEM: Health Check
    SIEM-->>CLI: Status OK
    CLI->>Monitor: Health Check
    Monitor-->>CLI: Status OK
    CLI->>GOAD: Test Data Flow
    GOAD-->>CLI: Data Flowing
    
    CLI-->>User: Deployment Complete
    CLI->>User: Provide Access Information
```

## Multi-Platform Deployment Options

```mermaid
graph TB
    subgraph "🎯 Deployment Target Selection"
        USER_CHOICE{👤 User Choice<br/>Platform Selection}
    end
    
    subgraph "💻 Local Virtualization"
        VMWARE_WS[🔷 VMware Workstation<br/>• Desktop Development<br/>• Full Feature Set<br/>• Offline Capability]
        
        VMWARE_ESXI[🔶 VMware ESXi<br/>• Enterprise Deployment<br/>• High Performance<br/>• Production Ready]
        
        VIRTUALBOX[📦 VirtualBox<br/>• Open Source<br/>• Cross Platform<br/>• Community Edition]
    end
    
    subgraph "☁️ Cloud Platforms"
        AWS_DEPLOY[🟠 Amazon Web Services<br/>• EC2 Instances<br/>• VPC Networks<br/>• S3 Storage]
        
        AZURE_DEPLOY[🔵 Microsoft Azure<br/>• Virtual Machines<br/>• Virtual Networks<br/>• Storage Accounts]
        
        GCP_DEPLOY[🔴 Google Cloud<br/>• Compute Engine<br/>• VPC Networks<br/>• Cloud Storage]
    end
    
    subgraph "🏢 Enterprise Platforms"
        PROXMOX[🟢 Proxmox VE<br/>• Open Source Hypervisor<br/>• KVM/LXC Support<br/>• Web Management<br/>• ZFS Storage]

        HYPER_V[🔷 Hyper-V<br/>• Windows Integration<br/>• System Center<br/>• Enterprise Features]

        OPENSTACK[🟡 OpenStack<br/>• Private Cloud<br/>• Multi-tenant<br/>• API Driven]
    end
    
    subgraph "⚙️ Deployment Automation"
        PACKER_BUILD[📦 Packer Build<br/>• Platform-specific images<br/>• Automated installation<br/>• Configuration baking]
        
        TERRAFORM_DEPLOY[🌍 Terraform Deploy<br/>• Infrastructure as Code<br/>• Multi-cloud support<br/>• State management]
        
        ANSIBLE_CONFIG[⚙️ Ansible Config<br/>• Configuration management<br/>• Application deployment<br/>• Integration setup]
    end
    
    subgraph "✅ Validation & Testing"
        PLATFORM_TEST[🧪 Platform Testing<br/>• Resource allocation<br/>• Network connectivity<br/>• Performance validation]
        
        INTEGRATION_VERIFY[🔗 Integration Verification<br/>• Component communication<br/>• Data flow testing<br/>• End-to-end validation]
        
        READY_STATE[✅ Ready State<br/>• All systems operational<br/>• Monitoring active<br/>• Training ready]
    end
    
    %% User Choice Flow
    USER_CHOICE -->|Local Development| VMWARE_WS
    USER_CHOICE -->|Enterprise Local| VMWARE_ESXI
    USER_CHOICE -->|Open Source| VIRTUALBOX
    USER_CHOICE -->|Cloud Scalable| AWS_DEPLOY
    USER_CHOICE -->|Enterprise Cloud| AZURE_DEPLOY
    USER_CHOICE -->|Analytics Focus| GCP_DEPLOY
    USER_CHOICE -->|Private Cloud| PROXMOX
    USER_CHOICE -->|Windows Enterprise| HYPER_V
    USER_CHOICE -->|Custom Cloud| OPENSTACK
    
    %% Local Virtualization Flow
    VMWARE_WS --> PACKER_BUILD
    VMWARE_ESXI --> PACKER_BUILD
    VIRTUALBOX --> PACKER_BUILD
    
    %% Cloud Platform Flow
    AWS_DEPLOY --> TERRAFORM_DEPLOY
    AZURE_DEPLOY --> TERRAFORM_DEPLOY
    GCP_DEPLOY --> TERRAFORM_DEPLOY
    
    %% Enterprise Platform Flow
    PROXMOX --> PACKER_BUILD
    HYPER_V --> PACKER_BUILD
    OPENSTACK --> TERRAFORM_DEPLOY
    
    %% Automation Flow
    PACKER_BUILD --> TERRAFORM_DEPLOY
    TERRAFORM_DEPLOY --> ANSIBLE_CONFIG
    ANSIBLE_CONFIG --> PLATFORM_TEST
    
    %% Validation Flow
    PLATFORM_TEST --> INTEGRATION_VERIFY
    INTEGRATION_VERIFY --> READY_STATE
    
    %% Styling
    classDef choice fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef local fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef cloud fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef enterprise fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef automation fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef validation fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    
    class USER_CHOICE choice
    class VMWARE_WS,VMWARE_ESXI,VIRTUALBOX local
    class AWS_DEPLOY,AZURE_DEPLOY,GCP_DEPLOY cloud
    class PROXMOX,HYPER_V,OPENSTACK enterprise
    class PACKER_BUILD,TERRAFORM_DEPLOY,ANSIBLE_CONFIG automation
    class PLATFORM_TEST,INTEGRATION_VERIFY,READY_STATE validation
```

## Error Handling & Recovery Flow

```mermaid
flowchart TD
    START([🚀 Deployment Start]) --> VALIDATE{✅ Validation}
    
    VALIDATE -->|Pass| BUILD[📦 Build Images]
    VALIDATE -->|Fail| ERROR_DEPS[❌ Dependency Error]
    
    BUILD -->|Success| PROVISION{🏗️ Provision Infrastructure}
    BUILD -->|Fail| ERROR_BUILD[❌ Build Error]
    
    PROVISION -->|Success| CONFIGURE[⚙️ Configure Systems]
    PROVISION -->|Fail| ERROR_INFRA[❌ Infrastructure Error]
    
    CONFIGURE -->|Success| INTEGRATE{🔗 Integrate with GOAD}
    CONFIGURE -->|Fail| ERROR_CONFIG[❌ Configuration Error]
    
    INTEGRATE -->|Success| TEST[🧪 Test & Validate]
    INTEGRATE -->|Fail| ERROR_INTEGRATION[❌ Integration Error]
    
    TEST -->|Pass| SUCCESS[✅ Deployment Complete]
    TEST -->|Fail| ERROR_TEST[❌ Testing Error]
    
    %% Error Handling
    ERROR_DEPS --> RETRY_DEPS{🔄 Retry Dependencies?}
    ERROR_BUILD --> RETRY_BUILD{🔄 Retry Build?}
    ERROR_INFRA --> CLEANUP_INFRA[🧹 Cleanup Infrastructure]
    ERROR_CONFIG --> ROLLBACK_CONFIG[↩️ Rollback Configuration]
    ERROR_INTEGRATION --> RETRY_INTEGRATION{🔄 Retry Integration?}
    ERROR_TEST --> DEBUG_TEST[🐛 Debug Testing]
    
    %% Retry Logic
    RETRY_DEPS -->|Yes| VALIDATE
    RETRY_DEPS -->|No| ABORT[❌ Abort Deployment]
    
    RETRY_BUILD -->|Yes| BUILD
    RETRY_BUILD -->|No| ABORT
    
    CLEANUP_INFRA --> RETRY_PROVISION{🔄 Retry Provision?}
    RETRY_PROVISION -->|Yes| PROVISION
    RETRY_PROVISION -->|No| ABORT
    
    ROLLBACK_CONFIG --> RETRY_CONFIG{🔄 Retry Config?}
    RETRY_CONFIG -->|Yes| CONFIGURE
    RETRY_CONFIG -->|No| ABORT
    
    RETRY_INTEGRATION -->|Yes| INTEGRATE
    RETRY_INTEGRATION -->|No| ABORT
    
    DEBUG_TEST --> FIX_TEST[🔧 Fix Issues]
    FIX_TEST --> TEST
    
    %% Final States
    SUCCESS --> READY[🎉 Environment Ready]
    ABORT --> CLEANUP[🧹 Cleanup & Exit]
    
    %% Styling
    classDef process fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef error fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef recovery fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class BUILD,CONFIGURE,TEST process
    class VALIDATE,PROVISION,INTEGRATE,RETRY_DEPS,RETRY_BUILD,RETRY_PROVISION,RETRY_CONFIG,RETRY_INTEGRATION decision
    class ERROR_DEPS,ERROR_BUILD,ERROR_INFRA,ERROR_CONFIG,ERROR_INTEGRATION,ERROR_TEST,ABORT error
    class SUCCESS,READY success
    class CLEANUP_INFRA,ROLLBACK_CONFIG,DEBUG_TEST,FIX_TEST,CLEANUP recovery
```
