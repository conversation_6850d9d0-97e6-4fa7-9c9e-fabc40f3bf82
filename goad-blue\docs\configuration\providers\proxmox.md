# Proxmox Configuration

This guide covers GOAD-Blue deployment and configuration on Proxmox Virtual Environment (PVE), including cluster setup, storage configuration, and network design.

## 🏗️ Proxmox Environment Overview

Proxmox VE provides enterprise-grade virtualization with high availability, clustering, and advanced storage features, making it ideal for production GOAD-Blue deployments.

```mermaid
graph TB
    subgraph "🏢 Proxmox Cluster"
        PVE1[🖥️ Proxmox Node 1<br/>pve-01.company.com<br/>Primary Node]
        PVE2[🖥️ Proxmox Node 2<br/>pve-02.company.com<br/>Secondary Node]
        PVE3[🖥️ Proxmox Node 3<br/>pve-03.company.com<br/>Tertiary Node]
    end
    
    subgraph "💾 Storage Systems"
        CEPH[🐙 Ceph Cluster<br/>Distributed Storage<br/>Replication & HA]
        NFS[📁 NFS Storage<br/>Shared Storage<br/>Backup & Templates]
        LOCAL[💽 Local Storage<br/>Node-specific<br/>Fast Access]
    end
    
    subgraph "🌐 Network Infrastructure"
        MGMT_NET[⚙️ Management Network<br/>***********/24<br/>Cluster Communication]
        VM_NET[🖥️ VM Network<br/>*************/24<br/>GOAD-Blue Components]
        STORAGE_NET[💾 Storage Network<br/>10.0.0.0/24<br/>Ceph & Replication]
        COROSYNC[💓 Corosync Network<br/>***********/24<br/>Cluster Heartbeat]
    end
    
    subgraph "🛡️ GOAD-Blue VMs"
        SPLUNK[📊 Splunk Cluster<br/>HA Configuration<br/>Load Balanced]
        SO[🧅 Security Onion<br/>Distributed Deployment<br/>Multiple Sensors]
        VELO[🦖 Velociraptor<br/>Clustered Setup<br/>High Availability]
        MISP[🧠 MISP Cluster<br/>Redis & MySQL<br/>Load Balanced]
    end
    
    PVE1 --> CEPH
    PVE2 --> CEPH
    PVE3 --> CEPH
    
    PVE1 --> NFS
    PVE2 --> NFS
    PVE3 --> NFS
    
    PVE1 --> LOCAL
    PVE2 --> LOCAL
    PVE3 --> LOCAL
    
    MGMT_NET --> PVE1
    MGMT_NET --> PVE2
    MGMT_NET --> PVE3
    
    VM_NET --> SPLUNK
    VM_NET --> SO
    VM_NET --> VELO
    VM_NET --> MISP
    
    STORAGE_NET --> CEPH
    COROSYNC --> PVE1
    COROSYNC --> PVE2
    COROSYNC --> PVE3
    
    classDef proxmox fill:#e97627,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef storage fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef network fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef vms fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class PVE1,PVE2,PVE3 proxmox
    class CEPH,NFS,LOCAL storage
    class MGMT_NET,VM_NET,STORAGE_NET,COROSYNC network
    class SPLUNK,SO,VELO,MISP vms
```

## 🏗️ Cluster Configuration

### **Proxmox Cluster Setup**

```yaml
# Proxmox cluster configuration
proxmox_cluster:
  # Cluster settings
  cluster:
    name: "goad-blue-cluster"
    
    # Nodes configuration
    nodes:
      - name: "pve-01"
        ip: "************"
        role: "primary"
        priority: 100
        
      - name: "pve-02"
        ip: "************"
        role: "secondary"
        priority: 90
        
      - name: "pve-03"
        ip: "************"
        role: "tertiary"
        priority: 80
        
    # Corosync configuration
    corosync:
      # Ring 0 (primary)
      ring0:
        network: "***********/24"
        multicast_address: "***********"
        port: 5405
        
      # Ring 1 (backup)
      ring1:
        network: "***********/24"
        multicast_address: "***********"
        port: 5407
        
    # Quorum settings
    quorum:
      provider: "corosync_votequorum"
      expected_votes: 3
      two_node: false
      
  # High Availability
  ha:
    enabled: true
    
    # HA groups
    groups:
      - name: "goad-blue-critical"
        nodes: ["pve-01:100", "pve-02:90", "pve-03:80"]
        restricted: false
        nofailback: false
        
    # HA resources
    resources:
      - vm_id: 100
        group: "goad-blue-critical"
        max_restart: 3
        max_relocate: 3
        
      - vm_id: 101
        group: "goad-blue-critical"
        max_restart: 3
        max_relocate: 3
```

### **Node Hardware Configuration**

```yaml
# Hardware specifications per node
node_hardware:
  # Standard node configuration
  standard_node:
    cpu:
      model: "Intel Xeon E5-2680 v4"
      cores: 14
      threads: 28
      frequency: "2.4 GHz"
      
    memory:
      total_gb: 128
      type: "DDR4 ECC"
      speed: "2400 MHz"
      
    storage:
      # OS drives (RAID 1)
      os_drives:
        - type: "SSD"
          size_gb: 500
          interface: "SATA"
          count: 2
          raid_level: 1
          
      # Ceph OSDs
      ceph_osds:
        - type: "NVMe SSD"
          size_gb: 2000
          interface: "PCIe"
          count: 4
          
      # Cache/Journal drives
      cache_drives:
        - type: "NVMe SSD"
          size_gb: 500
          interface: "PCIe"
          count: 2
          purpose: "Ceph journal/WAL"
          
    network:
      # Management interface
      management:
        interface: "eno1"
        speed: "1 Gbps"
        
      # VM traffic
      vm_traffic:
        interface: "eno2"
        speed: "10 Gbps"
        
      # Storage traffic
      storage:
        interface: "eno3"
        speed: "10 Gbps"
        
      # Corosync
      corosync:
        interface: "eno4"
        speed: "1 Gbps"
```

## 💾 Storage Configuration

### **Ceph Cluster Setup**

```yaml
# Ceph storage configuration
ceph_cluster:
  # Cluster settings
  cluster:
    name: "goad-blue-ceph"
    fsid: "12345678-1234-1234-1234-123456789012"
    
    # Monitor configuration
    monitors:
      - name: "pve-01"
        ip: "*********"
        
      - name: "pve-02"
        ip: "*********"
        
      - name: "pve-03"
        ip: "*********"
        
    # Manager configuration
    managers:
      - name: "pve-01"
      - name: "pve-02"
      - name: "pve-03"
        
  # OSD configuration
  osds:
    # Node 1 OSDs
    pve-01:
      - device: "/dev/nvme0n1"
        class: "ssd"
        journal: "/dev/nvme2n1p1"
        
      - device: "/dev/nvme1n1"
        class: "ssd"
        journal: "/dev/nvme2n1p2"
        
    # Node 2 OSDs
    pve-02:
      - device: "/dev/nvme0n1"
        class: "ssd"
        journal: "/dev/nvme2n1p1"
        
      - device: "/dev/nvme1n1"
        class: "ssd"
        journal: "/dev/nvme2n1p2"
        
    # Node 3 OSDs
    pve-03:
      - device: "/dev/nvme0n1"
        class: "ssd"
        journal: "/dev/nvme2n1p1"
        
      - device: "/dev/nvme1n1"
        class: "ssd"
        journal: "/dev/nvme2n1p2"
        
  # Pools configuration
  pools:
    # VM disk pool
    vm_disks:
      name: "goad-blue-vms"
      pg_num: 128
      pgp_num: 128
      size: 3
      min_size: 2
      crush_rule: "replicated_rule"
      
    # Container pool
    containers:
      name: "goad-blue-containers"
      pg_num: 64
      pgp_num: 64
      size: 3
      min_size: 2
      crush_rule: "replicated_rule"
      
    # Backup pool
    backups:
      name: "goad-blue-backups"
      pg_num: 32
      pgp_num: 32
      size: 2
      min_size: 1
      crush_rule: "replicated_rule"
```

### **Storage Classes and Performance**

```yaml
# Storage class definitions
storage_classes:
  # High performance for SIEM workloads
  high_performance:
    name: "ceph-ssd-high"
    pool: "goad-blue-vms"
    cache_mode: "writeback"
    io_thread: true
    discard: true
    
    # Performance settings
    settings:
      rbd_cache: true
      rbd_cache_size: "67108864"  # 64MB
      rbd_cache_max_dirty: "50331648"  # 48MB
      rbd_cache_target_dirty: "33554432"  # 32MB
      
  # Standard performance for general workloads
  standard:
    name: "ceph-ssd-standard"
    pool: "goad-blue-vms"
    cache_mode: "writethrough"
    io_thread: true
    discard: true
    
  # Archive storage for logs and backups
  archive:
    name: "ceph-archive"
    pool: "goad-blue-backups"
    cache_mode: "none"
    io_thread: false
    discard: false
```

## 🌐 Network Configuration

### **Bridge and VLAN Setup**

```yaml
# Network bridge configuration
network_bridges:
  # Management bridge
  vmbr0:
    type: "bridge"
    bridge_ports: "eno1"
    bridge_stp: false
    bridge_fd: 0
    cidr: "************/24"
    gateway: "192.168.1.1"
    
    # VLAN configuration
    vlans:
      - vlan_id: 10
        name: "management"
        cidr: "***********/24"
        
  # VM traffic bridge
  vmbr1:
    type: "bridge"
    bridge_ports: "eno2"
    bridge_stp: false
    bridge_fd: 0
    bridge_vlan_aware: true
    
    # VLAN configuration
    vlans:
      - vlan_id: 100
        name: "goad-blue-siem"
        cidr: "*************/26"
        
      - vlan_id: 101
        name: "goad-blue-monitoring"
        cidr: "192.168.100.64/26"
        
      - vlan_id: 102
        name: "goad-blue-analysis"
        cidr: "*************28/26"
        
      - vlan_id: 200
        name: "goad-production"
        cidr: "192.168.56.0/24"
        
  # Storage bridge
  vmbr2:
    type: "bridge"
    bridge_ports: "eno3"
    bridge_stp: false
    bridge_fd: 0
    cidr: "*********/24"
    
  # Corosync bridge
  vmbr3:
    type: "bridge"
    bridge_ports: "eno4"
    bridge_stp: false
    bridge_fd: 0
    cidr: "192.168.2.10/24"
```

### **Firewall Configuration**

```yaml
# Proxmox firewall configuration
firewall:
  # Cluster-level firewall
  cluster:
    enabled: true
    default_policy:
      input: "DROP"
      output: "ACCEPT"
      forward: "DROP"
      
    # Security groups
    security_groups:
      # Management access
      management:
        - action: "ACCEPT"
          type: "in"
          proto: "tcp"
          dport: "22,8006"
          source: "***********/24"
          comment: "SSH and web interface"
          
      # Cluster communication
      cluster:
        - action: "ACCEPT"
          type: "in"
          proto: "udp"
          dport: "5404:5412"
          source: "***********/24"
          comment: "Corosync"
          
        - action: "ACCEPT"
          type: "in"
          proto: "tcp"
          dport: "3128,85"
          source: "***********/24"
          comment: "Proxmox cluster"
          
      # Ceph communication
      ceph:
        - action: "ACCEPT"
          type: "in"
          proto: "tcp"
          dport: "6789,6800:7300"
          source: "10.0.0.0/24"
          comment: "Ceph monitors and OSDs"
          
  # Node-level firewall
  node:
    enabled: true
    
    # Rules for each node
    rules:
      - action: "ACCEPT"
        type: "in"
        proto: "icmp"
        comment: "Allow ping"
        
      - action: "ACCEPT"
        type: "in"
        proto: "tcp"
        dport: "22"
        source: "***********/24"
        comment: "SSH from management network"
```

## 🖥️ Virtual Machine Templates

### **VM Template Configuration**

```yaml
# VM template definitions
vm_templates:
  # Ubuntu 20.04 template
  ubuntu_2004:
    template_id: 9000
    name: "ubuntu-2004-template"
    
    # Hardware configuration
    hardware:
      cpu:
        cores: 2
        sockets: 1
        type: "host"
        
      memory:
        size_mb: 2048
        balloon: true
        
      network:
        - bridge: "vmbr1"
          model: "virtio"
          firewall: true
          
      storage:
        - storage: "goad-blue-vms"
          size: "20G"
          format: "raw"
          cache: "writeback"
          discard: true
          ssd: true
          
    # Cloud-init configuration
    cloud_init:
      user: "ubuntu"
      ssh_keys:
        - "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAAB..."
        
      network:
        dhcp: true
        
      packages:
        - "qemu-guest-agent"
        - "curl"
        - "wget"
        - "vim"
        
  # Windows Server 2019 template
  windows_2019:
    template_id: 9001
    name: "windows-2019-template"
    
    # Hardware configuration
    hardware:
      cpu:
        cores: 2
        sockets: 1
        type: "host"
        
      memory:
        size_mb: 4096
        balloon: false
        
      network:
        - bridge: "vmbr1"
          model: "virtio"
          firewall: true
          
      storage:
        - storage: "goad-blue-vms"
          size: "60G"
          format: "raw"
          cache: "writeback"
          discard: true
          ssd: true
          
    # Windows-specific settings
    windows:
      ostype: "win10"
      bios: "ovmf"
      efidisk: true
      tpmstate: true
```

### **GOAD-Blue VM Specifications**

```yaml
# GOAD-Blue VM configurations
goad_blue_vms:
  # Splunk Search Head
  splunk_search_head:
    vm_id: 100
    name: "goad-blue-splunk-sh"
    template: "ubuntu-2004-template"
    
    # Hardware overrides
    hardware:
      cpu:
        cores: 4
        
      memory:
        size_mb: 8192
        
      storage:
        - storage: "goad-blue-vms"
          size: "100G"
          cache: "writeback"
          
    # Network configuration
    network:
      - bridge: "vmbr1"
        tag: 100
        ip: "**************/26"
        gateway: "*************"
        
    # HA configuration
    ha:
      group: "goad-blue-critical"
      priority: 100
      
  # Splunk Indexer Cluster
  splunk_indexer_1:
    vm_id: 101
    name: "goad-blue-splunk-idx1"
    template: "ubuntu-2004-template"
    
    hardware:
      cpu:
        cores: 8
        
      memory:
        size_mb: 16384
        
      storage:
        - storage: "goad-blue-vms"
          size: "500G"
          cache: "writeback"
          
    network:
      - bridge: "vmbr1"
        tag: 100
        ip: "*************1/26"
        
  # Security Onion Manager
  security_onion_manager:
    vm_id: 110
    name: "goad-blue-so-manager"
    template: "ubuntu-2004-template"
    
    hardware:
      cpu:
        cores: 4
        
      memory:
        size_mb: 16384
        
      storage:
        - storage: "goad-blue-vms"
          size: "200G"
          cache: "writeback"
          
    network:
      - bridge: "vmbr1"
        tag: 101
        ip: "**************/26"
        
  # Velociraptor Server
  velociraptor:
    vm_id: 120
    name: "goad-blue-velociraptor"
    template: "ubuntu-2004-template"
    
    hardware:
      cpu:
        cores: 2
        
      memory:
        size_mb: 4096
        
      storage:
        - storage: "goad-blue-vms"
          size: "100G"
          cache: "writeback"
          
    network:
      - bridge: "vmbr1"
        tag: 100
        ip: "**************/26"
```

## 🔧 Terraform Configuration

### **Proxmox Provider Setup**

```hcl
# terraform/providers/proxmox/main.tf

terraform {
  required_providers {
    proxmox = {
      source  = "telmate/proxmox"
      version = "~> 2.9"
    }
  }
}

provider "proxmox" {
  pm_api_url      = var.proxmox_api_url
  pm_user         = var.proxmox_user
  pm_password     = var.proxmox_password
  pm_tls_insecure = var.proxmox_tls_insecure
}

# Splunk Search Head VM
resource "proxmox_vm_qemu" "splunk_search_head" {
  name        = "goad-blue-splunk-sh"
  target_node = var.target_node
  vmid        = 100
  
  # Template configuration
  clone      = var.ubuntu_template_name
  full_clone = true
  
  # Hardware configuration
  cores   = 4
  sockets = 1
  memory  = 8192
  
  # Storage configuration
  disk {
    storage = var.storage_pool
    type    = "scsi"
    size    = "100G"
    cache   = "writeback"
    discard = "on"
    ssd     = 1
  }
  
  # Network configuration
  network {
    bridge = "vmbr1"
    model  = "virtio"
    tag    = 100
  }
  
  # Cloud-init configuration
  os_type = "cloud-init"
  
  ipconfig0 = "ip=**************/26,gw=*************"
  
  sshkeys = var.ssh_public_key
  
  # Start VM after creation
  automatic_reboot = false
  
  # HA configuration
  hastate = "started"
  hagroup = "goad-blue-critical"
  
  tags = "goad-blue,splunk,search-head"
}

# Security Onion Manager VM
resource "proxmox_vm_qemu" "security_onion_manager" {
  name        = "goad-blue-so-manager"
  target_node = var.target_node
  vmid        = 110
  
  clone      = var.ubuntu_template_name
  full_clone = true
  
  cores   = 4
  sockets = 1
  memory  = 16384
  
  disk {
    storage = var.storage_pool
    type    = "scsi"
    size    = "200G"
    cache   = "writeback"
    discard = "on"
    ssd     = 1
  }
  
  network {
    bridge = "vmbr1"
    model  = "virtio"
    tag    = 101
  }
  
  os_type = "cloud-init"
  
  ipconfig0 = "ip=**************/26,gw=**************"
  
  sshkeys = var.ssh_public_key
  
  automatic_reboot = false
  
  tags = "goad-blue,security-onion,manager"
}
```

### **Ansible Integration**

```yaml
# ansible/proxmox-inventory.yml
plugin: community.general.proxmox
url: https://pve-01.company.com:8006
user: terraform@pve
password: "{{ proxmox_password }}"
validate_certs: false

# Group VMs by tags
keyed_groups:
  - key: proxmox_tags | split(',')
    prefix: tag
    separator: "_"
    
# Group VMs by node
keyed_groups:
  - key: proxmox_node
    prefix: node
    separator: "_"

# Compose variables
compose:
  ansible_host: proxmox_ipconfig0.ip | regex_replace('/.*', '')
  vm_id: proxmox_vmid
  node: proxmox_node
  tags: proxmox_tags
```

## 📊 Monitoring and Maintenance

### **Cluster Monitoring**

```bash
#!/bin/bash
# proxmox-monitoring.sh - Cluster monitoring script

# Check cluster status
echo "=== Cluster Status ==="
pvecm status

# Check node status
echo -e "\n=== Node Status ==="
pvecm nodes

# Check Ceph status
echo -e "\n=== Ceph Status ==="
ceph status

# Check VM status
echo -e "\n=== VM Status ==="
qm list

# Check storage usage
echo -e "\n=== Storage Usage ==="
pvesm status

# Check backup status
echo -e "\n=== Backup Status ==="
vzdump --dumpdir /var/lib/vz/dump --mode snapshot --compress gzip --storage local
```

### **Automated Backup Configuration**

```yaml
# Backup configuration
backup_jobs:
  # Daily VM backups
  daily_vm_backup:
    schedule: "02:00"
    storage: "goad-blue-backups"
    mode: "snapshot"
    compression: "gzip"
    retention: 7
    
    # VMs to backup
    vmids: [100, 101, 110, 120]
    
    # Email notifications
    mailto: "<EMAIL>"
    
  # Weekly full backup
  weekly_full_backup:
    schedule: "01:00"
    dow: "sun"
    storage: "nfs-backup"
    mode: "stop"
    compression: "lzo"
    retention: 4
    
    vmids: [100, 101, 110, 120]
```

---

!!! tip "Proxmox Best Practices"
    - Use Ceph for shared storage and high availability
    - Configure proper network segmentation with VLANs
    - Implement regular backup schedules
    - Monitor cluster health and storage usage
    - Use templates for consistent VM deployment

!!! warning "Resource Planning"
    Ensure adequate resources for Ceph overhead (typically 20-30% of total storage). Plan for at least 3 nodes for proper quorum and redundancy.

!!! info "Enterprise Features"
    Consider Proxmox VE subscription for enterprise support, stable updates, and additional features like Proxmox Backup Server integration.
