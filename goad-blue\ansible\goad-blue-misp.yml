---
# GOAD-Blue MISP Deployment Playbook
# Deploys and configures MISP for threat intelligence

- name: Deploy MISP
  hosts: misp_servers
  become: yes
  vars_files:
    - vars/goad-blue-config.yml
    - vars/misp-config.yml
  
  pre_tasks:
    - name: Validate MISP requirements
      assert:
        that:
          - ansible_memtotal_mb >= 4096
          - ansible_processor_vcpus >= 2
        fail_msg: "MISP requires minimum 4GB RAM and 2 CPU cores"

    - name: Update system packages
      package:
        name: "*"
        state: latest
      when: ansible_os_family == "RedHat"

    - name: Update system packages
      apt:
        upgrade: dist
        update_cache: yes
      when: ansible_os_family == "Debian"

  tasks:
    - name: Install MISP dependencies
      include_tasks: tasks/misp/install_dependencies.yml

    - name: Install and configure MySQL/MariaDB
      include_tasks: tasks/misp/configure_database.yml

    - name: Install and configure Redis
      include_tasks: tasks/misp/configure_redis.yml

    - name: Install and configure Apache
      include_tasks: tasks/misp/configure_apache.yml

    - name: Download and install MISP
      include_tasks: tasks/misp/install_misp.yml

    - name: Configure MISP database
      include_tasks: tasks/misp/setup_database.yml

    - name: Configure MISP settings
      include_tasks: tasks/misp/configure_misp_settings.yml

    - name: Configure MISP for GOAD-Blue integration
      include_tasks: tasks/misp/configure_goad_integration.yml

    - name: Install MISP modules
      include_tasks: tasks/misp/install_modules.yml

    - name: Configure MISP feeds
      include_tasks: tasks/misp/configure_feeds.yml

    - name: Configure MISP taxonomies
      include_tasks: tasks/misp/configure_taxonomies.yml

    - name: Configure MISP galaxy
      include_tasks: tasks/misp/configure_galaxy.yml

    - name: Configure MISP users and organizations
      include_tasks: tasks/misp/configure_users.yml

    - name: Configure MISP API access
      include_tasks: tasks/misp/configure_api.yml

    - name: Configure SIEM integration
      include_tasks: tasks/misp/configure_siem_integration.yml

    - name: Start MISP services
      include_tasks: tasks/misp/start_services.yml

    - name: Validate MISP deployment
      include_tasks: tasks/misp/validate_deployment.yml

  post_tasks:
    - name: Generate MISP access information
      template:
        src: templates/misp_access_info.j2
        dest: "{{ playbook_dir }}/output/misp_access.yml"
      delegate_to: localhost

    - name: Display MISP deployment summary
      debug:
        msg: |
          MISP Deployment Complete:
          Version: {{ misp.version }}
          Web Interface: https://{{ ansible_default_ipv4.address }}
          Admin User: {{ misp.admin_user }}
          API Key: {{ misp_api_key }}
          Database: {{ misp.database_name }}

- name: Configure MISP Integration with GOAD-Blue
  hosts: misp_servers
  become: yes
  vars_files:
    - vars/goad-blue-config.yml
    - vars/misp-config.yml
  
  tasks:
    - name: Create GOAD-Blue organization
      include_tasks: tasks/misp/create_goad_organization.yml

    - name: Import GOAD-Blue threat intelligence
      include_tasks: tasks/misp/import_goad_intelligence.yml

    - name: Configure automated IOC sharing
      include_tasks: tasks/misp/configure_ioc_sharing.yml

    - name: Configure MISP-Splunk integration
      include_tasks: tasks/misp/configure_splunk_integration.yml
      when: goad_blue_config.siem.type == 'splunk'

    - name: Configure MISP-Elastic integration
      include_tasks: tasks/misp/configure_elastic_integration.yml
      when: goad_blue_config.siem.type == 'elastic'

    - name: Configure MISP-Velociraptor integration
      include_tasks: tasks/misp/configure_velociraptor_integration.yml

    - name: Setup automated feed synchronization
      include_tasks: tasks/misp/setup_feed_sync.yml

    - name: Configure MISP warninglists
      include_tasks: tasks/misp/configure_warninglists.yml

    - name: Setup MISP backup and maintenance
      include_tasks: tasks/misp/setup_maintenance.yml
