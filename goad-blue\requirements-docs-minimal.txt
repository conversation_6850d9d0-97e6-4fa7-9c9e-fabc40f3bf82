# GOAD-Blue Documentation - Minimal Requirements
# Install with: pip install -r requirements-docs-minimal.txt

# Core MkDocs - Essential only
mkdocs>=1.5.0
mkdocs-material>=9.4.0

# Essential Extensions
pymdown-extensions>=10.0.0

# Note: This minimal setup provides:
# - Basic MkDocs functionality
# - Material theme with all features
# - Mermaid diagram support (built into Material theme)
# - Code highlighting and syntax support
# - Admonitions and content tabs
# - Search functionality

# To add more features, install from requirements-docs.txt instead
