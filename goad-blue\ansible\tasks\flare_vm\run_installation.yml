---
# Run FLARE-VM installation

- name: Create temporary directory for FLARE-VM installation
  win_file:
    path: C:\temp\flarevm
    state: directory

- name: Download FLARE-VM installation script
  win_get_url:
    url: "{{ flare_vm.install_script_url }}"
    dest: C:\temp\flarevm\install-flarevm.ps1
    timeout: 300

- name: Download FLARE-VM configuration file
  win_get_url:
    url: "{{ flare_vm.config_url | default('https://raw.githubusercontent.com/mandiant/flare-vm/main/flarevm.installer.flare.xml') }}"
    dest: C:\temp\flarevm\flarevm.installer.flare.xml
    timeout: 300

- name: Create custom FLARE-VM configuration
  win_copy:
    content: |
      <?xml version="1.0" encoding="utf-8"?>
      <packages>
        <!-- Core Analysis Tools -->
        <package id="7zip" />
        <package id="notepadplusplus" />
        <package id="firefox" />
        <package id="googlechrome" />
        <package id="vlc" />
        <package id="adobereader" />
        
        <!-- Hex Editors -->
        <package id="hxd" />
        <package id="010editor" />
        
        <!-- Disassemblers -->
        <package id="ida-free" />
        <package id="ghidra" />
        <package id="x64dbg" />
        <package id="ollydbg" />
        
        <!-- PE Analysis -->
        <package id="peid" />
        <package id="pestudio" />
        <package id="peview" />
        <package id="resourcehacker" />
        
        <!-- Network Analysis -->
        <package id="wireshark" />
        <package id="nmap" />
        <package id="tcpview" />
        <package id="fiddler" />
        
        <!-- System Monitoring -->
        <package id="procmon" />
        <package id="procexp" />
        <package id="autoruns" />
        <package id="regshot" />
        
        <!-- Forensics -->
        <package id="volatility" />
        <package id="autopsy" />
        <package id="ftk-imager" />
        
        <!-- Malware Analysis -->
        <package id="floss" />
        <package id="capa" />
        <package id="yara" />
        <package id="strings" />
        
        <!-- Scripting -->
        <package id="python3" />
        <package id="git" />
        <package id="vscode" />
        
        <!-- Utilities -->
        <package id="sysinternals" />
        <package id="nirsoft-package" />
        <package id="putty" />
        <package id="winscp" />
      </packages>
    dest: C:\temp\flarevm\custom-config.xml

- name: Set PowerShell execution policy
  win_shell: |
    Set-ExecutionPolicy Unrestricted -Force
    Set-ExecutionPolicy Unrestricted -Scope CurrentUser -Force

- name: Run FLARE-VM installation (this may take 2-4 hours)
  win_shell: |
    cd C:\temp\flarevm
    .\install-flarevm.ps1 -customConfig custom-config.xml -password {{ flare_vm.analysis_password }}
  register: flarevm_install_result
  async: 14400  # 4 hours timeout
  poll: 300     # Check every 5 minutes
  ignore_errors: yes

- name: Display FLARE-VM installation result
  debug:
    msg: |
      FLARE-VM Installation Result:
      Return Code: {{ flarevm_install_result.rc | default('N/A') }}
      {% if flarevm_install_result.stdout is defined %}
      Output: {{ flarevm_install_result.stdout }}
      {% endif %}
      {% if flarevm_install_result.stderr is defined %}
      Errors: {{ flarevm_install_result.stderr }}
      {% endif %}

- name: Check if FLARE-VM installation completed successfully
  win_stat:
    path: C:\Tools
  register: flarevm_tools_check

- name: Verify key FLARE-VM tools installation
  win_stat:
    path: "{{ item }}"
  register: flarevm_tool_checks
  loop:
    - C:\Tools\PEiD\PEiD.exe
    - C:\Tools\Wireshark\Wireshark.exe
    - C:\Tools\x64dbg\x64dbg.exe
    - C:\Tools\ProcessMonitor\Procmon.exe
    - C:\Tools\Volatility\volatility.exe
    - C:\Tools\YARA\yara64.exe

- name: Display tool installation status
  debug:
    msg: "Tool {{ item.item | basename }} installed: {{ item.stat.exists }}"
  loop: "{{ flarevm_tool_checks.results }}"

- name: Install additional Python packages for malware analysis
  win_shell: |
    python -m pip install --upgrade pip
    pip install pefile
    pip install yara-python
    pip install pycryptodome
    pip install requests
    pip install beautifulsoup4
    pip install lxml
    pip install oletools
    pip install python-magic-bin
    pip install ssdeep
    pip install pydeep
    pip install capstone
    pip install unicorn
    pip install keystone-engine
  ignore_errors: yes

- name: Create FLARE-VM desktop environment
  win_shell: |
    # Create analysis folders on desktop
    New-Item -ItemType Directory -Path "C:\Users\<USER>\Desktop\Samples" -Force
    New-Item -ItemType Directory -Path "C:\Users\<USER>\Desktop\Reports" -Force
    New-Item -ItemType Directory -Path "C:\Users\<USER>\Desktop\Tools" -Force
    New-Item -ItemType Directory -Path "C:\Users\<USER>\Desktop\Scripts" -Force

- name: Configure FLARE-VM environment variables
  win_environment:
    name: "{{ item.name }}"
    value: "{{ item.value }}"
    level: machine
  loop:
    - name: FLARE_VM_PATH
      value: C:\Tools
    - name: YARA_PATH
      value: C:\Tools\YARA
    - name: VOLATILITY_PATH
      value: C:\Tools\Volatility

- name: Create FLARE-VM analysis shortcuts
  win_shortcut:
    src: "{{ item.src }}"
    dest: "C:\\Users\\<USER>\\Desktop\\{{ item.name }}.lnk"
    arguments: "{{ item.args | default('') }}"
    icon: "{{ item.icon | default(item.src) }}"
  loop:
    - name: "PEiD"
      src: "C:\\Tools\\PEiD\\PEiD.exe"
    - name: "x64dbg"
      src: "C:\\Tools\\x64dbg\\x64dbg.exe"
    - name: "Process Monitor"
      src: "C:\\Tools\\ProcessMonitor\\Procmon.exe"
    - name: "Wireshark"
      src: "C:\\Tools\\Wireshark\\Wireshark.exe"
    - name: "HxD Hex Editor"
      src: "C:\\Tools\\HxD\\HxD.exe"
    - name: "IDA Free"
      src: "C:\\Tools\\IDA Free\\ida64.exe"
    - name: "Ghidra"
      src: "C:\\Tools\\Ghidra\\ghidraRun.bat"
    - name: "PE Studio"
      src: "C:\\Tools\\pestudio\\pestudio.exe"

- name: Install FLARE-VM VM tools and utilities
  win_chocolatey:
    name: "{{ item }}"
    state: present
  loop:
    - vmware-tools
    - virtualbox-guest-additions-guest.install
  ignore_errors: yes

- name: Configure FLARE-VM for snapshot management
  win_shell: |
    # Create snapshot management script
    $snapshotScript = @"
    # FLARE-VM Snapshot Management
    Write-Host "Creating clean snapshot..."
    # This would integrate with VMware/VirtualBox snapshot APIs
    "@
    
    $snapshotScript | Out-File -FilePath "C:\Users\<USER>\Desktop\create-snapshot.ps1" -Encoding UTF8

- name: Final FLARE-VM configuration
  win_shell: |
    # Update Windows Defender exclusions for analysis directories
    Add-MpPreference -ExclusionPath "C:\Users\<USER>\Desktop\Samples"
    Add-MpPreference -ExclusionPath "C:\MalwareAnalysis"
    Add-MpPreference -ExclusionPath "C:\Tools"
    
    # Set analysis user as auto-login
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon" -Name "AutoAdminLogon" -Value "1"
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon" -Name "DefaultUserName" -Value "{{ flare_vm.analysis_user }}"
  ignore_errors: yes

- name: Create FLARE-VM completion marker
  win_file:
    path: C:\flarevm-installation-complete.txt
    state: touch

- name: Schedule final reboot
  win_reboot:
    reboot_timeout: 600
    msg: "Final reboot to complete FLARE-VM installation"
