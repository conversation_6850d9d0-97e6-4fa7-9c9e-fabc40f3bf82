"""
GOAD-Blue Configuration Management
"""

import yaml
import os
from pathlib import Path
from goad.log import Log


class BlueTeamConfig:
    """Configuration manager for GOAD-Blue components"""
    
    def __init__(self):
        self.config_file = Path(__file__).parent.parent / "goad-blue-config.yml"
        self.config = self.load_default_config()
    
    def load_default_config(self):
        """Load default GOAD-Blue configuration"""
        default_config = {
            'goad_blue': {
                'name': 'goad-blue-lab',
                'provider': 'vmware',
                'version': '3.1.0'
            },
            'siem': {
                'type': 'splunk',
                'enabled': True
            },
            'components': {
                'security_onion': {
                    'enabled': True,
                    'version': '2.4'
                },
                'malcolm': {
                    'enabled': False
                },
                'velociraptor': {
                    'enabled': True
                },
                'misp': {
                    'enabled': True
                },
                'flare_vm': {
                    'enabled': True
                }
            },
            'network': {
                'base_cidr': '192.168.100.0/24',
                'siem_subnet': '192.168.100.0/26',
                'monitoring_subnet': '192.168.100.64/26',
                'red_team_subnet': '192.168.100.128/26',
                'analysis_subnet': '192.168.100.192/26'
            },
            'integration': {
                'goad_enabled': True,
                'auto_discover': True,
                'agent_deployment': True
            }
        }
        
        # Load from file if exists
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    file_config = yaml.safe_load(f)
                    if file_config:
                        default_config.update(file_config)
                        Log.info(f"Loaded configuration from {self.config_file}")
            except Exception as e:
                Log.warning(f"Failed to load config file: {e}")
        
        return default_config
    
    def save_config(self):
        """Save current configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False, indent=2)
            Log.success(f"Configuration saved to {self.config_file}")
        except Exception as e:
            Log.error(f"Failed to save configuration: {e}")
    
    def merge_config(self, args):
        """Merge command line arguments with configuration"""
        if hasattr(args, 'siem') and args.siem:
            self.config['siem']['type'] = args.siem
        
        if hasattr(args, 'provider') and args.provider:
            self.config['goad_blue']['provider'] = args.provider
        
        if hasattr(args, 'components') and args.components:
            components = args.components.split(',')
            for component in components:
                component = component.strip()
                if component in self.config['components']:
                    self.config['components'][component]['enabled'] = True
    
    def get_value(self, section, key, default=None):
        """Get configuration value"""
        try:
            return self.config[section][key]
        except KeyError:
            return default
    
    def set_value(self, section, key, value):
        """Set configuration value"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value
    
    def is_component_enabled(self, component):
        """Check if a component is enabled"""
        return self.config.get('components', {}).get(component, {}).get('enabled', False)
    
    def enable_component(self, component):
        """Enable a component"""
        if component in self.config['components']:
            self.config['components'][component]['enabled'] = True
            return True
        return False
    
    def disable_component(self, component):
        """Disable a component"""
        if component in self.config['components']:
            self.config['components'][component]['enabled'] = False
            return True
        return False
    
    def get_enabled_components(self):
        """Get list of enabled components"""
        enabled = []
        for component, config in self.config['components'].items():
            if config.get('enabled', False):
                enabled.append(component)
        return enabled
    
    def show(self):
        """Display current configuration"""
        print("\n🛡️  GOAD-Blue Configuration:")
        print("=" * 40)
        print(f"Lab Name: {self.config['goad_blue']['name']}")
        print(f"Provider: {self.config['goad_blue']['provider']}")
        print(f"SIEM: {self.config['siem']['type']} ({'Enabled' if self.config['siem']['enabled'] else 'Disabled'})")
        
        print("\nComponents:")
        for component, config in self.config['components'].items():
            status = "✅ Enabled" if config.get('enabled', False) else "❌ Disabled"
            print(f"  {component}: {status}")
        
        print(f"\nNetwork Configuration:")
        print(f"  Base CIDR: {self.config['network']['base_cidr']}")
        print(f"  SIEM Subnet: {self.config['network']['siem_subnet']}")
        print(f"  Monitoring Subnet: {self.config['network']['monitoring_subnet']}")
        
        print(f"\nGOAD Integration:")
        integration = self.config['integration']
        print(f"  Enabled: {'✅ Yes' if integration['goad_enabled'] else '❌ No'}")
        print(f"  Auto Discovery: {'✅ Yes' if integration['auto_discover'] else '❌ No'}")
