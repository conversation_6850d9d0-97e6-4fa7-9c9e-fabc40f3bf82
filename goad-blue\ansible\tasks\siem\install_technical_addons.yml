---
# Install Splunk Technical Add-ons for GOAD-Blue

- name: Create apps directory
  file:
    path: /opt/splunk/etc/apps
    state: directory
    owner: splunk
    group: splunk
    mode: '0755'

- name: Download Splunk Add-on for Microsoft Windows
  get_url:
    url: "{{ splunk_addons.windows.download_url }}"
    dest: "/tmp/{{ splunk_addons.windows.filename }}"
    mode: '0644'
  when: splunk_addons.windows.enabled | default(true)

- name: Extract Windows Add-on
  unarchive:
    src: "/tmp/{{ splunk_addons.windows.filename }}"
    dest: /opt/splunk/etc/apps/
    remote_src: yes
    owner: splunk
    group: splunk
  when: splunk_addons.windows.enabled | default(true)

- name: Download Splunk Add-on for Unix and Linux
  get_url:
    url: "{{ splunk_addons.unix.download_url }}"
    dest: "/tmp/{{ splunk_addons.unix.filename }}"
    mode: '0644'
  when: splunk_addons.unix.enabled | default(true)

- name: Extract Unix/Linux Add-on
  unarchive:
    src: "/tmp/{{ splunk_addons.unix.filename }}"
    dest: /opt/splunk/etc/apps/
    remote_src: yes
    owner: splunk
    group: splunk
  when: splunk_addons.unix.enabled | default(true)

- name: Download Splunk Add-on for Sysmon
  get_url:
    url: "{{ splunk_addons.sysmon.download_url }}"
    dest: "/tmp/{{ splunk_addons.sysmon.filename }}"
    mode: '0644'
  when: splunk_addons.sysmon.enabled | default(true)

- name: Extract Sysmon Add-on
  unarchive:
    src: "/tmp/{{ splunk_addons.sysmon.filename }}"
    dest: /opt/splunk/etc/apps/
    remote_src: yes
    owner: splunk
    group: splunk
  when: splunk_addons.sysmon.enabled | default(true)

- name: Download Splunk Add-on for PowerShell
  get_url:
    url: "{{ splunk_addons.powershell.download_url }}"
    dest: "/tmp/{{ splunk_addons.powershell.filename }}"
    mode: '0644'
  when: splunk_addons.powershell.enabled | default(true)

- name: Extract PowerShell Add-on
  unarchive:
    src: "/tmp/{{ splunk_addons.powershell.filename }}"
    dest: /opt/splunk/etc/apps/
    remote_src: yes
    owner: splunk
    group: splunk
  when: splunk_addons.powershell.enabled | default(true)

- name: Download Splunk Add-on for DNS
  get_url:
    url: "{{ splunk_addons.dns.download_url }}"
    dest: "/tmp/{{ splunk_addons.dns.filename }}"
    mode: '0644'
  when: splunk_addons.dns.enabled | default(true)

- name: Extract DNS Add-on
  unarchive:
    src: "/tmp/{{ splunk_addons.dns.filename }}"
    dest: /opt/splunk/etc/apps/
    remote_src: yes
    owner: splunk
    group: splunk
  when: splunk_addons.dns.enabled | default(true)

- name: Download Splunk Add-on for Suricata
  get_url:
    url: "{{ splunk_addons.suricata.download_url }}"
    dest: "/tmp/{{ splunk_addons.suricata.filename }}"
    mode: '0644'
  when: splunk_addons.suricata.enabled | default(true)

- name: Extract Suricata Add-on
  unarchive:
    src: "/tmp/{{ splunk_addons.suricata.filename }}"
    dest: /opt/splunk/etc/apps/
    remote_src: yes
    owner: splunk
    group: splunk
  when: splunk_addons.suricata.enabled | default(true)

- name: Download Splunk Add-on for Zeek (Bro)
  get_url:
    url: "{{ splunk_addons.zeek.download_url }}"
    dest: "/tmp/{{ splunk_addons.zeek.filename }}"
    mode: '0644'
  when: splunk_addons.zeek.enabled | default(true)

- name: Extract Zeek Add-on
  unarchive:
    src: "/tmp/{{ splunk_addons.zeek.filename }}"
    dest: /opt/splunk/etc/apps/
    remote_src: yes
    owner: splunk
    group: splunk
  when: splunk_addons.zeek.enabled | default(true)

- name: Install GOAD-Blue Custom Add-on
  copy:
    src: "{{ playbook_dir }}/files/splunk_apps/goad_blue_addon/"
    dest: /opt/splunk/etc/apps/goad_blue_addon/
    owner: splunk
    group: splunk
    mode: '0644'

- name: Configure Add-on inputs
  template:
    src: "{{ item.template }}"
    dest: "/opt/splunk/etc/apps/{{ item.app }}/local/inputs.conf"
    owner: splunk
    group: splunk
    mode: '0644'
  loop:
    - { app: "Splunk_TA_windows", template: "ta_windows_inputs.conf.j2" }
    - { app: "Splunk_TA_nix", template: "ta_nix_inputs.conf.j2" }
    - { app: "TA-microsoft-sysmon", template: "ta_sysmon_inputs.conf.j2" }
    - { app: "goad_blue_addon", template: "goad_blue_inputs.conf.j2" }
  notify: restart splunk

- name: Set Add-on permissions
  file:
    path: "/opt/splunk/etc/apps/{{ item }}"
    owner: splunk
    group: splunk
    recurse: yes
  loop:
    - Splunk_TA_windows
    - Splunk_TA_nix
    - TA-microsoft-sysmon
    - TA-powershell
    - TA-dns
    - TA-suricata
    - TA-zeek
    - goad_blue_addon

- name: Restart Splunk to load Add-ons
  systemd:
    name: splunk
    state: restarted
  notify: wait for splunk
