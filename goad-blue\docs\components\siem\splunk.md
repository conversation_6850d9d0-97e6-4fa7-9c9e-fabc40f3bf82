# Splunk Enterprise

Splunk Enterprise is the industry-leading SIEM platform that provides real-time search, monitoring, and analysis of machine-generated data. In GOAD-Blue, <PERSON>plunk serves as the central log aggregation and analysis platform.

## 🎯 Overview

Splunk Enterprise in GOAD-Blue provides comprehensive security information and event management capabilities, enabling security analysts to detect, investigate, and respond to threats effectively.

```mermaid
graph TB
    subgraph "📊 Splunk Architecture"
        SH[🔍 Search Head<br/>Web Interface<br/>Search Management]
        IDX[📚 Indexers<br/>Data Storage<br/>Search Processing]
        CM[⚙️ Cluster Master<br/>Index Management<br/>Configuration]
        DS[📤 Deployment Server<br/>App Distribution<br/>Configuration Management]
    end
    
    subgraph "📥 Data Sources"
        GOAD[🎮 GOAD Environment<br/>Windows Event Logs<br/>Sysmon Data]
        NETWORK[🌐 Network Data<br/>Security Onion<br/>Firewall Logs]
        ENDPOINT[🖥️ Endpoint Data<br/>Velociraptor<br/>EDR Telemetry]
    end
    
    subgraph "📤 Data Outputs"
        DASHBOARDS[📊 Dashboards<br/>Real-time Monitoring<br/>Executive Reports]
        ALERTS[🚨 Alerts<br/>Automated Detection<br/>Incident Response]
        REPORTS[📋 Reports<br/>Compliance<br/>Threat Hunting]
    end
    
    GOAD --> IDX
    NETWORK --> IDX
    ENDPOINT --> IDX
    
    IDX --> SH
    CM --> IDX
    DS --> SH
    
    SH --> DASHBOARDS
    SH --> ALERTS
    SH --> REPORTS
    
    classDef splunk fill:#1e88e5,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef data fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef output fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class SH,IDX,CM,DS splunk
    class GOAD,NETWORK,ENDPOINT data
    class DASHBOARDS,ALERTS,REPORTS output
```

## 🚀 Installation and Setup

### **Automated Installation**

```bash
# Install Splunk Enterprise using GOAD-Blue automation
python3 goad-blue.py install --component splunk --license-type enterprise

# Configure Splunk for GOAD integration
python3 goad-blue.py configure --component splunk --enable-goad-integration

# Start Splunk services
python3 goad-blue.py start --component splunk
```

### **Manual Installation**

```bash
# Download Splunk Enterprise
cd /tmp
wget -O splunk.tgz "https://download.splunk.com/products/splunk/releases/9.1.2/linux/splunk-9.1.2-b6b9c8185839-Linux-x86_64.tgz"

# Extract and install
tar -xzf splunk.tgz -C /opt/
chown -R splunk:splunk /opt/splunk

# Start Splunk and accept license
sudo -u splunk /opt/splunk/bin/splunk start --accept-license --answer-yes

# Set admin password
sudo -u splunk /opt/splunk/bin/splunk edit user admin -password 'YourSecurePassword123!' -auth admin:changeme
```

### **Configuration Files**

```yaml
# splunk-config.yml
splunk:
  # Server configuration
  server:
    hostname: "goad-blue-splunk"
    web_port: 8000
    management_port: 8089
    
  # Licensing
  license:
    type: "enterprise"  # enterprise, free
    server: "splunk-license-server:8089"
    
  # Indexes for GOAD-Blue data
  indexes:
    goad_blue_windows:
      max_data_size: "auto_high_volume"
      max_hot_buckets: 10
      max_warm_buckets: 300
      retention_days: 90
      
    goad_blue_network:
      max_data_size: "auto_high_volume"
      max_hot_buckets: 5
      max_warm_buckets: 200
      retention_days: 30
      
    goad_blue_endpoint:
      max_data_size: "auto_high_volume"
      max_hot_buckets: 3
      max_warm_buckets: 150
      retention_days: 60
      
  # Data inputs
  inputs:
    # Windows Event Log forwarding
    windows_events:
      - index: "goad_blue_windows"
        sourcetype: "WinEventLog:Security"
        
    # Sysmon data
    sysmon:
      - index: "goad_blue_windows"
        sourcetype: "WinEventLog:Microsoft-Windows-Sysmon/Operational"
        
    # Network data from Security Onion
    network:
      - index: "goad_blue_network"
        sourcetype: "suricata"
        
  # Apps and add-ons
  apps:
    - name: "Splunk_TA_windows"
      version: "8.8.0"
      
    - name: "Splunk_TA_nix"
      version: "8.8.0"
      
    - name: "TA-microsoft-sysmon"
      version: "10.6.2"
```

## 📊 GOAD-Blue Dashboards

### **Security Operations Center Dashboard**

```splunk
<!-- SOC Overview Dashboard -->
<dashboard version="1.1">
  <label>GOAD-Blue SOC Overview</label>
  
  <!-- Authentication Events -->
  <row>
    <panel>
      <title>Authentication Events (Last 24 Hours)</title>
      <chart>
        <search>
          <query>
            index=goad_blue_windows EventCode=4624 OR EventCode=4625
            | eval status=if(EventCode=4624,"Success","Failure")
            | timechart span=1h count by status
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
        </search>
        <option name="charting.chart">column</option>
        <option name="charting.legend.placement">bottom</option>
      </chart>
    </panel>
  </row>
  
  <!-- Top Failed Logins -->
  <row>
    <panel>
      <title>Top Failed Login Sources</title>
      <table>
        <search>
          <query>
            index=goad_blue_windows EventCode=4625
            | stats count by src_ip, user
            | sort -count
            | head 10
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
        </search>
      </table>
    </panel>
  </row>
  
  <!-- Network Alerts -->
  <row>
    <panel>
      <title>Network Security Alerts</title>
      <chart>
        <search>
          <query>
            index=goad_blue_network sourcetype=suricata
            | timechart span=1h count by alert.severity
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
        </search>
        <option name="charting.chart">area</option>
      </chart>
    </panel>
  </row>
</dashboard>
```

### **Threat Hunting Dashboard**

```splunk
<!-- Threat Hunting Dashboard -->
<dashboard version="1.1">
  <label>GOAD-Blue Threat Hunting</label>
  
  <!-- Lateral Movement Detection -->
  <row>
    <panel>
      <title>Potential Lateral Movement</title>
      <table>
        <search>
          <query>
            index=goad_blue_windows EventCode=4624 Logon_Type=3
            | eval src_ip=if(isnull(src_ip), "unknown", src_ip)
            | stats dc(dest) as unique_destinations by src_ip, user
            | where unique_destinations > 3
            | sort -unique_destinations
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
        </search>
      </table>
    </panel>
  </row>
  
  <!-- Privilege Escalation -->
  <row>
    <panel>
      <title>Privilege Escalation Events</title>
      <table>
        <search>
          <query>
            index=goad_blue_windows EventCode=4672
            | search NOT (user="*$" OR user="SYSTEM" OR user="LOCAL SERVICE")
            | stats count by user, dest, Privileges
            | sort -count
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
        </search>
      </table>
    </panel>
  </row>
  
  <!-- Suspicious Process Execution -->
  <row>
    <panel>
      <title>Suspicious Process Execution</title>
      <table>
        <search>
          <query>
            index=goad_blue_windows source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=1
            | search (Image="*\\powershell.exe" OR Image="*\\cmd.exe") 
            | search (CommandLine="*-ExecutionPolicy*" OR CommandLine="*-EncodedCommand*" OR CommandLine="*IEX*")
            | table _time, Computer, User, Image, CommandLine
            | sort -_time
          </query>
          <earliest>-24h@h</earliest>
          <latest>now</latest>
        </search>
      </table>
    </panel>
  </row>
</dashboard>
```

## 🔍 Essential Searches

### **Authentication Analysis**

```splunk
# Failed authentication attempts
index=goad_blue_windows EventCode=4625
| stats count by src_ip, user, dest
| where count > 10
| sort -count

# Successful logins after failures
index=goad_blue_windows (EventCode=4624 OR EventCode=4625)
| eval status=if(EventCode=4624,"Success","Failure")
| sort _time
| streamstats current=f last(status) as prev_status by user, src_ip
| where status="Success" AND prev_status="Failure"

# Off-hours authentication
index=goad_blue_windows EventCode=4624
| eval hour=strftime(_time,"%H")
| where hour < 6 OR hour > 22
| stats count by user, src_ip, hour
```

### **Network Security Analysis**

```splunk
# High-severity network alerts
index=goad_blue_network sourcetype=suricata alert.severity=1
| stats count by alert.signature, src_ip, dest_ip
| sort -count

# DNS tunneling detection
index=goad_blue_network sourcetype=suricata proto=UDP dest_port=53
| eval query_length=len(dns.query)
| where query_length > 50
| stats count by src_ip, dns.query
| sort -count

# Suspicious outbound connections
index=goad_blue_network sourcetype=suricata
| search NOT (dest_ip="192.168.*" OR dest_ip="10.*" OR dest_ip="172.16.*")
| stats count by src_ip, dest_ip, dest_port
| sort -count
```

### **Endpoint Security Analysis**

```splunk
# Process injection detection
index=goad_blue_windows source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=8
| stats count by SourceImage, TargetImage, Computer
| sort -count

# Persistence mechanisms
index=goad_blue_windows source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=13
| search (TargetObject="*\\Run\\*" OR TargetObject="*\\RunOnce\\*" OR TargetObject="*\\Services\\*")
| table _time, Computer, Image, TargetObject, Details

# File creation in suspicious locations
index=goad_blue_windows source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=11
| search (TargetFilename="*\\Windows\\Temp\\*" OR TargetFilename="*\\Users\\Public\\*" OR TargetFilename="*\\ProgramData\\*")
| stats count by Computer, Image, TargetFilename
| sort -count
```

## 🚨 Alerting and Detection Rules

### **Critical Security Alerts**

```splunk
# Golden Ticket Detection
index=goad_blue_windows EventCode=4769
| eval ticket_lifetime=tonumber(strftime(_time,"%s"))-tonumber(strftime(strptime(Account_Domain,""),"%s"))
| where ticket_lifetime > 86400
| stats count by Account_Name, Client_Address
| where count > 1

# DCSync Attack Detection
index=goad_blue_windows EventCode=4662 Object_Type="domainDNS"
| search Access_Mask="0x100" OR Access_Mask="0x40000"
| stats count by Account_Name, Object_Name
| where count > 1

# Mimikatz Detection
index=goad_blue_windows source="WinEventLog:Microsoft-Windows-Sysmon/Operational" EventCode=1
| search (CommandLine="*sekurlsa*" OR CommandLine="*lsadump*" OR CommandLine="*kerberos*")
| table _time, Computer, User, Image, CommandLine
```

### **Automated Response Actions**

```python
# Splunk SOAR integration for automated response
import requests
import json

def create_incident(alert_data):
    """Create incident in SOAR platform"""
    soar_url = "https://soar.company.com/api/incidents"
    headers = {"Authorization": "Bearer YOUR_API_TOKEN"}
    
    incident = {
        "title": alert_data.get("alert_name"),
        "description": alert_data.get("description"),
        "severity": alert_data.get("severity"),
        "source": "Splunk",
        "artifacts": alert_data.get("artifacts", [])
    }
    
    response = requests.post(soar_url, headers=headers, json=incident)
    return response.json()

def quarantine_host(hostname):
    """Quarantine host via Velociraptor"""
    velo_url = "https://velociraptor.goad-blue.local/api/v1/hunts"
    headers = {"Authorization": "Bearer YOUR_VELO_TOKEN"}
    
    hunt = {
        "artifacts": ["Windows.Remediation.Quarantine"],
        "condition": f"label(client_id) =~ '{hostname}'"
    }
    
    response = requests.post(velo_url, headers=headers, json=hunt)
    return response.json()
```

## 🔧 Performance Optimization

### **Search Optimization**

```splunk
# Optimized search practices
# Use time ranges
index=goad_blue_windows earliest=-24h@h latest=now

# Use specific indexes and sourcetypes
index=goad_blue_windows sourcetype="WinEventLog:Security"

# Filter early in the search
index=goad_blue_windows EventCode=4624 user!=*$ | stats count by user

# Use summary indexing for frequent searches
| collect index=summary_index source="daily_auth_summary"
```

### **Index Management**

```bash
# Index sizing and retention
/opt/splunk/bin/splunk set datastore-dir index goad_blue_windows /opt/splunk/var/lib/splunk/goad_blue_windows

# Hot/warm bucket management
/opt/splunk/bin/splunk edit index goad_blue_windows -maxHotBuckets 10 -maxWarmDBCount 300

# Archive old data
/opt/splunk/bin/splunk clean eventdata -index goad_blue_windows -f
```

## 🔗 Integration Points

### **GOAD Environment Integration**

```yaml
# Universal Forwarder configuration for GOAD systems
[tcpout]
defaultGroup = goad_blue_indexers

[tcpout:goad_blue_indexers]
server = goad-blue-splunk:9997
compressed = true

# Windows Event Log inputs
[WinEventLog://Security]
index = goad_blue_windows
sourcetype = WinEventLog:Security
disabled = false

[WinEventLog://Microsoft-Windows-Sysmon/Operational]
index = goad_blue_windows
sourcetype = WinEventLog:Microsoft-Windows-Sysmon/Operational
disabled = false
```

### **Security Onion Integration**

```bash
# Configure Security Onion to forward to Splunk
# Add to /etc/logstash/conf.d/splunk-output.conf
output {
  tcp {
    host => "goad-blue-splunk"
    port => 9999
    codec => json_lines
  }
}
```

### **MISP Integration**

```python
# MISP to Splunk IOC integration
from pymisp import PyMISP
import splunklib.client as client

def sync_misp_iocs():
    # Connect to MISP
    misp = PyMISP('https://misp.goad-blue.local', 'YOUR_API_KEY')
    
    # Connect to Splunk
    service = client.connect(
        host='goad-blue-splunk',
        port=8089,
        username='admin',
        password='password'
    )
    
    # Get recent IOCs from MISP
    events = misp.search(published=True, last='7d')
    
    # Index IOCs in Splunk
    index = service.indexes['threat_intel']
    for event in events:
        index.submit(json.dumps(event))
```

## 📚 Training Scenarios

### **Basic SOC Analyst Training**

1. **Alert Investigation**: Practice investigating authentication failures
2. **Log Analysis**: Learn to correlate events across multiple sources
3. **Dashboard Creation**: Build custom dashboards for monitoring
4. **Report Generation**: Create compliance and executive reports

### **Advanced Threat Hunting**

1. **Behavioral Analysis**: Detect anomalous user and system behavior
2. **Attack Reconstruction**: Piece together attack timelines
3. **Custom Detection**: Develop organization-specific detection rules
4. **Threat Intelligence**: Integrate and operationalize threat feeds

---

!!! tip "Splunk Best Practices"
    - Use specific time ranges and indexes in searches
    - Implement proper data retention policies
    - Regular backup of configurations and knowledge objects
    - Monitor license usage and performance metrics

!!! warning "Resource Requirements"
    Splunk can be resource-intensive. Ensure adequate CPU, memory, and storage for your deployment size.

!!! info "Learning Resources"
    - Splunk Fundamentals courses
    - GOAD-Blue specific training scenarios
    - Community-contributed dashboards and searches
