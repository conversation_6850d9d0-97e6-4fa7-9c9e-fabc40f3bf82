---
# Load datas
- import_playbook: data.yml
  vars:
    data_path: "../ad/{{domain_name}}/data/"
  tags: 'data'

- name: configure laps on DCs
  hosts: laps_dc
  roles:
    - { role: 'laps/dc', tags: 'laps-dc'}
  vars:
    domain: "{{lab.hosts[dict_key].domain}}"
    laps_path: "{{lab.domains[domain].laps_path if lab.domains[domain].laps_path is defined else false}}"
    hosts_dict: "{{lab.hosts}}"

- name: configure laps on servers
  hosts: laps_server, laps_workstation
  roles:
    - { role: 'laps/server', tags: 'laps-server'}
  vars:
    domain: "{{lab.hosts[dict_key].domain}}"
    laps_path: "{{lab.domains[domain].laps_path if lab.domains[domain].laps_path is defined else false}}"
    use_laps: "{{lab.hosts[dict_key].use_laps if lab.hosts[dict_key].use_laps is defined else false}}"


- name: verify and show laps passwords
  hosts: laps_dc
  roles:
    - { role: 'laps/verify', tags: 'laps-verify'}
  vars:
    domain: "{{lab.hosts[dict_key].domain}}"
    laps_path: "{{lab.domains[domain].laps_path if lab.domains[domain].laps_path is defined else false}}"
    hosts_dict: "{{lab.hosts}}"

- name: set laps users and groups permission
  hosts: laps_dc
  roles:
    - { role: 'laps/permissions', tags: 'laps-permissions'}
  vars:
    domain: "{{lab.hosts[dict_key].domain}}"
    laps_path: "{{lab.domains[domain].laps_path if lab.domains[domain].laps_path is defined else false}}"
    laps_readers: "{{lab.domains[domain].laps_readers  | default([]) }}"
