# VirtualBox Configuration

This guide covers GOAD-Blue configuration for VirtualBox environments, including network setup, VM configuration, and automation with Vagrant.

## 🖥️ VirtualBox Environment Overview

VirtualBox provides an excellent platform for GOAD-Blue development and testing environments, offering cost-effective virtualization for learning and small-scale deployments.

```mermaid
graph TB
    subgraph "🖥️ Host System"
        HOST[💻 Host Machine<br/>Windows/Linux/macOS<br/>VirtualBox Hypervisor]
        VBOX_MGMT[⚙️ VirtualBox Manager<br/>VM Management<br/>Network Configuration]
    end
    
    subgraph "🌐 Virtual Networks"
        NAT[🌐 NAT Network<br/>Internet Access<br/>********/24]
        HOST_ONLY[🔒 Host-Only Network<br/>Isolated Network<br/>************/24]
        INTERNAL[🔐 Internal Network<br/>VM-to-VM Only<br/>No Host Access]
        BRIDGED[🌉 Bridged Network<br/>Direct Host Network<br/>DHCP from Router]
    end
    
    subgraph "🛡️ GOAD-Blue VMs"
        SPLUNK[📊 Splunk VM<br/>Ubuntu 20.04<br/>4GB RAM, 2 vCPU]
        SO[🧅 Security Onion<br/>Ubuntu 20.04<br/>8GB RAM, 4 vCPU]
        VELO[🦖 Velociraptor<br/>Ubuntu 20.04<br/>2GB RAM, 2 vCPU]
        MISP[🧠 MISP VM<br/>Ubuntu 20.04<br/>4GB RAM, 2 vCPU]
        FLARE[🔥 FLARE-VM<br/>Windows 10<br/>8GB RAM, 4 vCPU]
    end
    
    subgraph "🎮 GOAD Environment"
        GOAD_DC[👑 Domain Controller<br/>Windows Server<br/>4GB RAM, 2 vCPU]
        GOAD_SERVERS[⚔️ Member Servers<br/>Windows Server<br/>2GB RAM, 2 vCPU]
        GOAD_CLIENTS[🖥️ Workstations<br/>Windows 10<br/>4GB RAM, 2 vCPU]
    end
    
    HOST --> VBOX_MGMT
    VBOX_MGMT --> NAT
    VBOX_MGMT --> HOST_ONLY
    VBOX_MGMT --> INTERNAL
    VBOX_MGMT --> BRIDGED
    
    NAT --> SPLUNK
    HOST_ONLY --> SPLUNK
    HOST_ONLY --> SO
    HOST_ONLY --> VELO
    HOST_ONLY --> MISP
    INTERNAL --> FLARE
    
    HOST_ONLY --> GOAD_DC
    HOST_ONLY --> GOAD_SERVERS
    HOST_ONLY --> GOAD_CLIENTS
    
    classDef host fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef network fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef goadblue fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef goad fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class HOST,VBOX_MGMT host
    class NAT,HOST_ONLY,INTERNAL,BRIDGED network
    class SPLUNK,SO,VELO,MISP,FLARE goadblue
    class GOAD_DC,GOAD_SERVERS,GOAD_CLIENTS goad
```

## 🌐 Network Configuration

### **Host-Only Network Setup**

```yaml
# VirtualBox network configuration
virtualbox_networks:
  # Host-only network for GOAD-Blue management
  host_only_management:
    name: "vboxnet0"
    ip_address: "************"
    netmask: "*************"
    dhcp_enabled: true
    dhcp_range:
      start: "************00"
      end: "**************"
    
    # Network settings
    settings:
      ipv6_enabled: false
      
  # Host-only network for GOAD-Blue components
  host_only_goadblue:
    name: "vboxnet1"
    ip_address: "*************"
    netmask: "*************"
    dhcp_enabled: false
    
    # Static IP assignments
    static_assignments:
      splunk: "*************0"
      security_onion: "**************"
      velociraptor: "**************"
      misp: "**************"
      
  # Internal network for analysis (isolated)
  internal_analysis:
    name: "goad-blue-analysis"
    type: "internal"
    # No IP configuration - VMs configure themselves
    
  # NAT network for internet access
  nat_network:
    name: "goad-blue-nat"
    network: "********/24"
    dhcp_enabled: true
    ipv6_enabled: false
    
    # Port forwarding rules
    port_forwarding:
      - name: "splunk-web"
        protocol: "tcp"
        host_ip: "127.0.0.1"
        host_port: 8000
        guest_ip: "*************0"
        guest_port: 8000
        
      - name: "security-onion-web"
        protocol: "tcp"
        host_ip: "127.0.0.1"
        host_port: 8443
        guest_ip: "**************"
        guest_port: 443
```

### **Network Creation Scripts**

```bash
#!/bin/bash
# create-networks.sh - Create VirtualBox networks

# Create host-only networks
VBoxManage hostonlyif create
VBoxManage hostonlyif ipconfig vboxnet0 --ip ************ --netmask *************
VBoxManage dhcpserver add --ifname vboxnet0 --ip ************ --netmask ************* --lowerip ************00 --upperip ************** --enable

VBoxManage hostonlyif create
VBoxManage hostonlyif ipconfig vboxnet1 --ip ************* --netmask *************

# Create NAT network
VBoxManage natnetwork add --netname goad-blue-nat --network "********/24" --enable --dhcp on

# Add port forwarding rules
VBoxManage natnetwork modify --netname goad-blue-nat --port-forward-4 "splunk-web:tcp:[127.0.0.1]:8000:[*************0]:8000"
VBoxManage natnetwork modify --netname goad-blue-nat --port-forward-4 "security-onion-web:tcp:[127.0.0.1]:8443:[**************]:443"

echo "VirtualBox networks created successfully!"
```

## 🖥️ Virtual Machine Configuration

### **VM Specifications**

```yaml
# Virtual Machine configurations
virtual_machines:
  # Splunk VM
  splunk:
    name: "GOAD-Blue-Splunk"
    os_type: "Ubuntu_64"
    memory_mb: 4096
    cpu_count: 2
    
    # Storage configuration
    storage:
      - name: "splunk-os"
        size_gb: 50
        type: "ssd"
        controller: "SATA"
        port: 0
        
      - name: "splunk-data"
        size_gb: 100
        type: "ssd"
        controller: "SATA"
        port: 1
        
    # Network adapters
    network_adapters:
      - adapter: 1
        type: "hostonly"
        hostonly_adapter: "vboxnet1"
        mac_address: "080027123456"
        
      - adapter: 2
        type: "nat"
        
    # Advanced settings
    settings:
      pae: true
      acpi: true
      ioapic: true
      rtc_use_utc: true
      hwvirtex: true
      nestedpaging: true
      largepages: false
      vtxvpid: true
      vtxux: true
      
  # Security Onion VM
  security_onion:
    name: "GOAD-Blue-SecurityOnion"
    os_type: "Ubuntu_64"
    memory_mb: 8192
    cpu_count: 4
    
    # Storage configuration
    storage:
      - name: "so-os"
        size_gb: 100
        type: "ssd"
        controller: "SATA"
        port: 0
        
      - name: "so-data"
        size_gb: 500
        type: "hdd"
        controller: "SATA"
        port: 1
        
    # Network adapters
    network_adapters:
      - adapter: 1
        type: "hostonly"
        hostonly_adapter: "vboxnet1"
        promiscuous_mode: "allow-all"
        
      - adapter: 2
        type: "hostonly"
        hostonly_adapter: "vboxnet0"
        promiscuous_mode: "allow-all"
        
      - adapter: 3
        type: "nat"
        
    # Advanced settings
    settings:
      pae: true
      acpi: true
      ioapic: true
      hwvirtex: true
      nestedpaging: true
      vtxvpid: true
      vtxux: true
      
  # Velociraptor VM
  velociraptor:
    name: "GOAD-Blue-Velociraptor"
    os_type: "Ubuntu_64"
    memory_mb: 2048
    cpu_count: 2
    
    # Storage configuration
    storage:
      - name: "velo-os"
        size_gb: 50
        type: "ssd"
        controller: "SATA"
        port: 0
        
    # Network adapters
    network_adapters:
      - adapter: 1
        type: "hostonly"
        hostonly_adapter: "vboxnet1"
        
      - adapter: 2
        type: "nat"
        
  # FLARE-VM (Windows)
  flare_vm:
    name: "GOAD-Blue-FLARE-VM"
    os_type: "Windows10_64"
    memory_mb: 8192
    cpu_count: 4
    
    # Storage configuration
    storage:
      - name: "flare-os"
        size_gb: 100
        type: "ssd"
        controller: "SATA"
        port: 0
        
    # Network adapters
    network_adapters:
      - adapter: 1
        type: "intnet"
        internal_network: "goad-blue-analysis"
        
      - adapter: 2
        type: "hostonly"
        hostonly_adapter: "vboxnet1"
        
    # Advanced settings
    settings:
      pae: true
      acpi: true
      ioapic: true
      hwvirtex: true
      nestedpaging: true
      vtxvpid: true
      vtxux: true
      clipboard_mode: "bidirectional"
      drag_and_drop: "bidirectional"
```

## 📦 Vagrant Configuration

### **Vagrantfile for GOAD-Blue**

```ruby
# Vagrantfile for GOAD-Blue VirtualBox deployment

Vagrant.configure("2") do |config|
  # Global configuration
  config.vm.box_check_update = false
  config.vm.synced_folder ".", "/vagrant", disabled: true
  
  # VirtualBox provider settings
  config.vm.provider "virtualbox" do |vb|
    vb.gui = false
    vb.linked_clone = true
    vb.customize ["modifyvm", :id, "--groups", "/GOAD-Blue"]
  end
  
  # Splunk VM
  config.vm.define "splunk" do |splunk|
    splunk.vm.box = "ubuntu/focal64"
    splunk.vm.hostname = "goad-blue-splunk"
    
    # Network configuration
    splunk.vm.network "private_network", 
                      ip: "*************0",
                      virtualbox__hostonly: "vboxnet1"
    
    # Provider-specific settings
    splunk.vm.provider "virtualbox" do |vb|
      vb.name = "GOAD-Blue-Splunk"
      vb.memory = 4096
      vb.cpus = 2
      
      # Add data disk
      unless File.exist?("./disks/splunk-data.vdi")
        vb.customize ["createhd", "--filename", "./disks/splunk-data.vdi", "--size", 102400]
      end
      vb.customize ["storageattach", :id, "--storagectl", "SCSI", "--port", 2, "--device", 0, "--type", "hdd", "--medium", "./disks/splunk-data.vdi"]
    end
    
    # Provisioning
    splunk.vm.provision "shell", inline: <<-SHELL
      apt-get update
      apt-get install -y curl wget
      
      # Mount data disk
      mkfs.ext4 /dev/sdc
      mkdir -p /opt/splunk
      mount /dev/sdc /opt/splunk
      echo '/dev/sdc /opt/splunk ext4 defaults 0 0' >> /etc/fstab
      
      # Install Splunk
      cd /tmp
      wget -O splunk.tgz "https://download.splunk.com/products/splunk/releases/9.1.2/linux/splunk-9.1.2-b6b9c8185839-Linux-x86_64.tgz"
      tar -xzf splunk.tgz -C /opt/
      chown -R splunk:splunk /opt/splunk
    SHELL
  end
  
  # Security Onion VM
  config.vm.define "security_onion" do |so|
    so.vm.box = "ubuntu/focal64"
    so.vm.hostname = "goad-blue-so"
    
    # Network configuration
    so.vm.network "private_network", 
                  ip: "**************",
                  virtualbox__hostonly: "vboxnet1"
    so.vm.network "private_network", 
                  ip: "*************",
                  virtualbox__hostonly: "vboxnet0",
                  promiscuous: "allow-all"
    
    # Provider-specific settings
    so.vm.provider "virtualbox" do |vb|
      vb.name = "GOAD-Blue-SecurityOnion"
      vb.memory = 8192
      vb.cpus = 4
      
      # Enable promiscuous mode for monitoring
      vb.customize ["modifyvm", :id, "--nicpromisc2", "allow-all"]
      vb.customize ["modifyvm", :id, "--nicpromisc3", "allow-all"]
      
      # Add data disk
      unless File.exist?("./disks/so-data.vdi")
        vb.customize ["createhd", "--filename", "./disks/so-data.vdi", "--size", 512000]
      end
      vb.customize ["storageattach", :id, "--storagectl", "SCSI", "--port", 2, "--device", 0, "--type", "hdd", "--medium", "./disks/so-data.vdi"]
    end
    
    # Provisioning
    so.vm.provision "shell", inline: <<-SHELL
      apt-get update
      apt-get install -y curl wget
      
      # Mount data disk
      mkfs.ext4 /dev/sdc
      mkdir -p /nsm
      mount /dev/sdc /nsm
      echo '/dev/sdc /nsm ext4 defaults 0 0' >> /etc/fstab
      
      # Download Security Onion
      cd /tmp
      wget https://github.com/Security-Onion-Solutions/securityonion/raw/2.4/VERIFY_ISO.sh
      wget https://download.securityonion.net/file/securityonion/securityonion-2.4.60-20231201.iso
      bash VERIFY_ISO.sh securityonion-2.4.60-20231201.iso
    SHELL
  end
  
  # Velociraptor VM
  config.vm.define "velociraptor" do |velo|
    velo.vm.box = "ubuntu/focal64"
    velo.vm.hostname = "goad-blue-velociraptor"
    
    # Network configuration
    velo.vm.network "private_network", 
                    ip: "**************",
                    virtualbox__hostonly: "vboxnet1"
    
    # Provider-specific settings
    velo.vm.provider "virtualbox" do |vb|
      vb.name = "GOAD-Blue-Velociraptor"
      vb.memory = 2048
      vb.cpus = 2
    end
    
    # Provisioning
    velo.vm.provision "shell", inline: <<-SHELL
      apt-get update
      apt-get install -y curl wget
      
      # Install Velociraptor
      cd /tmp
      wget https://github.com/Velocidex/velociraptor/releases/latest/download/velociraptor-linux-amd64
      chmod +x velociraptor-linux-amd64
      mv velociraptor-linux-amd64 /usr/local/bin/velociraptor
    SHELL
  end
  
  # MISP VM
  config.vm.define "misp" do |misp|
    misp.vm.box = "ubuntu/focal64"
    misp.vm.hostname = "goad-blue-misp"
    
    # Network configuration
    misp.vm.network "private_network", 
                    ip: "**************",
                    virtualbox__hostonly: "vboxnet1"
    
    # Provider-specific settings
    misp.vm.provider "virtualbox" do |vb|
      vb.name = "GOAD-Blue-MISP"
      vb.memory = 4096
      vb.cpus = 2
    end
    
    # Provisioning
    misp.vm.provision "shell", inline: <<-SHELL
      apt-get update
      apt-get install -y curl wget git
      
      # Clone MISP installation scripts
      git clone https://github.com/MISP/MISP.git /opt/MISP
      cd /opt/MISP/INSTALL
      
      # Run MISP installation
      bash INSTALL.sh -A
    SHELL
  end
  
  # FLARE-VM (Windows)
  config.vm.define "flare_vm" do |flare|
    flare.vm.box = "gusztavvargadr/windows-10"
    flare.vm.hostname = "goad-blue-flare"
    
    # Network configuration
    flare.vm.network "private_network", 
                     ip: "**************",
                     virtualbox__hostonly: "vboxnet1"
    
    # Provider-specific settings
    flare.vm.provider "virtualbox" do |vb|
      vb.name = "GOAD-Blue-FLARE-VM"
      vb.memory = 8192
      vb.cpus = 4
      vb.gui = true
      
      # Enable clipboard and drag-and-drop
      vb.customize ["modifyvm", :id, "--clipboard-mode", "bidirectional"]
      vb.customize ["modifyvm", :id, "--draganddrop", "bidirectional"]
    end
    
    # Provisioning
    flare.vm.provision "shell", inline: <<-SHELL
      # Download and install FLARE-VM
      [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
      Invoke-WebRequest -Uri "https://raw.githubusercontent.com/mandiant/flare-vm/main/install.ps1" -OutFile "install.ps1"
      Unblock-File .\install.ps1
      Set-ExecutionPolicy Unrestricted -Force
      .\install.ps1
    SHELL
  end
end
```

### **Vagrant Management Scripts**

```bash
#!/bin/bash
# vagrant-management.sh - Vagrant management scripts

# Function to start all VMs
start_all() {
    echo "Starting all GOAD-Blue VMs..."
    vagrant up
}

# Function to start specific VM
start_vm() {
    if [ -z "$1" ]; then
        echo "Usage: start_vm <vm_name>"
        echo "Available VMs: splunk, security_onion, velociraptor, misp, flare_vm"
        return 1
    fi
    
    echo "Starting $1..."
    vagrant up "$1"
}

# Function to stop all VMs
stop_all() {
    echo "Stopping all GOAD-Blue VMs..."
    vagrant halt
}

# Function to destroy all VMs
destroy_all() {
    echo "Destroying all GOAD-Blue VMs..."
    read -p "Are you sure? This will delete all VMs and data. (y/N): " confirm
    if [[ $confirm == [yY] ]]; then
        vagrant destroy -f
        rm -rf ./disks/
    fi
}

# Function to show VM status
status() {
    echo "GOAD-Blue VM Status:"
    vagrant status
}

# Function to SSH into VM
ssh_vm() {
    if [ -z "$1" ]; then
        echo "Usage: ssh_vm <vm_name>"
        return 1
    fi
    
    vagrant ssh "$1"
}

# Function to provision specific VM
provision_vm() {
    if [ -z "$1" ]; then
        echo "Usage: provision_vm <vm_name>"
        return 1
    fi
    
    vagrant provision "$1"
}

# Main script logic
case "$1" in
    start)
        if [ -z "$2" ]; then
            start_all
        else
            start_vm "$2"
        fi
        ;;
    stop)
        stop_all
        ;;
    destroy)
        destroy_all
        ;;
    status)
        status
        ;;
    ssh)
        ssh_vm "$2"
        ;;
    provision)
        provision_vm "$2"
        ;;
    *)
        echo "Usage: $0 {start|stop|destroy|status|ssh|provision} [vm_name]"
        echo ""
        echo "Commands:"
        echo "  start [vm_name]    - Start all VMs or specific VM"
        echo "  stop               - Stop all VMs"
        echo "  destroy            - Destroy all VMs"
        echo "  status             - Show VM status"
        echo "  ssh <vm_name>      - SSH into specific VM"
        echo "  provision <vm_name> - Provision specific VM"
        echo ""
        echo "Available VMs: splunk, security_onion, velociraptor, misp, flare_vm"
        exit 1
        ;;
esac
```

## 🔧 Performance Optimization

### **Host System Optimization**

```yaml
# VirtualBox host optimization
host_optimization:
  # VirtualBox settings
  virtualbox_settings:
    # Global settings
    default_machine_folder: "D:\\VirtualBox VMs"
    default_hard_disk_format: "VDI"
    vrde_auth_library: "VBoxAuth"
    web_service_auth_library: "VBoxAuth"
    log_history_count: 3
    
    # Performance settings
    host_memory_low_watermark: 512
    exclusive_hw_virt: true
    
  # Host system requirements
  system_requirements:
    minimum_ram_gb: 32
    recommended_ram_gb: 64
    minimum_cpu_cores: 8
    recommended_cpu_cores: 16
    minimum_storage_gb: 500
    recommended_storage_gb: 1000
    storage_type: "SSD"
    
  # Host network optimization
  network_optimization:
    # Disable unnecessary network adapters
    disable_unused_adapters: true
    
    # Optimize network buffer sizes
    network_buffer_size: "64KB"
    
    # Enable hardware acceleration
    hardware_acceleration: true
```

### **VM Performance Tuning**

```bash
#!/bin/bash
# vm-performance-tuning.sh - Optimize VM performance

# Function to optimize VM settings
optimize_vm() {
    local vm_name="$1"
    
    echo "Optimizing $vm_name..."
    
    # CPU optimization
    VBoxManage modifyvm "$vm_name" --cpuexecutioncap 100
    VBoxManage modifyvm "$vm_name" --pae on
    VBoxManage modifyvm "$vm_name" --hwvirtex on
    VBoxManage modifyvm "$vm_name" --nestedpaging on
    VBoxManage modifyvm "$vm_name" --largepages off
    VBoxManage modifyvm "$vm_name" --vtxvpid on
    VBoxManage modifyvm "$vm_name" --vtxux on
    
    # Memory optimization
    VBoxManage modifyvm "$vm_name" --pagefusion off
    
    # Storage optimization
    VBoxManage storagectl "$vm_name" --name "SATA" --hostiocache on
    
    # Network optimization
    VBoxManage modifyvm "$vm_name" --nictype1 82540EM
    VBoxManage modifyvm "$vm_name" --cableconnected1 on
    
    echo "$vm_name optimization complete."
}

# Optimize all GOAD-Blue VMs
for vm in "GOAD-Blue-Splunk" "GOAD-Blue-SecurityOnion" "GOAD-Blue-Velociraptor" "GOAD-Blue-MISP" "GOAD-Blue-FLARE-VM"; do
    if VBoxManage list vms | grep -q "$vm"; then
        optimize_vm "$vm"
    else
        echo "VM $vm not found, skipping..."
    fi
done

echo "All VM optimizations complete!"
```

---

!!! tip "VirtualBox Best Practices"
    - Allocate sufficient RAM to the host system (leave at least 8GB for the host)
    - Use SSD storage for better performance
    - Enable hardware virtualization in BIOS/UEFI
    - Close unnecessary applications on the host system
    - Use linked clones to save disk space

!!! warning "Resource Requirements"
    Running all GOAD-Blue components simultaneously requires significant resources. Consider running components individually or upgrading hardware for full deployment.

!!! info "Troubleshooting"
    - If VMs are slow, check host CPU and memory usage
    - Ensure virtualization is enabled in BIOS
    - Update VirtualBox to the latest version
    - Install VirtualBox Guest Additions for better performance
