# FLARE-VM

FLARE-VM is a comprehensive malware analysis and reverse engineering platform developed by FireEye's FLARE team. In GOAD-Blue, FLARE-VM serves as the primary malware analysis environment, providing a complete toolkit for analyzing threats discovered in the GOAD environment.

## 🎯 Overview

FLARE-VM provides a Windows-based malware analysis environment with pre-installed tools for static and dynamic analysis, reverse engineering, and threat research, enabling security analysts to safely analyze malware samples from GOAD incidents.

```mermaid
graph TB
    subgraph "🔥 FLARE-VM Architecture"
        BASE[🖥️ Windows 10 Base<br/>Isolated Environment<br/>Snapshot Capability]
        TOOLS[🛠️ Analysis Tools<br/>200+ Security Tools<br/>Automated Installation]
        ISOLATION[🔒 Network Isolation<br/>Controlled Internet<br/>Safe Execution]
        MONITORING[👁️ System Monitoring<br/>Process Monitor<br/>API Monitoring]
    end
    
    subgraph "🔬 Analysis Capabilities"
        STATIC[📄 Static Analysis<br/>File Properties<br/>String Analysis<br/>Signature Detection]
        DYNAMIC[⚡ Dynamic Analysis<br/>Behavioral Analysis<br/>Runtime Monitoring<br/>Network Activity]
        REVERSE[🔍 Reverse Engineering<br/>Disassembly<br/>Debugging<br/>Code Analysis]
        FORENSICS[🕵️ Digital Forensics<br/>Memory Analysis<br/>Artifact Recovery<br/>Timeline Analysis]
    end
    
    subgraph "🛠️ Tool Categories"
        DISASSEMBLERS[⚙️ Disassemblers<br/>IDA Pro<br/>Ghidra<br/>x64dbg]
        DEBUGGERS[🐛 Debuggers<br/>WinDbg<br/>OllyDbg<br/>x32dbg]
        MONITORS[📊 Monitors<br/>Process Monitor<br/>Process Hacker<br/>API Monitor]
        NETWORK[🌐 Network Tools<br/>Wireshark<br/>Fiddler<br/>TCPView]
        UTILITIES[🔧 Utilities<br/>Hex Editors<br/>File Analyzers<br/>Cryptography Tools]
    end
    
    subgraph "🎮 GOAD Integration"
        GOAD_SAMPLES[🦠 GOAD Malware<br/>Incident Artifacts<br/>Suspicious Files]
        ANALYSIS_RESULTS[📋 Analysis Results<br/>IOC Generation<br/>MISP Integration]
        THREAT_INTEL[🧠 Threat Intelligence<br/>Attribution<br/>Campaign Analysis]
    end
    
    BASE --> TOOLS
    BASE --> ISOLATION
    BASE --> MONITORING
    
    TOOLS --> STATIC
    TOOLS --> DYNAMIC
    TOOLS --> REVERSE
    TOOLS --> FORENSICS
    
    STATIC --> DISASSEMBLERS
    DYNAMIC --> DEBUGGERS
    REVERSE --> MONITORS
    FORENSICS --> NETWORK
    MONITORING --> UTILITIES
    
    GOAD_SAMPLES --> STATIC
    GOAD_SAMPLES --> DYNAMIC
    
    STATIC --> ANALYSIS_RESULTS
    DYNAMIC --> ANALYSIS_RESULTS
    REVERSE --> THREAT_INTEL
    FORENSICS --> THREAT_INTEL
    
    ANALYSIS_RESULTS --> THREAT_INTEL
    
    classDef flare fill:#ff6b35,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef analysis fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef tools fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef goad fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class BASE,TOOLS,ISOLATION,MONITORING flare
    class STATIC,DYNAMIC,REVERSE,FORENSICS analysis
    class DISASSEMBLERS,DEBUGGERS,MONITORS,NETWORK,UTILITIES tools
    class GOAD_SAMPLES,ANALYSIS_RESULTS,THREAT_INTEL goad
```

## 🚀 Installation and Setup

### **Automated Installation**

```bash
# Install FLARE-VM using GOAD-Blue automation
python3 goad-blue.py install --component flare-vm --vm-template windows10

# Configure FLARE-VM for GOAD integration
python3 goad-blue.py configure --component flare-vm --enable-goad-analysis

# Create analysis snapshots
python3 goad-blue.py snapshot --component flare-vm --name "clean-baseline"
```

### **Manual Installation**

```powershell
# Download and install FLARE-VM
# Run as Administrator in PowerShell

# Set execution policy
Set-ExecutionPolicy Unrestricted -Force

# Download FLARE-VM installer
[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/mandiant/flare-vm/main/install.ps1" -OutFile "install.ps1"

# Unblock the script
Unblock-File .\install.ps1

# Install FLARE-VM (this will take several hours)
.\install.ps1

# Reboot when prompted
Restart-Computer
```

### **GOAD-Blue Customization**

```powershell
# GOAD-Blue specific FLARE-VM customization script
# flare-goad-setup.ps1

# Create GOAD analysis directories
New-Item -ItemType Directory -Path "C:\GOAD-Analysis" -Force
New-Item -ItemType Directory -Path "C:\GOAD-Analysis\Samples" -Force
New-Item -ItemType Directory -Path "C:\GOAD-Analysis\Reports" -Force
New-Item -ItemType Directory -Path "C:\GOAD-Analysis\IOCs" -Force
New-Item -ItemType Directory -Path "C:\GOAD-Analysis\Scripts" -Force

# Install additional GOAD-specific tools
choco install -y yara
choco install -y volatility
choco install -y capa

# Download GOAD-specific YARA rules
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/GOAD-Blue/yara-rules/main/goad-malware.yar" -OutFile "C:\GOAD-Analysis\Scripts\goad-malware.yar"

# Configure Windows Defender exclusions for analysis
Add-MpPreference -ExclusionPath "C:\GOAD-Analysis"
Add-MpPreference -ExclusionPath "C:\Tools"
Add-MpPreference -ExclusionExtension ".exe"
Add-MpPreference -ExclusionExtension ".dll"
Add-MpPreference -ExclusionExtension ".bin"

# Create analysis shortcuts
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\GOAD Analysis.lnk")
$Shortcut.TargetPath = "C:\GOAD-Analysis"
$Shortcut.Save()

# Configure network isolation
netsh advfirewall set allprofiles firewallpolicy blockinbound,blockoutbound
netsh advfirewall firewall add rule name="Allow GOAD-Blue MISP" dir=out action=allow remoteip=**************
netsh advfirewall firewall add rule name="Allow DNS" dir=out action=allow protocol=UDP remoteport=53

Write-Host "GOAD-Blue FLARE-VM setup completed!"
```

## 🔬 Malware Analysis Workflows

### **Static Analysis Workflow**

```powershell
# Static analysis automation script
# static-analysis.ps1

param(
    [Parameter(Mandatory=$true)]
    [string]$SamplePath,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputDir = "C:\GOAD-Analysis\Reports"
)

function Analyze-Sample {
    param($FilePath, $ReportDir)
    
    $FileName = Split-Path $FilePath -Leaf
    $ReportPath = Join-Path $ReportDir "$FileName-static-report.txt"
    
    Write-Host "Starting static analysis of $FileName..."
    
    # Create report header
    @"
GOAD-Blue Static Analysis Report
================================
Sample: $FileName
Analysis Date: $(Get-Date)
Analyst: $env:USERNAME

"@ | Out-File $ReportPath
    
    # File properties
    "=== FILE PROPERTIES ===" | Out-File $ReportPath -Append
    Get-ItemProperty $FilePath | Format-List | Out-File $ReportPath -Append
    
    # File hashes
    "=== FILE HASHES ===" | Out-File $ReportPath -Append
    "MD5:    $(Get-FileHash $FilePath -Algorithm MD5 | Select-Object -ExpandProperty Hash)" | Out-File $ReportPath -Append
    "SHA1:   $(Get-FileHash $FilePath -Algorithm SHA1 | Select-Object -ExpandProperty Hash)" | Out-File $ReportPath -Append
    "SHA256: $(Get-FileHash $FilePath -Algorithm SHA256 | Select-Object -ExpandProperty Hash)" | Out-File $ReportPath -Append
    
    # PE analysis (if PE file)
    if ($FilePath -match '\.(exe|dll|sys)$') {
        "=== PE ANALYSIS ===" | Out-File $ReportPath -Append
        & "C:\Tools\PEStudio\pestudio.exe" /export $FilePath | Out-File $ReportPath -Append
    }
    
    # Strings analysis
    "=== STRINGS ANALYSIS ===" | Out-File $ReportPath -Append
    & "C:\Tools\SysinternalsSuite\strings.exe" -n 8 $FilePath | Out-File $ReportPath -Append
    
    # YARA scanning
    "=== YARA RULES ===" | Out-File $ReportPath -Append
    & "C:\Tools\yara\yara64.exe" "C:\GOAD-Analysis\Scripts\goad-malware.yar" $FilePath | Out-File $ReportPath -Append
    
    # Entropy analysis
    "=== ENTROPY ANALYSIS ===" | Out-File $ReportPath -Append
    & "C:\Tools\densityscout\densityscout.exe" $FilePath | Out-File $ReportPath -Append
    
    Write-Host "Static analysis completed. Report saved to: $ReportPath"
    return $ReportPath
}

# Execute analysis
$ReportFile = Analyze-Sample -FilePath $SamplePath -ReportDir $OutputDir

# Generate IOCs
$IOCs = @()

# Extract file hashes as IOCs
$MD5 = Get-FileHash $SamplePath -Algorithm MD5 | Select-Object -ExpandProperty Hash
$SHA256 = Get-FileHash $SamplePath -Algorithm SHA256 | Select-Object -ExpandProperty Hash

$IOCs += @{
    Type = "md5"
    Value = $MD5
    Description = "MD5 hash of analyzed sample"
}

$IOCs += @{
    Type = "sha256" 
    Value = $SHA256
    Description = "SHA256 hash of analyzed sample"
}

# Save IOCs to JSON
$IOCs | ConvertTo-Json | Out-File "C:\GOAD-Analysis\IOCs\$((Split-Path $SamplePath -Leaf))-iocs.json"

Write-Host "Analysis completed. IOCs extracted and saved."
```

### **Dynamic Analysis Workflow**

```powershell
# Dynamic analysis automation script
# dynamic-analysis.ps1

param(
    [Parameter(Mandatory=$true)]
    [string]$SamplePath,
    
    [Parameter(Mandatory=$false)]
    [int]$AnalysisTime = 300,  # 5 minutes
    
    [Parameter(Mandatory=$false)]
    [string]$OutputDir = "C:\GOAD-Analysis\Reports"
)

function Start-DynamicAnalysis {
    param($FilePath, $Duration, $ReportDir)
    
    $FileName = Split-Path $FilePath -Leaf
    $ReportPath = Join-Path $ReportDir "$FileName-dynamic-report.txt"
    $PcapPath = Join-Path $ReportDir "$FileName-network.pcap"
    
    Write-Host "Starting dynamic analysis of $FileName for $Duration seconds..."
    
    # Create report header
    @"
GOAD-Blue Dynamic Analysis Report
=================================
Sample: $FileName
Analysis Duration: $Duration seconds
Analysis Date: $(Get-Date)
Analyst: $env:USERNAME

"@ | Out-File $ReportPath
    
    # Start monitoring tools
    Write-Host "Starting monitoring tools..."
    
    # Start Process Monitor
    $ProcMon = Start-Process "C:\Tools\SysinternalsSuite\Procmon.exe" -ArgumentList "/BackingFile", "C:\GOAD-Analysis\Reports\$FileName-procmon.pml", "/Quiet", "/Minimized" -PassThru
    
    # Start network capture
    $NetCapture = Start-Process "C:\Tools\Wireshark\dumpcap.exe" -ArgumentList "-i", "1", "-w", $PcapPath -PassThru
    
    # Start API Monitor
    $ApiMon = Start-Process "C:\Tools\API Monitor\apimonitor-x64.exe" -ArgumentList "-m", $FilePath -PassThru
    
    # Take baseline snapshot
    $BaselineProcesses = Get-Process | Select-Object Name, Id, CPU
    $BaselineServices = Get-Service | Where-Object {$_.Status -eq "Running"}
    $BaselineConnections = Get-NetTCPConnection | Where-Object {$_.State -eq "Established"}
    
    # Execute sample
    Write-Host "Executing sample..."
    $SampleProcess = Start-Process $FilePath -PassThru -ErrorAction SilentlyContinue
    
    # Monitor for specified duration
    Start-Sleep -Seconds $Duration
    
    # Stop monitoring
    Write-Host "Stopping monitoring tools..."
    
    # Stop sample if still running
    if ($SampleProcess -and !$SampleProcess.HasExited) {
        Stop-Process -Id $SampleProcess.Id -Force
    }
    
    # Stop monitoring tools
    Stop-Process -Id $ProcMon.Id -Force
    Stop-Process -Id $NetCapture.Id -Force
    Stop-Process -Id $ApiMon.Id -Force
    
    # Analyze changes
    "=== PROCESS ANALYSIS ===" | Out-File $ReportPath -Append
    $NewProcesses = Get-Process | Where-Object {$_.Id -notin $BaselineProcesses.Id}
    $NewProcesses | Format-Table Name, Id, CPU | Out-File $ReportPath -Append
    
    "=== SERVICE ANALYSIS ===" | Out-File $ReportPath -Append
    $NewServices = Get-Service | Where-Object {$_.Status -eq "Running" -and $_.Name -notin $BaselineServices.Name}
    $NewServices | Format-Table Name, Status | Out-File $ReportPath -Append
    
    "=== NETWORK ANALYSIS ===" | Out-File $ReportPath -Append
    $NewConnections = Get-NetTCPConnection | Where-Object {$_.State -eq "Established" -and $_.LocalPort -notin $BaselineConnections.LocalPort}
    $NewConnections | Format-Table LocalAddress, LocalPort, RemoteAddress, RemotePort | Out-File $ReportPath -Append
    
    "=== FILE SYSTEM CHANGES ===" | Out-File $ReportPath -Append
    # Process ProcMon logs (simplified)
    "See ProcMon log: $FileName-procmon.pml" | Out-File $ReportPath -Append
    
    Write-Host "Dynamic analysis completed. Report saved to: $ReportPath"
    return $ReportPath
}

# Execute analysis
$ReportFile = Start-DynamicAnalysis -FilePath $SamplePath -Duration $AnalysisTime -ReportDir $OutputDir

Write-Host "Dynamic analysis completed."
```

### **Memory Analysis Workflow**

```powershell
# Memory analysis script using Volatility
# memory-analysis.ps1

param(
    [Parameter(Mandatory=$true)]
    [string]$MemoryDump,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputDir = "C:\GOAD-Analysis\Reports"
)

function Analyze-Memory {
    param($DumpPath, $ReportDir)
    
    $DumpName = Split-Path $DumpPath -Leaf
    $ReportPath = Join-Path $ReportDir "$DumpName-memory-report.txt"
    
    Write-Host "Starting memory analysis of $DumpName..."
    
    # Create report header
    @"
GOAD-Blue Memory Analysis Report
================================
Memory Dump: $DumpName
Analysis Date: $(Get-Date)
Analyst: $env:USERNAME

"@ | Out-File $ReportPath
    
    # Determine profile
    "=== PROFILE DETECTION ===" | Out-File $ReportPath -Append
    & python "C:\Tools\volatility\vol.py" -f $DumpPath imageinfo | Out-File $ReportPath -Append
    
    # Extract profile from imageinfo (simplified)
    $Profile = "Win10x64_19041"  # Default profile, should be detected automatically
    
    # Process list
    "=== PROCESS LIST ===" | Out-File $ReportPath -Append
    & python "C:\Tools\volatility\vol.py" -f $DumpPath --profile=$Profile pslist | Out-File $ReportPath -Append
    
    # Network connections
    "=== NETWORK CONNECTIONS ===" | Out-File $ReportPath -Append
    & python "C:\Tools\volatility\vol.py" -f $DumpPath --profile=$Profile netscan | Out-File $ReportPath -Append
    
    # Malware detection
    "=== MALWARE DETECTION ===" | Out-File $ReportPath -Append
    & python "C:\Tools\volatility\vol.py" -f $DumpPath --profile=$Profile malfind | Out-File $ReportPath -Append
    
    # Registry analysis
    "=== REGISTRY ANALYSIS ===" | Out-File $ReportPath -Append
    & python "C:\Tools\volatility\vol.py" -f $DumpPath --profile=$Profile printkey -K "Software\Microsoft\Windows\CurrentVersion\Run" | Out-File $ReportPath -Append
    
    # Command line analysis
    "=== COMMAND LINE ANALYSIS ===" | Out-File $ReportPath -Append
    & python "C:\Tools\volatility\vol.py" -f $DumpPath --profile=$Profile cmdline | Out-File $ReportPath -Append
    
    Write-Host "Memory analysis completed. Report saved to: $ReportPath"
    return $ReportPath
}

# Execute analysis
$ReportFile = Analyze-Memory -DumpPath $MemoryDump -ReportDir $OutputDir

Write-Host "Memory analysis completed."

## 🔗 GOAD-Blue Integration

### **MISP Integration for IOC Sharing**

```python
# Python script for MISP integration from FLARE-VM
# misp-integration.py

import requests
import json
import hashlib
import os
from datetime import datetime

class FLAREMISPIntegration:
    def __init__(self, misp_url, api_key):
        self.misp_url = misp_url
        self.api_key = api_key
        self.headers = {
            'Authorization': api_key,
            'Content-Type': 'application/json'
        }

    def create_analysis_event(self, sample_path, analysis_results):
        """Create MISP event from FLARE-VM analysis results"""

        # Calculate file hashes
        with open(sample_path, 'rb') as f:
            file_data = f.read()
            md5_hash = hashlib.md5(file_data).hexdigest()
            sha1_hash = hashlib.sha1(file_data).hexdigest()
            sha256_hash = hashlib.sha256(file_data).hexdigest()

        # Create event
        event = {
            'info': f'GOAD-Blue Malware Analysis: {os.path.basename(sample_path)}',
            'threat_level_id': '2',  # Medium
            'analysis': '1',  # Ongoing
            'distribution': '1',  # This community only
            'published': False,
            'Tag': [
                {'name': 'goad-blue:analysis-type="malware"'},
                {'name': 'tlp:amber'}
            ],
            'Attribute': []
        }

        # Add file attributes
        event['Attribute'].extend([
            {
                'type': 'filename',
                'value': os.path.basename(sample_path),
                'category': 'Payload delivery',
                'to_ids': True,
                'comment': 'Analyzed malware sample'
            },
            {
                'type': 'md5',
                'value': md5_hash,
                'category': 'Payload delivery',
                'to_ids': True,
                'comment': 'MD5 hash of analyzed sample'
            },
            {
                'type': 'sha1',
                'value': sha1_hash,
                'category': 'Payload delivery',
                'to_ids': True,
                'comment': 'SHA1 hash of analyzed sample'
            },
            {
                'type': 'sha256',
                'value': sha256_hash,
                'category': 'Payload delivery',
                'to_ids': True,
                'comment': 'SHA256 hash of analyzed sample'
            }
        ])

        # Add network IOCs from analysis
        if 'network_connections' in analysis_results:
            for connection in analysis_results['network_connections']:
                event['Attribute'].append({
                    'type': 'ip-dst',
                    'value': connection['remote_ip'],
                    'category': 'Network activity',
                    'to_ids': True,
                    'comment': f'C2 communication observed during analysis'
                })

        # Add registry IOCs
        if 'registry_changes' in analysis_results:
            for reg_change in analysis_results['registry_changes']:
                event['Attribute'].append({
                    'type': 'regkey',
                    'value': reg_change['key'],
                    'category': 'Persistence mechanism',
                    'to_ids': True,
                    'comment': 'Registry modification for persistence'
                })

        # Add file system IOCs
        if 'file_changes' in analysis_results:
            for file_change in analysis_results['file_changes']:
                if file_change['action'] == 'created':
                    event['Attribute'].append({
                        'type': 'filename',
                        'value': file_change['path'],
                        'category': 'Artifacts dropped',
                        'to_ids': True,
                        'comment': 'File created during malware execution'
                    })

        # Submit to MISP
        response = requests.post(
            f'{self.misp_url}/events/add',
            headers=self.headers,
            json=event
        )

        if response.status_code == 200:
            print(f"Successfully created MISP event: {response.json()['Event']['id']}")
            return response.json()
        else:
            print(f"Failed to create MISP event: {response.text}")
            return None

    def search_similar_samples(self, file_hash):
        """Search MISP for similar samples"""

        search_data = {
            'returnFormat': 'json',
            'value': file_hash,
            'type': ['md5', 'sha1', 'sha256']
        }

        response = requests.post(
            f'{self.misp_url}/attributes/restSearch',
            headers=self.headers,
            json=search_data
        )

        if response.status_code == 200:
            return response.json()
        else:
            return None

# Usage example
def analyze_and_share_sample(sample_path):
    """Complete analysis and MISP sharing workflow"""

    # Perform analysis (simplified)
    analysis_results = {
        'network_connections': [
            {'remote_ip': '*************', 'port': 4444}
        ],
        'registry_changes': [
            {'key': 'HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run\\Malware', 'action': 'created'}
        ],
        'file_changes': [
            {'path': 'C:\\Windows\\Temp\\malware.exe', 'action': 'created'}
        ]
    }

    # Share with MISP
    misp = FLAREMISPIntegration('https://misp.goad-blue.local', 'YOUR_API_KEY')
    event = misp.create_analysis_event(sample_path, analysis_results)

    return event
```

### **Automated Analysis Pipeline**

```powershell
# Automated analysis pipeline for GOAD-Blue
# analysis-pipeline.ps1

param(
    [Parameter(Mandatory=$true)]
    [string]$SamplePath,

    [Parameter(Mandatory=$false)]
    [switch]$FullAnalysis = $false,

    [Parameter(Mandatory=$false)]
    [string]$OutputDir = "C:\GOAD-Analysis\Reports"
)

function Start-AnalysisPipeline {
    param($FilePath, $FullAnalysis, $ReportDir)

    $FileName = Split-Path $FilePath -Leaf
    $AnalysisId = [System.Guid]::NewGuid().ToString().Substring(0,8)
    $AnalysisDir = Join-Path $ReportDir "Analysis-$AnalysisId"

    New-Item -ItemType Directory -Path $AnalysisDir -Force

    Write-Host "Starting analysis pipeline for $FileName (ID: $AnalysisId)"

    # Stage 1: Static Analysis
    Write-Host "Stage 1: Static Analysis"
    $StaticReport = & "C:\GOAD-Analysis\Scripts\static-analysis.ps1" -SamplePath $FilePath -OutputDir $AnalysisDir

    # Stage 2: Sandbox Analysis (if full analysis)
    if ($FullAnalysis) {
        Write-Host "Stage 2: Dynamic Analysis"

        # Create VM snapshot before analysis
        Write-Host "Creating VM snapshot..."
        & "C:\Tools\VMware\vmrun.exe" snapshot "C:\VMs\Analysis.vmx" "pre-analysis-$AnalysisId"

        # Run dynamic analysis
        $DynamicReport = & "C:\GOAD-Analysis\Scripts\dynamic-analysis.ps1" -SamplePath $FilePath -OutputDir $AnalysisDir

        # Revert to clean snapshot
        Write-Host "Reverting to clean snapshot..."
        & "C:\Tools\VMware\vmrun.exe" revertToSnapshot "C:\VMs\Analysis.vmx" "clean-baseline"
    }

    # Stage 3: Generate comprehensive report
    Write-Host "Stage 3: Generating comprehensive report"
    $ComprehensiveReport = Join-Path $AnalysisDir "comprehensive-report.html"

    # Create HTML report
    $HtmlReport = @"
<!DOCTYPE html>
<html>
<head>
    <title>GOAD-Blue Malware Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background-color: #ff6b35; color: white; padding: 20px; text-align: center; }
        .section { margin: 20px 0; }
        .metric { display: inline-block; margin: 10px; padding: 15px; background-color: #f5f5f5; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .high-risk { color: #d32f2f; font-weight: bold; }
        .medium-risk { color: #f57c00; font-weight: bold; }
        .low-risk { color: #388e3c; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>GOAD-Blue Malware Analysis Report</h1>
        <p>Sample: $FileName | Analysis ID: $AnalysisId</p>
        <p>Analysis Date: $(Get-Date)</p>
    </div>

    <div class="section">
        <h2>Executive Summary</h2>
        <div class="metric">
            <h3>Risk Level</h3>
            <p class="high-risk">HIGH</p>
        </div>
        <div class="metric">
            <h3>Malware Family</h3>
            <p>Unknown</p>
        </div>
        <div class="metric">
            <h3>Analysis Type</h3>
            <p>$(if ($FullAnalysis) { "Full Analysis" } else { "Static Analysis" })</p>
        </div>
    </div>

    <div class="section">
        <h2>File Information</h2>
        <table>
            <tr><th>Property</th><th>Value</th></tr>
            <tr><td>Filename</td><td>$FileName</td></tr>
            <tr><td>File Size</td><td>$((Get-Item $FilePath).Length) bytes</td></tr>
            <tr><td>MD5</td><td>$(Get-FileHash $FilePath -Algorithm MD5 | Select-Object -ExpandProperty Hash)</td></tr>
            <tr><td>SHA256</td><td>$(Get-FileHash $FilePath -Algorithm SHA256 | Select-Object -ExpandProperty Hash)</td></tr>
        </table>
    </div>

    <div class="section">
        <h2>Analysis Results</h2>
        <p>Detailed analysis results are available in the following reports:</p>
        <ul>
            <li><a href="$StaticReport">Static Analysis Report</a></li>
            $(if ($FullAnalysis) { "<li><a href='$DynamicReport'>Dynamic Analysis Report</a></li>" })
        </ul>
    </div>

    <div class="section">
        <h2>Indicators of Compromise (IOCs)</h2>
        <p>IOCs have been extracted and are available for integration with security tools.</p>
        <p>IOC file: <a href="$FileName-iocs.json">$FileName-iocs.json</a></p>
    </div>

    <div class="section">
        <h2>Recommendations</h2>
        <ul>
            <li>Deploy extracted IOCs to detection systems</li>
            <li>Update YARA rules based on analysis findings</li>
            <li>Review network logs for similar communication patterns</li>
            <li>Implement additional monitoring for identified TTPs</li>
        </ul>
    </div>
</body>
</html>
"@

    $HtmlReport | Out-File $ComprehensiveReport -Encoding UTF8

    # Stage 4: Share with MISP (if configured)
    if (Test-Path "C:\GOAD-Analysis\Scripts\misp-config.json") {
        Write-Host "Stage 4: Sharing with MISP"
        & python "C:\GOAD-Analysis\Scripts\misp-integration.py" $FilePath $AnalysisDir
    }

    Write-Host "Analysis pipeline completed. Results available in: $AnalysisDir"
    Write-Host "Comprehensive report: $ComprehensiveReport"

    # Open report in browser
    Start-Process $ComprehensiveReport

    return $AnalysisDir
}

# Execute pipeline
$ResultsDir = Start-AnalysisPipeline -FilePath $SamplePath -FullAnalysis:$FullAnalysis -ReportDir $OutputDir
```

## 🛠️ Essential FLARE-VM Tools

### **Static Analysis Tools**

| Tool | Purpose | Usage Example |
|------|---------|---------------|
| **PEStudio** | PE file analysis | `pestudio.exe malware.exe` |
| **Detect It Easy (DIE)** | Packer detection | `die.exe malware.exe` |
| **YARA** | Pattern matching | `yara64.exe rules.yar malware.exe` |
| **Strings** | String extraction | `strings.exe -n 8 malware.exe` |
| **Exeinfo PE** | PE information | GUI-based analysis |
| **Resource Hacker** | Resource analysis | GUI-based resource extraction |
| **CFF Explorer** | PE structure analysis | GUI-based PE exploration |

### **Dynamic Analysis Tools**

| Tool | Purpose | Usage Example |
|------|---------|---------------|
| **Process Monitor** | File/Registry monitoring | GUI-based monitoring |
| **Process Hacker** | Process analysis | GUI-based process management |
| **API Monitor** | API call monitoring | GUI-based API monitoring |
| **Wireshark** | Network analysis | GUI-based packet capture |
| **TCPView** | Network connections | GUI-based connection monitoring |
| **Regshot** | Registry comparison | GUI-based registry snapshots |

### **Reverse Engineering Tools**

| Tool | Purpose | Usage Example |
|------|---------|---------------|
| **IDA Pro** | Disassembly/Debugging | GUI-based reverse engineering |
| **Ghidra** | NSA reverse engineering | GUI-based analysis |
| **x64dbg** | Debugging | GUI-based debugging |
| **OllyDbg** | 32-bit debugging | GUI-based debugging |
| **Radare2** | Command-line RE | `r2 malware.exe` |
| **Binary Ninja** | Binary analysis | GUI-based analysis |

### **Memory Analysis Tools**

| Tool | Purpose | Usage Example |
|------|---------|---------------|
| **Volatility** | Memory forensics | `vol.py -f memory.dmp pslist` |
| **Rekall** | Memory analysis | `rekall -f memory.dmp pslist` |
| **WinDbg** | Windows debugging | GUI/command-line debugging |
| **MemProcFS** | Memory file system | Mount memory as filesystem |

## 🔒 Security and Isolation

### **Network Isolation Configuration**

```powershell
# Network isolation script for FLARE-VM
# network-isolation.ps1

# Block all outbound traffic by default
netsh advfirewall set allprofiles firewallpolicy blockinbound,blockoutbound

# Allow specific GOAD-Blue services
$AllowedIPs = @(
    "**************",  # MISP server
    "**************",  # Splunk server
    "**************"   # Velociraptor server
)

foreach ($IP in $AllowedIPs) {
    netsh advfirewall firewall add rule name="Allow GOAD-Blue $IP" dir=out action=allow remoteip=$IP
}

# Allow DNS for domain resolution
netsh advfirewall firewall add rule name="Allow DNS" dir=out action=allow protocol=UDP remoteport=53

# Allow NTP for time synchronization
netsh advfirewall firewall add rule name="Allow NTP" dir=out action=allow protocol=UDP remoteport=123

# Create isolated network adapter for malware analysis
New-NetAdapter -Name "Analysis" -InterfaceDescription "Analysis Network"
New-NetIPAddress -InterfaceAlias "Analysis" -IPAddress "**********" -PrefixLength 24

Write-Host "Network isolation configured for FLARE-VM"
```

### **Snapshot Management**

```powershell
# VM snapshot management for analysis
# snapshot-manager.ps1

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("create", "revert", "list", "delete")]
    [string]$Action,

    [Parameter(Mandatory=$false)]
    [string]$SnapshotName,

    [Parameter(Mandatory=$false)]
    [string]$VMPath = "C:\VMs\FLARE-VM.vmx"
)

function Manage-Snapshots {
    param($Action, $Name, $VM)

    switch ($Action) {
        "create" {
            if (-not $Name) {
                $Name = "snapshot-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
            }
            Write-Host "Creating snapshot: $Name"
            & "C:\Tools\VMware\vmrun.exe" snapshot $VM $Name
        }

        "revert" {
            if (-not $Name) {
                $Name = "clean-baseline"
            }
            Write-Host "Reverting to snapshot: $Name"
            & "C:\Tools\VMware\vmrun.exe" revertToSnapshot $VM $Name
        }

        "list" {
            Write-Host "Available snapshots:"
            & "C:\Tools\VMware\vmrun.exe" listSnapshots $VM
        }

        "delete" {
            if ($Name) {
                Write-Host "Deleting snapshot: $Name"
                & "C:\Tools\VMware\vmrun.exe" deleteSnapshot $VM $Name
            } else {
                Write-Host "Snapshot name required for delete operation"
            }
        }
    }
}

# Execute snapshot management
Manage-Snapshots -Action $Action -Name $SnapshotName -VM $VMPath

## 🎓 Training Scenarios

### **Malware Analysis Fundamentals**

1. **Static Analysis Training**
   - PE file structure analysis
   - String analysis and obfuscation detection
   - Packer identification and unpacking
   - Import/export table analysis
   - Resource analysis and extraction

2. **Dynamic Analysis Training**
   - Behavioral analysis techniques
   - API monitoring and analysis
   - Network traffic analysis
   - Registry and file system monitoring
   - Process injection detection

3. **Reverse Engineering Training**
   - Assembly language fundamentals
   - Disassembly and debugging techniques
   - Anti-analysis evasion techniques
   - Cryptographic analysis
   - Code flow analysis

### **GOAD-Specific Analysis Scenarios**

```powershell
# Training scenario generator for GOAD-Blue
# training-scenarios.ps1

function New-TrainingScenario {
    param(
        [Parameter(Mandatory=$true)]
        [ValidateSet("Mimikatz", "Cobalt Strike", "PowerShell Empire", "Lateral Movement", "Persistence")]
        [string]$ScenarioType
    )

    $ScenarioDir = "C:\GOAD-Analysis\Training\$ScenarioType"
    New-Item -ItemType Directory -Path $ScenarioDir -Force

    switch ($ScenarioType) {
        "Mimikatz" {
            # Create Mimikatz analysis scenario
            @"
GOAD-Blue Training Scenario: Mimikatz Analysis
==============================================

Objective: Analyze a Mimikatz sample and extract IOCs

Sample Location: $ScenarioDir\mimikatz.exe
Expected Findings:
- Credential dumping capabilities
- LSASS process access
- Specific API calls (LsaEnumerateLogonSessions, etc.)
- String artifacts related to credential extraction

Analysis Steps:
1. Perform static analysis to identify capabilities
2. Run dynamic analysis in isolated environment
3. Monitor API calls and system interactions
4. Extract IOCs for MISP integration
5. Document findings and create detection rules

Success Criteria:
- Identify credential dumping techniques
- Extract relevant IOCs
- Create YARA rule for detection
- Generate MISP event with findings
"@ | Out-File "$ScenarioDir\scenario-instructions.txt"
        }

        "Cobalt Strike" {
            # Create Cobalt Strike beacon analysis scenario
            @"
GOAD-Blue Training Scenario: Cobalt Strike Beacon Analysis
==========================================================

Objective: Analyze Cobalt Strike beacon and understand C2 communication

Sample Location: $ScenarioDir\beacon.exe
Expected Findings:
- C2 communication patterns
- Sleep/jitter configuration
- Malleable C2 profile artifacts
- Post-exploitation capabilities

Analysis Steps:
1. Static analysis for configuration extraction
2. Network analysis of C2 communication
3. Memory analysis for injected code
4. Behavioral analysis of post-exploitation activities
5. Attribution analysis based on configuration

Success Criteria:
- Extract C2 configuration
- Identify communication patterns
- Document post-exploitation capabilities
- Create network signatures for detection
"@ | Out-File "$ScenarioDir\scenario-instructions.txt"
        }

        "PowerShell Empire" {
            # Create PowerShell Empire analysis scenario
            @"
GOAD-Blue Training Scenario: PowerShell Empire Analysis
=======================================================

Objective: Analyze PowerShell Empire stager and modules

Sample Location: $ScenarioDir\empire-stager.ps1
Expected Findings:
- PowerShell obfuscation techniques
- C2 communication methods
- Module loading mechanisms
- Persistence techniques

Analysis Steps:
1. Deobfuscate PowerShell code
2. Analyze communication protocols
3. Identify loaded modules and capabilities
4. Document persistence mechanisms
5. Create detection rules for PowerShell activity

Success Criteria:
- Deobfuscate and understand code functionality
- Identify C2 infrastructure
- Document module capabilities
- Create PowerShell logging rules
"@ | Out-File "$ScenarioDir\scenario-instructions.txt"
        }
    }

    Write-Host "Training scenario created: $ScenarioDir"
    Write-Host "Instructions available in: $ScenarioDir\scenario-instructions.txt"
}

# Create all training scenarios
$Scenarios = @("Mimikatz", "Cobalt Strike", "PowerShell Empire", "Lateral Movement", "Persistence")
foreach ($Scenario in $Scenarios) {
    New-TrainingScenario -ScenarioType $Scenario
}
```

### **Assessment and Certification**

```powershell
# Analysis skills assessment script
# skills-assessment.ps1

function Start-SkillsAssessment {
    param(
        [Parameter(Mandatory=$true)]
        [string]$AnalystName,

        [Parameter(Mandatory=$true)]
        [string]$SamplePath
    )

    $AssessmentId = [System.Guid]::NewGuid().ToString().Substring(0,8)
    $AssessmentDir = "C:\GOAD-Analysis\Assessments\$AnalystName-$AssessmentId"
    New-Item -ItemType Directory -Path $AssessmentDir -Force

    $StartTime = Get-Date

    Write-Host "Starting skills assessment for $AnalystName"
    Write-Host "Assessment ID: $AssessmentId"
    Write-Host "Sample: $SamplePath"

    # Assessment criteria
    $Criteria = @{
        "File Identification" = @{
            "Points" = 10
            "Description" = "Correctly identify file type, packer, and basic properties"
        }
        "Static Analysis" = @{
            "Points" = 20
            "Description" = "Extract strings, identify imports, analyze PE structure"
        }
        "Dynamic Analysis" = @{
            "Points" = 25
            "Description" = "Monitor behavior, identify network activity, document changes"
        }
        "IOC Extraction" = @{
            "Points" = 20
            "Description" = "Extract relevant IOCs for detection and hunting"
        }
        "Report Quality" = @{
            "Points" = 15
            "Description" = "Clear, comprehensive analysis report"
        }
        "MISP Integration" = @{
            "Points" = 10
            "Description" = "Properly format and share intelligence"
        }
    }

    # Create assessment template
    $AssessmentTemplate = @"
GOAD-Blue Malware Analysis Skills Assessment
============================================

Analyst: $AnalystName
Assessment ID: $AssessmentId
Start Time: $StartTime
Sample: $SamplePath

Instructions:
1. Perform comprehensive malware analysis of the provided sample
2. Document all findings in the sections below
3. Extract IOCs and create MISP event
4. Complete analysis within 4 hours

Assessment Criteria:
$(foreach ($Criterion in $Criteria.GetEnumerator()) {
    "$($Criterion.Key) ($($Criterion.Value.Points) points): $($Criterion.Value.Description)"
})

=== FILE IDENTIFICATION ===
[ ] File type identified
[ ] Packer/obfuscation detected
[ ] File properties documented
[ ] Hash values calculated

=== STATIC ANALYSIS ===
[ ] Strings extracted and analyzed
[ ] Import/export tables analyzed
[ ] PE structure examined
[ ] Resources analyzed
[ ] Suspicious indicators identified

=== DYNAMIC ANALYSIS ===
[ ] Behavioral analysis performed
[ ] Network activity monitored
[ ] File system changes documented
[ ] Registry modifications tracked
[ ] Process interactions analyzed

=== IOC EXTRACTION ===
[ ] File hashes documented
[ ] Network indicators extracted
[ ] Registry keys identified
[ ] File paths documented
[ ] Behavioral indicators noted

=== REPORT QUALITY ===
[ ] Executive summary provided
[ ] Technical details documented
[ ] Findings clearly presented
[ ] Recommendations included
[ ] Professional formatting

=== MISP INTEGRATION ===
[ ] MISP event created
[ ] IOCs properly categorized
[ ] Appropriate tags applied
[ ] Sharing settings configured

FINDINGS:
=========

[Document your analysis findings here]

IOCS:
=====

[List extracted IOCs here]

RECOMMENDATIONS:
================

[Provide security recommendations here]
"@

    $AssessmentTemplate | Out-File "$AssessmentDir\assessment-template.txt"

    Write-Host "Assessment template created: $AssessmentDir\assessment-template.txt"
    Write-Host "Please complete your analysis and submit results within 4 hours."

    return $AssessmentDir
}

# Example usage
# Start-SkillsAssessment -AnalystName "John.Doe" -SamplePath "C:\Samples\unknown-malware.exe"
```

## 📊 Performance and Optimization

### **System Performance Monitoring**

```powershell
# FLARE-VM performance monitoring script
# performance-monitor.ps1

function Monitor-FLAREPerformance {
    $Report = @{
        "Timestamp" = Get-Date
        "CPU_Usage" = (Get-Counter "\Processor(_Total)\% Processor Time").CounterSamples.CookedValue
        "Memory_Usage" = [math]::Round((Get-Counter "\Memory\% Committed Bytes In Use").CounterSamples.CookedValue, 2)
        "Disk_Usage" = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "C:"} | ForEach-Object {[math]::Round((($_.Size - $_.FreeSpace) / $_.Size) * 100, 2)}
        "Running_Processes" = (Get-Process | Measure-Object).Count
        "Network_Connections" = (Get-NetTCPConnection | Where-Object {$_.State -eq "Established"} | Measure-Object).Count
    }

    # Check for resource-intensive processes
    $TopProcesses = Get-Process | Sort-Object CPU -Descending | Select-Object -First 5 Name, CPU, WorkingSet

    Write-Host "FLARE-VM Performance Report - $(Get-Date)"
    Write-Host "=========================================="
    Write-Host "CPU Usage: $($Report.CPU_Usage)%"
    Write-Host "Memory Usage: $($Report.Memory_Usage)%"
    Write-Host "Disk Usage: $($Report.Disk_Usage)%"
    Write-Host "Running Processes: $($Report.Running_Processes)"
    Write-Host "Network Connections: $($Report.Network_Connections)"
    Write-Host ""
    Write-Host "Top CPU Consumers:"
    $TopProcesses | Format-Table -AutoSize

    # Performance recommendations
    if ($Report.CPU_Usage -gt 80) {
        Write-Warning "High CPU usage detected. Consider closing unnecessary applications."
    }

    if ($Report.Memory_Usage -gt 85) {
        Write-Warning "High memory usage detected. Consider increasing VM memory allocation."
    }

    if ($Report.Disk_Usage -gt 90) {
        Write-Warning "Low disk space. Consider cleaning up old analysis files."
    }

    return $Report
}

# Schedule performance monitoring
$Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\GOAD-Analysis\Scripts\performance-monitor.ps1"
$Trigger = New-ScheduledTaskTrigger -Once -At (Get-Date).AddMinutes(5) -RepetitionInterval (New-TimeSpan -Minutes 30)
$Settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
Register-ScheduledTask -TaskName "FLARE-VM Performance Monitor" -Action $Action -Trigger $Trigger -Settings $Settings
```

### **Cleanup and Maintenance**

```powershell
# FLARE-VM cleanup and maintenance script
# cleanup-maintenance.ps1

function Start-FLAREMaintenance {
    Write-Host "Starting FLARE-VM maintenance..."

    # Clean temporary files
    Write-Host "Cleaning temporary files..."
    Remove-Item -Path "$env:TEMP\*" -Recurse -Force -ErrorAction SilentlyContinue
    Remove-Item -Path "C:\Windows\Temp\*" -Recurse -Force -ErrorAction SilentlyContinue

    # Clean old analysis files (older than 30 days)
    Write-Host "Cleaning old analysis files..."
    $OldFiles = Get-ChildItem -Path "C:\GOAD-Analysis\Reports" -Recurse | Where-Object {$_.LastWriteTime -lt (Get-Date).AddDays(-30)}
    $OldFiles | Remove-Item -Recurse -Force -ErrorAction SilentlyContinue

    # Clean browser cache and history
    Write-Host "Cleaning browser data..."
    Remove-Item -Path "$env:LOCALAPPDATA\Google\Chrome\User Data\Default\Cache\*" -Recurse -Force -ErrorAction SilentlyContinue
    Remove-Item -Path "$env:LOCALAPPDATA\Mozilla\Firefox\Profiles\*\cache2\*" -Recurse -Force -ErrorAction SilentlyContinue

    # Update Windows Defender signatures
    Write-Host "Updating Windows Defender signatures..."
    Update-MpSignature

    # Defragment disk
    Write-Host "Optimizing disk..."
    Optimize-Volume -DriveLetter C -Defrag

    # Clear event logs
    Write-Host "Clearing event logs..."
    wevtutil el | ForEach-Object {wevtutil cl "$_"}

    # Reset network settings
    Write-Host "Resetting network settings..."
    netsh winsock reset
    netsh int ip reset

    Write-Host "FLARE-VM maintenance completed!"
}

# Run maintenance
Start-FLAREMaintenance
```

---

!!! tip "FLARE-VM Best Practices"
    - Always work with snapshots for safe analysis
    - Maintain network isolation during malware execution
    - Document all analysis steps and findings
    - Regularly update tools and signatures
    - Practice proper evidence handling and chain of custody

!!! warning "Safety Considerations"
    - Never analyze malware on production systems
    - Ensure proper network isolation before executing samples
    - Use VM snapshots to maintain clean analysis environment
    - Be aware of VM escape techniques in advanced malware
    - Follow organizational policies for malware handling

!!! info "Learning Resources"
    - FLARE team blog and training materials
    - Malware analysis books and courses
    - GOAD-Blue specific analysis scenarios
    - Community malware analysis challenges
    - Reverse engineering tutorials and workshops
```
```
