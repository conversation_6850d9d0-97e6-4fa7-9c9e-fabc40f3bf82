# Provider-Specific Deployment Guides

GOAD-Blue supports deployment across multiple virtualization platforms and cloud providers. This section provides detailed, platform-specific deployment guides and configurations.

## 🌐 Supported Platforms

```mermaid
graph TB
    subgraph "☁️ Cloud Providers"
        AWS[🟠 Amazon Web Services<br/>• EC2 Instances<br/>• VPC Networking<br/>• EBS Storage<br/>• IAM Security]
        
        AZURE[🔵 Microsoft Azure<br/>• Virtual Machines<br/>• Virtual Networks<br/>• Managed Disks<br/>• Azure AD Integration]
        
        GCP[🟡 Google Cloud Platform<br/>• Compute Engine<br/>• VPC Networks<br/>• Persistent Disks<br/>• Cloud IAM]
    end
    
    subgraph "🖥️ Virtualization Platforms"
        VMWARE[🔷 VMware<br/>• vSphere/ESXi<br/>• Workstation/Player<br/>• vCenter Management<br/>• NSX Networking]
        
        PROXMOX[🟢 Proxmox VE<br/>• KVM Hypervisor<br/>• LXC Containers<br/>• ZFS Storage<br/>• Web Management]
        
        VIRTUALBOX[🟦 VirtualBox<br/>• Host-only Networks<br/>• NAT Configuration<br/>• Snapshot Support<br/>• Cross-platform]
    end
    
    subgraph "🏢 Enterprise Platforms"
        HYPERV[🔷 Hyper-V<br/>• Windows Integration<br/>• System Center<br/>• PowerShell Management<br/>• Failover Clustering]
        
        OPENSTACK[🟡 OpenStack<br/>• Private Cloud<br/>• Multi-tenant<br/>• API-driven<br/>• Neutron Networking]
        
        KUBERNETES[⚙️ Kubernetes<br/>• Container Orchestration<br/>• Helm Charts<br/>• Service Mesh<br/>• Cloud Native]
    end
    
    subgraph "🔧 Deployment Tools"
        TERRAFORM[🟣 Terraform<br/>• Infrastructure as Code<br/>• Multi-provider<br/>• State Management<br/>• Resource Planning]
        
        ANSIBLE[🔴 Ansible<br/>• Configuration Management<br/>• Agentless<br/>• Playbook Automation<br/>• Dynamic Inventory]
        
        PACKER[📦 Packer<br/>• Image Building<br/>• Multi-format Output<br/>• Template Automation<br/>• CI/CD Integration]
    end
    
    %% Connections
    AWS --> TERRAFORM
    AZURE --> TERRAFORM
    GCP --> TERRAFORM
    VMWARE --> TERRAFORM
    PROXMOX --> TERRAFORM
    VIRTUALBOX --> TERRAFORM
    HYPERV --> TERRAFORM
    OPENSTACK --> TERRAFORM
    
    TERRAFORM --> ANSIBLE
    TERRAFORM --> PACKER
    
    classDef cloud fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef virtualization fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef enterprise fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef tools fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class AWS,AZURE,GCP cloud
    class VMWARE,PROXMOX,VIRTUALBOX virtualization
    class HYPERV,OPENSTACK,KUBERNETES enterprise
    class TERRAFORM,ANSIBLE,PACKER tools
```

## 📋 Platform Comparison

| Platform | Complexity | Cost | Performance | Scalability | Best For |
|----------|------------|------|-------------|-------------|----------|
| **AWS** | Medium | Pay-per-use | High | Excellent | Production, Enterprise |
| **Azure** | Medium | Pay-per-use | High | Excellent | Microsoft Environments |
| **GCP** | Medium | Pay-per-use | High | Excellent | Data Analytics, ML |
| **VMware vSphere** | High | License + Hardware | Excellent | Excellent | Enterprise Data Centers |
| **Proxmox VE** | Low | Free + Hardware | High | Good | Cost-effective Labs |
| **VirtualBox** | Low | Free | Medium | Limited | Development, Testing |
| **Hyper-V** | Medium | Windows License | High | Good | Windows Environments |

## 🚀 Quick Start by Platform

### **☁️ Cloud Deployments**

#### **Amazon Web Services (AWS)**
```bash
# Configure AWS credentials
aws configure

# Deploy GOAD-Blue to AWS
cd terraform/providers/aws
terraform init
terraform plan -var-file="production.tfvars"
terraform apply
```

**Key Features:**
- Auto-scaling groups for high availability
- Application Load Balancer for traffic distribution
- RDS for managed database services
- CloudWatch for monitoring and alerting
- VPC with public/private subnets

#### **Microsoft Azure**
```bash
# Login to Azure
az login

# Deploy GOAD-Blue to Azure
cd terraform/providers/azure
terraform init
terraform plan -var-file="production.tfvars"
terraform apply
```

**Key Features:**
- Virtual Machine Scale Sets
- Azure Load Balancer
- Azure Database services
- Azure Monitor integration
- Virtual Network with NSGs

#### **Google Cloud Platform (GCP)**
```bash
# Authenticate with GCP
gcloud auth application-default login

# Deploy GOAD-Blue to GCP
cd terraform/providers/gcp
terraform init
terraform plan -var-file="production.tfvars"
terraform apply
```

**Key Features:**
- Managed Instance Groups
- Cloud Load Balancing
- Cloud SQL for databases
- Cloud Monitoring
- VPC with firewall rules

### **🖥️ Virtualization Deployments**

#### **VMware vSphere**
```bash
# Configure vSphere credentials
export VSPHERE_SERVER="vcenter.example.com"
export VSPHERE_USER="<EMAIL>"
export VSPHERE_PASSWORD="password"

# Deploy GOAD-Blue to vSphere
cd terraform/providers/vmware
terraform init
terraform plan -var-file="vsphere.tfvars"
terraform apply
```

**Key Features:**
- Distributed Resource Scheduler (DRS)
- High Availability (HA)
- vMotion for live migration
- Distributed switches
- Storage vMotion

#### **Proxmox VE**
```bash
# Configure Proxmox credentials
export PROXMOX_API_URL="https://proxmox.example.com:8006/api2/json"
export PROXMOX_USER="root@pam"
export PROXMOX_PASSWORD="password"

# Deploy GOAD-Blue to Proxmox
cd terraform/providers/proxmox
terraform init
terraform plan -var-file="proxmox.tfvars"
terraform apply
```

**Key Features:**
- KVM virtualization
- LXC containers
- ZFS storage pools
- Live migration
- Web-based management

#### **VirtualBox**
```bash
# Ensure VirtualBox is installed
VBoxManage --version

# Deploy GOAD-Blue to VirtualBox
cd terraform/providers/virtualbox
terraform init
terraform plan -var-file="virtualbox.tfvars"
terraform apply
```

**Key Features:**
- Host-only networking
- NAT configuration
- Snapshot management
- Cross-platform support
- Extension pack features

## 📊 Resource Requirements by Platform

### **Minimum Requirements**

| Component | CPU Cores | RAM (GB) | Storage (GB) | Network |
|-----------|-----------|----------|--------------|---------|
| **Splunk Enterprise** | 4 | 8 | 100 | 1 Gbps |
| **Security Onion Manager** | 8 | 16 | 200 | 1 Gbps |
| **Security Onion Sensor** | 4 | 8 | 100 | 1 Gbps |
| **Velociraptor Server** | 2 | 4 | 50 | 100 Mbps |
| **MISP Server** | 2 | 4 | 50 | 100 Mbps |
| **FLARE-VM** | 4 | 8 | 100 | 100 Mbps |

### **Recommended Requirements**

| Component | CPU Cores | RAM (GB) | Storage (GB) | Network |
|-----------|-----------|----------|--------------|---------|
| **Splunk Enterprise** | 8 | 16 | 500 | 10 Gbps |
| **Security Onion Manager** | 16 | 32 | 1000 | 10 Gbps |
| **Security Onion Sensor** | 8 | 16 | 500 | 10 Gbps |
| **Velociraptor Server** | 4 | 8 | 200 | 1 Gbps |
| **MISP Server** | 4 | 8 | 200 | 1 Gbps |
| **FLARE-VM** | 8 | 16 | 500 | 1 Gbps |

## 🔧 Platform-Specific Features

### **Cloud Provider Features**

#### **AWS-Specific Features**
- **Auto Scaling**: Automatic scaling based on demand
- **Spot Instances**: Cost optimization for non-critical workloads
- **EFS**: Shared file storage across instances
- **Route 53**: DNS management and health checks
- **CloudFormation**: Alternative IaC deployment method

#### **Azure-Specific Features**
- **Azure AD Integration**: Single sign-on and identity management
- **Azure Security Center**: Built-in security monitoring
- **Azure Backup**: Automated backup solutions
- **Azure Site Recovery**: Disaster recovery capabilities
- **Azure DevOps**: Integrated CI/CD pipelines

#### **GCP-Specific Features**
- **BigQuery Integration**: Large-scale log analysis
- **Cloud Functions**: Serverless automation
- **Cloud Storage**: Object storage for artifacts
- **Cloud Build**: Container and application building
- **Anthos**: Hybrid and multi-cloud management

### **Virtualization Platform Features**

#### **VMware-Specific Features**
- **vSAN**: Software-defined storage
- **NSX**: Network virtualization and security
- **vRealize Suite**: Management and automation
- **Horizon**: Virtual desktop infrastructure
- **Site Recovery Manager**: Disaster recovery

#### **Proxmox-Specific Features**
- **Ceph Integration**: Distributed storage
- **Backup Server**: Centralized backup management
- **Firewall**: Built-in network security
- **Mail Gateway**: Email security appliance
- **Cluster Management**: Multi-node orchestration

## 🛠️ Deployment Automation

### **Terraform Modules**

Each platform has dedicated Terraform modules:

```
terraform/
├── modules/
│   ├── aws/
│   │   ├── vpc/
│   │   ├── ec2/
│   │   ├── rds/
│   │   └── security/
│   ├── azure/
│   │   ├── resource-group/
│   │   ├── virtual-network/
│   │   ├── virtual-machines/
│   │   └── security/
│   ├── gcp/
│   │   ├── vpc/
│   │   ├── compute/
│   │   ├── storage/
│   │   └── security/
│   └── vmware/
│       ├── datacenter/
│       ├── cluster/
│       ├── virtual-machines/
│       └── networking/
```

### **Ansible Playbooks**

Platform-specific configuration management:

```
ansible/
├── playbooks/
│   ├── aws/
│   │   ├── ec2-setup.yml
│   │   ├── security-groups.yml
│   │   └── cloudwatch.yml
│   ├── azure/
│   │   ├── vm-setup.yml
│   │   ├── nsg-config.yml
│   │   └── monitoring.yml
│   ├── gcp/
│   │   ├── compute-setup.yml
│   │   ├── firewall-rules.yml
│   │   └── stackdriver.yml
│   └── vmware/
│       ├── vm-deployment.yml
│       ├── network-config.yml
│       └── vcenter-setup.yml
```

### **Packer Templates**

Platform-optimized image building:

```
packer/
├── templates/
│   ├── aws/
│   │   ├── amazon-linux-goad-blue.json
│   │   ├── ubuntu-goad-blue.json
│   │   └── windows-goad-blue.json
│   ├── azure/
│   │   ├── ubuntu-azure-goad-blue.json
│   │   └── windows-azure-goad-blue.json
│   ├── gcp/
│   │   ├── ubuntu-gcp-goad-blue.json
│   │   └── windows-gcp-goad-blue.json
│   └── vmware/
│       ├── ubuntu-vmware-goad-blue.json
│       └── windows-vmware-goad-blue.json
```

## 📚 Platform-Specific Guides

### **Detailed Documentation**

- **[AWS Deployment Guide](aws.md)** - Complete AWS deployment with best practices
- **[Azure Deployment Guide](azure.md)** - Azure-specific configuration and optimization
- **[GCP Deployment Guide](gcp.md)** - Google Cloud deployment and management
- **[VMware Deployment Guide](vmware.md)** - vSphere and Workstation deployment
- **[Proxmox Deployment Guide](proxmox.md)** - Proxmox VE configuration and setup
- **[VirtualBox Deployment Guide](virtualbox.md)** - VirtualBox development environment
- **[Hyper-V Deployment Guide](hyperv.md)** - Windows Hyper-V deployment
- **[OpenStack Deployment Guide](openstack.md)** - Private cloud deployment
- **[Kubernetes Deployment Guide](kubernetes.md)** - Container orchestration deployment

### **Migration Guides**

- **[Platform Migration](migration.md)** - Moving between platforms
- **[Backup and Restore](backup-restore.md)** - Cross-platform data protection
- **[Disaster Recovery](disaster-recovery.md)** - Business continuity planning

---

!!! info "Platform Selection"
    Choose your platform based on:
    
    - **Budget**: Cloud vs. on-premises costs
    - **Scalability**: Current and future requirements
    - **Expertise**: Team familiarity with platforms
    - **Integration**: Existing infrastructure compatibility
    - **Compliance**: Regulatory and security requirements

!!! tip "Multi-Platform Deployment"
    Consider hybrid deployments:
    
    - **Development**: VirtualBox or Proxmox
    - **Testing**: Cloud platforms for scalability
    - **Production**: Enterprise platforms for reliability
    - **Disaster Recovery**: Cross-platform redundancy

!!! warning "Platform Limitations"
    Be aware of platform-specific limitations:
    
    - **VirtualBox**: Limited networking and performance
    - **Cloud**: Ongoing costs and data egress charges
    - **On-premises**: Hardware maintenance and scaling
    - **Licensing**: Commercial platform costs and restrictions
