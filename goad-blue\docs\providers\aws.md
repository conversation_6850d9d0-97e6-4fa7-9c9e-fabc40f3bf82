# AWS Deployment Guide

This guide covers deploying GOAD-Blue on Amazon Web Services (AWS) using Infrastructure as Code with Terraform and configuration management with Ansible.

## 🏗️ AWS Architecture

```mermaid
graph TB
    subgraph "🌐 Internet"
        INTERNET[🌍 Internet Gateway<br/>Public Access]
    end
    
    subgraph "☁️ AWS VPC (10.0.0.0/16)"
        IGW[🚪 Internet Gateway<br/>10.0.0.1]
        
        subgraph "🔒 Public Subnets"
            PUB_1[📡 Public Subnet 1<br/>********/24<br/>us-east-1a]
            PUB_2[📡 Public Subnet 2<br/>********/24<br/>us-east-1b]
            
            ALB[⚖️ Application Load Balancer<br/>HTTPS Termination]
            NAT_1[🔄 NAT Gateway 1<br/>us-east-1a]
            NAT_2[🔄 NAT Gateway 2<br/>us-east-1b]
        end
        
        subgraph "🔐 Private Subnets"
            PRIV_1[🏠 Private Subnet 1<br/>*********/24<br/>us-east-1a]
            PRIV_2[🏠 Private Subnet 2<br/>*********/24<br/>us-east-1b]
            
            SPLUNK[📊 Splunk Enterprise<br/>c5.2xlarge<br/>10.0.10.10]
            SO_MGR[🧅 Security Onion Manager<br/>c5.2xlarge<br/>10.0.10.70]
            SO_SENSOR[📡 Security Onion Sensor<br/>c5.xlarge<br/>10.0.20.71]
            VELO[🦖 Velociraptor Server<br/>t3.large<br/>10.0.10.85]
            MISP[🧠 MISP Server<br/>t3.large<br/>10.0.20.30]
        end
        
        subgraph "🗄️ Database Subnets"
            DB_1[💾 Database Subnet 1<br/>*********/24<br/>us-east-1a]
            DB_2[💾 Database Subnet 2<br/>*********/24<br/>us-east-1b]
            
            RDS[🗃️ RDS PostgreSQL<br/>Multi-AZ<br/>Splunk & MISP DB]
        end
        
        subgraph "🔍 Monitoring"
            CLOUDWATCH[📊 CloudWatch<br/>Metrics & Logs]
            CLOUDTRAIL[📋 CloudTrail<br/>API Auditing]
            CONFIG[⚙️ AWS Config<br/>Compliance]
        end
    end
    
    subgraph "🔒 Security Groups"
        SG_ALB[🛡️ ALB Security Group<br/>80, 443 from Internet]
        SG_WEB[🛡️ Web Security Group<br/>8000, 8889 from ALB]
        SG_APP[🛡️ App Security Group<br/>Internal Communication]
        SG_DB[🛡️ Database Security Group<br/>5432 from App SG]
    end
    
    %% Connections
    INTERNET --> IGW
    IGW --> ALB
    ALB --> SPLUNK
    ALB --> VELO
    
    PUB_1 --> NAT_1
    PUB_2 --> NAT_2
    NAT_1 --> PRIV_1
    NAT_2 --> PRIV_2
    
    SPLUNK --> RDS
    MISP --> RDS
    
    SPLUNK --> CLOUDWATCH
    SO_MGR --> CLOUDWATCH
    VELO --> CLOUDWATCH
    
    classDef public fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef private fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef database fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef security fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef monitoring fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class IGW,PUB_1,PUB_2,ALB,NAT_1,NAT_2 public
    class PRIV_1,PRIV_2,SPLUNK,SO_MGR,SO_SENSOR,VELO,MISP private
    class DB_1,DB_2,RDS database
    class SG_ALB,SG_WEB,SG_APP,SG_DB security
    class CLOUDWATCH,CLOUDTRAIL,CONFIG monitoring
```

## 📋 Prerequisites

### **AWS Account Setup**

1. **AWS Account**: Active AWS account with billing enabled
2. **IAM User**: Programmatic access with appropriate permissions
3. **AWS CLI**: Installed and configured
4. **Terraform**: Version 1.0+ installed
5. **Ansible**: Version 4.0+ installed

### **Required AWS Permissions**

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ec2:*",
        "vpc:*",
        "iam:*",
        "rds:*",
        "elasticloadbalancing:*",
        "autoscaling:*",
        "cloudwatch:*",
        "logs:*",
        "s3:*",
        "route53:*",
        "acm:*"
      ],
      "Resource": "*"
    }
  ]
}
```

### **AWS CLI Configuration**

```bash
# Install AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Configure credentials
aws configure
# AWS Access Key ID: YOUR_ACCESS_KEY
# AWS Secret Access Key: YOUR_SECRET_KEY
# Default region name: us-east-1
# Default output format: json

# Verify configuration
aws sts get-caller-identity
```

## 🚀 Deployment Process

### **1. Infrastructure Deployment**

#### **Terraform Configuration**

```hcl
# terraform/providers/aws/main.tf
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  
  backend "s3" {
    bucket = "goad-blue-terraform-state"
    key    = "aws/terraform.tfstate"
    region = "us-east-1"
  }
}

provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = "GOAD-Blue"
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# VPC Module
module "vpc" {
  source = "../../modules/aws/vpc"
  
  name_prefix = var.name_prefix
  vpc_cidr    = var.vpc_cidr
  
  availability_zones = data.aws_availability_zones.available.names
  public_subnets     = var.public_subnets
  private_subnets    = var.private_subnets
  database_subnets   = var.database_subnets
  
  enable_nat_gateway = true
  enable_vpn_gateway = false
  
  tags = var.tags
}

# Security Groups
module "security_groups" {
  source = "../../modules/aws/security"
  
  name_prefix = var.name_prefix
  vpc_id      = module.vpc.vpc_id
  
  allowed_cidr_blocks = var.allowed_cidr_blocks
  
  tags = var.tags
}

# Application Load Balancer
module "alb" {
  source = "../../modules/aws/alb"
  
  name_prefix = var.name_prefix
  vpc_id      = module.vpc.vpc_id
  
  public_subnet_ids  = module.vpc.public_subnet_ids
  security_group_ids = [module.security_groups.alb_security_group_id]
  
  certificate_arn = var.ssl_certificate_arn
  
  tags = var.tags
}

# EC2 Instances
module "ec2_instances" {
  source = "../../modules/aws/ec2"
  
  name_prefix = var.name_prefix
  
  vpc_id             = module.vpc.vpc_id
  private_subnet_ids = module.vpc.private_subnet_ids
  
  security_group_ids = [
    module.security_groups.web_security_group_id,
    module.security_groups.app_security_group_id
  ]
  
  instances = var.instances
  
  key_pair_name = var.key_pair_name
  
  tags = var.tags
}

# RDS Database
module "rds" {
  source = "../../modules/aws/rds"
  
  name_prefix = var.name_prefix
  
  vpc_id               = module.vpc.vpc_id
  database_subnet_ids  = module.vpc.database_subnet_ids
  security_group_ids   = [module.security_groups.database_security_group_id]
  
  engine_version = var.rds_engine_version
  instance_class = var.rds_instance_class
  
  database_name = var.database_name
  username      = var.database_username
  password      = var.database_password
  
  backup_retention_period = var.backup_retention_period
  multi_az               = var.multi_az
  
  tags = var.tags
}

# Auto Scaling Groups
module "autoscaling" {
  source = "../../modules/aws/autoscaling"
  
  name_prefix = var.name_prefix
  
  vpc_id             = module.vpc.vpc_id
  private_subnet_ids = module.vpc.private_subnet_ids
  
  target_group_arns = module.alb.target_group_arns
  
  launch_template_configs = var.launch_template_configs
  
  tags = var.tags
}
```

#### **Variables Configuration**

```hcl
# terraform/providers/aws/variables.tf
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

variable "name_prefix" {
  description = "Prefix for all resource names"
  type        = string
  default     = "goad-blue"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "production"
}

variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnets" {
  description = "CIDR blocks for public subnets"
  type        = list(string)
  default     = ["********/24", "********/24"]
}

variable "private_subnets" {
  description = "CIDR blocks for private subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24"]
}

variable "database_subnets" {
  description = "CIDR blocks for database subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24"]
}

variable "instances" {
  description = "EC2 instance configurations"
  type = map(object({
    instance_type = string
    ami_id        = string
    subnet_type   = string
    volume_size   = number
    volume_type   = string
  }))
  default = {
    splunk_enterprise = {
      instance_type = "c5.2xlarge"
      ami_id        = "ami-0abcdef1234567890"
      subnet_type   = "private"
      volume_size   = 500
      volume_type   = "gp3"
    }
    security_onion_manager = {
      instance_type = "c5.2xlarge"
      ami_id        = "ami-0abcdef1234567890"
      subnet_type   = "private"
      volume_size   = 1000
      volume_type   = "gp3"
    }
    security_onion_sensor = {
      instance_type = "c5.xlarge"
      ami_id        = "ami-0abcdef1234567890"
      subnet_type   = "private"
      volume_size   = 500
      volume_type   = "gp3"
    }
    velociraptor_server = {
      instance_type = "t3.large"
      ami_id        = "ami-0abcdef1234567890"
      subnet_type   = "private"
      volume_size   = 200
      volume_type   = "gp3"
    }
    misp_server = {
      instance_type = "t3.large"
      ami_id        = "ami-0abcdef1234567890"
      subnet_type   = "private"
      volume_size   = 200
      volume_type   = "gp3"
    }
  }
}

variable "key_pair_name" {
  description = "AWS key pair name for EC2 instances"
  type        = string
}

variable "ssl_certificate_arn" {
  description = "ARN of SSL certificate for ALB"
  type        = string
  default     = ""
}

variable "allowed_cidr_blocks" {
  description = "CIDR blocks allowed to access the environment"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default = {
    Project     = "GOAD-Blue"
    Environment = "production"
    ManagedBy   = "Terraform"
  }
}
```

#### **Deployment Commands**

```bash
# Navigate to AWS provider directory
cd terraform/providers/aws

# Initialize Terraform
terraform init

# Create terraform.tfvars file
cat > terraform.tfvars << EOF
aws_region = "us-east-1"
name_prefix = "goad-blue-prod"
environment = "production"

vpc_cidr = "10.0.0.0/16"
public_subnets = ["********/24", "********/24"]
private_subnets = ["*********/24", "*********/24"]
database_subnets = ["*********/24", "*********/24"]

key_pair_name = "goad-blue-keypair"
ssl_certificate_arn = "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"

allowed_cidr_blocks = ["***********/24"]  # Your office IP range

tags = {
  Project = "GOAD-Blue"
  Environment = "production"
  Owner = "Security-Team"
  CostCenter = "IT-Security"
}
EOF

# Plan deployment
terraform plan -var-file="terraform.tfvars"

# Apply deployment
terraform apply -var-file="terraform.tfvars"
```

### **2. AMI Creation with Packer**

#### **Splunk Enterprise AMI**

```json
{
  "variables": {
    "aws_region": "us-east-1",
    "instance_type": "t3.large",
    "source_ami": "ami-0abcdef1234567890",
    "ssh_username": "ubuntu"
  },
  "builders": [
    {
      "type": "amazon-ebs",
      "region": "{{user `aws_region`}}",
      "source_ami": "{{user `source_ami`}}",
      "instance_type": "{{user `instance_type`}}",
      "ssh_username": "{{user `ssh_username`}}",
      "ami_name": "goad-blue-splunk-{{timestamp}}",
      "ami_description": "GOAD-Blue Splunk Enterprise AMI",
      "tags": {
        "Name": "GOAD-Blue Splunk Enterprise",
        "Component": "splunk",
        "Project": "GOAD-Blue",
        "BuildDate": "{{timestamp}}"
      }
    }
  ],
  "provisioners": [
    {
      "type": "shell",
      "inline": [
        "sudo apt-get update",
        "sudo apt-get upgrade -y",
        "sudo apt-get install -y curl wget unzip"
      ]
    },
    {
      "type": "file",
      "source": "../../files/splunk/",
      "destination": "/tmp/splunk-files/"
    },
    {
      "type": "shell",
      "script": "../../scripts/aws/install-splunk.sh"
    },
    {
      "type": "shell",
      "script": "../../scripts/aws/configure-cloudwatch.sh"
    },
    {
      "type": "shell",
      "inline": [
        "sudo apt-get autoremove -y",
        "sudo apt-get autoclean",
        "sudo rm -rf /tmp/*",
        "history -c"
      ]
    }
  ]
}
```

#### **Build AMIs**

```bash
# Build Splunk AMI
cd packer/templates/aws
packer build -var-file="variables.json" goad-blue-splunk.json

# Build Security Onion AMI
packer build -var-file="variables.json" goad-blue-security-onion.json

# Build Velociraptor AMI
packer build -var-file="variables.json" goad-blue-velociraptor.json

# Build MISP AMI
packer build -var-file="variables.json" goad-blue-misp.json
```

### **3. Configuration with Ansible**

#### **AWS Dynamic Inventory**

```yaml
# ansible/inventory/aws_ec2.yml
plugin: amazon.aws.aws_ec2
regions:
  - us-east-1
  - us-west-2

filters:
  tag:Project: GOAD-Blue
  instance-state-name: running

keyed_groups:
  - key: tags.Component
    prefix: component
  - key: tags.Environment
    prefix: env
  - key: placement.availability_zone
    prefix: az

hostnames:
  - private-ip-address
  - tag:Name

compose:
  ansible_host: private_ip_address
  ansible_user: ubuntu
  component: tags.Component
  environment: tags.Environment
```

#### **Site Playbook**

```yaml
# ansible/playbooks/aws/site.yml
---
- name: Configure AWS GOAD-Blue Infrastructure
  hosts: localhost
  gather_facts: false
  tasks:
    - name: Display deployment information
      debug:
        msg: |
          Configuring GOAD-Blue on AWS
          Region: {{ aws_region }}
          Environment: {{ environment }}

- name: Configure common AWS settings
  hosts: all
  become: true
  roles:
    - common
    - aws-cloudwatch
    - aws-ssm
  tags:
    - common
    - aws

- name: Configure Splunk Enterprise
  hosts: component_splunk
  become: true
  roles:
    - splunk-enterprise
    - aws-splunk-integration
  tags:
    - splunk

- name: Configure Security Onion
  hosts: component_security_onion
  become: true
  roles:
    - security-onion
    - aws-security-onion-integration
  tags:
    - security-onion

- name: Configure Velociraptor
  hosts: component_velociraptor
  become: true
  roles:
    - velociraptor
    - aws-velociraptor-integration
  tags:
    - velociraptor

- name: Configure MISP
  hosts: component_misp
  become: true
  roles:
    - misp
    - aws-misp-integration
  tags:
    - misp

- name: Configure AWS integrations
  hosts: all
  become: true
  roles:
    - aws-backup
    - aws-monitoring
    - aws-security
  tags:
    - aws-integration
```

#### **Run Ansible Configuration**

```bash
# Navigate to Ansible directory
cd ansible

# Install AWS collection
ansible-galaxy collection install amazon.aws

# Configure AWS credentials for Ansible
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export AWS_DEFAULT_REGION="us-east-1"

# Run site playbook
ansible-playbook -i inventory/aws_ec2.yml playbooks/aws/site.yml

# Run specific components
ansible-playbook -i inventory/aws_ec2.yml playbooks/aws/site.yml --tags splunk
ansible-playbook -i inventory/aws_ec2.yml playbooks/aws/site.yml --tags security-onion
```

## 🔧 AWS-Specific Features

### **Auto Scaling Configuration**

```hcl
# Auto Scaling for Security Onion Sensors
resource "aws_autoscaling_group" "security_onion_sensors" {
  name                = "${var.name_prefix}-so-sensors"
  vpc_zone_identifier = var.private_subnet_ids
  target_group_arns   = [aws_lb_target_group.security_onion.arn]
  health_check_type   = "ELB"
  
  min_size         = 2
  max_size         = 10
  desired_capacity = 2
  
  launch_template {
    id      = aws_launch_template.security_onion_sensor.id
    version = "$Latest"
  }
  
  tag {
    key                 = "Name"
    value               = "${var.name_prefix}-so-sensor"
    propagate_at_launch = true
  }
  
  tag {
    key                 = "Component"
    value               = "security-onion-sensor"
    propagate_at_launch = true
  }
}

# Auto Scaling Policy
resource "aws_autoscaling_policy" "security_onion_scale_up" {
  name                   = "${var.name_prefix}-so-scale-up"
  scaling_adjustment     = 1
  adjustment_type        = "ChangeInCapacity"
  cooldown              = 300
  autoscaling_group_name = aws_autoscaling_group.security_onion_sensors.name
}

# CloudWatch Alarm for scaling
resource "aws_cloudwatch_metric_alarm" "high_cpu" {
  alarm_name          = "${var.name_prefix}-so-high-cpu"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = "120"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors ec2 cpu utilization"
  alarm_actions       = [aws_autoscaling_policy.security_onion_scale_up.arn]
  
  dimensions = {
    AutoScalingGroupName = aws_autoscaling_group.security_onion_sensors.name
  }
}
```

### **CloudWatch Integration**

```bash
# Install CloudWatch agent
sudo wget https://s3.amazonaws.com/amazoncloudwatch-agent/amazon_linux/amd64/latest/amazon-cloudwatch-agent.rpm
sudo rpm -U ./amazon-cloudwatch-agent.rpm

# Configure CloudWatch agent
sudo tee /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json > /dev/null <<EOF
{
  "metrics": {
    "namespace": "GOAD-Blue",
    "metrics_collected": {
      "cpu": {
        "measurement": ["cpu_usage_idle", "cpu_usage_iowait", "cpu_usage_user", "cpu_usage_system"],
        "metrics_collection_interval": 60
      },
      "disk": {
        "measurement": ["used_percent"],
        "metrics_collection_interval": 60,
        "resources": ["*"]
      },
      "mem": {
        "measurement": ["mem_used_percent"],
        "metrics_collection_interval": 60
      }
    }
  },
  "logs": {
    "logs_collected": {
      "files": {
        "collect_list": [
          {
            "file_path": "/var/log/splunk/splunkd.log",
            "log_group_name": "goad-blue-splunk",
            "log_stream_name": "{instance_id}-splunkd"
          },
          {
            "file_path": "/var/log/suricata/suricata.log",
            "log_group_name": "goad-blue-security-onion",
            "log_stream_name": "{instance_id}-suricata"
          }
        ]
      }
    }
  }
}
EOF

# Start CloudWatch agent
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl \
  -a fetch-config -m ec2 -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json -s
```

### **S3 Integration for Backups**

```bash
# Create S3 bucket for backups
aws s3 mb s3://goad-blue-backups-$(date +%s)

# Configure automated backups
cat > /etc/cron.daily/goad-blue-backup << 'EOF'
#!/bin/bash
BACKUP_BUCKET="goad-blue-backups-123456789"
DATE=$(date +%Y%m%d)

# Backup Splunk configuration
tar -czf /tmp/splunk-config-$DATE.tar.gz /opt/splunk/etc/
aws s3 cp /tmp/splunk-config-$DATE.tar.gz s3://$BACKUP_BUCKET/splunk/

# Backup Security Onion configuration
tar -czf /tmp/so-config-$DATE.tar.gz /opt/so/
aws s3 cp /tmp/so-config-$DATE.tar.gz s3://$BACKUP_BUCKET/security-onion/

# Cleanup local backups older than 7 days
find /tmp/ -name "*-config-*.tar.gz" -mtime +7 -delete
EOF

chmod +x /etc/cron.daily/goad-blue-backup
```

## 💰 Cost Optimization

### **Instance Right-Sizing**

```bash
# Use AWS Cost Explorer API to analyze usage
aws ce get-cost-and-usage \
  --time-period Start=2024-01-01,End=2024-01-31 \
  --granularity MONTHLY \
  --metrics BlendedCost \
  --group-by Type=DIMENSION,Key=SERVICE

# Get recommendations for instance types
aws compute-optimizer get-ec2-instance-recommendations \
  --instance-arns arn:aws:ec2:us-east-1:123456789012:instance/i-1234567890abcdef0
```

### **Spot Instances for Development**

```hcl
# Use Spot instances for non-critical workloads
resource "aws_spot_instance_request" "goad_blue_dev" {
  ami                    = var.ami_id
  spot_price             = "0.05"
  instance_type          = "c5.large"
  spot_type              = "one-time"
  wait_for_fulfillment   = true
  
  vpc_security_group_ids = [aws_security_group.goad_blue.id]
  subnet_id              = aws_subnet.private[0].id
  
  tags = {
    Name = "GOAD-Blue-Dev-Spot"
    Environment = "development"
  }
}
```

---

!!! success "AWS Deployment Complete"
    Your GOAD-Blue environment is now deployed on AWS with enterprise-grade scalability, security, and monitoring capabilities.

!!! tip "Cost Management"
    Monitor your AWS costs regularly:
    - Use AWS Cost Explorer for usage analysis
    - Set up billing alerts for budget management
    - Consider Reserved Instances for long-term deployments
    - Use Spot Instances for development environments

!!! warning "Security Best Practices"
    - Regularly rotate IAM access keys
    - Enable AWS CloudTrail for audit logging
    - Use AWS Config for compliance monitoring
    - Implement least privilege access principles
    - Enable GuardDuty for threat detection
