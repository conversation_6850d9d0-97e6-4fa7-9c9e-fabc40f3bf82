---
# Load datas
- import_playbook: data.yml
  vars:
    data_path: "../ad/{{domain_name}}/data/"
  tags: 'data'

- name: build all
  hosts: domain
  roles:
    - { role: 'common', tags: 'common', http_proxy: "{{enable_http_proxy}}"}
    - { role: 'settings/keyboard', tags: 'keyboard', layouts: "{{keyboard_layouts}}" }

# do not add srv with no update -> generate error on iis install
- name: build all no update
  hosts: no_update
  roles:
    - { role: 'settings/no_updates', tags: 'no_updates' }


- name: "Launch windows updates before continue"
  hosts: update
  roles:
    - { role: 'settings/updates', tags: 'updates'}