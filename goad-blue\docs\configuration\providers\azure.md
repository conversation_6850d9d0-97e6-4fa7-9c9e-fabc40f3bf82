# Azure Configuration

This guide covers GOAD-Blue deployment and configuration on Microsoft Azure, including Virtual Networks, Virtual Machines, and Azure-native security services.

## ☁️ Azure Architecture Overview

GOAD-Blue on Azure leverages Azure's enterprise-grade services for security, scalability, and integration with Microsoft ecosystems.

```mermaid
graph TB
    subgraph "🌐 Azure Global Infrastructure"
        REGION[🌍 Azure Region<br/>East US<br/>Primary Region]
        AZ1[📍 Availability Zone 1<br/>Primary Zone<br/>Main Components]
        AZ2[📍 Availability Zone 2<br/>Secondary Zone<br/>HA Components]
    end
    
    subgraph "🔒 Virtual Network"
        VNET[🏠 GOAD-Blue VNet<br/>10.0.0.0/16<br/>Isolated Network]
        NSG[🛡️ Network Security Groups<br/>Firewall Rules<br/>Traffic Control]
        
        WEB_SUBNET[🌐 Web Subnet<br/>********/24<br/>Load Balancers]
        APP_SUBNET[🔒 App Subnet<br/>********/24<br/>Application Tier]
        DATA_SUBNET[🗄️ Data Subnet<br/>********/24<br/>Database Tier]
    end
    
    subgraph "🖥️ Compute Resources"
        ALB[⚖️ Azure Load Balancer<br/>Traffic Distribution<br/>High Availability]
        SPLUNK[📊 Splunk VMs<br/>Standard_D4s_v3<br/>VM Scale Sets]
        SO[🧅 Security Onion<br/>Standard_D8s_v3<br/>Network Monitoring]
        VELO[🦖 Velociraptor<br/>Standard_B2ms<br/>Endpoint Detection]
    end
    
    subgraph "🗄️ Storage & Data"
        STORAGE[💾 Azure Storage<br/>Premium SSD<br/>Encrypted Disks]
        BLOB[🪣 Blob Storage<br/>Log Archive<br/>Backup Storage]
        SQL[🗄️ Azure SQL Database<br/>Managed Database<br/>High Availability]
    end
    
    subgraph "🔍 Monitoring & Security"
        MONITOR[📊 Azure Monitor<br/>Metrics & Logs<br/>Application Insights]
        SENTINEL[🛡️ Azure Sentinel<br/>Cloud SIEM<br/>AI-powered Security]
        DEFENDER[🛡️ Azure Defender<br/>Threat Protection<br/>Security Center]
    end
    
    REGION --> AZ1
    REGION --> AZ2
    
    VNET --> NSG
    VNET --> WEB_SUBNET
    VNET --> APP_SUBNET
    VNET --> DATA_SUBNET
    
    WEB_SUBNET --> ALB
    APP_SUBNET --> SPLUNK
    APP_SUBNET --> SO
    APP_SUBNET --> VELO
    
    SPLUNK --> STORAGE
    SO --> STORAGE
    VELO --> STORAGE
    
    SPLUNK --> BLOB
    SO --> BLOB
    
    SPLUNK --> SQL
    VELO --> SQL
    
    SPLUNK --> MONITOR
    SO --> MONITOR
    VELO --> MONITOR
    
    classDef azure fill:#0078d4,stroke:#ffffff,stroke-width:2px,color:#fff
    classDef network fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef compute fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef storage fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef monitoring fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    
    class REGION,AZ1,AZ2 azure
    class VNET,NSG,WEB_SUBNET,APP_SUBNET,DATA_SUBNET network
    class ALB,SPLUNK,SO,VELO compute
    class STORAGE,BLOB,SQL storage
    class MONITOR,SENTINEL,DEFENDER monitoring
```

## 🏗️ Virtual Network Configuration

### **VNet and Subnet Setup**

```yaml
# Azure Virtual Network configuration
azure_vnet:
  # Resource Group
  resource_group:
    name: "rg-goad-blue-prod"
    location: "East US"
    
    tags:
      Environment: "production"
      Project: "GOAD-Blue"
      CostCenter: "Security"
      
  # Virtual Network
  virtual_network:
    name: "vnet-goad-blue"
    address_space: ["10.0.0.0/16"]
    location: "East US"
    
    # DNS servers
    dns_servers: ["*********", "*********"]
    
    tags:
      Name: "GOAD-Blue-VNet"
      Environment: "production"
      
  # Subnets
  subnets:
    # Web tier subnet
    web:
      name: "snet-web"
      address_prefix: "********/24"
      
      # Service endpoints
      service_endpoints:
        - "Microsoft.Storage"
        - "Microsoft.Sql"
        
    # Application tier subnet
    app:
      name: "snet-app"
      address_prefix: "********/24"
      
      # Delegation for container instances
      delegations:
        - name: "container-delegation"
          service_delegation:
            name: "Microsoft.ContainerInstance/containerGroups"
            
    # Data tier subnet
    data:
      name: "snet-data"
      address_prefix: "********/24"
      
      # Private endpoint policies
      private_endpoint_network_policies_enabled: false
      
    # Management subnet
    management:
      name: "snet-mgmt"
      address_prefix: "*********/24"
      
    # Azure Bastion subnet
    bastion:
      name: "AzureBastionSubnet"
      address_prefix: "**********/27"
```

### **Network Security Groups**

```yaml
# Network Security Groups configuration
network_security_groups:
  # Web tier NSG
  web_nsg:
    name: "nsg-web-tier"
    location: "East US"
    
    security_rules:
      # Inbound rules
      - name: "AllowHTTPS"
        priority: 100
        direction: "Inbound"
        access: "Allow"
        protocol: "Tcp"
        source_port_range: "*"
        destination_port_range: "443"
        source_address_prefix: "*"
        destination_address_prefix: "*"
        
      - name: "AllowHTTP"
        priority: 110
        direction: "Inbound"
        access: "Allow"
        protocol: "Tcp"
        source_port_range: "*"
        destination_port_range: "80"
        source_address_prefix: "*"
        destination_address_prefix: "*"
        
      # Outbound rules
      - name: "AllowAppTier"
        priority: 100
        direction: "Outbound"
        access: "Allow"
        protocol: "*"
        source_port_range: "*"
        destination_port_range: "*"
        source_address_prefix: "********/24"
        destination_address_prefix: "********/24"
        
  # Application tier NSG
  app_nsg:
    name: "nsg-app-tier"
    location: "East US"
    
    security_rules:
      # Splunk Web interface
      - name: "AllowSplunkWeb"
        priority: 100
        direction: "Inbound"
        access: "Allow"
        protocol: "Tcp"
        source_port_range: "*"
        destination_port_range: "8000"
        source_address_prefix: "********/24"
        destination_address_prefix: "*"
        
      # Splunk management
      - name: "AllowSplunkMgmt"
        priority: 110
        direction: "Inbound"
        access: "Allow"
        protocol: "Tcp"
        source_port_range: "*"
        destination_port_range: "8089"
        source_address_prefix: "********/24"
        destination_address_prefix: "*"
        
      # SSH from bastion
      - name: "AllowSSHFromBastion"
        priority: 120
        direction: "Inbound"
        access: "Allow"
        protocol: "Tcp"
        source_port_range: "*"
        destination_port_range: "22"
        source_address_prefix: "**********/27"
        destination_address_prefix: "*"
        
  # Data tier NSG
  data_nsg:
    name: "nsg-data-tier"
    location: "East US"
    
    security_rules:
      # PostgreSQL from app tier
      - name: "AllowPostgreSQL"
        priority: 100
        direction: "Inbound"
        access: "Allow"
        protocol: "Tcp"
        source_port_range: "*"
        destination_port_range: "5432"
        source_address_prefix: "********/24"
        destination_address_prefix: "*"
```

## 🖥️ Virtual Machine Configuration

### **VM Specifications**

```yaml
# Virtual Machine configurations
virtual_machines:
  # Splunk Search Head
  splunk_search_head:
    name: "vm-splunk-sh-01"
    location: "East US"
    size: "Standard_D4s_v3"  # 4 vCPUs, 16 GB RAM
    
    # OS configuration
    os_profile:
      computer_name: "splunk-sh-01"
      admin_username: "azureuser"
      disable_password_authentication: true
      
    # SSH key
    ssh_keys:
      - path: "/home/<USER>/.ssh/authorized_keys"
        key_data: "${ssh_public_key}"
        
    # Storage profile
    storage_profile:
      image_reference:
        publisher: "Canonical"
        offer: "0001-com-ubuntu-server-focal"
        sku: "20_04-lts-gen2"
        version: "latest"
        
      os_disk:
        name: "osdisk-splunk-sh-01"
        caching: "ReadWrite"
        storage_account_type: "Premium_LRS"
        disk_size_gb: 128
        
      data_disks:
        - name: "datadisk-splunk-sh-01"
          disk_size_gb: 512
          storage_account_type: "Premium_LRS"
          caching: "ReadWrite"
          lun: 0
          
    # Network configuration
    network_interface:
      name: "nic-splunk-sh-01"
      subnet_id: "/subscriptions/${subscription_id}/resourceGroups/rg-goad-blue-prod/providers/Microsoft.Network/virtualNetworks/vnet-goad-blue/subnets/snet-app"
      private_ip_address: "*********"
      
    # Availability set
    availability_set_id: "/subscriptions/${subscription_id}/resourceGroups/rg-goad-blue-prod/providers/Microsoft.Compute/availabilitySets/as-splunk"
    
    tags:
      Component: "Splunk"
      Role: "SearchHead"
      Environment: "production"
      
  # Security Onion Manager
  security_onion_manager:
    name: "vm-so-manager-01"
    location: "East US"
    size: "Standard_D8s_v3"  # 8 vCPUs, 32 GB RAM
    
    # OS configuration
    os_profile:
      computer_name: "so-manager-01"
      admin_username: "azureuser"
      disable_password_authentication: true
      
    # Storage profile
    storage_profile:
      os_disk:
        name: "osdisk-so-manager-01"
        caching: "ReadWrite"
        storage_account_type: "Premium_LRS"
        disk_size_gb: 128
        
      data_disks:
        - name: "datadisk-so-manager-01"
          disk_size_gb: 1024
          storage_account_type: "Premium_LRS"
          caching: "ReadWrite"
          lun: 0
          
    # Network configuration
    network_interface:
      name: "nic-so-manager-01"
      subnet_id: "/subscriptions/${subscription_id}/resourceGroups/rg-goad-blue-prod/providers/Microsoft.Network/virtualNetworks/vnet-goad-blue/subnets/snet-app"
      private_ip_address: "*********"
      
    # Accelerated networking
    enable_accelerated_networking: true
    
    tags:
      Component: "SecurityOnion"
      Role: "Manager"
      Environment: "production"
```

### **VM Scale Sets**

```yaml
# VM Scale Sets for auto-scaling
vm_scale_sets:
  # Splunk Indexer Scale Set
  splunk_indexers:
    name: "vmss-splunk-indexers"
    location: "East US"
    sku:
      name: "Standard_D4s_v3"
      tier: "Standard"
      capacity: 3
      
    # Upgrade policy
    upgrade_policy_mode: "Automatic"
    
    # OS profile
    os_profile:
      computer_name_prefix: "splunk-idx"
      admin_username: "azureuser"
      disable_password_authentication: true
      
    # Storage profile
    storage_profile:
      image_reference:
        publisher: "Canonical"
        offer: "0001-com-ubuntu-server-focal"
        sku: "20_04-lts-gen2"
        version: "latest"
        
      os_disk:
        caching: "ReadWrite"
        storage_account_type: "Premium_LRS"
        
      data_disk:
        disk_size_gb: 1024
        storage_account_type: "Premium_LRS"
        caching: "ReadWrite"
        lun: 0
        
    # Network profile
    network_profile:
      network_interface_configurations:
        - name: "nic-splunk-indexers"
          primary: true
          
          ip_configurations:
            - name: "internal"
              subnet_id: "/subscriptions/${subscription_id}/resourceGroups/rg-goad-blue-prod/providers/Microsoft.Network/virtualNetworks/vnet-goad-blue/subnets/snet-app"
              load_balancer_backend_address_pool_ids:
                - "/subscriptions/${subscription_id}/resourceGroups/rg-goad-blue-prod/providers/Microsoft.Network/loadBalancers/lb-splunk/backendAddressPools/splunk-indexers"
                
    # Auto-scaling
    autoscale_settings:
      name: "autoscale-splunk-indexers"
      
      profiles:
        - name: "default"
          capacity:
            default: 3
            minimum: 2
            maximum: 10
            
          rules:
            # Scale out rule
            - metric_trigger:
                metric_name: "Percentage CPU"
                metric_resource_id: "/subscriptions/${subscription_id}/resourceGroups/rg-goad-blue-prod/providers/Microsoft.Compute/virtualMachineScaleSets/vmss-splunk-indexers"
                time_grain: "PT1M"
                statistic: "Average"
                time_window: "PT5M"
                time_aggregation: "Average"
                operator: "GreaterThan"
                threshold: 75
                
              scale_action:
                direction: "Increase"
                type: "ChangeCount"
                value: 1
                cooldown: "PT5M"
                
            # Scale in rule
            - metric_trigger:
                metric_name: "Percentage CPU"
                metric_resource_id: "/subscriptions/${subscription_id}/resourceGroups/rg-goad-blue-prod/providers/Microsoft.Compute/virtualMachineScaleSets/vmss-splunk-indexers"
                time_grain: "PT1M"
                statistic: "Average"
                time_window: "PT5M"
                time_aggregation: "Average"
                operator: "LessThan"
                threshold: 25
                
              scale_action:
                direction: "Decrease"
                type: "ChangeCount"
                value: 1
                cooldown: "PT5M"
```

## 🗄️ Azure SQL Database

### **Managed Database Configuration**

```yaml
# Azure SQL Database configuration
azure_sql:
  # SQL Server
  sql_server:
    name: "sql-goad-blue-prod"
    location: "East US"
    version: "12.0"
    administrator_login: "goadblue_admin"
    administrator_login_password: "${sql_admin_password}"
    
    # Azure AD authentication
    azuread_administrator:
      login_username: "<EMAIL>"
      object_id: "12345678-1234-1234-1234-**********12"
      
    # Firewall rules
    firewall_rules:
      - name: "AllowAzureServices"
        start_ip_address: "0.0.0.0"
        end_ip_address: "0.0.0.0"
        
      - name: "AllowAppSubnet"
        start_ip_address: "********"
        end_ip_address: "**********"
        
    tags:
      Environment: "production"
      Component: "Database"
      
  # SQL Database
  sql_database:
    name: "sqldb-goad-blue"
    server_name: "sql-goad-blue-prod"
    
    # Performance tier
    sku_name: "S2"  # Standard tier, 50 DTUs
    
    # Backup configuration
    short_term_retention_policy:
      retention_days: 7
      
    long_term_retention_policy:
      weekly_retention: "P4W"
      monthly_retention: "P12M"
      yearly_retention: "P7Y"
      week_of_year: 1
      
    # Threat detection
    threat_detection_policy:
      state: "Enabled"
      email_account_admins: "Enabled"
      email_addresses: ["<EMAIL>"]
      retention_days: 90
      
    # Transparent data encryption
    transparent_data_encryption:
      status: "Enabled"
      
    tags:
      Environment: "production"
      Component: "Database"
```

## 🪣 Azure Storage Configuration

### **Storage Accounts and Blob Storage**

```yaml
# Azure Storage configuration
azure_storage:
  # Storage account for logs
  log_storage:
    name: "stgoadbluelogs${random_id}"
    location: "East US"
    account_tier: "Standard"
    account_replication_type: "GRS"
    account_kind: "StorageV2"
    
    # Access tier
    access_tier: "Hot"
    
    # Security settings
    enable_https_traffic_only: true
    min_tls_version: "TLS1_2"
    
    # Network rules
    network_rules:
      default_action: "Deny"
      bypass: ["AzureServices"]
      
      virtual_network_subnet_ids:
        - "/subscriptions/${subscription_id}/resourceGroups/rg-goad-blue-prod/providers/Microsoft.Network/virtualNetworks/vnet-goad-blue/subnets/snet-app"
        
    # Blob properties
    blob_properties:
      versioning_enabled: true
      change_feed_enabled: true
      
      # Lifecycle management
      lifecycle_policy:
        rules:
          - name: "log_lifecycle"
            enabled: true
            
            filters:
              blob_types: ["blockBlob"]
              prefix_match: ["logs/"]
              
            actions:
              base_blob:
                tier_to_cool_after_days_since_modification_greater_than: 30
                tier_to_archive_after_days_since_modification_greater_than: 90
                delete_after_days_since_modification_greater_than: 2555
                
    tags:
      Environment: "production"
      Purpose: "LogStorage"
      
  # Storage account for backups
  backup_storage:
    name: "stgoadbluebackup${random_id}"
    location: "East US"
    account_tier: "Standard"
    account_replication_type: "RAGRS"
    account_kind: "StorageV2"
    
    # Access tier
    access_tier: "Cool"
    
    # Immutable storage
    blob_properties:
      immutability_policy:
        allow_protected_append_writes: false
        state: "Unlocked"
        immutability_period_since_creation_in_days: 2555
        
    tags:
      Environment: "production"
      Purpose: "BackupStorage"
```

## 📊 Azure Monitor Configuration

### **Monitoring and Alerting**

```yaml
# Azure Monitor configuration
azure_monitor:
  # Log Analytics Workspace
  log_analytics_workspace:
    name: "law-goad-blue-prod"
    location: "East US"
    sku: "PerGB2018"
    retention_in_days: 90
    
    # Solutions
    solutions:
      - "SecurityCenterFree"
      - "Security"
      - "Updates"
      - "VMInsights"
      
    tags:
      Environment: "production"
      Component: "Monitoring"
      
  # Application Insights
  application_insights:
    name: "ai-goad-blue-prod"
    location: "East US"
    application_type: "web"
    workspace_id: "/subscriptions/${subscription_id}/resourceGroups/rg-goad-blue-prod/providers/Microsoft.OperationalInsights/workspaces/law-goad-blue-prod"
    
  # Action Groups
  action_groups:
    critical_alerts:
      name: "ag-critical-alerts"
      short_name: "critical"
      
      email_receivers:
        - name: "admin"
          email_address: "<EMAIL>"
          
      sms_receivers:
        - name: "oncall"
          country_code: "1"
          phone_number: "**********"
          
      webhook_receivers:
        - name: "slack"
          service_uri: "https://hooks.slack.com/services/..."
          
  # Metric Alerts
  metric_alerts:
    # High CPU alert
    high_cpu:
      name: "alert-high-cpu"
      description: "High CPU utilization detected"
      severity: 2
      frequency: "PT1M"
      window_size: "PT5M"
      
      criteria:
        - metric_namespace: "Microsoft.Compute/virtualMachines"
          metric_name: "Percentage CPU"
          aggregation: "Average"
          operator: "GreaterThan"
          threshold: 80
          
      action_group_ids:
        - "/subscriptions/${subscription_id}/resourceGroups/rg-goad-blue-prod/providers/Microsoft.Insights/actionGroups/ag-critical-alerts"
        
    # Low disk space alert
    low_disk_space:
      name: "alert-low-disk-space"
      description: "Low disk space detected"
      severity: 1
      frequency: "PT5M"
      window_size: "PT15M"
      
      criteria:
        - metric_namespace: "Microsoft.Compute/virtualMachines"
          metric_name: "OS Disk Free Space"
          aggregation: "Average"
          operator: "LessThan"
          threshold: 20
```

## 🔧 Terraform Configuration

### **Azure Provider Configuration**

```hcl
# terraform/providers/azure/main.tf

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
  }
  
  backend "azurerm" {
    resource_group_name  = "rg-terraform-state"
    storage_account_name = "stterraformstate"
    container_name       = "tfstate"
    key                  = "goad-blue.terraform.tfstate"
  }
}

provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
    
    virtual_machine {
      delete_os_disk_on_deletion = true
    }
  }
}

# Resource Group
resource "azurerm_resource_group" "goad_blue" {
  name     = var.resource_group_name
  location = var.location
  
  tags = var.common_tags
}

# Virtual Network
resource "azurerm_virtual_network" "goad_blue" {
  name                = "vnet-goad-blue"
  address_space       = [var.vnet_address_space]
  location            = azurerm_resource_group.goad_blue.location
  resource_group_name = azurerm_resource_group.goad_blue.name
  
  tags = var.common_tags
}

# Subnets
resource "azurerm_subnet" "app" {
  name                 = "snet-app"
  resource_group_name  = azurerm_resource_group.goad_blue.name
  virtual_network_name = azurerm_virtual_network.goad_blue.name
  address_prefixes     = [var.app_subnet_address_prefix]
  
  service_endpoints = ["Microsoft.Storage", "Microsoft.Sql"]
}

# Network Security Group
resource "azurerm_network_security_group" "app" {
  name                = "nsg-app-tier"
  location            = azurerm_resource_group.goad_blue.location
  resource_group_name = azurerm_resource_group.goad_blue.name
  
  security_rule {
    name                       = "AllowSplunkWeb"
    priority                   = 100
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range          = "*"
    destination_port_range     = "8000"
    source_address_prefix      = var.vnet_address_space
    destination_address_prefix = "*"
  }
  
  tags = var.common_tags
}

# Virtual Machine
resource "azurerm_linux_virtual_machine" "splunk_search_head" {
  name                = "vm-splunk-sh-01"
  location            = azurerm_resource_group.goad_blue.location
  resource_group_name = azurerm_resource_group.goad_blue.name
  size                = var.splunk_vm_size
  admin_username      = var.admin_username
  
  disable_password_authentication = true
  
  network_interface_ids = [
    azurerm_network_interface.splunk_search_head.id,
  ]
  
  admin_ssh_key {
    username   = var.admin_username
    public_key = var.ssh_public_key
  }
  
  os_disk {
    caching              = "ReadWrite"
    storage_account_type = "Premium_LRS"
  }
  
  source_image_reference {
    publisher = "Canonical"
    offer     = "0001-com-ubuntu-server-focal"
    sku       = "20_04-lts-gen2"
    version   = "latest"
  }
  
  tags = merge(var.common_tags, {
    Component = "Splunk"
    Role      = "SearchHead"
  })
}
```

---

!!! tip "Azure Best Practices"
    - Use Azure AD for identity management
    - Implement Azure Policy for governance
    - Use Azure Key Vault for secrets management
    - Enable Azure Security Center for security monitoring
    - Use managed identities instead of service principals

!!! warning "Cost Management"
    Monitor Azure costs with Azure Cost Management. Use Azure Advisor recommendations for cost optimization. Consider using Azure Reserved Instances for predictable workloads.

!!! info "Security Considerations"
    - Enable Azure Defender for all resource types
    - Use Azure Sentinel for SIEM capabilities
    - Implement Azure Private Link for secure connectivity
    - Use Azure Firewall for network security
