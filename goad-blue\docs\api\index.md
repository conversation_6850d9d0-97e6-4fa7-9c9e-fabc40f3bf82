# API Reference

GOAD-<PERSON> provides comprehensive REST APIs for automation, integration, and custom development. This section covers all available APIs and their usage.

## 🔌 API Overview

### **API Architecture**

```mermaid
graph TB
    subgraph "🌐 API Gateway"
        GATEWAY[🚪 API Gateway<br/>Authentication & Routing]
        AUTH[🔐 Authentication Service<br/>JWT & API Keys]
        RATE[⏱️ Rate Limiting<br/>Request Throttling]
    end
    
    subgraph "🎯 Core APIs"
        MGMT[🎛️ Management API<br/>System Control]
        CONFIG[⚙️ Configuration API<br/>Settings Management]
        STATUS[📊 Status API<br/>Health & Metrics]
        DEPLOY[🚀 Deployment API<br/>Infrastructure Control]
    end
    
    subgraph "🔧 Component APIs"
        SIEM[📊 SIEM API<br/>Search & Alerts]
        MONITOR[🔍 Monitoring API<br/>Network Data]
        ENDPOINT[💻 Endpoint API<br/>Agent Management]
        INTEL[🧠 Intelligence API<br/>IOC Management]
    end
    
    subgraph "🎓 Training APIs"
        SCENARIO[🎮 Scenario API<br/>Training Management]
        SIMULATE[⚔️ Simulation API<br/>Attack Generation]
        ASSESS[📋 Assessment API<br/>Progress Tracking]
    end
    
    GATEWAY --> AUTH
    GATEWAY --> RATE
    AUTH --> MGMT
    AUTH --> CONFIG
    AUTH --> STATUS
    AUTH --> DEPLOY
    
    MGMT --> SIEM
    MGMT --> MONITOR
    MGMT --> ENDPOINT
    MGMT --> INTEL
    
    CONFIG --> SCENARIO
    CONFIG --> SIMULATE
    CONFIG --> ASSESS
    
    classDef gateway fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef core fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef component fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef training fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class GATEWAY,AUTH,RATE gateway
    class MGMT,CONFIG,STATUS,DEPLOY core
    class SIEM,MONITOR,ENDPOINT,INTEL component
    class SCENARIO,SIMULATE,ASSESS training
```

### **API Base URL**
```
https://goad-blue-api.local/api/v1
```

### **Authentication**

All API requests require authentication using either:

#### **API Key Authentication**
```bash
curl -H "X-API-Key: your-api-key" \
     https://goad-blue-api.local/api/v1/status
```

#### **JWT Token Authentication**
```bash
# Get token
curl -X POST https://goad-blue-api.local/api/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username": "admin", "password": "password"}'

# Use token
curl -H "Authorization: Bearer your-jwt-token" \
     https://goad-blue-api.local/api/v1/status
```

## 🎛️ Management API

### **System Status**

#### **GET /api/v1/status**
Get overall system status and health information.

```bash
curl -H "X-API-Key: your-api-key" \
     https://goad-blue-api.local/api/v1/status
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "components": {
    "siem": {
      "status": "running",
      "type": "splunk",
      "version": "9.1.2",
      "health": "green"
    },
    "monitoring": {
      "status": "running",
      "sensors": 2,
      "health": "green"
    },
    "endpoint": {
      "status": "running",
      "agents": 6,
      "health": "yellow"
    }
  },
  "metrics": {
    "cpu_usage": 45.2,
    "memory_usage": 67.8,
    "disk_usage": 34.1
  }
}
```

#### **GET /api/v1/health**
Detailed health check with component diagnostics.

```bash
curl -H "X-API-Key: your-api-key" \
     https://goad-blue-api.local/api/v1/health
```

### **Component Management**

#### **GET /api/v1/components**
List all components and their status.

#### **POST /api/v1/components/{component}/restart**
Restart a specific component.

```bash
curl -X POST \
     -H "X-API-Key: your-api-key" \
     https://goad-blue-api.local/api/v1/components/splunk/restart
```

#### **GET /api/v1/components/{component}/logs**
Retrieve component logs.

```bash
curl -H "X-API-Key: your-api-key" \
     "https://goad-blue-api.local/api/v1/components/splunk/logs?lines=100&level=error"
```

## ⚙️ Configuration API

### **Configuration Management**

#### **GET /api/v1/config**
Retrieve current configuration.

```bash
curl -H "X-API-Key: your-api-key" \
     https://goad-blue-api.local/api/v1/config
```

#### **PUT /api/v1/config**
Update configuration.

```bash
curl -X PUT \
     -H "X-API-Key: your-api-key" \
     -H "Content-Type: application/json" \
     -d @config.json \
     https://goad-blue-api.local/api/v1/config
```

#### **POST /api/v1/config/validate**
Validate configuration before applying.

```bash
curl -X POST \
     -H "X-API-Key: your-api-key" \
     -H "Content-Type: application/json" \
     -d @new-config.json \
     https://goad-blue-api.local/api/v1/config/validate
```

### **Network Configuration**

#### **GET /api/v1/config/network**
Get network configuration.

#### **PUT /api/v1/config/network**
Update network settings.

```json
{
  "base_cidr": "192.168.100.0/24",
  "subnets": {
    "management": "192.168.100.0/26",
    "monitoring": "192.168.100.64/26"
  },
  "firewall": {
    "enabled": true,
    "rules": [
      {
        "name": "ssh_access",
        "action": "allow",
        "protocol": "tcp",
        "port": 22
      }
    ]
  }
}
```

## 📊 SIEM API

### **Search and Query**

#### **POST /api/v1/siem/search**
Execute SIEM searches.

```bash
curl -X POST \
     -H "X-API-Key: your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
       "query": "index=goad_blue_windows EventCode=4625",
       "earliest_time": "-1h",
       "latest_time": "now"
     }' \
     https://goad-blue-api.local/api/v1/siem/search
```

**Response:**
```json
{
  "search_id": "search_12345",
  "status": "completed",
  "result_count": 42,
  "results": [
    {
      "_time": "2024-01-15T10:25:00Z",
      "EventCode": 4625,
      "Account_Name": "test_user",
      "Computer_Name": "GOAD-DC01"
    }
  ]
}
```

#### **GET /api/v1/siem/search/{search_id}**
Get search results by ID.

#### **GET /api/v1/siem/alerts**
Retrieve recent alerts.

```bash
curl -H "X-API-Key: your-api-key" \
     "https://goad-blue-api.local/api/v1/siem/alerts?severity=high&limit=50"
```

### **Index Management**

#### **GET /api/v1/siem/indexes**
List all indexes and their status.

#### **POST /api/v1/siem/indexes/{index}/optimize**
Optimize a specific index.

## 🔍 Monitoring API

### **Network Monitoring**

#### **GET /api/v1/monitoring/sensors**
List all monitoring sensors.

```bash
curl -H "X-API-Key: your-api-key" \
     https://goad-blue-api.local/api/v1/monitoring/sensors
```

#### **GET /api/v1/monitoring/alerts**
Get network monitoring alerts.

#### **POST /api/v1/monitoring/pcap/search**
Search PCAP data.

```bash
curl -X POST \
     -H "X-API-Key: your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
       "filter": "host ***************",
       "start_time": "2024-01-15T09:00:00Z",
       "end_time": "2024-01-15T10:00:00Z"
     }' \
     https://goad-blue-api.local/api/v1/monitoring/pcap/search
```

### **Traffic Analysis**

#### **GET /api/v1/monitoring/traffic/stats**
Get traffic statistics.

#### **GET /api/v1/monitoring/connections**
List active network connections.

## 💻 Endpoint API

### **Agent Management**

#### **GET /api/v1/endpoint/agents**
List all endpoint agents.

```bash
curl -H "X-API-Key: your-api-key" \
     https://goad-blue-api.local/api/v1/endpoint/agents
```

**Response:**
```json
{
  "agents": [
    {
      "agent_id": "agent_001",
      "hostname": "GOAD-DC01",
      "ip_address": "***************",
      "os": "Windows Server 2019",
      "status": "online",
      "last_seen": "2024-01-15T10:29:00Z",
      "version": "0.7.0"
    }
  ],
  "total": 6,
  "online": 5,
  "offline": 1
}
```

#### **POST /api/v1/endpoint/agents/{agent_id}/collect**
Trigger artifact collection.

```bash
curl -X POST \
     -H "X-API-Key: your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
       "artifacts": ["Windows.System.Pslist", "Windows.Network.Netstat"],
       "timeout": 300
     }' \
     https://goad-blue-api.local/api/v1/endpoint/agents/agent_001/collect
```

### **Hunt Management**

#### **POST /api/v1/endpoint/hunts**
Create a new hunt.

```bash
curl -X POST \
     -H "X-API-Key: your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Suspicious PowerShell Hunt",
       "description": "Hunt for encoded PowerShell commands",
       "artifacts": ["Windows.Events.ProcessCreation"],
       "condition": "ProcessCommandLine =~ \"powershell.*-enc\"",
       "targets": ["all"]
     }' \
     https://goad-blue-api.local/api/v1/endpoint/hunts
```

#### **GET /api/v1/endpoint/hunts/{hunt_id}/results**
Get hunt results.

## 🧠 Threat Intelligence API

### **IOC Management**

#### **GET /api/v1/intel/iocs**
List threat intelligence indicators.

```bash
curl -H "X-API-Key: your-api-key" \
     "https://goad-blue-api.local/api/v1/intel/iocs?type=ip&limit=100"
```

#### **POST /api/v1/intel/iocs**
Add new IOCs.

```bash
curl -X POST \
     -H "X-API-Key: your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
       "indicators": [
         {
           "type": "ip",
           "value": "*************",
           "confidence": 85,
           "tags": ["malware", "c2"],
           "source": "internal_analysis"
         }
       ]
     }' \
     https://goad-blue-api.local/api/v1/intel/iocs
```

### **Threat Feeds**

#### **GET /api/v1/intel/feeds**
List configured threat feeds.

#### **POST /api/v1/intel/feeds/{feed_id}/update**
Trigger feed update.

## 🎓 Training API

### **Scenario Management**

#### **GET /api/v1/training/scenarios**
List available training scenarios.

```bash
curl -H "X-API-Key: your-api-key" \
     https://goad-blue-api.local/api/v1/training/scenarios
```

#### **POST /api/v1/training/scenarios/{scenario_id}/start**
Start a training scenario.

```bash
curl -X POST \
     -H "X-API-Key: your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
       "participants": ["user1", "user2"],
       "difficulty": "intermediate",
       "duration": 3600
     }' \
     https://goad-blue-api.local/api/v1/training/scenarios/kerberoasting/start
```

### **Attack Simulation**

#### **POST /api/v1/training/simulate**
Trigger attack simulation.

```bash
curl -X POST \
     -H "X-API-Key: your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
       "technique": "kerberoasting",
       "target": "GOAD-DC01",
       "duration": 300,
       "stealth_level": "medium"
     }' \
     https://goad-blue-api.local/api/v1/training/simulate
```

## 📊 Metrics and Analytics API

### **Performance Metrics**

#### **GET /api/v1/metrics/performance**
Get system performance metrics.

#### **GET /api/v1/metrics/usage**
Get resource usage statistics.

### **Training Analytics**

#### **GET /api/v1/analytics/training**
Get training completion statistics.

#### **GET /api/v1/analytics/detection**
Get detection accuracy metrics.

## 🔧 API Client Libraries

### **Python Client**

```python
from goad_blue_client import GoadBlueAPI

# Initialize client
client = GoadBlueAPI(
    base_url="https://goad-blue-api.local/api/v1",
    api_key="your-api-key"
)

# Get system status
status = client.get_status()
print(f"System status: {status['status']}")

# Execute SIEM search
results = client.siem.search(
    query="index=goad_blue_windows EventCode=4625",
    earliest_time="-1h"
)

# Start training scenario
scenario = client.training.start_scenario(
    scenario_id="kerberoasting",
    participants=["analyst1"]
)
```

### **PowerShell Module**

```powershell
# Install module
Install-Module -Name GoadBlue

# Connect to API
Connect-GoadBlue -ApiUrl "https://goad-blue-api.local/api/v1" -ApiKey "your-api-key"

# Get component status
Get-GoadBlueStatus

# Execute search
Search-GoadBlueSiem -Query "index=goad_blue_windows EventCode=4625" -EarliestTime "-1h"

# Start scenario
Start-GoadBlueScenario -ScenarioId "kerberoasting" -Participants @("analyst1")
```

---

!!! info "API Documentation"
    For detailed API documentation with interactive examples, visit:
    
    - **Swagger UI**: `https://goad-blue-api.local/docs`
    - **ReDoc**: `https://goad-blue-api.local/redoc`

!!! tip "Rate Limiting"
    API requests are rate-limited to 1000 requests per hour per API key. Contact administrators for higher limits if needed.

!!! warning "API Security"
    Always use HTTPS for API requests and keep API keys secure. Rotate keys regularly and use least-privilege access principles.
