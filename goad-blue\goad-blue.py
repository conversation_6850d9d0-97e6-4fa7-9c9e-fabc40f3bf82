#!/usr/bin/env python3
"""
GOAD-Blue: Blue Team Enhancement for GOAD
Main CLI interface for deploying and managing blue team components
"""

import cmd
import argparse
import sys
import os
import time
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

# Try to import GOAD components if available
try:
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from goad.config import Config
    from goad.log import Log
    from goad.exceptions import JumpBoxInitFailed
    from goad.menu import print_logo
    from goad.infos import *
    GOAD_AVAILABLE = True
except ImportError:
    # Fallback implementations if GOAD is not available
    GOAD_AVAILABLE = False

    class Log:
        @staticmethod
        def info(msg): print(f"[INFO] {msg}")
        @staticmethod
        def success(msg): print(f"[SUCCESS] {msg}")
        @staticmethod
        def warning(msg): print(f"[WARNING] {msg}")
        @staticmethod
        def error(msg): print(f"[ERROR] {msg}")

    def print_logo():
        print("🛡️  GOAD-Blue: Full-Spectrum Cybersecurity Training Platform 🛡️")

from goad_blue.blue_team_manager import BlueTeamManager
from goad_blue.config import BlueTeamConfig


class GOADBlue(cmd.Cmd):
    """Main GOAD-Blue CLI interface"""

    def __init__(self, args):
        super().__init__()
        self.args = args
        
        # Initialize GOAD-Blue configuration
        self.blue_config = BlueTeamConfig()
        self.blue_config.merge_config(args)
        
        # Initialize Blue Team Manager
        self.blue_manager = BlueTeamManager().init(self.blue_config, args)
        
        # Check for existing GOAD installation
        self.goad_integration = self.check_goad_integration()
        
        if args.task == '' or args.task is None:
            Log.info('Starting GOAD-Blue interactive mode')
            self.blue_manager.load_default_configuration()

        self.welcome()
        self.refresh_prompt()

    def check_goad_integration(self):
        """Check if GOAD is available for integration"""
        goad_path = Path(__file__).parent.parent / "goad.py"
        if goad_path.exists():
            Log.success("GOAD installation detected - integration enabled")
            return True
        else:
            Log.warning("GOAD installation not found - running in standalone mode")
            return False

    def welcome(self):
        print_logo()
        print("🛡️  GOAD-Blue: Full-Spectrum Cybersecurity Training Platform 🛡️")
        print("=" * 70)
        Log.info('Blue team components:')
        self.blue_manager.show_components()
        if self.goad_integration:
            Log.info('GOAD integration: ENABLED')
        else:
            Log.warning('GOAD integration: DISABLED')

    def refresh_prompt(self):
        if self.blue_manager.get_current_instance_id() == '':
            self.prompt = f"\n{self.blue_manager.inline_settings()} > "
        else:
            self.prompt = f"\n{self.blue_manager.inline_settings()} ({self.blue_manager.get_current_instance_id()}) > "

    def default(self, line):
        print()

    def do_help(self, arg):
        """Show GOAD-Blue help menu"""
        self.print_blue_menu()

    def print_blue_menu(self):
        """Print GOAD-Blue specific menu"""
        print("\n🛡️  GOAD-Blue Commands:")
        print("=" * 50)
        print("Configuration:")
        print("  configure          - Interactive component selection")
        print("  set_siem <type>    - Set SIEM type (splunk/elastic)")
        print("  enable <component> - Enable blue team component")
        print("  disable <component>- Disable blue team component")
        print("  show_config        - Show current configuration")
        print("\nDeployment:")
        print("  install_blue       - Install all enabled components")
        print("  install_siem       - Install SIEM only")
        print("  install_monitoring - Install monitoring tools")
        print("  install_analysis   - Install analysis tools")
        print("\nManagement:")
        print("  status_blue        - Show status of blue components")
        print("  start_blue         - Start all blue components")
        print("  stop_blue          - Stop all blue components")
        print("  integrate_goad     - Integrate with existing GOAD")
        print("\nTesting:")
        print("  simulate_attack    - Run attack simulation")
        print("  test_detection     - Test detection capabilities")
        print("  generate_report    - Generate detection report")

    def do_exit(self, arg):
        """Exit GOAD-Blue"""
        print('🛡️  GOAD-Blue shutdown complete')
        return True

    # Configuration commands
    def do_configure(self, arg=''):
        """Interactive configuration of blue team components"""
        self.blue_manager.interactive_configure()
        self.refresh_prompt()

    def do_set_siem(self, arg):
        """Set SIEM type (splunk/elastic/none)"""
        if arg == '':
            Log.error('missing SIEM type argument')
            Log.info('set_siem <type> (allowed values: splunk, elastic, none)')
        else:
            try:
                self.blue_manager.set_siem(arg)
                self.refresh_prompt()
            except ValueError as err:
                Log.error(err.args[0])

    def do_enable(self, arg):
        """Enable a blue team component"""
        if arg == '':
            Log.error('missing component name')
            Log.info('enable <component>')
            self.blue_manager.list_available_components()
        else:
            self.blue_manager.enable_component(arg)

    def do_disable(self, arg):
        """Disable a blue team component"""
        if arg == '':
            Log.error('missing component name')
            Log.info('disable <component>')
        else:
            self.blue_manager.disable_component(arg)

    def do_show_config(self, arg=''):
        """Show current GOAD-Blue configuration"""
        self.blue_manager.show_configuration()

    # Deployment commands
    def do_install_blue(self, arg=''):
        """Install all enabled blue team components"""
        Log.info('Starting GOAD-Blue installation...')
        result = self.blue_manager.install_all_components()
        if result:
            Log.success('GOAD-Blue installation completed successfully')
        else:
            Log.error('GOAD-Blue installation failed')

    def do_install_siem(self, arg=''):
        """Install SIEM component only"""
        result = self.blue_manager.install_siem()
        if result:
            Log.success('SIEM installation completed')

    def do_install_monitoring(self, arg=''):
        """Install monitoring components"""
        result = self.blue_manager.install_monitoring_components()
        if result:
            Log.success('Monitoring components installation completed')

    def do_install_analysis(self, arg=''):
        """Install analysis components"""
        result = self.blue_manager.install_analysis_components()
        if result:
            Log.success('Analysis components installation completed')

    # Management commands
    def do_status_blue(self, arg=''):
        """Show status of all blue team components"""
        self.blue_manager.show_status()

    def do_start_blue(self, arg=''):
        """Start all blue team components"""
        self.blue_manager.start_all_components()

    def do_stop_blue(self, arg=''):
        """Stop all blue team components"""
        self.blue_manager.stop_all_components()

    def do_integrate_goad(self, arg=''):
        """Integrate with existing GOAD environment"""
        if self.goad_integration:
            result = self.blue_manager.integrate_with_goad()
            if result:
                Log.success('GOAD integration completed')
        else:
            Log.error('GOAD installation not found')

    # Testing commands
    def do_simulate_attack(self, arg):
        """Run attack simulation for testing detection"""
        if arg == '':
            Log.error('missing attack type')
            Log.info('simulate_attack <attack_type>')
            Log.info('Available attacks: kerberoasting, dcsync, golden_ticket, lateral_movement')
        else:
            self.blue_manager.simulate_attack(arg)

    def do_test_detection(self, arg=''):
        """Test detection capabilities"""
        self.blue_manager.test_detection_capabilities()

    def do_generate_report(self, arg=''):
        """Generate detection and analysis report"""
        report_path = self.blue_manager.generate_detection_report()
        if report_path:
            Log.success(f'Report generated: {report_path}')


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        prog='goad-blue.py',
        description='GOAD-Blue: Blue Team Enhancement for GOAD',
        epilog=show_help(),
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument("-t", "--task", help="Task to execute", required=False)
    parser.add_argument("-s", "--siem", help="SIEM type (splunk/elastic)", default='splunk', required=False)
    parser.add_argument("-c", "--components", help="Components to enable (comma-separated)", required=False)
    parser.add_argument("-p", "--provider", help="Provider to use", default='vmware', required=False)
    parser.add_argument("-i", "--interactive", help="Interactive configuration", action='store_true', required=False)
    parser.add_argument("--integrate-goad", help="Integrate with existing GOAD", action='store_true', required=False)
    
    return parser.parse_args()


def show_help():
    return '''
Examples:
 - Interactive setup: python3 goad-blue.py --interactive
 - Quick install with Splunk: python3 goad-blue.py -t install -s splunk -c security_onion,velociraptor,misp
 - Integrate with GOAD: python3 goad-blue.py --integrate-goad
'''


if __name__ == '__main__':
    args = parse_args()
    goad_blue = GOADBlue(args)

    if args.interactive or args.task is None:
        goad_blue.cmdloop()
    else:
        # Command line execution
        if args.task == 'install':
            goad_blue.do_install_blue()
        elif args.task == 'configure':
            goad_blue.do_configure()
        elif args.task == 'integrate':
            goad_blue.do_integrate_goad()
        elif args.task == 'status':
            goad_blue.do_status_blue()
