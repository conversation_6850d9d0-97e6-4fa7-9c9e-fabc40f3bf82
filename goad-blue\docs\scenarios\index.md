# Training Scenarios

GOAD-<PERSON> provides a comprehensive collection of hands-on training scenarios designed to develop real-world cybersecurity skills. These scenarios range from basic SOC operations to advanced threat hunting and incident response.

## 🎯 Scenario Framework

Our training scenarios are built on a progressive learning model that takes you from fundamental concepts to expert-level skills through practical, hands-on exercises.

```mermaid
graph TB
    subgraph "📚 Learning Levels"
        BASIC[📖 Basic Level<br/>Weeks 1-2<br/>Fundamental Skills]
        INTERMEDIATE[🔥 Intermediate Level<br/>Weeks 3-6<br/>Complex Scenarios]
        ADVANCED[⚡ Advanced Level<br/>Weeks 7-10<br/>Expert Techniques]
        EXPERT[🏆 Expert Level<br/>Weeks 11+<br/>Research & Innovation]
    end
    
    subgraph "🎭 Scenario Types"
        GUIDED[📋 Guided Exercises<br/>Step-by-Step<br/>Learning Objectives]
        SIMULATION[🎮 Attack Simulations<br/>Realistic Threats<br/>Response Challenges]
        COMPETITION[🏁 Competitions<br/>Red vs Blue<br/>Timed Challenges]
        RESEARCH[🔬 Research Projects<br/>Custom Development<br/>Advanced Topics]
    end
    
    subgraph "🛠️ Skill Areas"
        SOC[🖥️ SOC Operations<br/>Alert Triage<br/>Incident Response]
        HUNT[🔍 Threat Hunting<br/>Proactive Detection<br/>Behavioral Analysis]
        FORENSICS[🔬 Digital Forensics<br/>Evidence Collection<br/>Timeline Analysis]
        INTEL[🧠 Threat Intelligence<br/>IOC Development<br/>Attribution Analysis]
        MALWARE[🦠 Malware Analysis<br/>Reverse Engineering<br/>Dynamic Analysis]
    end
    
    BASIC --> INTERMEDIATE
    INTERMEDIATE --> ADVANCED
    ADVANCED --> EXPERT
    
    BASIC --> GUIDED
    INTERMEDIATE --> SIMULATION
    ADVANCED --> COMPETITION
    EXPERT --> RESEARCH
    
    GUIDED --> SOC
    SIMULATION --> HUNT
    COMPETITION --> FORENSICS
    RESEARCH --> INTEL
    RESEARCH --> MALWARE
    
    classDef levels fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef types fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef skills fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    
    class BASIC,INTERMEDIATE,ADVANCED,EXPERT levels
    class GUIDED,SIMULATION,COMPETITION,RESEARCH types
    class SOC,HUNT,FORENSICS,INTEL,MALWARE skills
```

## 📚 Scenario Categories

### **📖 Basic Scenarios (Weeks 1-2)**

Perfect for beginners and those new to blue team operations. These scenarios focus on fundamental skills and tool familiarization.

#### **[Authentication Attacks](basic/authentication-attacks.md)**
- **Duration**: 2-3 hours
- **Difficulty**: Beginner
- **Tools**: Splunk, Security Onion
- **Skills**: Log analysis, alert investigation, basic response

**Learning Objectives:**
- Identify failed authentication attempts
- Analyze authentication logs
- Detect credential stuffing attacks
- Create basic detection rules

#### **[Malware Detection](basic/malware-detection.md)**
- **Duration**: 3-4 hours
- **Difficulty**: Beginner
- **Tools**: FLARE-VM, Velociraptor, MISP
- **Skills**: Static analysis, IOC extraction, basic forensics

**Learning Objectives:**
- Identify malware infections
- Extract indicators of compromise
- Use threat intelligence platforms
- Document findings

#### **[Network Intrusions](basic/network-intrusions.md)**
- **Duration**: 2-3 hours
- **Difficulty**: Beginner
- **Tools**: Security Onion, Suricata, Zeek
- **Skills**: Traffic analysis, signature development

**Learning Objectives:**
- Analyze network traffic
- Identify suspicious connections
- Create network-based detection rules
- Understand attack patterns

### **🔥 Intermediate Scenarios (Weeks 3-6)**

Building on basic skills, these scenarios introduce complex attack patterns and multi-stage investigations.

#### **[Advanced Persistent Threats](intermediate/apt-campaigns.md)**
- **Duration**: 6-8 hours
- **Difficulty**: Intermediate
- **Tools**: Full GOAD-Blue stack
- **Skills**: Campaign tracking, attribution, advanced response

**Learning Objectives:**
- Track multi-stage attacks
- Correlate events across time
- Understand APT tactics and techniques
- Develop comprehensive response plans

#### **[Lateral Movement Detection](intermediate/lateral-movement.md)**
- **Duration**: 4-6 hours
- **Difficulty**: Intermediate
- **Tools**: Velociraptor, Splunk, Sysmon
- **Skills**: Endpoint analysis, privilege escalation detection

**Learning Objectives:**
- Detect lateral movement techniques
- Analyze Windows event logs
- Track privilege escalation
- Map attack progression

#### **[Insider Threats](intermediate/insider-threats.md)**
- **Duration**: 4-6 hours
- **Difficulty**: Intermediate
- **Tools**: Behavioral analytics, Splunk, Velociraptor
- **Skills**: User behavior analysis, privilege monitoring

**Learning Objectives:**
- Identify anomalous user behavior
- Monitor privileged account usage
- Detect data exfiltration
- Investigate insider activities

### **⚡ Advanced Scenarios (Weeks 7-10)**

Expert-level scenarios that challenge experienced analysts with sophisticated attacks and complex investigations.

#### **[Zero-Day Exploitation](advanced/zero-day-attacks.md)**
- **Duration**: 8-12 hours
- **Difficulty**: Advanced
- **Tools**: Behavioral analysis, machine learning, custom tools
- **Skills**: Anomaly detection, signature development, research

**Learning Objectives:**
- Detect unknown attack techniques
- Develop behavioral detection rules
- Use machine learning for anomaly detection
- Create custom analysis tools

#### **[Nation-State Campaigns](advanced/nation-state.md)**
- **Duration**: 12-16 hours
- **Difficulty**: Advanced
- **Tools**: Full intelligence stack, attribution tools
- **Skills**: Attribution analysis, strategic intelligence

**Learning Objectives:**
- Analyze sophisticated attack campaigns
- Perform threat attribution
- Understand geopolitical context
- Develop strategic intelligence

#### **[Supply Chain Attacks](advanced/supply-chain.md)**
- **Duration**: 8-12 hours
- **Difficulty**: Advanced
- **Tools**: Code analysis, integrity monitoring
- **Skills**: Software analysis, trust verification

**Learning Objectives:**
- Detect compromised software
- Analyze software supply chains
- Implement integrity monitoring
- Develop mitigation strategies

### **🏆 Expert Scenarios (Weeks 11+)**

Research-level scenarios for those pursuing cutting-edge cybersecurity knowledge and tool development.

#### **[Custom Tool Development](expert/tool-development.md)**
- **Duration**: 20+ hours
- **Difficulty**: Expert
- **Tools**: Programming languages, APIs, frameworks
- **Skills**: Software development, security research

**Learning Objectives:**
- Develop custom security tools
- Integrate with existing platforms
- Contribute to open-source projects
- Publish security research

#### **[Machine Learning for Security](expert/ml-security.md)**
- **Duration**: 20+ hours
- **Difficulty**: Expert
- **Tools**: Python, TensorFlow, scikit-learn
- **Skills**: Data science, machine learning, algorithm development

**Learning Objectives:**
- Apply ML to security problems
- Develop custom detection algorithms
- Evaluate model performance
- Deploy ML solutions

## 🎮 Scenario Formats

### **📋 Guided Exercises**

Step-by-step tutorials with clear learning objectives and detailed instructions.

**Structure:**
- **Introduction** - Scenario background and objectives
- **Prerequisites** - Required knowledge and tools
- **Step-by-Step Instructions** - Detailed walkthrough
- **Verification** - How to confirm successful completion
- **Additional Challenges** - Optional advanced tasks

### **🎭 Attack Simulations**

Realistic attack scenarios where students must detect and respond to ongoing threats.

**Structure:**
- **Scenario Brief** - Attack description and context
- **Initial Indicators** - Starting point for investigation
- **Investigation Tasks** - What students need to discover
- **Response Actions** - Required response activities
- **Debrief** - Analysis of performance and lessons learned

### **🏁 Competitions**

Timed challenges and red vs blue exercises for skill assessment and team building.

**Formats:**
- **Capture the Flag (CTF)** - Find hidden flags through investigation
- **King of the Hill** - Maintain control of systems under attack
- **Attack/Defend** - Teams alternate between attacking and defending
- **Incident Response** - Respond to simulated security incidents

### **🔬 Research Projects**

Open-ended projects for advanced students to explore cutting-edge topics.

**Examples:**
- **Threat Intelligence Research** - Analyze emerging threats
- **Tool Development** - Create new security tools
- **Detection Engineering** - Develop advanced detection rules
- **Academic Research** - Contribute to security research

## 🎯 Scenario Selection Guide

### **By Experience Level**

| Experience | Recommended Scenarios | Duration |
|------------|----------------------|----------|
| **Beginner** | Basic authentication, malware detection | 2-4 weeks |
| **Intermediate** | APT campaigns, lateral movement | 4-6 weeks |
| **Advanced** | Zero-day attacks, nation-state campaigns | 6-8 weeks |
| **Expert** | Custom development, research projects | 8+ weeks |

### **By Role**

| Role | Focus Areas | Key Scenarios |
|------|-------------|---------------|
| **SOC Analyst** | Alert triage, incident response | Authentication attacks, malware detection |
| **Threat Hunter** | Proactive detection, behavioral analysis | Lateral movement, insider threats |
| **Incident Responder** | Investigation, containment | APT campaigns, zero-day attacks |
| **Security Researcher** | Tool development, research | Custom development, ML security |

### **By Time Available**

| Time Available | Scenario Type | Examples |
|----------------|---------------|----------|
| **2-3 hours** | Basic guided exercises | Authentication attacks, network intrusions |
| **4-6 hours** | Intermediate simulations | Lateral movement, insider threats |
| **8-12 hours** | Advanced scenarios | Zero-day attacks, supply chain |
| **20+ hours** | Expert projects | Tool development, research |

## 🚀 Getting Started

### **Prerequisites Check**

Before starting scenarios, ensure you have:

- [ ] **Working GOAD-Blue Environment** - All components deployed and functional
- [ ] **GOAD Integration** - Connection to GOAD environment established
- [ ] **Basic Tool Knowledge** - Familiarity with Splunk, Security Onion, etc.
- [ ] **Scenario Data** - Required datasets and attack simulations loaded

### **Environment Setup**

```bash
# Verify environment readiness
python3 goad-blue.py verify-scenario-environment

# Load scenario data
python3 goad-blue.py load-scenario-data --scenario basic-authentication

# Start scenario
python3 goad-blue.py start-scenario --name "authentication-attacks" --level basic

# Check scenario status
python3 goad-blue.py scenario-status
```

### **Scenario Navigation**

```bash
# List available scenarios
python3 goad-blue.py list-scenarios --level basic

# Get scenario details
python3 goad-blue.py scenario-info --name "authentication-attacks"

# Start specific scenario
python3 goad-blue.py start-scenario --name "authentication-attacks"

# Submit scenario completion
python3 goad-blue.py submit-scenario --name "authentication-attacks" --evidence evidence.zip
```

## 📊 Progress Tracking

### **Individual Progress**

Track your learning journey with built-in progress monitoring:

- **Scenario Completion** - Track completed scenarios and achievements
- **Skill Development** - Monitor skill progression across different areas
- **Time Investment** - Track time spent on different scenario types
- **Performance Metrics** - Measure detection accuracy and response time

### **Certification Paths**

Complete scenario tracks to earn GOAD-Blue certifications:

- **GOAD-Blue Certified SOC Analyst** - Complete basic and intermediate SOC scenarios
- **GOAD-Blue Certified Threat Hunter** - Complete hunting and detection scenarios
- **GOAD-Blue Certified Incident Responder** - Complete response and forensics scenarios
- **GOAD-Blue Certified Security Researcher** - Complete expert-level research projects

## 🆘 Scenario Support

### **Getting Help**

- **[Scenario FAQ](faq.md)** - Common questions and answers
- **[Troubleshooting](../troubleshooting/scenarios.md)** - Technical issues with scenarios
- **Community Forum** - Get help from other students and instructors
- **Office Hours** - Regular support sessions with instructors

### **Feedback and Improvement**

- **Scenario Ratings** - Rate scenarios and provide feedback
- **Improvement Suggestions** - Suggest new scenarios or enhancements
- **Bug Reports** - Report technical issues with scenarios
- **Content Contributions** - Submit your own scenarios

---

!!! tip "Learning Strategy"
    Start with basic scenarios to build foundational skills, then progress to more complex challenges. Don't skip levels - each builds on the previous one.

!!! info "Instructor Resources"
    Educators can access instructor guides, answer keys, and assessment rubrics for each scenario in the instructor portal.

!!! warning "Scenario Environment"
    Scenarios are designed for the GOAD-Blue training environment. Some may not work correctly in production environments.
