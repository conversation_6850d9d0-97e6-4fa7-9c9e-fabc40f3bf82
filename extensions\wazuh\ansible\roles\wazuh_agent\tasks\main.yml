- name: Check if Wazuh Agent service is installed
  win_service:
    name: WazuhSvc
  register: wazuh_agent_service

- name: Create wazuh_install_location folder if not exist
  ansible.windows.win_file:
    path: "{{wazuh_install_location}}"
    state: directory

- name: Download Wazuh Agent MSI package
  win_get_url:
    url: "{{ wazuh_agent_install_package }}"
    dest: "{{wazuh_install_location}}/wazuh-agent"
  register: wazuh_agent_download
  until: wazuh_agent_download is succeeded
  when: not wazuh_agent_service.exists

- name: Install Wazuh Agent
  win_command: msiexec.exe /i "{{wazuh_install_location}}\wazuh-agent" /q WAZUH_MANAGER={{ wazuh_manager_host }} WAZUH_REGISTRATION_SERVER={{ wazuh_manager_host }}
  when: wazuh_agent_download is succeeded and not wazuh_agent_service.exists

- name: Start Wazuh Agent service
  win_service:
    name: WazuhSvc
    state: started
    start_mode: auto
  when: wazuh_agent_download is succeeded and not wazuh_agent_service.exists
