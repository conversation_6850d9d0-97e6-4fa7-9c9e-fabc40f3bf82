---
# Load datas
- import_playbook: data.yml
  vars:
    data_path: "../ad/{{domain_name}}/data/"
  tags: 'data'

# set AD datas ==================================================================================================

- name: GMSA inside AD
  hosts: dc
  roles:
  - { role: 'gmsa', tags: 'gmsa'}
  vars:
    ad_gmsa: "{{lab.domains[lab.hosts[dict_key].domain].gmsa | default({})}}"
    domain: "{{lab.hosts[dict_key].domain}}"
    domain_username: "{{domain}}\\{{admin_user}}"
    domain_password: "{{lab.domains[domain].domain_password}}"

- name: GMSA hosts
  hosts: server, workstation
  roles:
  - { role: 'gmsa_hosts', tags: 'gmsa'}
  vars:
    ad_gmsa: "{{lab.domains[lab.hosts[dict_key].domain].gmsa | default({})}}"
    domain: "{{lab.hosts[dict_key].domain}}"
    domain_username: "{{domain}}\\{{admin_user}}"
    domain_password: "{{lab.domains[domain].domain_password}}"
    hostname: "{{lab.hosts[dict_key].hostname}}"
