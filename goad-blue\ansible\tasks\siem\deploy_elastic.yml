---
# Deploy Elasticsearch cluster for GOAD-Blue

- name: Create elasticsearch user
  user:
    name: elasticsearch
    system: yes
    shell: /bin/false
    home: /var/lib/elasticsearch
    create_home: yes

- name: Install Java (OpenJDK 11)
  package:
    name: "{{ 'openjdk-11-jdk' if ansible_os_family == 'Debian' else 'java-11-openjdk' }}"
    state: present

- name: Set JAVA_HOME environment variable
  lineinfile:
    path: /etc/environment
    line: 'JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64'
    create: yes
  when: ansible_os_family == "Debian"

- name: Set JAVA_HOME environment variable (RedHat)
  lineinfile:
    path: /etc/environment
    line: 'JAVA_HOME=/usr/lib/jvm/java-11-openjdk'
    create: yes
  when: ansible_os_family == "RedHat"

- name: Download Elasticsearch
  get_url:
    url: "{{ elasticsearch.download_url }}"
    dest: "/tmp/elasticsearch-{{ elasticsearch.version }}.tar.gz"
    mode: '0644'

- name: Extract Elasticsearch
  unarchive:
    src: "/tmp/elasticsearch-{{ elasticsearch.version }}.tar.gz"
    dest: /opt
    remote_src: yes
    owner: elasticsearch
    group: elasticsearch
    creates: "/opt/elasticsearch-{{ elasticsearch.version }}"

- name: Create Elasticsearch symlink
  file:
    src: "/opt/elasticsearch-{{ elasticsearch.version }}"
    dest: "{{ elasticsearch.install_path }}"
    state: link
    owner: elasticsearch
    group: elasticsearch

- name: Create Elasticsearch directories
  file:
    path: "{{ item }}"
    state: directory
    owner: elasticsearch
    group: elasticsearch
    mode: '0755'
  loop:
    - "{{ elasticsearch.data_path }}"
    - "{{ elasticsearch.logs_path }}"
    - /etc/elasticsearch
    - /var/run/elasticsearch

- name: Configure Elasticsearch
  template:
    src: elasticsearch.yml.j2
    dest: /etc/elasticsearch/elasticsearch.yml
    owner: elasticsearch
    group: elasticsearch
    mode: '0644'
    backup: yes
  notify: restart elasticsearch

- name: Configure Elasticsearch JVM options
  template:
    src: jvm.options.j2
    dest: /etc/elasticsearch/jvm.options
    owner: elasticsearch
    group: elasticsearch
    mode: '0644'
  notify: restart elasticsearch

- name: Set Elasticsearch heap size
  lineinfile:
    path: /etc/elasticsearch/jvm.options
    regexp: "{{ item.regexp }}"
    line: "{{ item.line }}"
  loop:
    - regexp: '^-Xms'
      line: "-Xms{{ elasticsearch.heap_size | default('2g') }}"
    - regexp: '^-Xmx'
      line: "-Xmx{{ elasticsearch.heap_size | default('2g') }}"
  notify: restart elasticsearch

- name: Configure Elasticsearch log4j2 properties
  template:
    src: log4j2.properties.j2
    dest: /etc/elasticsearch/log4j2.properties
    owner: elasticsearch
    group: elasticsearch
    mode: '0644'

- name: Create Elasticsearch systemd service
  template:
    src: elasticsearch.service.j2
    dest: /etc/systemd/system/elasticsearch.service
    mode: '0644'
  notify:
    - reload systemd
    - restart elasticsearch

- name: Set system limits for Elasticsearch
  pam_limits:
    domain: elasticsearch
    limit_type: "{{ item.type }}"
    limit_item: "{{ item.item }}"
    value: "{{ item.value }}"
  loop:
    - { type: 'soft', item: 'nofile', value: '65536' }
    - { type: 'hard', item: 'nofile', value: '65536' }
    - { type: 'soft', item: 'memlock', value: 'unlimited' }
    - { type: 'hard', item: 'memlock', value: 'unlimited' }

- name: Configure system settings for Elasticsearch
  sysctl:
    name: "{{ item.name }}"
    value: "{{ item.value }}"
    state: present
    reload: yes
  loop:
    - name: vm.max_map_count
      value: "262144"
    - name: fs.file-max
      value: "65536"

- name: Start and enable Elasticsearch
  systemd:
    name: elasticsearch
    state: started
    enabled: yes
    daemon_reload: yes

- name: Wait for Elasticsearch to be ready
  uri:
    url: "http://{{ elasticsearch.network.host }}:{{ elasticsearch.network.port }}/_cluster/health"
    method: GET
    status_code: 200
  register: elasticsearch_health
  until: elasticsearch_health.status == 200
  retries: 30
  delay: 10

- name: Create Elasticsearch index templates for GOAD-Blue
  uri:
    url: "http://{{ elasticsearch.network.host }}:{{ elasticsearch.network.port }}/_index_template/goad-blue-template"
    method: PUT
    body_format: json
    body:
      index_patterns:
        - "goad-blue-*"
      template:
        settings:
          number_of_shards: "{{ elasticsearch.indices['goad-blue-windows'].number_of_shards }}"
          number_of_replicas: "{{ elasticsearch.indices['goad-blue-windows'].number_of_replicas }}"
          refresh_interval: "30s"
        mappings:
          properties:
            "@timestamp":
              type: date
            host:
              type: keyword
            source:
              type: keyword
            message:
              type: text
              analyzer: standard
            level:
              type: keyword
            event_id:
              type: keyword
            user:
              type: keyword
            process:
              type: keyword
            command_line:
              type: text
    status_code: 200

- name: Create GOAD-Blue specific indices
  uri:
    url: "http://{{ elasticsearch.network.host }}:{{ elasticsearch.network.port }}/{{ item }}"
    method: PUT
    body_format: json
    body:
      settings:
        number_of_shards: 1
        number_of_replicas: 0
    status_code: [200, 400]  # 400 if index already exists
  loop:
    - goad-blue-windows
    - goad-blue-linux
    - goad-blue-network
    - goad-blue-security

- name: Configure Elasticsearch security (if enabled)
  block:
    - name: Generate Elasticsearch certificates
      shell: |
        cd {{ elasticsearch.install_path }}
        bin/elasticsearch-certutil ca --out config/elastic-stack-ca.p12 --pass ""
        bin/elasticsearch-certutil cert --ca config/elastic-stack-ca.p12 --ca-pass "" --out config/elastic-certificates.p12 --pass ""
      become_user: elasticsearch
      args:
        creates: "{{ elasticsearch.install_path }}/config/elastic-certificates.p12"

    - name: Set certificate permissions
      file:
        path: "{{ elasticsearch.install_path }}/config/{{ item }}"
        owner: elasticsearch
        group: elasticsearch
        mode: '0660'
      loop:
        - elastic-stack-ca.p12
        - elastic-certificates.p12

    - name: Setup Elasticsearch passwords
      shell: |
        cd {{ elasticsearch.install_path }}
        echo "y" | bin/elasticsearch-setup-passwords auto
      become_user: elasticsearch
      register: elasticsearch_passwords
      when: elasticsearch.security.enabled | default(false)

    - name: Save Elasticsearch passwords
      copy:
        content: "{{ elasticsearch_passwords.stdout }}"
        dest: /etc/elasticsearch/passwords.txt
        owner: elasticsearch
        group: elasticsearch
        mode: '0600'
      when: elasticsearch.security.enabled | default(false)

  when: elasticsearch.security.enabled | default(false)

- name: Verify Elasticsearch cluster health
  uri:
    url: "http://{{ elasticsearch.network.host }}:{{ elasticsearch.network.port }}/_cluster/health"
    method: GET
  register: cluster_health

- name: Display Elasticsearch cluster information
  debug:
    msg: |
      Elasticsearch deployment completed!
      Cluster Name: {{ cluster_health.json.cluster_name }}
      Status: {{ cluster_health.json.status }}
      Number of Nodes: {{ cluster_health.json.number_of_nodes }}
      Active Shards: {{ cluster_health.json.active_shards }}
      Elasticsearch URL: http://{{ elasticsearch.network.host }}:{{ elasticsearch.network.port }}
