---
# SIEM Configuration Variables

# Splunk Configuration
splunk:
  version: "9.1.2"
  build: "b6b9c8185839"
  download_url: "https://download.splunk.com/products/splunk/releases/9.1.2/linux/splunk-9.1.2-b6b9c8185839-linux-2.6-x86_64.tgz"
  
  # Installation paths
  install_path: "/opt/splunk"
  home_path: "/opt/splunk"
  
  # Admin configuration
  admin_user: "admin"
  admin_password: "{{ vault_splunk_admin_password | default('ChangeMeNow!') }}"
  
  # License configuration
  license_file: "/opt/goad-blue/licenses/splunk.lic"
  license_master: false
  
  # Server configuration
  server:
    hostname: "{{ ansible_hostname }}"
    servername: "{{ ansible_fqdn }}"
    
    # Web interface
    web_port: 8000
    web_ssl: true
    web_cert: "/opt/splunk/etc/auth/server.pem"
    
    # Management port
    mgmt_port: 8089
    mgmt_ssl: true
    
    # Splunkd configuration
    splunkd_port: 8089
    kvstore_port: 8191
    
    # Indexing configuration
    max_data_size: "auto_high_volume"
    max_hot_buckets: 10
    max_warm_buckets: 300
    
    # Performance tuning
    max_concurrent_searches: 20
    max_searches_per_cpu: 1
    base_max_searches: 6
  
  # Indexer configuration
  indexer:
    replication_factor: 1
    search_factor: 1
    cluster_label: "goad_blue_cluster"
    
    # Index configuration
    indexes:
      goad_blue_main:
        max_data_size: "1024MB"
        max_hot_buckets: 3
        max_warm_buckets: 100
        home_path: "$SPLUNK_DB/goad_blue_main/db"
        cold_path: "$SPLUNK_DB/goad_blue_main/colddb"
        thawed_path: "$SPLUNK_DB/goad_blue_main/thaweddb"
      
      goad_blue_windows:
        max_data_size: "2048MB"
        max_hot_buckets: 5
        max_warm_buckets: 150
        home_path: "$SPLUNK_DB/goad_blue_windows/db"
        cold_path: "$SPLUNK_DB/goad_blue_windows/colddb"
        thawed_path: "$SPLUNK_DB/goad_blue_windows/thaweddb"
      
      goad_blue_linux:
        max_data_size: "1024MB"
        max_hot_buckets: 3
        max_warm_buckets: 100
        home_path: "$SPLUNK_DB/goad_blue_linux/db"
        cold_path: "$SPLUNK_DB/goad_blue_linux/colddb"
        thawed_path: "$SPLUNK_DB/goad_blue_linux/thaweddb"
      
      goad_blue_network:
        max_data_size: "2048MB"
        max_hot_buckets: 5
        max_warm_buckets: 200
        home_path: "$SPLUNK_DB/goad_blue_network/db"
        cold_path: "$SPLUNK_DB/goad_blue_network/colddb"
        thawed_path: "$SPLUNK_DB/goad_blue_network/thaweddb"
      
      goad_blue_security:
        max_data_size: "1024MB"
        max_hot_buckets: 3
        max_warm_buckets: 100
        home_path: "$SPLUNK_DB/goad_blue_security/db"
        cold_path: "$SPLUNK_DB/goad_blue_security/colddb"
        thawed_path: "$SPLUNK_DB/goad_blue_security/thaweddb"
  
  # Universal Forwarder configuration
  universal_forwarder:
    version: "9.1.2"
    download_url: "https://download.splunk.com/products/universalforwarder/releases/9.1.2/linux/splunkforwarder-9.1.2-b6b9c8185839-linux-2.6-x86_64.tgz"
    install_path: "/opt/splunkforwarder"
    
    # Deployment server
    deployment_server: "**************:8089"
    
    # Output configuration
    outputs:
      - server: "**************:9997"
        compressed: true
        use_ack: true
  
  # Apps and Add-ons
  apps:
    goad_blue_app:
      name: "GOAD-Blue Security App"
      version: "1.0.0"
      description: "GOAD-Blue security monitoring and analysis"
      
    splunk_security_essentials:
      enabled: true
      version: "3.4.0"
      
    enterprise_security:
      enabled: false
      version: "7.3.0"

# Technical Add-ons
splunk_addons:
  windows:
    enabled: true
    filename: "splunk-add-on-for-microsoft-windows_8.8.0.tgz"
    download_url: "https://splunkbase.splunk.com/app/742/"
    
  unix:
    enabled: true
    filename: "splunk-add-on-for-unix-and-linux_9.0.0.tgz"
    download_url: "https://splunkbase.splunk.com/app/833/"
    
  sysmon:
    enabled: true
    filename: "ta-microsoft-sysmon_10.6.2.tgz"
    download_url: "https://splunkbase.splunk.com/app/1914/"
    
  powershell:
    enabled: true
    filename: "ta-powershell_1.4.3.tgz"
    download_url: "https://splunkbase.splunk.com/app/2889/"
    
  dns:
    enabled: true
    filename: "splunk-add-on-for-dns_1.1.0.tgz"
    download_url: "https://splunkbase.splunk.com/app/1923/"
    
  suricata:
    enabled: true
    filename: "ta-suricata_2.3.3.tgz"
    download_url: "https://splunkbase.splunk.com/app/2760/"
    
  zeek:
    enabled: true
    filename: "ta-zeek_1.3.1.tgz"
    download_url: "https://splunkbase.splunk.com/app/1617/"

# Elasticsearch Configuration
elasticsearch:
  version: "8.11.0"
  download_url: "https://artifacts.elastic.co/downloads/elasticsearch/elasticsearch-8.11.0-linux-x86_64.tar.gz"
  
  # Installation
  install_path: "/opt/elasticsearch"
  data_path: "/var/lib/elasticsearch"
  logs_path: "/var/log/elasticsearch"
  
  # Cluster configuration
  cluster:
    name: "goad-blue-cluster"
    initial_master_nodes: ["goad-blue-es-01"]
    
  # Node configuration
  node:
    name: "{{ ansible_hostname }}"
    roles: ["master", "data", "ingest"]
    
  # Network configuration
  network:
    host: "0.0.0.0"
    port: 9200
    
  # Security configuration
  security:
    enabled: true
    ssl_enabled: true
    
  # Index configuration
  indices:
    goad-blue-windows:
      number_of_shards: 1
      number_of_replicas: 0
      
    goad-blue-linux:
      number_of_shards: 1
      number_of_replicas: 0
      
    goad-blue-network:
      number_of_shards: 2
      number_of_replicas: 0

# Kibana Configuration
kibana:
  version: "8.11.0"
  download_url: "https://artifacts.elastic.co/downloads/kibana/kibana-8.11.0-linux-x86_64.tar.gz"
  
  # Installation
  install_path: "/opt/kibana"
  
  # Server configuration
  server:
    port: 5601
    host: "0.0.0.0"
    name: "{{ ansible_hostname }}"
    
  # Elasticsearch configuration
  elasticsearch:
    hosts: ["http://localhost:9200"]
    username: "kibana_system"
    password: "{{ vault_kibana_password }}"

# Logstash Configuration
logstash:
  version: "8.11.0"
  download_url: "https://artifacts.elastic.co/downloads/logstash/logstash-8.11.0-linux-x86_64.tar.gz"
  
  # Installation
  install_path: "/opt/logstash"
  
  # Pipeline configuration
  pipeline:
    workers: 4
    batch_size: 125
    batch_delay: 50
    
  # Input configuration
  inputs:
    beats:
      port: 5044
      
    syslog:
      port: 514
      
    tcp:
      port: 5000

# Beats Configuration
beats:
  filebeat:
    version: "8.11.0"
    download_url: "https://artifacts.elastic.co/downloads/beats/filebeat/filebeat-8.11.0-linux-x86_64.tar.gz"
    
    # Output configuration
    output:
      logstash:
        hosts: ["localhost:5044"]
        
  winlogbeat:
    version: "8.11.0"
    download_url: "https://artifacts.elastic.co/downloads/beats/winlogbeat/winlogbeat-8.11.0-windows-x86_64.zip"
    
    # Event logs to monitor
    event_logs:
      - name: Application
      - name: System
      - name: Security
      - name: Microsoft-Windows-Sysmon/Operational
      - name: Microsoft-Windows-PowerShell/Operational
