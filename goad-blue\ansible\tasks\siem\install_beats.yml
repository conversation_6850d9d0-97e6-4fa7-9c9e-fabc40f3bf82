---
# Install and configure Elastic Beats for GOAD-Blue

- name: Install Filebeat (Linux)
  block:
    - name: Download Filebeat
      get_url:
        url: "{{ beats.filebeat.download_url }}"
        dest: "/tmp/filebeat-{{ beats.filebeat.version }}.tar.gz"
        mode: '0644'

    - name: Extract Filebeat
      unarchive:
        src: "/tmp/filebeat-{{ beats.filebeat.version }}.tar.gz"
        dest: /opt
        remote_src: yes
        creates: "/opt/filebeat-{{ beats.filebeat.version }}"

    - name: Create Filebeat symlink
      file:
        src: "/opt/filebeat-{{ beats.filebeat.version }}"
        dest: /opt/filebeat
        state: link

    - name: Create Filebeat user
      user:
        name: filebeat
        system: yes
        shell: /bin/false
        home: /var/lib/filebeat
        create_home: yes

    - name: Create Filebeat directories
      file:
        path: "{{ item }}"
        state: directory
        owner: filebeat
        group: filebeat
        mode: '0755'
      loop:
        - /etc/filebeat
        - /var/log/filebeat
        - /var/lib/filebeat

    - name: Configure Filebeat
      template:
        src: filebeat.yml.j2
        dest: /etc/filebeat/filebeat.yml
        owner: filebeat
        group: filebeat
        mode: '0600'
        backup: yes
      notify: restart filebeat

    - name: Create Filebeat systemd service
      template:
        src: filebeat.service.j2
        dest: /etc/systemd/system/filebeat.service
        mode: '0644'
      notify:
        - reload systemd
        - restart filebeat

    - name: Start and enable Filebeat
      systemd:
        name: filebeat
        state: started
        enabled: yes
        daemon_reload: yes

  when: ansible_os_family != "Windows"

- name: Install Winlogbeat (Windows)
  block:
    - name: Create Winlogbeat directory
      win_file:
        path: C:\Program Files\Winlogbeat
        state: directory

    - name: Download Winlogbeat
      win_get_url:
        url: "{{ beats.winlogbeat.download_url }}"
        dest: C:\temp\winlogbeat.zip

    - name: Extract Winlogbeat
      win_unzip:
        src: C:\temp\winlogbeat.zip
        dest: C:\Program Files\Winlogbeat
        creates: C:\Program Files\Winlogbeat\winlogbeat.exe

    - name: Configure Winlogbeat
      win_template:
        src: winlogbeat.yml.j2
        dest: C:\Program Files\Winlogbeat\winlogbeat.yml
        backup: yes

    - name: Install Winlogbeat service
      win_shell: |
        cd "C:\Program Files\Winlogbeat"
        .\winlogbeat.exe --service.name winlogbeat install
      args:
        creates: C:\Program Files\Winlogbeat\winlogbeat.exe

    - name: Start Winlogbeat service
      win_service:
        name: winlogbeat
        state: started
        start_mode: auto

  when: ansible_os_family == "Windows"

- name: Configure Filebeat modules for GOAD-Blue
  shell: |
    cd /opt/filebeat
    ./filebeat modules enable system
    ./filebeat modules enable auditd
    ./filebeat modules enable apache
    ./filebeat modules enable nginx
    ./filebeat modules enable mysql
    ./filebeat modules enable suricata
    ./filebeat modules enable zeek
  become_user: filebeat
  when: ansible_os_family != "Windows"

- name: Configure custom Filebeat inputs for GOAD
  template:
    src: filebeat-goad-inputs.yml.j2
    dest: /etc/filebeat/conf.d/goad-inputs.yml
    owner: filebeat
    group: filebeat
    mode: '0644'
  notify: restart filebeat
  when: ansible_os_family != "Windows"

- name: Setup Filebeat index template
  shell: |
    cd /opt/filebeat
    ./filebeat setup --index-management -E output.logstash.enabled=false -E 'output.elasticsearch.hosts=["{{ elasticsearch.network.host }}:{{ elasticsearch.network.port }}"]'
  become_user: filebeat
  when: ansible_os_family != "Windows"

- name: Install Metricbeat for system monitoring
  block:
    - name: Download Metricbeat
      get_url:
        url: "https://artifacts.elastic.co/downloads/beats/metricbeat/metricbeat-{{ beats.filebeat.version }}-linux-x86_64.tar.gz"
        dest: "/tmp/metricbeat-{{ beats.filebeat.version }}.tar.gz"
        mode: '0644'

    - name: Extract Metricbeat
      unarchive:
        src: "/tmp/metricbeat-{{ beats.filebeat.version }}.tar.gz"
        dest: /opt
        remote_src: yes
        creates: "/opt/metricbeat-{{ beats.filebeat.version }}"

    - name: Create Metricbeat symlink
      file:
        src: "/opt/metricbeat-{{ beats.filebeat.version }}"
        dest: /opt/metricbeat
        state: link

    - name: Create Metricbeat user
      user:
        name: metricbeat
        system: yes
        shell: /bin/false
        home: /var/lib/metricbeat
        create_home: yes

    - name: Configure Metricbeat
      template:
        src: metricbeat.yml.j2
        dest: /etc/metricbeat/metricbeat.yml
        owner: metricbeat
        group: metricbeat
        mode: '0600'

    - name: Create Metricbeat systemd service
      template:
        src: metricbeat.service.j2
        dest: /etc/systemd/system/metricbeat.service
        mode: '0644'
      notify:
        - reload systemd
        - restart metricbeat

    - name: Start and enable Metricbeat
      systemd:
        name: metricbeat
        state: started
        enabled: yes
        daemon_reload: yes

  when: ansible_os_family != "Windows"

- name: Install Packetbeat for network monitoring
  block:
    - name: Download Packetbeat
      get_url:
        url: "https://artifacts.elastic.co/downloads/beats/packetbeat/packetbeat-{{ beats.filebeat.version }}-linux-x86_64.tar.gz"
        dest: "/tmp/packetbeat-{{ beats.filebeat.version }}.tar.gz"
        mode: '0644'

    - name: Extract Packetbeat
      unarchive:
        src: "/tmp/packetbeat-{{ beats.filebeat.version }}.tar.gz"
        dest: /opt
        remote_src: yes
        creates: "/opt/packetbeat-{{ beats.filebeat.version }}"

    - name: Create Packetbeat symlink
      file:
        src: "/opt/packetbeat-{{ beats.filebeat.version }}"
        dest: /opt/packetbeat
        state: link

    - name: Create Packetbeat user
      user:
        name: packetbeat
        system: yes
        shell: /bin/false
        home: /var/lib/packetbeat
        create_home: yes

    - name: Configure Packetbeat
      template:
        src: packetbeat.yml.j2
        dest: /etc/packetbeat/packetbeat.yml
        owner: packetbeat
        group: packetbeat
        mode: '0600'

    - name: Create Packetbeat systemd service
      template:
        src: packetbeat.service.j2
        dest: /etc/systemd/system/packetbeat.service
        mode: '0644'
      notify:
        - reload systemd
        - restart packetbeat

    - name: Start and enable Packetbeat
      systemd:
        name: packetbeat
        state: started
        enabled: yes
        daemon_reload: yes

  when: ansible_os_family != "Windows"

- name: Verify Beats connectivity to Elasticsearch
  uri:
    url: "http://{{ elasticsearch.network.host }}:{{ elasticsearch.network.port }}/_cat/indices/filebeat-*"
    method: GET
  register: filebeat_indices
  until: filebeat_indices.status == 200
  retries: 10
  delay: 30
  when: ansible_os_family != "Windows"

- name: Display Beats installation status
  debug:
    msg: |
      Elastic Beats installation completed!
      
      Installed Beats:
      {% if ansible_os_family != "Windows" %}
      - Filebeat: /opt/filebeat
      - Metricbeat: /opt/metricbeat
      - Packetbeat: /opt/packetbeat
      {% else %}
      - Winlogbeat: C:\Program Files\Winlogbeat
      {% endif %}
      
      Elasticsearch Connection: {{ elasticsearch.network.host }}:{{ elasticsearch.network.port }}
      {% if filebeat_indices is defined %}
      Filebeat Indices: {{ filebeat_indices.content.split('\n') | length - 1 }} indices found
      {% endif %}
