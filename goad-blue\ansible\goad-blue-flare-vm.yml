---
# GOAD-Blue FLARE-VM Deployment Playbook
# Deploys and configures FLARE-VM for malware analysis

- name: Deploy FLARE-VM
  hosts: flare_vm_servers
  vars_files:
    - vars/goad-blue-config.yml
    - vars/flare-vm-config.yml
  
  pre_tasks:
    - name: Validate FLARE-VM requirements
      assert:
        that:
          - ansible_os_family == "Windows"
          - ansible_memtotal_mb >= 8192
          - ansible_processor_vcpus >= 2
        fail_msg: "FLARE-VM requires Windows with minimum 8GB RAM and 2 CPU cores"

    - name: Check Windows version compatibility
      win_shell: |
        (Get-WmiObject -Class Win32_OperatingSystem).Version
      register: windows_version

    - name: Validate Windows version
      assert:
        that:
          - windows_version.stdout | regex_search('10\.|2016|2019|2022')
        fail_msg: "FLARE-VM requires Windows 10 or Windows Server 2016+"

  tasks:
    - name: Disable Windows Defender
      include_tasks: tasks/flare_vm/disable_defender.yml

    - name: Configure Windows for malware analysis
      include_tasks: tasks/flare_vm/configure_windows.yml

    - name: Install Chocolatey package manager
      include_tasks: tasks/flare_vm/install_chocolatey.yml

    - name: Download FLARE-VM installation script
      win_get_url:
        url: "{{ flare_vm.install_script_url }}"
        dest: "C:\\temp\\install-flarevm.ps1"

    - name: Run FLARE-VM installation
      include_tasks: tasks/flare_vm/run_installation.yml

    - name: Configure FLARE-VM for GOAD-Blue integration
      include_tasks: tasks/flare_vm/configure_goad_integration.yml

    - name: Install additional analysis tools
      include_tasks: tasks/flare_vm/install_additional_tools.yml

    - name: Configure malware analysis environment
      include_tasks: tasks/flare_vm/configure_analysis_environment.yml

    - name: Setup isolated network configuration
      include_tasks: tasks/flare_vm/configure_network_isolation.yml

    - name: Configure VM snapshots
      include_tasks: tasks/flare_vm/configure_snapshots.yml

    - name: Install GOAD-Blue analysis scripts
      include_tasks: tasks/flare_vm/install_analysis_scripts.yml

    - name: Configure log forwarding to SIEM
      include_tasks: tasks/flare_vm/configure_log_forwarding.yml

    - name: Setup malware sample management
      include_tasks: tasks/flare_vm/setup_sample_management.yml

    - name: Configure analysis reporting
      include_tasks: tasks/flare_vm/configure_reporting.yml

    - name: Validate FLARE-VM deployment
      include_tasks: tasks/flare_vm/validate_deployment.yml

  post_tasks:
    - name: Generate FLARE-VM access information
      template:
        src: templates/flare_vm_access_info.j2
        dest: "{{ playbook_dir }}/output/flare_vm_access.yml"
      delegate_to: localhost

    - name: Display FLARE-VM deployment summary
      debug:
        msg: |
          FLARE-VM Deployment Complete:
          Version: {{ flare_vm.version }}
          RDP Access: {{ ansible_default_ipv4.address }}:3389
          Analysis User: {{ flare_vm.analysis_user }}
          Tools Installed: {{ flare_vm_tools_count }}
          Network Mode: {{ flare_vm.network_mode }}

- name: Configure FLARE-VM Analysis Environment
  hosts: flare_vm_servers
  vars_files:
    - vars/goad-blue-config.yml
    - vars/flare-vm-config.yml
  
  tasks:
    - name: Create analysis directories
      win_file:
        path: "{{ item }}"
        state: directory
      loop:
        - "C:\\MalwareAnalysis"
        - "C:\\MalwareAnalysis\\Samples"
        - "C:\\MalwareAnalysis\\Reports"
        - "C:\\MalwareAnalysis\\Tools"
        - "C:\\MalwareAnalysis\\Scripts"

    - name: Install custom analysis tools
      include_tasks: tasks/flare_vm/install_custom_tools.yml

    - name: Configure analysis automation
      include_tasks: tasks/flare_vm/configure_automation.yml

    - name: Setup malware detonation chamber
      include_tasks: tasks/flare_vm/setup_detonation_chamber.yml

    - name: Configure YARA rules
      include_tasks: tasks/flare_vm/configure_yara_rules.yml

    - name: Setup Cuckoo Sandbox integration
      include_tasks: tasks/flare_vm/setup_cuckoo_integration.yml
      when: flare_vm.enable_cuckoo | default(false)

    - name: Configure REMnux integration
      include_tasks: tasks/flare_vm/configure_remnux_integration.yml
      when: flare_vm.enable_remnux | default(false)

    - name: Setup analysis workflow
      include_tasks: tasks/flare_vm/setup_analysis_workflow.yml

    - name: Configure threat intelligence integration
      include_tasks: tasks/flare_vm/configure_threat_intel.yml

    - name: Setup automated reporting
      include_tasks: tasks/flare_vm/setup_automated_reporting.yml
