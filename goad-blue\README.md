# GOAD-Blue: Full-Spectrum Cybersecurity Training Platform

<div align="center">
  <h1>🛡️ GOAD-Blue 🛡️</h1>
  <p><strong>Blue Team Enhancement for GOAD (Game of Active Directory)</strong></p>
</div>

## Overview

GOAD-Blue is a comprehensive blue team enhancement that transforms the original GOAD red team Active Directory lab into a full-spectrum cybersecurity training and testing platform. It integrates defensive capabilities alongside existing offensive tools, creating an environment for both red team and blue team operations.

## 🎯 Key Features

- **Modular Architecture**: User-selectable components via interactive CLI
- **Multi-Platform Support**: AWS, Azure, VMware ESXi, Proxmox, VirtualBox
- **Centralized SIEM Integration**: Splunk Enterprise or Elastic Stack
- **Comprehensive Tool Suite**: Network monitoring, endpoint visibility, threat intelligence, and malware analysis
- **Infrastructure as Code**: Packer, Terraform, and Ansible automation
- **Seamless GOAD Integration**: Works with existing GOAD environments

## 🏗️ Architecture

### Network Segmentation

| Subnet | CIDR | Purpose | Components |
|--------|------|---------|------------|
| Management | 192.168.100.0/26 | SIEM & Central Services | Splunk/ELK, MISP |
| Monitoring | **************/26 | Security Tools | Security Onion, Malcolm, Velociraptor |
| Red Team | ***************/26 | GOAD Environment | Domain Controllers, Servers |
| Analysis | ***************/26 | Malware Analysis | FLARE-VM, Isolated Analysis |

## 🔧 Components

### 🔎 Centralized SIEM (Select One)
- **Splunk Enterprise**: Complete SIEM with Security Essentials, custom TAs, and CIM normalization
- **Elastic Stack (ELK)**: Elasticsearch + Logstash + Kibana with ECS mapping

### 🔐 Network & Host Monitoring
- **Security Onion**: Suricata, Zeek, Wazuh for network and host intrusion detection
- **Malcolm**: Network forensics platform (PCAP, Zeek, Suricata, Arkime, OpenSearch)
- **Velociraptor**: Endpoint visibility and DFIR capabilities

### 🧠 Threat Intelligence
- **MISP**: Threat intelligence sharing platform with IOC correlation

### 🧪 Malware Analysis
- **FLARE-VM**: Windows 10 analysis environment with comprehensive toolset

## 🚀 Quick Start

> **New to GOAD-Blue?**
>
> **[📖 Follow the First Steps Guide →](docs/first-steps.md)**
>
> Complete beginner-friendly guide that gets you up and running in 5 minutes!

### Three Deployment Options

| Option | Best For | Time | Requirements |
|--------|----------|------|--------------|
| **🏠 Home Lab** | Learning & Development | 30 min | VirtualBox, 32GB RAM |
| **☁️ Cloud** | Teams & Scalability | 15 min | AWS/Azure/GCP Account |
| **🏢 Enterprise** | Production Training | 20 min | VMware/Proxmox |

### One-Line Deployment

```bash
# Home Lab (VirtualBox)
git clone https://github.com/your-org/goad-blue.git && cd goad-blue && vagrant up

# Cloud (AWS)
cd terraform/providers/aws && terraform apply -var-file="production.tfvars"

# Enterprise (VMware)
cd terraform/providers/vmware && terraform apply -var-file="enterprise.tfvars"
```

## 📚 Documentation

- **[🚀 First Steps Guide](docs/first-steps.md)** - Quick start for beginners
- **[📖 Complete Documentation](docs/index.md)** - Full documentation site
- **[⚙️ Configuration](docs/configuration/index.md)** - Detailed configuration
- **[🔧 Deployment](docs/deployment/index.md)** - Infrastructure as Code
- **[🎓 Training Scenarios](docs/scenarios/index.md)** - Hands-on exercises
- **[🔍 Troubleshooting](docs/troubleshooting/index.md)** - Common issues and solutions

## 🔗 Integration with GOAD

GOAD-Blue seamlessly integrates with existing GOAD environments:

1. **Automatic Discovery**: Detects existing GOAD instances
2. **Agent Deployment**: Installs monitoring agents on GOAD VMs
3. **Log Integration**: Forwards GOAD logs to chosen SIEM
4. **Network Monitoring**: Captures traffic between GOAD components

## 🎭 Use Cases

### Red Team vs Blue Team Exercises
- Simulate attacks using GOAD environment
- Detect and respond using GOAD-Blue tools
- Practice incident response workflows

### Security Training
- Learn SIEM operations with real attack data
- Practice threat hunting techniques
- Understand attack-defense correlation

### Tool Evaluation
- Test security tools in realistic environment
- Compare detection capabilities
- Validate security controls

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines.

## 📄 License

This project is licensed under the GPL-3.0 License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Original GOAD project by Orange Cyberdefense
- Security Onion team
- Splunk and Elastic communities
- MISP project contributors
